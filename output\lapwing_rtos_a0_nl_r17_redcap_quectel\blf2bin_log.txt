

FBFMake Version: 4.9.1.3
FBFMake Date: 2021-9-7


PreTempForDownload....

ProcessBLF.....
ASREXT platform type

Parsing Blf file: D:/xy695/quectel_build/release/RG255AACNAAR01A01M16_OCPU_MIFI_TESTDW/RG255AACNAAR01A01M16_OCPU_MIFI_TESTDW.blf
ASREXT platform type
Parsing Reserved data.....
complete to parsing reserved data
Parsing ExtendedReservedData

complete to parse ExtendedReservedData
begin to parsing Erase Only Area

begin to parsing image
Number of images in blf file is 8
Complete to parsing image info in blf file
complete to parsing blf file


ProcessBLF return.....

ProcessBLF return.....
preparing image data.....

Platform is .....

Prepare FBF list.....
Prepare FBF list return.....
Platform is .....

Preparing  DKBlist

complete to prepare  DKBlist

Tim file name is D:\xy695\quectel_build\tools\MakeFBFPackage\temp\DKB_ntimheader.bin

Generating image bin ....

GenerateTim.....
Tim file name is D:\xy695\quectel_build\release\RG255AACNAAR01A01M16_OCPU_MIFI_TESTDW\ntim_ddr.bin

GenerateTim  Successfully.....
Complete to prepare image data.....

EraseAll is 0
ResetBBT is 0
NandID is 0x0
parsing  Blf File ....

Writing Device Header:
m_dev.nOfImages = 8
m_dev.DeviceFlags = 0

Image file name:Not a filename
image header:
image->Image_ID = 0x00000000 
image->Image_In_TIM = 0x0 
image->Flash_partition = 0x00000000 
image->Flash_erase_size = 0x00020000 
image->commands = 0x2 
image->First_Sector = 0x0 
image->length = 0x00000000 
image->Flash_Start_Address = 0x00000000 
image->ChecksumFormatVersion2 = 0x0 

Image file name:D:\xy695\quectel_build\release\RG255AACNAAR01A01M16_OCPU_MIFI_TESTDW\AIC_FMACFW.bin
image header:
image->Image_ID = 0x57494649 
image->Image_In_TIM = 0x1 
image->Flash_partition = 0x00000000 
image->Flash_erase_size = 0x00080000 
image->commands = 0x3 
image->First_Sector = 0x1 
image->length = 0x00010000 
image->Flash_Start_Address = 0x00940000 
image->ChecksumFormatVersion2 = 0xf3b2a5d 

Image file name:D:\xy695\quectel_build\release\RG255AACNAAR01A01M16_OCPU_MIFI_TESTDW\WebData_compressed.bin
image header:
image->Image_ID = 0x57454249 
image->Image_In_TIM = 0x1 
image->Flash_partition = 0x00000000 
image->Flash_erase_size = 0x00100000 
image->commands = 0x3 
image->First_Sector = 0x11 
image->length = 0x00067c54 
image->Flash_Start_Address = 0x00840000 
image->ChecksumFormatVersion2 = 0x3e71011b 

Image file name:D:\xy695\quectel_build\release\RG255AACNAAR01A01M16_OCPU_MIFI_TESTDW\LAPWING_A0_INT_BX2_OVL_RTOS_lzma.bin
image header:
image->Image_ID = 0x47524249 
image->Image_In_TIM = 0x1 
image->Flash_partition = 0x00000000 
image->Flash_erase_size = 0x00180000 
image->commands = 0x73 
image->First_Sector = 0x79 
image->length = 0x00155000 
image->Flash_Start_Address = 0x006c0000 
image->ChecksumFormatVersion2 = 0xc01ab7eb 

Image file name:D:\xy695\quectel_build\release\RG255AACNAAR01A01M16_OCPU_MIFI_TESTDW\LAPWING_RTOS_NL_R17_REDCAP.cponly_lzma.bin
image header:
image->Image_ID = 0x4f534c4f 
image->Image_In_TIM = 0x1 
image->Flash_partition = 0x00000000 
image->Flash_erase_size = 0x00680000 
image->commands = 0x73 
image->First_Sector = 0x1ce 
image->length = 0x00442000 
image->Flash_Start_Address = 0x00040000 
image->ChecksumFormatVersion2 = 0x42dda9da 

Image file name:D:\xy695\quectel_build\release\RG255AACNAAR01A01M16_OCPU_MIFI_TESTDW\rf_lzma.bin
image header:
image->Image_ID = 0x5246424e 
image->Image_In_TIM = 0x1 
image->Flash_partition = 0x00000000 
image->Flash_erase_size = 0x00010000 
image->commands = 0x73 
image->First_Sector = 0x610 
image->length = 0x00000bfc 
image->Flash_Start_Address = 0x00030000 
image->ChecksumFormatVersion2 = 0xfedc61ba 

Image file name:D:\xy695\quectel_build\release\RG255AACNAAR01A01M16_OCPU_MIFI_TESTDW\ReliableData.bin
image header:
image->Image_ID = 0x52424c49 
image->Image_In_TIM = 0x1 
image->Flash_partition = 0x00000000 
image->Flash_erase_size = 0x00010000 
image->commands = 0x3 
image->First_Sector = 0x611 
image->length = 0x00010000 
image->Flash_Start_Address = 0x00020000 
image->ChecksumFormatVersion2 = 0x2f5bd696 

Image file name:D:\xy695\quectel_build\release\RG255AACNAAR01A01M16_OCPU_MIFI_TESTDW\ntim_ddr.bin
image header:
image->Image_ID = 0x54494d48 
image->Image_In_TIM = 0x1 
image->Flash_partition = 0x00000000 
image->Flash_erase_size = 0x0001d4a0 
image->commands = 0x3 
image->First_Sector = 0x621 
image->length = 0x0001d4a0 
image->Flash_Start_Address = 0x00000000 
image->ChecksumFormatVersion2 = 0xc56f708e 

dump to fbf file
Finish to dump to fbf file
Complete to Create FBF Bin
Successfully to generate FBF bin file

Creating NTIM for FBF....
Tim file name is D:\xy695\quectel_build\tools\MakeFBFPackage\temp\FBF_Ntimheader.bin
Complete to create NTIM for FBF....

Successfully to prepare temp folder file for wtptp download
Complete to prepare fbf files for download



DoFBFMake successfully....
