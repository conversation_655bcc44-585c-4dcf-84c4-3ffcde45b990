# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.19

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: OBM
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================
# Object build statements for EXECUTABLE target Lapwing_Loader_EVB_QSPI_quectel


#############################################
# Order-only phony target for Lapwing_Loader_EVB_QSPI_quectel

build cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel: phony || CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\QSPI\spi_nor.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI\spi_nor.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\QSPI\spi_nor.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\QSPI
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\QSPI\spi_nor.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\mpu.o: ASM_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\StartUp\mpu.s || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\mpu.o.d
  FLAGS = --cpu=Cortex-R4 --no_unaligned_access --predefine "BOOTROM SETA 0" --predefine "WKNG SETA 1" --predefine "DDRBASE SETA 1" --predefine "ISRAMBASE SETA 0" --predefine "DDR_BASE_ADDRESS SETA 0x06000000"
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\mpu.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\bl_StartUp_ttc.o: ASM_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\StartUp\bl_StartUp_ttc.s || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\bl_StartUp_ttc.o.d
  FLAGS = --cpu=Cortex-R4 --no_unaligned_access --predefine "BOOTROM SETA 0" --predefine "WKNG SETA 1" --predefine "DDRBASE SETA 1" --predefine "ISRAMBASE SETA 0" --predefine "DDR_BASE_ADDRESS SETA 0x06000000"
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\bl_StartUp_ttc.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\version_block.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\StartUp\version_block.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\version_block.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\version_block.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Platforms\WKNG\platform_arch.o: ASM_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\Platforms\WKNG\platform_arch.s || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Platforms\WKNG\platform_arch.o.d
  FLAGS = --cpu=Cortex-R4 --no_unaligned_access --predefine "BOOTROM SETA 0" --predefine "WKNG SETA 1" --predefine "DDRBASE SETA 1" --predefine "ISRAMBASE SETA 0" --predefine "DDR_BASE_ADDRESS SETA 0x06000000"
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Platforms\WKNG
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Platforms\WKNG\platform_arch.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Platforms\WKNG\platform_StartUp.o: ASM_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\Platforms\WKNG\platform_StartUp.s || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Platforms\WKNG\platform_StartUp.o.d
  FLAGS = --cpu=Cortex-R4 --no_unaligned_access --predefine "BOOTROM SETA 0" --predefine "WKNG SETA 1" --predefine "DDRBASE SETA 1" --predefine "ISRAMBASE SETA 0" --predefine "DDR_BASE_ADDRESS SETA 0x06000000"
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Platforms\WKNG
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Platforms\WKNG\platform_StartUp.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\bbu_PI2C.o: ASM_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\StartUp\bbu_PI2C.s || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\bbu_PI2C.o.d
  FLAGS = --cpu=Cortex-R4 --no_unaligned_access --predefine "BOOTROM SETA 0" --predefine "WKNG SETA 1" --predefine "DDRBASE SETA 1" --predefine "ISRAMBASE SETA 0" --predefine "DDR_BASE_ADDRESS SETA 0x06000000"
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\bbu_PI2C.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\bbu_CI2C.o: ASM_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\StartUp\bbu_CI2C.s || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\bbu_CI2C.o.d
  FLAGS = --cpu=Cortex-R4 --no_unaligned_access --predefine "BOOTROM SETA 0" --predefine "WKNG SETA 1" --predefine "DDRBASE SETA 1" --predefine "ISRAMBASE SETA 0" --predefine "DDR_BASE_ADDRESS SETA 0x06000000"
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\bbu_CI2C.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\oled.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\I2C\oled.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\oled.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\oled.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\misc.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Misc\misc.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\misc.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\misc.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\timer.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Misc\timer.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\timer.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\timer.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\RegInstructions.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Misc\RegInstructions.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\RegInstructions.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\RegInstructions.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\keypad.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Misc\keypad.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\keypad.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\keypad.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\print.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Misc\print.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\print.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\print.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\tr069.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Misc\tr069.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\tr069.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\tr069.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\Flash.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Flash\Flash.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\Flash.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\Flash.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\FM.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Flash\FM.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\FM.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\FM.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DMA\dma.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\DMA\dma.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DMA\dma.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DMA
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DMA\dma.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\BootLoader.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\Main\BootLoader.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\BootLoader.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\BootLoader.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\BootMode.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\Main\BootMode.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\BootMode.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\BootMode.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\DownloadMode.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\Main\DownloadMode.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\DownloadMode.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\DownloadMode.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\TIMDownload.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\Main\TIMDownload.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\TIMDownload.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\TIMDownload.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\serial.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\Main\serial.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\serial.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\serial.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\FreqChange.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\Main\FreqChange.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\FreqChange.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\FreqChange.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\wdt.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\Main\wdt.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\wdt.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\wdt.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\qpress.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\Main\qpress.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\qpress.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\qpress.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\quicklz.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\Main\quicklz.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\quicklz.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\quicklz.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\mpu.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\Main\mpu.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\mpu.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\mpu.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\mpu_api.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\StartUp\mpu_api.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\mpu_api.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\mpu_api.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Platforms\WKNG\PlatformConfig.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\PlatformConfig.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Platforms\WKNG\PlatformConfig.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Platforms\WKNG
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Platforms\WKNG\PlatformConfig.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Platforms\WKNG\platform_interrupts.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\platform_interrupts.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Platforms\WKNG\platform_interrupts.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Platforms\WKNG
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Platforms\WKNG\platform_interrupts.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\ProtocolManager.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Download\ProtocolManager.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\ProtocolManager.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\ProtocolManager.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb_descriptors.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Download\usb_descriptors.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb_descriptors.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb_descriptors.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb2_enumeration.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Download\usb2_enumeration.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb2_enumeration.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb2_enumeration.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb2_main.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Download\usb2_main.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb2_main.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb2_main.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb2_memory.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Download\usb2_memory.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb2_memory.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb2_memory.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usbapi.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Download\usbapi.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usbapi.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usbapi.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\SDRam\DDR_Cfg.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\SDRam\DDR_Cfg.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\SDRam\DDR_Cfg.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\SDRam
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\SDRam\DDR_Cfg.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Tim\tim.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Tim\tim.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Tim\tim.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Tim
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Tim\tim.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\ADC_PM803\adc_pm803.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803\adc_pm803.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\ADC_PM803\adc_pm803.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\ADC_PM803
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\ADC_PM803\adc_pm803.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\I2C.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\I2C\I2C.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\I2C.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\I2C.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\charger.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\I2C\charger.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\charger.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\charger.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\oled_lib.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\I2C\oled_lib.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\oled_lib.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\oled_lib.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\QSPI\qspi_host.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI\qspi_host.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\QSPI\qspi_host.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\QSPI
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\QSPI\qspi_host.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\ustica.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\I2C\ustica.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\ustica.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\ustica.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\guilin.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\I2C\guilin.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\guilin.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\guilin.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\guilin_lite.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\I2C\guilin_lite.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\guilin_lite.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\guilin_lite.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DFota\Bsdiff\bspatch.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff\bspatch.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DFota\Bsdiff\bspatch.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DFota\Bsdiff
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DFota\Bsdiff\bspatch.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DFota\Bsdiff\tinyalloc.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff\tinyalloc.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DFota\Bsdiff\tinyalloc.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DFota\Bsdiff
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DFota\Bsdiff\tinyalloc.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\LzmaDecode.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Loader\Main\LzmaDecode.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\LzmaDecode.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\LzmaDecode.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\st7735s.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\I2C\st7735s.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\st7735s.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\st7735s.o.rsp

build CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\bq24259.o: C_COMPILER__Lapwing_Loader_EVB_QSPI_quectel_ D$:\xy695\PLT\OBM\Lapwing\Common\I2C\bq24259.c || cmake_object_order_depends_target_Lapwing_Loader_EVB_QSPI_quectel
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\bq24259.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167
  INCLUDES = -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\bq24259.o.rsp


# =============================================================================
# Link build statements for EXECUTABLE target Lapwing_Loader_EVB_QSPI_quectel


#############################################
# Link the executable bin\Lapwing_Loader_EVB_QSPI_quectel.elf

build bin\Lapwing_Loader_EVB_QSPI_quectel.elf: C_EXECUTABLE_LINKER__Lapwing_Loader_EVB_QSPI_quectel_ CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\QSPI\spi_nor.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\mpu.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\bl_StartUp_ttc.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\version_block.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Platforms\WKNG\platform_arch.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Platforms\WKNG\platform_StartUp.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\bbu_PI2C.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\bbu_CI2C.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\oled.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\misc.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\timer.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\RegInstructions.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\keypad.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\print.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Misc\tr069.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\Flash.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\FM.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DMA\dma.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\BootLoader.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\BootMode.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\DownloadMode.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\TIMDownload.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\serial.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\FreqChange.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\wdt.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\qpress.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\quicklz.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\mpu.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\StartUp\mpu_api.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Platforms\WKNG\PlatformConfig.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Platforms\WKNG\platform_interrupts.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\ProtocolManager.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb_descriptors.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb2_enumeration.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb2_main.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usb2_memory.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Download\usbapi.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\SDRam\DDR_Cfg.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Tim\tim.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\ADC_PM803\adc_pm803.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\I2C.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\charger.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\oled_lib.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\Flash\QSPI\qspi_host.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\ustica.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\guilin.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\guilin_lite.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DFota\Bsdiff\bspatch.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\DFota\Bsdiff\tinyalloc.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Loader\Main\LzmaDecode.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\st7735s.o CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir\Common\I2C\bq24259.o | D$:\xy695\PLT\quectel\lib\boot\libql_bootloader.a quectel\app\libquectel-bootloader-app.a
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork
  LINK_FLAGS = --elf --scatter=D:/xy695/PLT/OBM/Lapwing/build/Lapwing_Loader_Common.sct --map --symbols --info=sizes,totals
  LINK_LIBRARIES = D:\xy695\PLT\quectel\lib\boot\libql_bootloader.a  quectel\app\libquectel-bootloader-app.a
  OBJECT_DIR = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.dir
  POST_BUILD = cmd.exe /C "cd /D D:\xy695\output\Lapwing_Loader_EVB_QSPI_quectel && "F:\DS-5 v5.26.0\sw\ARMCompiler5.06u4\bin\fromelf.exe" --bin bin/Lapwing_Loader_EVB_QSPI_quectel.elf --output bin/Lapwing_Loader_EVB_QSPI_quectel.bin && D:\xy695\ENV\misc\windows-x86\tree.exe -L 1 -hs -C -D --dirsfirst --noreport --nolinks D:/xy695/output/Lapwing_Loader_EVB_QSPI_quectel/bin && "F:\Program Files (x86)\Git\usr\bin\perl.exe" D:/xy695/PLT/OBM/Lapwing/Common/ADC_PM803/tool/external_ext_bin_size_update.pl bin/Lapwing_Loader_EVB_QSPI_quectel.bin OBM_SIZE && "F:\Program Files (x86)\Git\usr\bin\perl.exe" D:/xy695/PLT/OBM/Lapwing/Common/ADC_PM803/tool/external_ext_bin_append.pl OBMAPPEND bin/Lapwing_Loader_EVB_QSPI_quectel.bin OBM_SIZE D:/xy695/PLT/OBM/Lapwing/Common/ADC_PM803/lib/dsp_ADC_lzma.bin ADC_SIZE y && "F:\Program Files (x86)\Git\usr\bin\perl.exe" D:/xy695/PLT/OBM/Lapwing/Common/ADC_PM803/tool/external_ext_bin_size_update.pl bin/Lapwing_Loader_EVB_QSPI_quectel.bin EXT_SIZE && "F:\Program Files (x86)\Git\usr\bin\perl.exe" D:/xy695/PLT/OBM/Lapwing/Common/ADC_PM803/tool/external_append_pad_crc.pl bin/Lapwing_Loader_EVB_QSPI_quectel.bin"
  PRE_LINK = cd .
  TARGET_FILE = bin\Lapwing_Loader_EVB_QSPI_quectel.elf
  TARGET_PDB = Lapwing_Loader_EVB_QSPI_quectel.elf.dbg
  RSP_FILE = CMakeFiles\Lapwing_Loader_EVB_QSPI_quectel.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\xy695\output\Lapwing_Loader_EVB_QSPI_quectel && D:\xy695\ENV\cmake\windows-x86\bin\cmake-gui.exe -SD:\xy695\PLT\OBM\Lapwing -BD:\xy695\output\Lapwing_Loader_EVB_QSPI_quectel"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\xy695\output\Lapwing_Loader_EVB_QSPI_quectel && D:\xy695\ENV\cmake\windows-x86\bin\cmake.exe --regenerate-during-build -SD:\xy695\PLT\OBM\Lapwing -BD:\xy695\output\Lapwing_Loader_EVB_QSPI_quectel"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/xy695/PLT/OBM/Lapwing/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target quectel-bootloader-app_objects


#############################################
# Order-only phony target for quectel-bootloader-app_objects

build cmake_object_order_depends_target_quectel-bootloader-app_objects: phony || quectel\app\CMakeFiles\quectel-bootloader-app_objects.dir

build quectel\app\CMakeFiles\quectel-bootloader-app_objects.dir\quec_boot_platform.o: C_COMPILER__quectel-bootloader-app_objects_ D$:\xy695\PLT\quectel\open\bootloader\app\quec_boot_platform.c || cmake_object_order_depends_target_quectel-bootloader-app_objects
  DEFINES = -DAUTO_BOOTUP=0 -DAZW_OLED=0 -DBACKUP_IMAGE=0 -DBL_USE_GEU_FUSE_PROG=0 -DBL_USE_SECURITY_CALLBACK=0 -DBOARD_DEBUG=0 -DBOOTROM=0 -DBQ24259=1 -DCI2_USB_DDR_BUF=1 -DCONFIG_QL_PROJECT_DEF_RG255AA -DCONFIG_QUEC_NORFLASH_ERASE_OPTIMIZE -DCONFIG_QUEC_PROJECT_BOOTLOADER_COMPILE -DCOPYIMAGESTOFLASH=1 -DDDR_BASE_ADDRESS=0x06000000 -DDECOMPRESS_SUPPORT=1 -DDFOTA_SUPPORT=1 -DDMA=0 -DDOWNLOAD_USED=1 -DENABLE_LOAD_MRD=0 -DETA6005=0 -DFAN540X=0 -DFAN_54013=0 -DFBF=0 -DFBF_NEW=1 -DGUILIN=1 -DGUILINLITE=1 -DHASH_VERIFICATION="" -DHSI=0 -DI2C=1 -DICASE=0 -DJTAG_PROTOCOL_OVER_JTAG_SUPPORTED=0 -DLAPWING=1 -DLED_DISPLAY=0 -DLINUX_BUILD=0 -DLWG_LTG_SUPPORT=1 -DMIFI3=0 -DMIFI_V2R0=0 -DMIFI_V2R1=0 -DMIFI_V3R0=0 -DMIFI_V3R1=0 -DMIFI_V3R2=0 -DMIFI_V3R3=0 -DMMC_CODE=0 -DMMC_DEBUG=0 -DMMC_SDMA_MODE=0 -DMPU_ENABLE=1 -DMPU_SUPPORT=1 -DMRD_CHECK=1 -DNAND_CODE=0 -DNEZHA_MIFI_V3R1=0 -DNEZHA_MIFI_V4R1=0 -DNEZHA_MIFI_V4R1R1=0 -DNEZHA_MIFI_V5R0=1 -DNOR_CODE=0 -DOLED_SUPPORT=1 -DONLY_DISABLE_WD=0 -DPM8607=0 -DPM8609=0 -DPRODUCTION_MODE_SUPPORT=1 -DPRODUCT_BUILD=0 -DPROTOCOL_JTAG_USED=0 -DQPRESS=0 -DQSPINAND_CODE=0 -DQSPINOR_CODE=1 -DQUECTEL_PROJECT_CUST -DRT8973=0 -DRT9455=0 -DSANREMO=0 -DSBOOT=0 -DSH1106=0 -DSILENT_RESET=0 -DSPINAND_CODE=0 -DSPI_CODE=0 -DSSD1306=0 -DST7735S=1 -DTR069_SUPPORT=1 -DTRUSTED=0 -DUART_UPLOAD=0 -DUPDATE_USE_ATCMD=0 -DUPDATE_USE_DETECT_USB=1 -DUPDATE_USE_GPIO=0 -DUPDATE_USE_KEY=0 -DUSBCI=0 -DUSE_AP_UART=0 -DUSE_HAPS=0 -DUSE_IPPCP_LIB=0 -DUSE_SERIAL_DEBUG=1 -DUSTICA=1 -DVERBOSE_DEBUG=0 -DVERBOSE_MODE=1 -DWKNG=1 -D__PMIC_TYPE_MACRO=PMIC_GUILIN_LITE
  DEP_FILE = quectel\app\CMakeFiles\quectel-bootloader-app_objects.dir\quec_boot_platform.o.d
  FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork -g -Ospace --no_inline --split_sections -W --gnu --diag_suppress 268,167 --diag_suppress=61 --diag_suppress=188
  INCLUDES = -ID:\xy695\PLT\quectel\open\bootloader\inc -ID:\xy695\PLT\quectel\open\system\inc -ID:\xy695\PLT\quectel\open\system\inc\os -ID:\xy695\PLT\quectel\open\system\inc\hal -ID:\xy695\PLT\OBM\Lapwing\Common\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\Include -ID:\xy695\PLT\OBM\Lapwing\Common\Download -ID:\xy695\PLT\OBM\Lapwing\Common\Flash -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\DFC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\NAND -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\XIP -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC -ID:\xy695\PLT\OBM\Lapwing\Common\Flash\SDMMC\SDHC2 -ID:\xy695\PLT\OBM\Lapwing\Common\Misc -ID:\xy695\PLT\OBM\Lapwing\Common\SDRam -ID:\xy695\PLT\OBM\Lapwing\Common\Tim -ID:\xy695\PLT\OBM\Lapwing\Loader\Main -ID:\xy695\PLT\OBM\Lapwing\Loader\StartUp -ID:\xy695\PLT\OBM\Lapwing\Common\I2C -ID:\xy695\PLT\OBM\Lapwing\Common\DMA -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Xdelta -ID:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff -ID:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803 -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\GEU\WKNG -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\FuseBlock -ID:\xy695\PLT\OBM\Lapwing\Common\SecureBoot\cryptlib -ID:\xy695\PLT\OBM\Lapwing\Loader\SecureBoot\SecurityCallBack -ID:\xy695\output\quectel -ID:\xy695\PLT\quectel\open\common_api\include -ID:\xy695\PLT\hal\GPIO\inc
  OBJECT_DIR = quectel\app\CMakeFiles\quectel-bootloader-app_objects.dir
  OBJECT_FILE_DIR = quectel\app\CMakeFiles\quectel-bootloader-app_objects.dir
  RSP_FILE = quectel\app\CMakeFiles\quectel-bootloader-app_objects.dir\quec_boot_platform.o.rsp



#############################################
# Object library quectel-bootloader-app_objects

build quectel\app\quectel-bootloader-app_objects: phony quectel\app\CMakeFiles\quectel-bootloader-app_objects.dir\quec_boot_platform.o

# =============================================================================
# Object build statements for STATIC_LIBRARY target quectel-bootloader-app


#############################################
# Order-only phony target for quectel-bootloader-app

build cmake_object_order_depends_target_quectel-bootloader-app: phony || cmake_object_order_depends_target_quectel-bootloader-app_objects


# =============================================================================
# Link build statements for STATIC_LIBRARY target quectel-bootloader-app


#############################################
# Link the static library quectel\app\libquectel-bootloader-app.a

build quectel\app\libquectel-bootloader-app.a: C_STATIC_LIBRARY_LINKER__quectel-bootloader-app_ quectel\app\CMakeFiles\quectel-bootloader-app_objects.dir\quec_boot_platform.o || quectel\app\quectel-bootloader-app_objects
  LANGUAGE_COMPILE_FLAGS = --cpu=7-R --fpu=softvfp --littleend --apcs=/interwork
  OBJECT_DIR = quectel\app\CMakeFiles\quectel-bootloader-app.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = quectel\app\libquectel-bootloader-app.a
  TARGET_PDB = quectel-bootloader-app.a.dbg
  RSP_FILE = CMakeFiles\quectel-bootloader-app.rsp


#############################################
# Utility command for edit_cache

build quectel\app\CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\xy695\output\Lapwing_Loader_EVB_QSPI_quectel\quectel\app && D:\xy695\ENV\cmake\windows-x86\bin\cmake-gui.exe -SD:\xy695\PLT\OBM\Lapwing -BD:\xy695\output\Lapwing_Loader_EVB_QSPI_quectel"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build quectel\app\edit_cache: phony quectel\app\CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build quectel\app\CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\xy695\output\Lapwing_Loader_EVB_QSPI_quectel\quectel\app && D:\xy695\ENV\cmake\windows-x86\bin\cmake.exe --regenerate-during-build -SD:\xy695\PLT\OBM\Lapwing -BD:\xy695\output\Lapwing_Loader_EVB_QSPI_quectel"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build quectel\app\rebuild_cache: phony quectel\app\CMakeFiles\rebuild_cache.util

# =============================================================================
# Target aliases.

build Lapwing_Loader_EVB_QSPI_quectel: phony bin\Lapwing_Loader_EVB_QSPI_quectel.elf

build Lapwing_Loader_EVB_QSPI_quectel.elf: phony bin\Lapwing_Loader_EVB_QSPI_quectel.elf

build libquectel-bootloader-app.a: phony quectel\app\libquectel-bootloader-app.a

build quectel-bootloader-app: phony quectel\app\libquectel-bootloader-app.a

build quectel-bootloader-app_objects: phony quectel\app\quectel-bootloader-app_objects

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/xy695/output/Lapwing_Loader_EVB_QSPI_quectel

build all: phony bin\Lapwing_Loader_EVB_QSPI_quectel.elf quectel\app\all

# =============================================================================

#############################################
# Folder: D:/xy695/output/Lapwing_Loader_EVB_QSPI_quectel/quectel/app

build quectel\app\all: phony quectel\app\quectel-bootloader-app_objects quectel\app\libquectel-bootloader-app.a

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles\3.19.2\CMakeASMCompiler.cmake CMakeFiles\3.19.2\CMakeCCompiler.cmake CMakeFiles\3.19.2\CMakeSystem.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeASMInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeCInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeCommonLanguageInclude.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeGenericSystem.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeInitializeConfigs.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeLanguageInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeSystemSpecificInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeSystemSpecificInitialize.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Compiler\ARMCC-ASM.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Compiler\ARMCC-C.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Compiler\ARMCC.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Platform\Generic.cmake D$:\xy695\PLT\OBM\Lapwing\CMakeLists.txt D$:\xy695\PLT\OBM\Lapwing\build\CMakeLists_OBM_toolchain_mcu.cmake D$:\xy695\PLT\OBM\Lapwing\build\Lapwing_Loader_EVB_QSPI_quectel_config.cmake D$:\xy695\PLT\quectel\open\bootloader\app\CMakeLists.txt D$:\xy695\PLT\quectel\open\bootloader\inc\ql_boot_pro_config.h.in D$:\xy695\quectel_build\config\obm_config.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles\3.19.2\CMakeASMCompiler.cmake CMakeFiles\3.19.2\CMakeCCompiler.cmake CMakeFiles\3.19.2\CMakeSystem.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeASMInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeCInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeCommonLanguageInclude.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeGenericSystem.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeInitializeConfigs.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeLanguageInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeSystemSpecificInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeSystemSpecificInitialize.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Compiler\ARMCC-ASM.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Compiler\ARMCC-C.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Compiler\ARMCC.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Platform\Generic.cmake D$:\xy695\PLT\OBM\Lapwing\CMakeLists.txt D$:\xy695\PLT\OBM\Lapwing\build\CMakeLists_OBM_toolchain_mcu.cmake D$:\xy695\PLT\OBM\Lapwing\build\Lapwing_Loader_EVB_QSPI_quectel_config.cmake D$:\xy695\PLT\quectel\open\bootloader\app\CMakeLists.txt D$:\xy695\PLT\quectel\open\bootloader\inc\ql_boot_pro_config.h.in D$:\xy695\quectel_build\config\obm_config.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
