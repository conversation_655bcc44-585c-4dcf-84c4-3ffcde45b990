//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_mep.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/mep.c
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
typedef UINT8 MEP_BCD ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 number [ 32 ] ;	 
 UINT8 length ;	 
 } MEP_PASSWORD;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT16 year ;	 
 UINT8 month ;	 
 UINT8 day ;	 
 } MEP_DATE;

typedef UINT16 MEP_MNC ;
typedef UINT16 MEP_MCC ;
typedef UINT16 MEP_AccessTechnologyId ;
typedef UINT8 MEP_Boolean ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 serviceproviderId [ 4 ] ;	 
 } MEP_CODE_SP;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 corporateId [ 4 ] ;	 
 } MEP_CODE_CP;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_SIMPLMN networkIds [ 100 ] ;	 
 MEP_CODE_NS lockNS [ 100 ] ;	 
 MEP_CODE_SP lockSP [ 100 ] ;	 
 MEP_CODE_CP lockCP [ 100 ] ;	 
 MEP_CODE_SIMUSIM SimUsim ;	 
 } MEP_CODE;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_CODE code ;	 
 UINT8 DefaultSiActivated ;	 
 UINT8 DefaultSiEnabled ;	 
 } MEP_CAT_DATA;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_PASSWORD CatPsw [ MEP_MAX_CAT ] ;	 
 MEP_PASSWORD UnblockPsw ;	 
 // #if defined ( SS_IPC_SUPPORT ) && defined ( EXTENED_TRIAL_LIMIT_MEP )	 
	 
	 
 UINT8 TrialLimit [ MEP_MAX_CAT ] ;	 
	 
	 
	 
 } MEP_BLOCK_DATA;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 /*modified for #499867 2014.031800 by yunhail begin*/	 
 UINT32 VersionSize ;	 
 /*modified for #499867 2014.031800 by yunhail end*/	 
 MEP_CAT_DATA data ; // !!! Run F8 And F9 on this field	 
 MEP_BLOCK_DATA blocking ; // !!! Run F8 And F9 on this field	 
 UINT8 signature [ 20 ] ; // This is the output of the F9 function	 
 UINT8 DataIsEncrypted ; // Should be set to FALSE or TRUE -- do not run F8 | F9	 
 } MEP_FIX;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 SiActivated ;	 
 UINT8 SiEnabled ;	 
 UINT8 TrialCounter [ MEP_MAX_CAT ] ;	 
 UINT8 padding [ 15 ] ;	 
 UINT8 signature [ 20 ] ;	 
 } MEP_CHANGEABLE;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 iccid [ 10 ] ;	 
 MEP_DATE date ;	 
 } UDP_ASL_DATA;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UDP_ASL_DATA data [ 10 ] ;	 
 UINT8 NumAslEntries ;	 
 } UDP_ASL;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_PASSWORD psw ;	 
 UDP_ASL asl ;	 
 UINT8 si ;	 
 UINT8 padding [ 15 ] ;	 
 UINT8 signature [ 20 ] ;	 
 } UDP_CHANGEABLE;

typedef void ( *MEP_Callback ) ( MEP_UDP_RC* ) ;
typedef union
 {
 void ( *MEP_PutTC_Callback ) ( MEP_UDP_RC* ) ;
 void ( *MEP_PutSI_Callback ) ( MEP_UDP_RC* ) ;
 void ( *UDP_PutSI_Callback ) ( MEP_UDP_RC* ) ;
 void ( *UDP_PutASL_Callback ) ( MEP_UDP_RC* ) ;
 void ( *UDP_PutPassword_Callback ) ( MEP_UDP_RC* ) ;
 } MEP_UDP_CallBackFuncS ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 ulDebugInfoTag ;	 
 UINT32 ulMrdInfo ;	 
 UINT32 ulMepInfo ;	 
 } MRD_DEBUG_INFO;

typedef unsigned int size_t ;
typedef va_list __gnuc_va_list ;
typedef unsigned int size_t ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0 = 0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 , GPIO_PIN_33 , GPIO_PIN_34 , GPIO_PIN_35 , GPIO_PIN_36 , GPIO_PIN_37 , GPIO_PIN_38 , GPIO_PIN_39 ,	 
	 
 GPIO_PIN_40 , GPIO_PIN_41 , GPIO_PIN_42 , GPIO_PIN_43 , GPIO_PIN_44 , GPIO_PIN_45 , GPIO_PIN_46 , GPIO_PIN_47 ,	 
 GPIO_PIN_48 , GPIO_PIN_49 , GPIO_PIN_50 , GPIO_PIN_51 , GPIO_PIN_52 , GPIO_PIN_53 , GPIO_PIN_54 , GPIO_PIN_55 ,	 
 GPIO_PIN_56 , GPIO_PIN_57 , GPIO_PIN_58 , GPIO_PIN_59 , GPIO_PIN_60 , GPIO_PIN_61 , GPIO_PIN_62 , GPIO_PIN_63 ,	 
	 
 GPIO_PIN_64 , GPIO_PIN_65 , GPIO_PIN_66 , GPIO_PIN_67 , GPIO_PIN_68 , GPIO_PIN_69 , GPIO_PIN_70 , GPIO_PIN_71 ,	 
 GPIO_PIN_72 , GPIO_PIN_73 , GPIO_PIN_74 , GPIO_PIN_75 , GPIO_PIN_76 , GPIO_PIN_77 , GPIO_PIN_78 , GPIO_PIN_79 ,	 
 GPIO_PIN_80 , GPIO_PIN_81 , GPIO_PIN_82 , GPIO_PIN_83 , GPIO_PIN_84 , GPIO_PIN_85 , GPIO_PIN_86 , GPIO_PIN_87 ,	 
 GPIO_PIN_88 , GPIO_PIN_89 , GPIO_PIN_90 , GPIO_PIN_91 , GPIO_PIN_92 , GPIO_PIN_93 , GPIO_PIN_94 , GPIO_PIN_95 ,	 
	 
 GPIO_PIN_96 , GPIO_PIN_97 , GPIO_PIN_98 , GPIO_PIN_99 , GPIO_PIN_100 , GPIO_PIN_101 , GPIO_PIN_102 , GPIO_PIN_103 ,	 
 GPIO_PIN_104 , GPIO_PIN_105 , GPIO_PIN_106 , GPIO_PIN_107 , GPIO_PIN_108 , GPIO_PIN_109 , GPIO_PIN_110 , GPIO_PIN_111 ,	 
 GPIO_PIN_112 , GPIO_PIN_113 , GPIO_PIN_114 , GPIO_PIN_115 , GPIO_PIN_116 , GPIO_PIN_117 , GPIO_PIN_118 , GPIO_PIN_119 ,	 
 GPIO_PIN_120 , GPIO_PIN_121 , GPIO_PIN_122 , GPIO_PIN_123 , GPIO_PIN_124 , GPIO_PIN_125 , GPIO_PIN_126 , GPIO_PIN_127 ,	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
typedef UINT32 Attribs_Mmu ;
typedef void ( * mmuNotifyEventFunc ) ( MMU_ERROR_STATUS mmuErrorHandle ) ;
typedef union {
 rw_region_item compress_rw_region_list [ 64 ] ;

 UINT8 filer [ 2048 ] ;
 } LOADTABLE_AREA_RW_CPZ_INFO ;
typedef unsigned char BYTE ;
typedef unsigned short int WORD ;
typedef unsigned long DWORD ;
typedef WORD * WORD_PTR ;
typedef DWORD * DWORD_PTR ;
typedef BYTE BYTE_BITMASK ;
typedef VOID_PTR * VOID_PTR_PTR ;
typedef WORD FLASH_DATA_WIDTH ;
typedef char FDI_TCHAR ;
typedef OSASemaRef SEM_ID ;
typedef int SEM_STATUS ;
typedef SEM_MTX * SEM_MTX_ID ;
typedef WORD IDTYPE ;
typedef DATA_LOOKUP * DATA_LOOKUP_PTR ;
typedef DATA_LOCATION * DATA_LOC_PTR ;
typedef OPEN_PARAM * OPEN_STREAM_PTR ;
typedef COMMAND * COMMAND_PTR ;
typedef UNIT_HEADER * UNIT_HDR_PTR ;
typedef MULTI_INSTANCE * MULTI_INST_PTR ;
typedef BLOCK_INFO * BLK_INFO_PTR ;
typedef COMMAND_CONTROL * CMD_CNTRL_PTR ;
typedef WORD FILE_ID ;
typedef UINT32 lowTaskEventHandler_t ;
typedef void ( *LowEventFuncPtr ) ( lowTaskEventHandler_t ) ;
typedef unsigned char Ipp8u ;
typedef unsigned short Ipp16u ;
typedef unsigned int Ipp32u ;
typedef signed char Ipp8s ;
typedef signed short Ipp16s ;
typedef signed int Ipp32s ;
typedef float Ipp32f ;
typedef long long Ipp64s ;
typedef unsigned long long Ipp64u ;
typedef double Ipp64f ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
DIAG_FILTER ( SW_PLAT , MEP , CheckSignatureFail , DIAG_ERROR)  
 diagPrintf ( " Error -- Mep signature checking failed " );

DIAG_FILTER ( SW_PLAT , MEP , CheckSignature , DIAG_INFORMATION)  
 diagPrintf ( " Signature checking finish ok " );

DIAG_FILTER ( SW_PLAT , MEP , ippsSHA1MessageDigestFail , DIAG_ERROR)  
 diagPrintf ( " Error -- Integrity checking failed " );

DIAG_FILTER ( SW_PLAT , MEP , ippsSHA1MessageDigest , DIAG_INFORMATION)  
 diagPrintf ( " Integrity checking finish ok " );

DIAG_FILTER ( SW_PLAT , MEP , ippsRijndael128DecryptCBCFail , DIAG_ERROR)  
 diagPrintf ( " Error -- ippsRijndael128 Decrypt CBC " );

DIAG_FILTER ( SW_PLAT , MEP , ippsRijndael128DecryptCBC , DIAG_INFORMATION)  
 diagPrintf ( " Decrypt ippsRijndael128 finish ok " );

DIAG_FILTER ( SW_PLAT , MEP , ippsRijndael128EncryptCBCFail , DIAG_ERROR)  
 diagPrintf ( " Error -- ippsRijndael128 Encrypt CBC " );

DIAG_FILTER ( SW_PLAT , MEP , ippsRijndael128EncryptCBC , DIAG_INFORMATION)  
 diagPrintf ( " Encrypt ippsRijndael128 finish ok " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_MAIN_FIX_DATA , DIAG_INFORMATION)  
 diagPrintf ( " Initialization Mep fix data base " );

DIAG_FILTER ( SW_PLAT , MEP , RunConfidentiality_start , DIAG_INFORMATION)  
 diagPrintf ( " Encrypt mep --- Run Confidentiality started " );

DIAG_FILTER ( SW_PLAT , MEP , RunConfidentiality_ended , DIAG_INFORMATION)  
 diagPrintf ( " Encrypt mep --- Run Confidentiality ended " );

DIAG_FILTER ( SW_PLAT , MEP , RunIntegrity_start , DIAG_INFORMATION)  
 diagPrintf ( " Integrity mep --- Run Integrity started " );

DIAG_FILTER ( SW_PLAT , MEP , RunIntegrity_end , DIAG_INFORMATION)  
 diagPrintf ( " Integrity mep --- Run Integrity ended " );

DIAG_FILTER ( SW_PLAT , MEP , UdpRunConfidentiality_start , DIAG_INFORMATION)  
 diagPrintf ( " Encrypt udp --- Run Confidentiality started " );

DIAG_FILTER ( SW_PLAT , MEP , UdpRunConfidentiality_ended , DIAG_INFORMATION)  
 diagPrintf ( " Encrypt udp --- Run Confidentiality ended " );

DIAG_FILTER ( SW_PLAT , MEP , UdpRunIntegrity_start , DIAG_INFORMATION)  
 diagPrintf ( " Integrity udp --- Run Integrity started " );

DIAG_FILTER ( SW_PLAT , MEP , UdpRunIntegrity_end , DIAG_INFORMATION)  
 diagPrintf ( " Integrity udp --- Run Integrity ended " );

//ICAT EXPORTED STRUCT 
 typedef struct NVM_Header 
 {	 
 unsigned long StructSize ; // the size of the user structure below	 
 unsigned long NumofStructs ; // >1 in case of array of structs ( default is 1 ) .	 
 char StructName [ 64 ] ; // the name of the user structure below	 
 char Date [ 32 ] ; // date updated by the ICAT when the file is saved. Filled by calibration SW.	 
 char time [ 32 ] ; // time updated by the ICAT when the file is saved. Filled by calibration SW.	 
 char Version [ 64 ] ; // user version - this field is updated by the SW eng. Every time they update the UserStruct.	 
 char HW_ID [ 32 ] ; // signifies the board number. Filled by calibration SW.	 
 char CalibVersion [ 32 ] ; // signifies the calibration SW version. Filled by calibration SW.	 
 } NVM_Header_ts;

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE , DIAG_INFORMATION)  
 diagPrintf ( " Start saving mep file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE_HEADER , DIAG_INFORMATION)  
 diagPrintf ( " the mep.nvm header write fail " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE_END , DIAG_INFORMATION)  
 diagPrintf ( " Saving mep file end " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_WRITE_FILE , DIAG_ERROR)  
 diagPrintf ( " Error -- Writing to MEP file failed " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CLOSE_FILE_start , DIAG_INFORMATION)  
 diagPrintf ( " Start closing mep file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CLOSE_FILE , DIAG_INFORMATION)  
 diagPrintf ( " Mep file closed " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE_FAIL , DIAG_ERROR)  
 diagPrintf ( " Error -- Cant open MEP file for save " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE_2 , DIAG_INFORMATION)  
 diagPrintf ( " Start saving mep file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE_HEADER_2 , DIAG_INFORMATION)  
 diagPrintf ( " the mep.nvm header write fail " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE_END_2 , DIAG_INFORMATION)  
 diagPrintf ( " Saving mep file end " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_WRITE_FILE_2 , DIAG_ERROR)  
 diagPrintf ( " Error -- Writing to MEP file failed " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CLOSE_FILE_start_2 , DIAG_INFORMATION)  
 diagPrintf ( " Start closing mep file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CLOSE_FILE_2 , DIAG_INFORMATION)  
 diagPrintf ( " Mep file closed " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE_FAIL_2 , DIAG_ERROR)  
 diagPrintf ( " Error -- Cant open MEP file for save " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_FILE , DIAG_INFORMATION)  
 diagPrintf ( " Start saving udp file " );

DIAG_FILTER ( SW_PLAT , UDP , UDP_SAVE_FILE_HEADER , DIAG_INFORMATION)  
 diagPrintf ( " the UDP.nvm header write fail " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_FILE_END , DIAG_INFORMATION)  
 diagPrintf ( " Saving udp file end " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_WRITE_FILE , DIAG_ERROR)  
 diagPrintf ( " Error -- Writing to UDP file failed " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_CLOSE_FILE_start , DIAG_INFORMATION)  
 diagPrintf ( " Start closing udp file " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_CLOSE_FILE , DIAG_INFORMATION)  
 diagPrintf ( " Udp file closed " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_FILE_FAIL , DIAG_ERROR)  
 diagPrintf ( " Error -- Cant open UDP file for save " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CHANGEABLE_OPEN_RO , DIAG_INFORMATION)  
 diagPrintf ( " Opening MEP Changeable Data file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CHANGEABLE_OPEN_RW , DIAG_INFORMATION)  
 diagPrintf ( " Creating new MEP changeable data file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_DEFAULT_IMEI , DIAG_INFORMATION)  
 diagPrintf ( " Got default IMEI - MEP changeable data file wasn ' t created " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CHANGEABLE_OPEN_RO_2 , DIAG_INFORMATION)  
 diagPrintf ( " Opening MEP Changeable Data file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CHANGEABLE_OPEN_RW_2 , DIAG_INFORMATION)  
 diagPrintf ( " Creating new MEP changeable data file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_DEFAULT_IMEI_2 , DIAG_INFORMATION)  
 diagPrintf ( " Got default IMEI - MEP changeable data file wasn ' t created " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_CHANGEABLE_OPEN_RO , DIAG_INFORMATION)  
 diagPrintf ( " Opening UDP Changeable Data file " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_CHANGEABLE_OPEN_RW , DIAG_INFORMATION)  
 diagPrintf ( " Creating new UDP changeable data file " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_DEFAULT_IMEI , DIAG_INFORMATION)  
 diagPrintf ( " Got default IMEI - UDP changeable data file wasn ' t created " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb1 , DIAG_INFORMATION)  
 diagPrintf ( " Udp calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb_end1 , DIAG_INFORMATION)  
 diagPrintf ( " Udp after CB " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb_end2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp after CB " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb1_2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb_end1_2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp after CB " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb2_2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb_end2_2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp after CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb , DIAG_INFORMATION)  
 diagPrintf ( " Udp calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb_end , DIAG_INFORMATION)  
 diagPrintf ( " Udp after CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb1 , DIAG_INFORMATION)  
 diagPrintf ( " Mep calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb_end1 , DIAG_INFORMATION)  
 diagPrintf ( " Mep after CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb_2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb_end_2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp after CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb1_2 , DIAG_INFORMATION)  
 diagPrintf ( " Mep calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb_end1_2 , DIAG_INFORMATION)  
 diagPrintf ( " Mep after CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetCK_CMD , DIAG_INFORMATION)  
 diagPrintf ( " MEP got CK read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetGC_CMD , DIAG_INFORMATION)  
 diagPrintf ( " MEP got GC read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetGC_CMD_2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP 2 got GC read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetTC_CMD , DIAG_INFORMATION)  
 diagPrintf ( " MEP got TC read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetTC_CMD_2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP 2 got TC read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetSI_CMD_2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP 2 got SI read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetTL_CMD , DIAG_INFORMATION)  
 diagPrintf ( " MEP got TL read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetTL_CMD_2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP 2 got TL read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetUPW_CMD , DIAG_INFORMATION)  
 diagPrintf ( " MEP got UPSW read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetUPW_CMD_2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP 2 got UPSW read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutTC_CMD , DIAG_INFORMATION)  
 diagPrintf ( " MEP got TC write command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutTC_CMD1 , DIAG_INFORMATION)  
 diagPrintf ( " MEP_PutTC errorCode=%d " , mep->MepErrorCode );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutTC_CMD2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP_PutTC second para is valid " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutTC_CMD3 , DIAG_INFORMATION)  
 diagPrintf ( " MEP_SaveMepData send sigal fail " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutTC_CMD_2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP 2 got TC write command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutSI_CMD , DIAG_INFORMATION)  
 diagPrintf ( " MEP got SI write command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutSI_CMD1 , DIAG_INFORMATION)  
 diagPrintf ( " MEP_PutSI errorCode=%d " , mep->MepErrorCode );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutSI_CMD_2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP 2 got SI write command " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_GetSI_CMD , DIAG_INFORMATION)  
 diagPrintf ( " UDP got SI read command " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_GetASL_CMD , DIAG_INFORMATION)  
 diagPrintf ( " UDP got ASL read command " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_GetPassword_CMD , DIAG_INFORMATION)  
 diagPrintf ( " UDP got PSW read command " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_PutSI_CMD , DIAG_INFORMATION)  
 diagPrintf ( " UDP got SI write command " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_PutASL_CMD , DIAG_INFORMATION)  
 diagPrintf ( " UDP got ASL write command " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_PutPassword_CMD , DIAG_INFORMATION)  
 diagPrintf ( " UDP got PSW write command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_MAIN_FIX , DIAG_INFORMATION)  
 diagPrintf ( " Mep fix data base init finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_MAIN_CHANGEABLE , DIAG_INFORMATION)  
 diagPrintf ( " Mep changeable data init finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_MAIN_FIX , DIAG_INFORMATION)  
 diagPrintf ( " Mep fix data base init finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_MAIN_CHANGEABLE , DIAG_INFORMATION)  
 diagPrintf ( " Mep changeable data init finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_MAIN_CHANGEABLE , DIAG_INFORMATION)  
 diagPrintf ( " Udp changeable data init finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_MAIN_CHANGEABLE , DIAG_INFORMATION)  
 diagPrintf ( " Udp changeable data init finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutTC_Callback , DIAG_INFORMATION)  
 diagPrintf ( " MEP_PutTC_Callback finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutSI_Callback , DIAG_INFORMATION)  
 diagPrintf ( " MEP_PutSI_Callback finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_PutSI_Callback , DIAG_INFORMATION)  
 diagPrintf ( " UDP_PutSI_Callback finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_PutASL_Callback , DIAG_INFORMATION)  
 diagPrintf ( " UDP_PutASL_Callback finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_PutPassword_Callback , DIAG_INFORMATION)  
 diagPrintf ( " UDP_PutPassword_Callback finished ok " );

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_read_si 
 void mep_read_si ( void ) 
 {	 
 UINT8 SiActivated , SiEnabled ;	 
	 
	 
 MEP_GetSI ( &SiActivated , &SiEnabled ) ;	 
	 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_si , DIAG_INFORMATION)  
 diagPrintf ( " Mep SiActivated value is = %x and SiEnabled is = %x " , SiActivated , SiEnabled );

	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_write_si 
 void mep_write_si ( void *p ) 
 {	 
 UINT8 *param = ( UINT8 * ) p ;	 
	 
	 
 cb.MEP_PutSI_Callback = MEP_PutSI_Callback ;	 
 MEP_PutSI ( &param [ 0 ] , &param [ 1 ] , &cb ) ;	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_read_tl 
 void mep_read_tl ( void ) 
 {	 
 UINT8 tl ;	 
 MEP_UDP_RC rc ;	 
	 
 MEP_CAT category ;	 
	 
 for ( category = MEP_CAT_NP ; category < MEP_MAX_CAT ; category ++ )	 
 {		 
 rc = MEP_GetTL ( category , &tl ) ;		 
 if ( rc != MEP_UDP_RC_OK )		 
 {			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_tl_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading MEP - TL " );

			 
 return ;			 
 }		 
		 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_tl , DIAG_INFORMATION)  
 diagPrintf ( " Mep TL [ cat: %d ] value is = %x " , category , tl );

		 
 }	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_read_upsw 
 void mep_read_upsw ( void ) 
 {	 
 MEP_PASSWORD Upsw ;	 
 MEP_UDP_RC rc ;	 
	 
	 
 rc = ME_GetUPW ( &Upsw ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_upsw_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading MEP - UPSW " );

		 
 return ;		 
 }	 
	 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_upw , DIAG_INFORMATION)  
 diagPrintf ( " Mep u_psw value is = %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , length = %x " , Upsw.number [ 0 ] , Upsw.number [ 1 ] , Upsw.number [ 2 ] , Upsw.number [ 3 ] , Upsw.number [ 4 ] , Upsw.number [ 5 ] , Upsw.number [ 6 ] , Upsw.number [ 7 ] , Upsw.number [ 8 ] , Upsw.number [ 9 ] , Upsw.number [ 10 ] , Upsw.number [ 11 ] , Upsw.number [ 12 ] , Upsw.number [ 13 ] , Upsw.number [ 14 ] , Upsw.number [ 15 ] , Upsw.length );

	 
	 
	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_read_ck 
 void mep_read_ck ( void ) 
 {	 
 MEP_PASSWORD ck ;	 
 MEP_UDP_RC rc ;	 
 MEP_CAT category ;	 
	 
	 
 for ( category = MEP_CAT_NP ; category < MEP_MAX_CAT ; category++ )	 
 {		 
 rc = MEP_GetCK ( category , &ck ) ;		 
 if ( rc != MEP_UDP_RC_OK )		 
 {			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_ck_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading MEP - CK " );

			 
 return ;			 
 }		 
		 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_ck , DIAG_INFORMATION)  
 diagPrintf ( " Mep CK of category %x value is = %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , length = %x " , category , ck.number [ 0 ] , ck.number [ 1 ] , ck.number [ 2 ] , ck.number [ 3 ] , ck.number [ 4 ] , ck.number [ 5 ] , ck.number [ 6 ] , ck.number [ 7 ] , ck.number [ 8 ] , ck.number [ 9 ] , ck.number [ 10 ] , ck.number [ 11 ] , ck.number [ 12 ] , ck.number [ 13 ] , ck.number [ 14 ] , ck.number [ 15 ] , ck.length );

		 
		 
		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_read_gc 
 void mep_read_gc ( void ) 
 {	 
 MEP_CODE gc ;	 
 MEP_UDP_RC rc ;	 
 MEP_CODE_ID codeId ;	 
 UINT8 i ;	 
	 
 for ( codeId = MEP_CODE_ID_NP ; codeId < MEP_MAX_CODE_ID_CAT ; codeId++ )	 
 {		 
 rc = MEP_GetGC ( codeId , &gc ) ;		 
 if ( rc != MEP_UDP_RC_OK )		 
 {			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_gc_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading MEP - GC " );

			 
 return ;			 
 }		 
		 
 switch ( codeId )		 
 {			 
 case MEP_CODE_ID_NP :			 
 for ( i = 0 ; i < 100 ; i++ )			 
 {				 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_gc_NP , DIAG_INFORMATION)  
 diagPrintf ( " NP code_id value is mcc [ %x ] = %x , mnc [ %x ] = %x , at [ %x ] = %x , ThreeDigitsDecoded [ %x ] = %x " , i , gc.networkIds [ i ] .plmn.mcc , i , gc.networkIds [ i ] .plmn.mnc , i , gc.networkIds [ i ] .plmn.AccessTechnology , i , gc.networkIds [ i ] .mncThreeDigitsDecoding );

				 
				 
 }			 
 break ;			 
 case MEP_CODE_ID_NSP :			 
			 
			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_gc_NSP , DIAG_INFORMATION)  
 diagPrintf ( " NSP code_id value is = %x , " , gc.lockNS [ 0 ] .networkSubsetId [ 0 ] );

			 
			 
 break ;			 
 case MEP_CODE_ID_SP :			 
			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_gc_SP , DIAG_INFORMATION)  
 diagPrintf ( " SP code_id value is = %x " , gc.lockSP [ 0 ] .serviceproviderId [ 0 ] );

			 
			 
 break ;			 
 case MEP_CODE_ID_CP :			 
			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_gc_CP , DIAG_INFORMATION)  
 diagPrintf ( " CP code_id value is = %x " , gc.lockCP [ 0 ] .corporateId [ 0 ] );

			 
			 
 break ;			 
 case MEP_CODE_ID_SIM_LOCK :			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_gc_SIMLOCK , DIAG_INFORMATION)  
 diagPrintf ( " SIM_LOCK code_id length is = %x , contents = %x , %x , %x , %x , %x , %x , %x , %x " , gc.SimUsim.simId.length , gc.SimUsim.simId.contents [ 0 ] , gc.SimUsim.simId.contents [ 1 ] , gc.SimUsim.simId.contents [ 2 ] , gc.SimUsim.simId.contents [ 3 ] , gc.SimUsim.simId.contents [ 4 ] , gc.SimUsim.simId.contents [ 5 ] , gc.SimUsim.simId.contents [ 6 ] , gc.SimUsim.simId.contents [ 7 ] );

			 
			 
			 
 break ;			 
 default:			 
 return ;			 
 }		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_read_tc 
 void mep_read_tc ( void ) 
 {	 
 UINT8 tc ;	 
 MEP_UDP_RC rc ;	 
 MEP_CAT category ;	 
	 
	 
 for ( category = MEP_CAT_NP ; category < MEP_MAX_CAT ; category++ )	 
 {		 
 rc = MEP_GetTC ( category , &tc ) ;		 
 if ( rc != MEP_UDP_RC_OK )		 
 {			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_tc_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading MEP - TC " );

			 
 return ;			 
 }		 
		 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_gc , DIAG_INFORMATION)  
 diagPrintf ( " Mep TC of category %x value is = %x " , category , tc );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , udp_read_si 
 void udp_read_si ( void ) 
 {	 
 UINT8 SiActivated ;	 
 MEP_UDP_RC rc ;	 
	 
	 
 rc = UDP_GetSI ( &SiActivated ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_si_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading UDP - SI or UDP is not support " );

		 
 return ;		 
 }	 
	 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_si , DIAG_INFORMATION)  
 diagPrintf ( " Udp SiActivated value is = %x " , SiActivated );

	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , udp_read_psw 
 void udp_read_psw ( void ) 
 {	 
 MEP_PASSWORD psw ;	 
 MEP_UDP_RC rc ;	 
	 
	 
 rc = UDP_GetPassword ( &psw ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_psw_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading UDP - PSW or UDP is not support " );

		 
 return ;		 
 }	 
	 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_psw , DIAG_INFORMATION)  
 diagPrintf ( " Udp psw value is = %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x " , psw.number [ 0 ] , psw.number [ 1 ] , psw.number [ 2 ] , psw.number [ 3 ] , psw.number [ 4 ] , psw.number [ 5 ] , psw.number [ 6 ] , psw.number [ 7 ] , psw.number [ 8 ] , psw.number [ 9 ] , psw.number [ 10 ] , psw.number [ 11 ] , psw.number [ 12 ] , psw.number [ 13 ] , psw.number [ 14 ] , psw.number [ 15 ] );

	 
	 
	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , udp_read_asl 
 void udp_read_asl ( void ) 
 {	 
 UDP_ASL asl ;	 
 UINT8 i ;	 
 MEP_UDP_RC rc ;	 
	 
	 
 rc = UDP_GetASL ( &asl ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_asl_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading UDP - ASL or UDP is not support " );

		 
 return ;		 
 }	 
	 
 for ( i = 0 ; i < 10 ; i++ )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_asl , DIAG_INFORMATION)  
 diagPrintf ( " Udp ASL data_%x is = %x , %x , %x , %x , %x , %x , %x , %x , %x , %x Date = %u.%u.%u " , i , asl.data [ i ] .iccid [ 0 ] , asl.data [ i ] .iccid [ 1 ] , asl.data [ i ] .iccid [ 2 ] , asl.data [ i ] .iccid [ 3 ] , asl.data [ i ] .iccid [ 4 ] , asl.data [ i ] .iccid [ 5 ] , asl.data [ i ] .iccid [ 6 ] , asl.data [ i ] .iccid [ 7 ] , asl.data [ i ] .iccid [ 8 ] , asl.data [ i ] .iccid [ 9 ] , asl.data [ i ] .date.day , asl.data [ i ] .date.month , asl.data [ i ] .date.year );

		 
		 
		 
		 
 }	 
	 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_asl_1 , DIAG_INFORMATION)  
 diagPrintf ( " Udp ASL number of entries is = %x " , asl.NumAslEntries );

	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_write_tc 
 void mep_write_tc ( void *p ) 
 {	 
 UINT8 *param = ( UINT8 * ) p ;	 
 MEP_UDP_RC rc ;	 
	 
	 
 cb.MEP_PutTC_Callback = MEP_PutTC_Callback ;	 
 rc = MEP_PutTC ( &param [ 0 ] , ( MEP_CAT ) param [ 1 ] , &cb ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_write_tc_fail , DIAG_ERROR)  
 diagPrintf ( " Error while writing to MEP TC " );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , udp_write_si 
 void udp_write_si ( void *p ) 
 {	 
 UINT8 *param = ( UINT8 * ) p ;	 
 MEP_UDP_RC rc ;	 
	 
	 
 cb.UDP_PutSI_Callback = UDP_PutSI_Callback ;	 
 rc = UDP_PutSI ( &param [ 0 ] , &cb ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_write_si_fail , DIAG_ERROR)  
 diagPrintf ( " Error while writing to Udp si or UDP is not support " );

		 
 return ;		 
 }	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , udp_write_psw 
 void udp_write_psw ( void *p ) 
 {	 
 UINT8 *param = ( UINT8 * ) p ;	 
 MEP_PASSWORD psw ;	 
 UINT8 i ;	 
 MEP_UDP_RC rc ;	 
	 
	 
	 
	 
 rc = UDP_GetPassword ( &psw ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_write_psw_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading Udp psw or UDP is not support " );

		 
 return ;		 
 }	 
	 
 if ( param [ 0 ] == 1 )	 
 {		 
 for ( i = 0 ; i < 32 ; i++ )		 
 psw.number [ i ] = ( psw.number [ i ] + 1 ) ;		 
 }	 
 else	 
 {		 
 for ( i = 0 ; i < 32 ; i++ )		 
 psw.number [ i ] = ( psw.number [ i ] - 1 ) ;		 
 }	 
	 
 cb.UDP_PutPassword_Callback = UDP_PutPassword_Callback ;	 
 rc = UDP_PutPassword ( &psw , &cb ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_write_psw_fail , DIAG_ERROR)  
 diagPrintf ( " Error while writing to Udp psw or UDP is not support " );

		 
 return ;		 
 }	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , MepEncryptTest 
 void MepEncryptTest ( void ) 
 {	 
	 
 }

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_INT_IFC ,	 
 DIAG_EXT_IFC	 
 } DIAG_IF_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_UART_SPEED_921600 ,	 
 DIAG_UART_SPEED_1842000 ,	 
 DIAG_UART_SPEED_3000000 ,	 
 DIAG_UART_DISABLE ,	 
 } DIAG_UART_SPEED_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_MEM_ENABLE ,	 
 DIAG_MEM_DISABLE	 
 } DIAG_MEM_SWITCH;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_DEV_USB ,	 
 DIAG_DEV_SD ,	 
 DIAG_DEV_FS ,	 
 DIAG_DEV_UART ,	 
 DIAG_DEV_SPI ,	 
 DIAG_DEV_NONE	 
 } DIAG_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 SULOG_DEV_USB ,	 
 SULOG_DEV_SD	 
 } SULOG_DEV_TYPE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 DIAG_IF_TYPE useIntIf ; /* use internal interface or external interface */	 
 DIAG_UART_SPEED_TYPE useHighSpeedUART ; /* high speed UART ( 460800 ) or low speed UART ( 115200 ) */	 
 DIAG_MEM_SWITCH diagMemSwitch ;	 
 DIAG_DEV_TYPE diagDevType ;	 
 SULOG_DEV_TYPE sulogDevType ;	 
 BOOL sdlAutoDelete ;	 
 } diagCfgDataS;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 dbID ;	 
 UINT32 filterBitLength ;	 
 UINT32 reserved [ 6 ] ;	 
 } DIAG_Nvm_Filter_File_Header_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MASS_STORAGE_DISABLE = 0x0 ,	 
 MASS_STORAGE_ENABLE = 0x1	 
 } MassStorage_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* MIFI driver*/	 
 USB_GENERIC_MIFI_DRIVER = 0x0 ,	 
 USB_MARVELL_MIFI_DRIVER = 0x1 ,	 
 USB_ASR_MIFI_DRIVER = 0x2 ,	 
 USB_GENERIC_MOD_DRIVER = 0x10 ,	 
 USB_GENERIC_MOD_ECM_DRIVER = 0x12 ,	 
 USB_DIAG_UAC_DRIVER = 0x14 ,	 
 USB_RNDIS_ONLY_DRIVER = 0x15 ,	 
	 
 /* MBIM driver*/	 
 USB_MBIM_ONLY_DRIVER = 0x40 ,	 
 USB_MBIM_GENERIC_DRIVER = 0x41 ,	 
 USB_MBIM_MAX_DRIVER = 0x4F ,	 
	 
 /* Other driver*/	 
 USB_CDROM_ONLY_DRIVER = 0x91 ,	 
 USB_CDROM_DIAG_DRIVER = 0x92 ,	 
 USB_DIAG_ONLY_DRIVER = 0x93 ,	 
 USB_MODEM_ONLY_DRIVER = 0x94 ,	 
 USB_MODEM_DIAG_DRIVER = 0x95 ,	 
	 
 USB_MAX_DRIVER = 0xFF	 
 } Usb_driver_typeE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_AUTO_INSTALL_DISABLE = 0x0 ,	 
 USB_AUTO_INSTALL_ENABLE = 0x1	 
 } Usb_auto_install_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_OS_DETECT_DISABLE = 0x0 ,	 
 USB_OS_DETECT_ENABLE = 0x1	 
 } Usb_OS_detect_type;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Usb_driver_typeE usb_driver ;	 
 MassStorage_ConfigE mass_storage ;	 
 Usb_auto_install_type auto_install ;	 
 Usb_OS_detect_type os_detect ;	 
 } Usb_DriverS;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 YMODEM_DISABLE = 0x0 ,	 
 YMODEM_ENABLE = 0x1	 
 } Ymodem_Dump_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Ymodem_Dump_config config ;	 
 } Ymodem_Dump_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MBIM_ONLY_DESP = 0x0 ,	 
 MBIM_ATDIAG_DESP = 0x1	 
 } MBIM_DESP_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 MBIM_DESP_config config ;	 
 } MBIM_CFG_type;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 EEH_DUMP_DEV_USB_SD ,	 
 EEH_DUMP_DEV_SPI	 
 } EEH_DUMP_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PMU_POR = 1 ,	 
 PMU_EMR ,	 
 PMU_WDTR = ( PMU_EMR+2 )	 
 } PMU_LastResetStatus;

typedef UINT8 UART_Activity ;
//ICAT EXPORTED STRUCT 
 typedef struct /* This is structure of the UART Configuration */ 
 {	 
 UART_OpMode opMode ; /* fifo mode , non fifo mode or DMA for basic interface*/	 
 UART_TriggerLevel triggerLevel ; /* the trigger level interrupt on 1 , 8 , 16 , 32 */	 
 UART_BaudRates baudRate ; /* the rate of the transmit and the receive up to 111520 ( default - 9600 ) .*/	 
 UART_WordLen numDataBits ; /* 5 , 6 , 7 , or 8 number of data bits in the UART data frame ( default - 8 ) . */	 
 UART_StopBits stopBits ; /* 1 , 1.500000 or 2 stop bits in the UART data frame ( default - 1 ) . */	 
 UART_ParityTBits parityBitType ; /* Even , Odd or no-parity bit type in the UART data frame ( default - Non ) . */	 
 UART_InterfaceType interfaceType ; /* number of interface that the UART driver supplies ( default - UART_IF_TYPE_L2 ) */	 
 BOOL modemSignal ; /* enable operate modem - TRUE , disable modem - FALSE */	 
 BOOL flowControl ; /* enable Auto flow Control - TRUE , disable Auto flow Control - FALSE */	 
 UINT8 sleepMode ; /* enable sleep mode - TRUE , more fine control - see UARTSleepMode enum */	 
 BOOL auto_baud ; /* enable auto_baud , auto-baud-rate detection within the UART ( default - FALSE ) */	 
 UART_SIRConfigure sirIrDA ;	 
 } UARTConfiguration;

//ICAT EXPORTED ENUM 
 typedef enum // change the order -1 to + 
 {	 
 UART_RC_OK = 1 , /* 1 - no errors */	 
	 
 UART_RC_PORT_NUM_ERROR = -100 , /* -100 - Error in the UART port number */	 
 UART_RC_NO_DATA_TO_READ , /* -99 - Eror no data to read from the FIFO UART */	 
 UART_RC_ILLEGAL_BAUD_RATE , /* -98 - Error in the UART Bayd Rate */	 
 UART_RC_UART_PARITY_BITS_ERROR , /* -97 - Error in parity bit */	 
 UART_RC_UART_ONE_STOP_BIT_ERROR , /* -96 - Error in one stop bit */	 
 UART_RC_ONE_HALF_OR_TWO_STOP_BIT_ERROR , /* -95 - Error in two stop bit */	 
 UART_RC_BAD_INTERFACE_TYPE , /* -94 - Error in the Interface Type */	 
 UART_RC_UART_NOT_AVAILABLE , /* -93 - Error in try to open UART that is open */	 
 UART_RC_NO_DATA_TO_WRITE , /* -92 - Error No data to writ the len = 0 */	 
 UART_RC_NOT_ALL_BYTE_WRITTEN , /* -91 - Error Not all the Byte write to the UART FIFO */	 
 UART_RC_ISR_ALREADY_BIND , /* -90 - Error try to bind ISR for Basic Interface */	 
 UART_RC_WRONG_ISR_UNBIND , /* -89 - Error in the UnBind ISR for Basic Interface */	 
 UART_RC_FIFO_NOT_EMPTY , /* -88 - Error , the UART FIFO not empty */	 
 UART_RC_UART_OPEN , /* -87 - Error try chance the configurr when the UART open */	 
 UART_RC_GPIO_ERR , /* -86 - Error in the Configure of the GPIO */	 
 UART_RC_IRDA_CONFIG_ERR /* -85 - Illegal IrDA configuration */	 
 } UART_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAM_LOG = 0x00 ,	 
 USB_RAM_LOG = 0x01 ,	 
 MAX_RAM_LOG = 0x02	 
 } Ram_Log_Type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAMLOG_ENABLE = 0x00 ,	 
 USB_RAMLOG_ENABLE = 0x01	 
 } RamLog_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 LOG_DISABLE = 0x0 ,	 
 UART_LOG_ENABLE = 0x1 ,	 
 ACAT_LOG_ENABLE = 0x2	 
 } Log_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MSG_DISABLE = 0x0 ,	 
 ACAT_MSG_ENABLE = 0x1	 
 } Msg_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RTI_LOG_DISABLE = 0x0 ,	 
 RTI_LOG_ENABLE = 0x1	 
 } RTI_ConfigE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Log_ConfigE log_cfg ;	 
 Msg_ConfigE msg_cfg ;	 
 RTI_ConfigE rti_cfg ;	 
 RamLog_ConfigE ramlog ;	 
 } Log_ConfigS;

typedef void ( *UARTNotifyInterrupt ) ( UART_Port ) ;
typedef void ( *UsbLogPrint_t ) ( const char * , ... ) ;
typedef unsigned int time_t ;
DIAG_FILTER ( SW_PLAT , MEP , MEP_UpdateToMRD_NOMEM , DIAG_ERROR)  
 diagPrintf ( " MEP_UpdateToMRD Error: no memory " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_UpdateToMRD_RDISKERROR , DIAG_ERROR)  
 diagPrintf ( " MEP_UpdateToMRD Error: open rdisk file:%s failed " , name );

DIAG_FILTER ( SW_PLAT , MEP , MEP_UpdateToMRD_FileAddError , DIAG_INFORMATION)  
 diagPrintf ( " MEP_UpdateToMRD: Add File:%s to MRD failed! " , name );

