The target system is: Generic -  - arm
The host system is: Windows - 10.0.22635 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: F:/DS-5 v5.26.0/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: --cpu;Cortex-R5
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "__image.axf"

The C compiler identification is ARMCC, found in "D:/xy695/output/Lapwing_updater/CMakeFiles/3.19.2/CompilerIdC/CMakeCCompilerId.o"

Checking whether the ASM compiler is ARMCC using "" matched "(ARM Compiler)|(ARM Assembler)":
Product: DS-5 Professional 5.26.0
Component: ARM Compiler 5.06 update 4 (build 422)
Tool: armasm [4d35cf]
For Educational purposes only
Software supplied by: ARM Limited

Usage:      armasm [options] sourcefile

Options:
--list       listingfile   Write a listing file (see manual for options)
 -o          outputfile    Name the final output file
--depend     dependfile    Save 'make' source file dependencies
--errors     errorsfile    Put stderr diagnostics to errorsfile
 -I          dir[,dir]     Add dirs to source file search path
--pd
--predefine  directive     Pre-execute a SET{L,A,S} directive
--maxcache   <n>           Maximum cache size    (default 8MB)
--no_esc                   Ignore C-style (\c) escape sequences
--no_warn                  Turn off Warning messages
 -g                        Output debugging tables
--apcs       /<quals>      Make pre-definitions to match the
                           chosen procedure-call standard
--checkreglist             Warn about out of order LDM/STM register lists
--help                     Print this information
--li                       Little-endian ARM
--bi                       Big-endian ARM
 -M                        Write source file dependency lists to stdout
--MD                       Write source file dependency lists to inputfile.d
--keep                     Keep local labels in symbol table of object file
--regnames none            Do not predefine register names
--split_ldm                Fault long LDM/STM
--unsafe                   Downgrade certain errors to warnings
--via        <file>        Read further arguments from <file>
--cpu        <target-cpu>  Set the target ARM core type
--cpu list                 Output a list of all the selectable CPUs
--fpu        <target-arch> Set target FP architecture version
--fpu list                 Output a list of all selectable FP architectures
--thumb                    Assemble Thumb instructions
--arm                      Assemble ARM instructions
Detecting C compiler ABI info compiled with the following output:
Change Dir: D:/xy695/output/Lapwing_updater/CMakeFiles/CMakeTmp

Run Build Command(s):D:/xy695/ENV/misc/windows-x86/ninja.exe cmTC_dd58e && [35mHOST PROCESSOR = 12[0m

[1/2][0.051s]Building C object CMakeFiles\cmTC_dd58e.dir\CMakeCCompilerABI.o

[2/2][0.093s]Linking C static library libcmTC_dd58e.a




