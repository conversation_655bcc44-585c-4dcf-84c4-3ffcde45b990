[BLF_Version]
Blf_Version_Number = V3.0.0
[UE_Options]
UE_Boot_Option = 1
[Flash_Properties]
Flash_Block_Size = 0x20000
Max_Upload_Split_Size = 0x1cff000
Max_FBF_Split_Size = 0x1cff000
Max_OTA_Image_Size = 0x2000000
OTA_Image_Split_Size = 
Flash_Family = SPI-NOR
Spare_Area_Size = 64
Data_Area_Size = 2048
FBF_Sector_Size = 4096
[Flash_Options]
ProductionMode = 0
Skip_Blocks_Number = 
Erase_All_Flash = 0
Reset_BBT = 0
AB_System = 0
[TIM_Configuration]
Number_of_Images = 8
Number_of_Keys = 0
Boot_Flash_Signature = 0x5350491B
Processor_Type = ASR1903
FFOS_Type = OWRT
OEM_UniqueID = 0x4E5A4133
Issue_Date = 0x20140601
Version = 0x00030400
Trusted = 0
[Reserved_Data]
UARTID
Port(FFIDENTIFIER) = 0x00004646
Enabled = 0x00000001
End_UARTID
LTWS
LWG only = 0x00000003
End_LTWS
TRFU
Enabled = 0x00000001
Flash_Address = 0x00A94000
Magic = 0x54524657
End_TRFU
HTFX
Load_Address = 0xD1004000
HTFX_PATH = ./hotfix.bin
Patch_Size = 0x1B4C
End_HTFX
End_Reserved_Data
[EraseOnly_Option]
Total_Eraseonly_Areas = 2
1_Eraseonly_Area_Size = 0x20000
1_Eraseonly_Area_FlashStartAddress = 0x00000000
1_Eraseonly_Area_Partition = 0
1_Eraseonly_Area_Enable = 1
2_Eraseonly_Area_Size = 0x100000
2_Eraseonly_Area_FlashStartAddress = 0xa00000
2_Eraseonly_Area_Partition = 0
2_Eraseonly_Area_Enable = 0
[Extended_Reserved_Data]
Consumer_ID
CID = TBRI
PID = DDR1
End_Consumer_ID
DDR_Initialization
DDR_PID = DDR1
DDROperations
DDR_INIT_ENABLE = 0x00000001
DDR_MEMTEST_ENABLE = 0x00000000
End_DDROperations
Instructions
WRITE = <0xD4282D18,0x1B000000>    ;DDR QOS
End_Instructions
End_DDR_Initialization
End_Extended_Reserved_Data
[Image_List]
1_Image_Enable = 1
1_Image_Tim_Included = 1
1_Image_Image_ID = 0x54494D48
1_Image_Next_Image_ID = 0x4F424D49
1_Image_Path = ntim_ddr.bin
1_Image_Flash_Entry_Address = 0x00000000
1_Image_Load_Address = 0xD1000000
1_Image_Type = RAW
1_Image_ID_Name = TIMH
1_Image_Erase_Size = 
1_Image_Partition_Number = 0
1_Image_Hash_Algorithm_ID = SHA-256
1_Image_Image_Size_To_Hash_in_bytes = 0xFFFFFFFF
2_Image_Enable = 1
2_Image_Tim_Included = 1
2_Image_Image_ID = 0x4F424D49
2_Image_Next_Image_ID = 0x4F534C4F
2_Image_Path = Lapwing_Loader_EVB_QSPI_quectel.bin
2_Image_Flash_Entry_Address = 0x00006000
2_Image_Load_Address = 0x07800000
2_Image_Type = RAW
2_Image_ID_Name = OBMI
2_Image_Erase_Size = 
2_Image_Partition_Number = 0
2_Image_Hash_Algorithm_ID = SHA-256
2_Image_Image_Size_To_Hash_in_bytes = 0xFFFFFFFF
3_Image_Enable = 1
3_Image_Tim_Included = 1
3_Image_Image_ID = 0x52424C49
3_Image_Next_Image_ID = 0x55505445
3_Image_Path = ReliableData.bin
3_Image_Flash_Entry_Address = 0x20000
3_Image_Load_Address = 0xFFFFFFFF
3_Image_Type = RAW
3_Image_ID_Name = RBLI
3_Image_Erase_Size = 0x10000
3_Image_Partition_Number = 0
3_Image_Hash_Algorithm_ID = 
3_Image_Image_Size_To_Hash_in_bytes = 
4_Image_Enable = 1
4_Image_Tim_Included = 1
4_Image_Image_ID = 0x5246424E
4_Image_Next_Image_ID = 0x52424C49
4_Image_Path = rf_lzma.bin
4_Image_Flash_Entry_Address = 0x30000
4_Image_Load_Address = 0x07FF0000
4_Image_Type = LZMA
4_Image_ID_Name = RFBN
4_Image_Erase_Size = 0x10000
4_Image_Partition_Number = 0
4_Image_Hash_Algorithm_ID = 
4_Image_Image_Size_To_Hash_in_bytes = 
5_Image_Enable = 1
5_Image_Tim_Included = 1
5_Image_Image_ID = 0x4F534C4F
5_Image_Next_Image_ID = 0x47524249
5_Image_Path = LAPWING_RTOS_NL_R17_REDCAP.cponly_lzma.bin
5_Image_Flash_Entry_Address = 0x40000
5_Image_Load_Address = 0x06000000
5_Image_Type = LZMA
5_Image_ID_Name = OSLO
5_Image_Erase_Size = 0x680000
5_Image_Partition_Number = 0
5_Image_Hash_Algorithm_ID = 
5_Image_Image_Size_To_Hash_in_bytes = 
6_Image_Enable = 1
6_Image_Tim_Included = 1
6_Image_Image_ID = 0x47524249
6_Image_Next_Image_ID = 0x5246424E
6_Image_Path = LAPWING_A0_INT_BX2_OVL_RTOS_lzma.bin
6_Image_Flash_Entry_Address = 0x6c0000
6_Image_Load_Address = 0x07C40000
6_Image_Type = LZMA
6_Image_ID_Name = GRBI
6_Image_Erase_Size = 0x180000
6_Image_Partition_Number = 0
6_Image_Hash_Algorithm_ID = 
6_Image_Image_Size_To_Hash_in_bytes =
7_Image_Enable = 1
7_Image_Tim_Included = 1
7_Image_Image_ID = 0x57454249
7_Image_Next_Image_ID = 0x57494649
7_Image_Path = WebData_compressed.bin
7_Image_Flash_Entry_Address = 0x840000
7_Image_Load_Address = 0xFFFFFFFF
7_Image_Type = RAW
7_Image_ID_Name = WEBI
7_Image_Erase_Size = 0x100000
7_Image_Partition_Number = 0
7_Image_Hash_Algorithm_ID = 
7_Image_Image_Size_To_Hash_in_bytes = 
8_Image_Enable = 1
8_Image_Tim_Included = 1
8_Image_Image_ID = 0x57494649
8_Image_Next_Image_ID = 0xFFFFFFFF
8_Image_Path = AIC_FMACFW.bin
8_Image_Flash_Entry_Address = 0x940000
8_Image_Load_Address = 0xFFFFFFFF
8_Image_Type = RAW
8_Image_ID_Name = WIFI
8_Image_Erase_Size = 0x80000
8_Image_Partition_Number = 0
8_Image_Hash_Algorithm_ID = 
8_Image_Image_Size_To_Hash_in_bytes = 
