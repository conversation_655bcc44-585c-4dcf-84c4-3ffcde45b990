set(CMAKE_HOST_SYSTEM "Windows-10.0.22635")
set(CMAKE_HOST_SYSTEM_NAME "Windows")
set(CMAKE_HOST_SYSTEM_VERSION "10.0.22635")
set(CMAKE_HOST_SYSTEM_PROCESSOR "AMD64")

include("D:/xy695/PLT/startup/updater/build/CMakeLists_updater_toolchain_mcu.cmake")

set(CMAKE_SYSTEM "Generic")
set(CMAKE_SYSTEM_NAME "Generic")
set(CMAKE_SYSTEM_VERSION "")
set(CMAKE_SYSTEM_PROCESSOR "arm")

set(CMAKE_CROSSCOMPILING "TRUE")

set(CMAKE_SYSTEM_LOADED 1)
