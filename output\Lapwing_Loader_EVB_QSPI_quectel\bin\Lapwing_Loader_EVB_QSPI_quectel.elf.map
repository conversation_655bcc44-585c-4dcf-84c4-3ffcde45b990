Component: ARM Compiler 5.06 update 4 (build 422) Tool: armlink [4d35d2]

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv_div0_cr4.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv_cr4.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv_rt_div0_cr4.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv_rt_cr4.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    D:\jenkins\depot3\ASR\1903SR\R01_SDK\PLT\quectel\open\bootloader\core\quec_boot_all_pin_map.c 0x00000000   Number         0  quec_boot_all_pin_map.o ABSOLUTE
    D:\jenkins\depot3\ASR\1903SR\R01_SDK\PLT\quectel\open\bootloader\core\quec_boot_all_pin_test.c 0x00000000   Number         0  quec_boot_all_pin_test.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\ADC_PM803\adc_pm803.c 0x00000000   Number         0  adc_pm803.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff\bspatch.c 0x00000000   Number         0  bspatch.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\DFota\Bsdiff\tinyalloc.c 0x00000000   Number         0  tinyalloc.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\DMA\dma.c 0x00000000   Number         0  dma.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Download\ProtocolManager.c 0x00000000   Number         0  ProtocolManager.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Download\usb2_enumeration.c 0x00000000   Number         0  usb2_enumeration.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Download\usb2_main.c 0x00000000   Number         0  usb2_main.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Download\usb2_memory.c 0x00000000   Number         0  usb2_memory.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Download\usb_descriptors.c 0x00000000   Number         0  usb_descriptors.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Download\usbapi.c 0x00000000   Number         0  usbapi.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Flash\FM.c 0x00000000   Number         0  FM.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Flash\Flash.c 0x00000000   Number         0  Flash.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI\qspi_host.c 0x00000000   Number         0  qspi_host.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Flash\QSPI\spi_nor.c 0x00000000   Number         0  spi_nor.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\I2C\I2C.c 0x00000000   Number         0  I2C.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\I2C\bq24259.c 0x00000000   Number         0  bq24259.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\I2C\charger.c 0x00000000   Number         0  charger.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\I2C\guilin.c 0x00000000   Number         0  guilin.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\I2C\guilin_lite.c 0x00000000   Number         0  guilin_lite.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\I2C\oled.c 0x00000000   Number         0  oled.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\I2C\oled_lib.c 0x00000000   Number         0  oled_lib.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\I2C\st7735s.c 0x00000000   Number         0  st7735s.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\I2C\ustica.c 0x00000000   Number         0  ustica.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Misc\RegInstructions.c 0x00000000   Number         0  RegInstructions.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Misc\keypad.c 0x00000000   Number         0  keypad.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Misc\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Misc\print.c 0x00000000   Number         0  print.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Misc\timer.c 0x00000000   Number         0  timer.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Misc\tr069.c 0x00000000   Number         0  tr069.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\PlatformConfig.c 0x00000000   Number         0  PlatformConfig.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Platforms\WKNG\platform_interrupts.c 0x00000000   Number         0  platform_interrupts.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\SDRam\DDR_Cfg.c 0x00000000   Number         0  DDR_Cfg.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Common\Tim\tim.c 0x00000000   Number         0  tim.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\Main\BootLoader.c 0x00000000   Number         0  BootLoader.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\Main\BootMode.c 0x00000000   Number         0  BootMode.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\Main\DownloadMode.c 0x00000000   Number         0  DownloadMode.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\Main\FreqChange.c 0x00000000   Number         0  FreqChange.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\Main\LzmaDecode.c 0x00000000   Number         0  LzmaDecode.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\Main\TIMDownload.c 0x00000000   Number         0  TIMDownload.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\Main\mpu.c 0x00000000   Number         0  mpu.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\Main\qpress.c 0x00000000   Number         0  qpress.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\Main\quicklz.c 0x00000000   Number         0  quicklz.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\Main\serial.c 0x00000000   Number         0  serial.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\Main\wdt.c 0x00000000   Number         0  wdt.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\Platforms\WKNG\platform_StartUp.s 0x00000000   Number         0  platform_StartUp.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\Platforms\WKNG\platform_arch.s 0x00000000   Number         0  platform_arch.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\StartUp\bbu_CI2C.s 0x00000000   Number         0  bbu_CI2C.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\StartUp\bbu_PI2C.s 0x00000000   Number         0  bbu_PI2C.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\StartUp\bl_StartUp_ttc.s 0x00000000   Number         0  bl_StartUp_ttc.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\StartUp\mpu.s 0x00000000   Number         0  mpu.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\StartUp\mpu_api.c 0x00000000   Number         0  mpu_api.o ABSOLUTE
    D:\xy695\PLT\OBM\Lapwing\Loader\StartUp\version_block.c 0x00000000   Number         0  version_block.o ABSOLUTE
    D:\xy695\PLT\quectel\open\bootloader\app\quec_boot_platform.c 0x00000000   Number         0  quec_boot_platform.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    IMG_HEADER_INFO                          0x07800000   Section      256  version_block.o(IMG_HEADER_INFO)
    .text                                    0x07800100   Section        0  strncmp.o(.text)
    .text                                    0x078001f0   Section      136  rt_memcpy_v6.o(.text)
    .text                                    0x07800278   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x078002dc   Section       78  rt_memclr_w.o(.text)
    I2C                                      0x0780032c   Section      600  bbu_PI2C.o(I2C)
    I2C                                      0x07800584   Section      576  bbu_CI2C.o(I2C)
    Init                                     0x078007e0   Section     1104  bl_StartUp_ttc.o(Init)
    __BL_XFER_STR_ADDR                       0x07800818   Data           0  bl_StartUp_ttc.o(Init)
    __TransferControlCode                    0x078009f4   ARM Code       0  bl_StartUp_ttc.o(Init)
    __TransferControlCodeEnd                 0x07800a74   ARM Code       0  bl_StartUp_ttc.o(Init)
    VectorRedirector                         0x07800aa0   ARM Code       0  bl_StartUp_ttc.o(Init)
    IniRWROMLimit                            0x07800c00   Data           4  bl_StartUp_ttc.o(Init)
    Init                                     0x07800c30   Section      148  platform_StartUp.o(Init)
    MPU                                      0x07800cc4   Section     3508  mpu.o(MPU)
    OBM_asm_arbel_cp15_read_end              0x07800cf0   ARM Code       0  mpu.o(MPU)
    OBM_asm_arbel_cp15_read_base             0x07800cf4   ARM Code       0  mpu.o(MPU)
    OBM_asm_arbel_cp15_write_end             0x07800ed8   ARM Code       0  mpu.o(MPU)
    OBM_asm_arbel_cp15_write_base            0x07800edc   ARM Code       0  mpu.o(MPU)
    TEXT                                     0x07801a78   Section       32  platform_arch.o(TEXT)
    i.ADCForPM803DeInit                      0x07801a98   Section        0  adc_pm803.o(i.ADCForPM803DeInit)
    i.ADCForPM803Init                        0x07801abc   Section        0  adc_pm803.o(i.ADCForPM803Init)
    i.AbortHandler                           0x07801b18   Section        0  platform_interrupts.o(i.AbortHandler)
    i.AddMessageError                        0x07801b34   Section        0  ProtocolManager.o(i.AddMessageError)
    i.Align_Ptr                              0x07801bf4   Section        0  misc.o(i.Align_Ptr)
    i.Allocate_USB2_Device                   0x07801c08   Section        0  usb2_memory.o(i.Allocate_USB2_Device)
    i.AndInstruction                         0x07801c94   Section        0  RegInstructions.o(i.AndInstruction)
    i.And_SM_SM_Instruction                  0x07801cb4   Section        0  RegInstructions.o(i.And_SM_SM_Instruction)
    i.And_SM_Val_Instruction                 0x07801cf8   Section        0  RegInstructions.o(i.And_SM_Val_Instruction)
    i.BQ24259Read                            0x07801d24   Section        0  bq24259.o(i.BQ24259Read)
    i.BQ24259Write                           0x07801d44   Section        0  bq24259.o(i.BQ24259Write)
    i.BQ24259_chg_disable_pinmux_config      0x07801d64   Section        0  bq24259.o(i.BQ24259_chg_disable_pinmux_config)
    i.BQ24259_chg_mode_pinmux_config         0x07801da0   Section        0  bq24259.o(i.BQ24259_chg_mode_pinmux_config)
    i.BQ24259_chg_otg_pinmux_config          0x07801ddc   Section        0  bq24259.o(i.BQ24259_chg_otg_pinmux_config)
    i.BQ24259_chg_status_pinmux_config       0x07801e18   Section        0  bq24259.o(i.BQ24259_chg_status_pinmux_config)
    i.BatCharging_On                         0x07801e4c   Section        0  st7735s.o(i.BatCharging_On)
    i.Battery_Charing_Display                0x07801edc   Section        0  st7735s.o(i.Battery_Charing_Display)
    i.BinarySearch                           0x07801f4c   Section        0  FM.o(i.BinarySearch)
    i.BootLoaderMain                         0x0780207c   Section        0  BootLoader.o(i.BootLoaderMain)
    i.BootModeMain                           0x078023fc   Section        0  BootMode.o(i.BootModeMain)
    i.CacheCleanAndInvalidateMemory          0x078024b0   Section        0  mpu.o(i.CacheCleanAndInvalidateMemory)
    i.CacheInvalidateMemory                  0x0780251c   Section        0  mpu.o(i.CacheInvalidateMemory)
    i.CalcImageChecksum                      0x07802588   Section        0  misc.o(i.CalcImageChecksum)
    i.Charger_Voltage_Set                    0x078025ac   Section        0  bq24259.o(i.Charger_Voltage_Set)
    i.Charger_init                           0x078025b0   Section        0  I2C.o(i.Charger_init)
    i.CheckAndConfigureDDR                   0x07802638   Section        0  DDR_Cfg.o(i.CheckAndConfigureDDR)
    i.CheckAndFillStringIndexTable           0x07802730   Section        0  usb_descriptors.o(i.CheckAndFillStringIndexTable)
    i.CheckDefaultClocks                     0x078027e8   Section        0  PlatformConfig.o(i.CheckDefaultClocks)
    i.CheckForceDownload                     0x07802850   Section        0  adc_pm803.o(i.CheckForceDownload)
    CheckForceDownload                       0x07802850   ARM Code      60  adc_pm803.o(i.CheckForceDownload)
    i.CheckMemoryReliability                 0x078028b8   Section        0  DDR_Cfg.o(i.CheckMemoryReliability)
    i.CheckProtocolTimeOut                   0x078028f4   Section        0  ProtocolManager.o(i.CheckProtocolTimeOut)
    i.CheckReserved                          0x0780291c   Section        0  tim.o(i.CheckReserved)
    i.ClearFM                                0x07802960   Section        0  FM.o(i.ClearFM)
    i.ClearPortInterruptFlag                 0x078029c8   Section        0  platform_interrupts.o(i.ClearPortInterruptFlag)
    i.Clear_DDR_Flag_Table                   0x078029dc   Section        0  BootMode.o(i.Clear_DDR_Flag_Table)
    i.Clear_Screen                           0x07802a14   Section        0  st7735s.o(i.Clear_Screen)
    i.ConfigBQ24259Charger                   0x07802a4c   Section        0  bq24259.o(i.ConfigBQ24259Charger)
    i.ConfigCharger                          0x07802b10   Section        0  charger.o(i.ConfigCharger)
    i.ConfigRegRestore                       0x07802b14   Section        0  PlatformConfig.o(i.ConfigRegRestore)
    i.ConfigRegWrite                         0x07802b38   Section        0  PlatformConfig.o(i.ConfigRegWrite)
    ConfigRegWrite                           0x07802b38   ARM Code      36  PlatformConfig.o(i.ConfigRegWrite)
    i.ConfigureDDRMemory                     0x07802b5c   Section        0  DDR_Cfg.o(i.ConfigureDDRMemory)
    i.Configure_Flashes                      0x07802cb0   Section        0  Flash.o(i.Configure_Flashes)
    i.CopyUploadDataIntoBuffer               0x07802da0   Section        0  ProtocolManager.o(i.CopyUploadDataIntoBuffer)
    i.CoreIsCa7                              0x07802e00   Section        0  PlatformConfig.o(i.CoreIsCa7)
    i.Crc_Check_16                           0x07802e30   Section        0  tr069.o(i.Crc_Check_16)
    Crc_Check_16                             0x07802e30   ARM Code      80  tr069.o(i.Crc_Check_16)
    i.CreateBBT_Legacy                       0x07802e84   Section        0  FM.o(i.CreateBBT_Legacy)
    i.CreateUSBAPIhandle                     0x07802f30   Section        0  usbapi.o(i.CreateUSBAPIhandle)
    i.DO_DWC3_GADGET_WAKEUP                  0x07802f84   Section        0  usb2_main.o(i.DO_DWC3_GADGET_WAKEUP)
    DO_DWC3_GADGET_WAKEUP                    0x07802f84   ARM Code     156  usb2_main.o(i.DO_DWC3_GADGET_WAKEUP)
    i.DWC3_CONNDONE_INT                      0x07803024   Section        0  usb2_main.o(i.DWC3_CONNDONE_INT)
    DWC3_CONNDONE_INT                        0x07803024   ARM Code     136  usb2_main.o(i.DWC3_CONNDONE_INT)
    i.DWC3_Complete_Int                      0x078030ac   Section        0  usb2_main.o(i.DWC3_Complete_Int)
    i.DWC3_Controller_Setup                  0x078031d8   Section        0  usb2_main.o(i.DWC3_Controller_Setup)
    i.DWC3_DEV_INT                           0x078032f4   Section        0  usb2_main.o(i.DWC3_DEV_INT)
    DWC3_DEV_INT                             0x078032f4   ARM Code     100  usb2_main.o(i.DWC3_DEV_INT)
    i.DWC3_DISCONNECT_INT                    0x07803358   Section        0  usb2_main.o(i.DWC3_DISCONNECT_INT)
    DWC3_DISCONNECT_INT                      0x07803358   ARM Code      40  usb2_main.o(i.DWC3_DISCONNECT_INT)
    i.DWC3_EP0_COMPLETE_DATA                 0x07803380   Section        0  usb2_main.o(i.DWC3_EP0_COMPLETE_DATA)
    DWC3_EP0_COMPLETE_DATA                   0x07803380   ARM Code     108  usb2_main.o(i.DWC3_EP0_COMPLETE_DATA)
    i.DWC3_EP0_COMPLETE_STATUS               0x078033ec   Section        0  usb2_main.o(i.DWC3_EP0_COMPLETE_STATUS)
    DWC3_EP0_COMPLETE_STATUS                 0x078033ec   ARM Code      68  usb2_main.o(i.DWC3_EP0_COMPLETE_STATUS)
    i.DWC3_EP0_HANDLE_SETUP                  0x07803430   Section        0  usb2_main.o(i.DWC3_EP0_HANDLE_SETUP)
    DWC3_EP0_HANDLE_SETUP                    0x07803430   ARM Code     132  usb2_main.o(i.DWC3_EP0_HANDLE_SETUP)
    i.DWC3_EP0_INT                           0x078034b4   Section        0  usb2_main.o(i.DWC3_EP0_INT)
    i.DWC3_EP0_OUT_START                     0x078034e0   Section        0  usb2_main.o(i.DWC3_EP0_OUT_START)
    i.DWC3_EP0_STALL_AND_RESTART             0x07803504   Section        0  usb2_main.o(i.DWC3_EP0_STALL_AND_RESTART)
    i.DWC3_EP0_XFERNOTREADY                  0x0780354c   Section        0  usb2_main.o(i.DWC3_EP0_XFERNOTREADY)
    DWC3_EP0_XFERNOTREADY                    0x0780354c   ARM Code     216  usb2_main.o(i.DWC3_EP0_XFERNOTREADY)
    i.DWC3_EP0_XFER_COMPLETE                 0x0780362c   Section        0  usb2_main.o(i.DWC3_EP0_XFER_COMPLETE)
    DWC3_EP0_XFER_COMPLETE                   0x0780362c   ARM Code     240  usb2_main.o(i.DWC3_EP0_XFER_COMPLETE)
    i.DWC3_EP_INT                            0x07803724   Section        0  usb2_main.o(i.DWC3_EP_INT)
    DWC3_EP_INT                              0x07803724   ARM Code      72  usb2_main.o(i.DWC3_EP_INT)
    i.DWC3_EP_SET_CFG                        0x0780376c   Section        0  usb2_main.o(i.DWC3_EP_SET_CFG)
    DWC3_EP_SET_CFG                          0x0780376c   ARM Code     160  usb2_main.o(i.DWC3_EP_SET_CFG)
    i.DWC3_EP_SET_XFER_RESOURCE              0x0780380c   Section        0  usb2_main.o(i.DWC3_EP_SET_XFER_RESOURCE)
    DWC3_EP_SET_XFER_RESOURCE                0x0780380c   ARM Code      44  usb2_main.o(i.DWC3_EP_SET_XFER_RESOURCE)
    i.DWC3_EP_START_CFG                      0x07803838   Section        0  usb2_main.o(i.DWC3_EP_START_CFG)
    DWC3_EP_START_CFG                        0x07803838   ARM Code      40  usb2_main.o(i.DWC3_EP_START_CFG)
    i.DWC3_GET_CONN_SPEED                    0x07803860   Section        0  usb2_main.o(i.DWC3_GET_CONN_SPEED)
    i.DWC3_LINKSTS_CHANGE_INT                0x07803884   Section        0  usb2_main.o(i.DWC3_LINKSTS_CHANGE_INT)
    DWC3_LINKSTS_CHANGE_INT                  0x07803884   ARM Code      12  usb2_main.o(i.DWC3_LINKSTS_CHANGE_INT)
    i.DWC3_PROCESS_EVENT_ENTRY               0x07803890   Section        0  usb2_main.o(i.DWC3_PROCESS_EVENT_ENTRY)
    DWC3_PROCESS_EVENT_ENTRY                 0x07803890   ARM Code      28  usb2_main.o(i.DWC3_PROCESS_EVENT_ENTRY)
    i.DWC3_REG_READ                          0x078038ac   Section        0  usb2_enumeration.o(i.DWC3_REG_READ)
    DWC3_REG_READ                            0x078038ac   ARM Code      12  usb2_enumeration.o(i.DWC3_REG_READ)
    i.DWC3_REG_READ                          0x078038b8   Section        0  usb2_main.o(i.DWC3_REG_READ)
    DWC3_REG_READ                            0x078038b8   ARM Code      12  usb2_main.o(i.DWC3_REG_READ)
    i.DWC3_REG_WRITE                         0x078038c4   Section        0  usb2_enumeration.o(i.DWC3_REG_WRITE)
    DWC3_REG_WRITE                           0x078038c4   ARM Code      12  usb2_enumeration.o(i.DWC3_REG_WRITE)
    i.DWC3_REG_WRITE                         0x078038d0   Section        0  usb2_main.o(i.DWC3_REG_WRITE)
    DWC3_REG_WRITE                           0x078038d0   ARM Code      12  usb2_main.o(i.DWC3_REG_WRITE)
    i.DWC3_RESET_INT                         0x078038dc   Section        0  usb2_main.o(i.DWC3_RESET_INT)
    DWC3_RESET_INT                           0x078038dc   ARM Code      48  usb2_main.o(i.DWC3_RESET_INT)
    i.DWC3_SEND_EP_CMD                       0x0780390c   Section        0  usb2_main.o(i.DWC3_SEND_EP_CMD)
    i.DWC3_SET_LINK_STATE                    0x07803ae0   Section        0  usb2_main.o(i.DWC3_SET_LINK_STATE)
    i.DWC3_SUSPEND_INT                       0x07803b4c   Section        0  usb2_main.o(i.DWC3_SUSPEND_INT)
    DWC3_SUSPEND_INT                         0x07803b4c   ARM Code      12  usb2_main.o(i.DWC3_SUSPEND_INT)
    i.DWC3_WAKEUP_INT                        0x07803b58   Section        0  usb2_main.o(i.DWC3_WAKEUP_INT)
    DWC3_WAKEUP_INT                          0x07803b58   ARM Code       4  usb2_main.o(i.DWC3_WAKEUP_INT)
    i.DecompressRegion                       0x07803b5c   Section        0  BootMode.o(i.DecompressRegion)
    i.Delay                                  0x07803cc8   Section        0  timer.o(i.Delay)
    i.DetermineModeAndDownload               0x07803d00   Section        0  DownloadMode.o(i.DetermineModeAndDownload)
    i.DetermineOperatingMode                 0x07803eac   Section        0  BootLoader.o(i.DetermineOperatingMode)
    i.DisableBQ24259Charger                  0x0780411c   Section        0  bq24259.o(i.DisableBQ24259Charger)
    i.DisableCharger                         0x07804154   Section        0  charger.o(i.DisableCharger)
    i.DisableInt                             0x07804194   Section        0  platform_interrupts.o(i.DisableInt)
    i.DisablePeripheralIRQInterrupt          0x078041a8   Section        0  platform_interrupts.o(i.DisablePeripheralIRQInterrupt)
    i.DisplayBatstate                        0x078041b8   Section        0  st7735s.o(i.DisplayBatstate)
    i.DivideTwoNumbers                       0x07804214   Section        0  misc.o(i.DivideTwoNumbers)
    i.DownloadModeMain                       0x07804268   Section        0  DownloadMode.o(i.DownloadModeMain)
    i.DownloadTIMImages                      0x078043cc   Section        0  TIMDownload.o(i.DownloadTIMImages)
    i.EnableBQ24259Charger                   0x07804870   Section        0  bq24259.o(i.EnableBQ24259Charger)
    i.EnableCharger                          0x078048a8   Section        0  charger.o(i.EnableCharger)
    i.EnableInt                              0x07804908   Section        0  platform_interrupts.o(i.EnableInt)
    i.EnablePeripheralIRQInterrupt           0x07804940   Section        0  platform_interrupts.o(i.EnablePeripheralIRQInterrupt)
    i.Enable_SMPL                            0x07804950   Section        0  I2C.o(i.Enable_SMPL)
    i.EraseAllFlash                          0x078049e4   Section        0  Flash.o(i.EraseAllFlash)
    i.EraseFlash                             0x078049f4   Section        0  Flash.o(i.EraseFlash)
    i.External_Power_Display                 0x07804a44   Section        0  st7735s.o(i.External_Power_Display)
    i.FatalError                             0x07804a60   Section        0  BootLoader.o(i.FatalError)
    i.FillRam_IO                             0x07804abc   Section        0  st7735s.o(i.FillRam_IO)
    i.FinalizeFM                             0x07804b54   Section        0  FM.o(i.FinalizeFM)
    i.FinalizeSetup                          0x07804ba8   Section        0  BootLoader.o(i.FinalizeSetup)
    i.Finalize_Flashes                       0x07804bd0   Section        0  Flash.o(i.Finalize_Flashes)
    i.FindBBT_Legacy                         0x07804c2c   Section        0  FM.o(i.FindBBT_Legacy)
    i.FindFirstPackageTypeInReserved         0x07804e8c   Section        0  tim.o(i.FindFirstPackageTypeInReserved)
    i.FindImageInTIM                         0x07804f40   Section        0  tim.o(i.FindImageInTIM)
    i.FindMyConsumerArray                    0x07804f90   Section        0  tim.o(i.FindMyConsumerArray)
    i.FindNextPackageTypeInReserved          0x0780502c   Section        0  tim.o(i.FindNextPackageTypeInReserved)
    i.FindPackageInReserved                  0x078050ec   Section        0  tim.o(i.FindPackageInReserved)
    i.FindVendorRequestInTIM                 0x0780516c   Section        0  usb_descriptors.o(i.FindVendorRequestInTIM)
    i.FiqHandler                             0x078051f0   Section        0  platform_interrupts.o(i.FiqHandler)
    i.FuseOverwriteForDownload               0x078051f4   Section        0  PlatformConfig.o(i.FuseOverwriteForDownload)
    i.GetBadBlockNum                         0x078051f8   Section        0  FM.o(i.GetBadBlockNum)
    i.GetBatInstantVolt                      0x07805208   Section        0  I2C.o(i.GetBatInstantVolt)
    i.GetBatteryPercent                      0x078052b0   Section        0  I2C.o(i.GetBatteryPercent)
    i.GetBlockSize                           0x078052cc   Section        0  Flash.o(i.GetBlockSize)
    i.GetCompressedType                      0x078052dc   Section        0  BootMode.o(i.GetCompressedType)
    i.GetDDRSize                             0x07805384   Section        0  DDR_Cfg.o(i.GetDDRSize)
    i.GetFMProperties                        0x07805474   Section        0  FM.o(i.GetFMProperties)
    i.GetFlashProperties                     0x07805480   Section        0  Flash.o(i.GetFlashProperties)
    i.GetFlashType                           0x07805494   Section        0  Flash.o(i.GetFlashType)
    i.GetImageReadBackCrcBuffer              0x078054a8   Section        0  TIMDownload.o(i.GetImageReadBackCrcBuffer)
    i.GetInitRoutine                         0x078054b0   Section        0  Flash.o(i.GetInitRoutine)
    i.GetIrqStatus                           0x07805538   Section        0  platform_interrupts.o(i.GetIrqStatus)
    i.GetOSCR0                               0x07805558   Section        0  timer.o(i.GetOSCR0)
    i.GetPageSize                            0x0780557c   Section        0  Flash.o(i.GetPageSize)
    i.GetPartitionOffset                     0x0780558c   Section        0  FM.o(i.GetPartitionOffset)
    i.GetSMPtr                               0x078055b4   Section        0  RegInstructions.o(i.GetSMPtr)
    i.GetTimPointer                          0x078055d0   Section        0  tim.o(i.GetTimPointer)
    i.GetUSBAPIhandle_BootNum                0x078055dc   Section        0  usbapi.o(i.GetUSBAPIhandle_BootNum)
    i.GetUSBAPIhandle_InterruptNum           0x07805618   Section        0  usbapi.o(i.GetUSBAPIhandle_InterruptNum)
    i.GetUSBIDFuseBits                       0x07805654   Section        0  PlatformConfig.o(i.GetUSBIDFuseBits)
    i.GetUploadCommand                       0x0780565c   Section        0  ProtocolManager.o(i.GetUploadCommand)
    i.GetUseSpareArea                        0x078056a0   Section        0  Flash.o(i.GetUseSpareArea)
    i.Get_DC_Properties                      0x078056b4   Section        0  usb2_memory.o(i.Get_DC_Properties)
    i.Get_Dispaly_Percent                    0x078056f8   Section        0  st7735s.o(i.Get_Dispaly_Percent)
    i.Get_New_DCProps                        0x07805750   Section        0  usb2_memory.o(i.Get_New_DCProps)
    Get_New_DCProps                          0x07805750   ARM Code      28  usb2_memory.o(i.Get_New_DCProps)
    i.Get_TR069_Firmware                     0x07805770   Section        0  tr069.o(i.Get_TR069_Firmware)
    i.GuilinBaseRead                         0x07805a38   Section        0  guilin.o(i.GuilinBaseRead)
    i.GuilinBaseWrite                        0x07805a4c   Section        0  guilin.o(i.GuilinBaseWrite)
    i.GuilinChargerInit                      0x07805a60   Section        0  guilin.o(i.GuilinChargerInit)
    i.GuilinCheckBatteryConnect              0x07805ad8   Section        0  guilin.o(i.GuilinCheckBatteryConnect)
    i.GuilinCheckBootONKey                   0x07805b80   Section        0  guilin.o(i.GuilinCheckBootONKey)
    i.GuilinCheckUSBConnect                  0x07805c08   Section        0  guilin.o(i.GuilinCheckUSBConnect)
    i.GuilinCheckWakeup                      0x07805c68   Section        0  guilin.o(i.GuilinCheckWakeup)
    i.GuilinClkInit                          0x07805dc4   Section        0  guilin.o(i.GuilinClkInit)
    i.GuilinEnableSMPL                       0x07805e88   Section        0  guilin.o(i.GuilinEnableSMPL)
    i.GuilinGetBatInstantVolt                0x07805e90   Section        0  guilin.o(i.GuilinGetBatInstantVolt)
    i.GuilinGpadcRead                        0x07805f44   Section        0  guilin.o(i.GuilinGpadcRead)
    i.GuilinGpadcWrite                       0x07805f58   Section        0  guilin.o(i.GuilinGpadcWrite)
    i.GuilinLiteBaseRead                     0x07805f6c   Section        0  guilin_lite.o(i.GuilinLiteBaseRead)
    i.GuilinLiteBaseWrite                    0x07805f8c   Section        0  guilin_lite.o(i.GuilinLiteBaseWrite)
    i.GuilinLiteChargerInit                  0x07805fa0   Section        0  guilin_lite.o(i.GuilinLiteChargerInit)
    i.GuilinLiteCheckBatteryConnect          0x07805fc0   Section        0  guilin_lite.o(i.GuilinLiteCheckBatteryConnect)
    i.GuilinLiteCheckBootONKey               0x07806030   Section        0  guilin_lite.o(i.GuilinLiteCheckBootONKey)
    i.GuilinLiteCheckUSBConnect              0x078060b8   Section        0  guilin_lite.o(i.GuilinLiteCheckUSBConnect)
    i.GuilinLiteCheckWakeup                  0x07806118   Section        0  guilin_lite.o(i.GuilinLiteCheckWakeup)
    i.GuilinLiteClkInit                      0x07806274   Section        0  guilin_lite.o(i.GuilinLiteClkInit)
    i.GuilinLiteDisableWDT                   0x07806278   Section        0  guilin_lite.o(i.GuilinLiteDisableWDT)
    i.GuilinLiteEnableSMPL                   0x07806294   Section        0  guilin_lite.o(i.GuilinLiteEnableSMPL)
    i.GuilinLiteGetBatInstantVolt            0x0780629c   Section        0  guilin_lite.o(i.GuilinLiteGetBatInstantVolt)
    i.GuilinLitePowerRead                    0x07806340   Section        0  guilin_lite.o(i.GuilinLitePowerRead)
    i.GuilinLitePowerWrite                   0x07806360   Section        0  guilin_lite.o(i.GuilinLitePowerWrite)
    i.GuilinLitePoweroff                     0x07806374   Section        0  guilin_lite.o(i.GuilinLitePoweroff)
    i.GuilinLiteReadBatVolt                  0x078063ec   Section        0  guilin_lite.o(i.GuilinLiteReadBatVolt)
    i.GuilinLiteResetReg                     0x07806430   Section        0  guilin_lite.o(i.GuilinLiteResetReg)
    i.GuilinLiteSetMainVoltage               0x07806568   Section        0  guilin_lite.o(i.GuilinLiteSetMainVoltage)
    i.GuilinLite_Aditional_Workaround        0x078065bc   Section        0  guilin_lite.o(i.GuilinLite_Aditional_Workaround)
    i.GuilinLite_VBUCK1_CFG                  0x07806600   Section        0  guilin_lite.o(i.GuilinLite_VBUCK1_CFG)
    i.GuilinLite_VBUCK_Set_VOUT              0x0780660c   Section        0  guilin_lite.o(i.GuilinLite_VBUCK_Set_VOUT)
    i.GuilinPowerRead                        0x0780668c   Section        0  guilin.o(i.GuilinPowerRead)
    i.GuilinPowerWrite                       0x078066a0   Section        0  guilin.o(i.GuilinPowerWrite)
    i.GuilinPoweroff                         0x078066b4   Section        0  guilin.o(i.GuilinPoweroff)
    i.GuilinReadBatVolt                      0x0780672c   Section        0  guilin.o(i.GuilinReadBatVolt)
    i.GuilinResetReg                         0x0780676c   Section        0  guilin.o(i.GuilinResetReg)
    i.GuilinSetMainVoltage                   0x078068ac   Section        0  guilin.o(i.GuilinSetMainVoltage)
    i.Guilin_Aditional_Workaround            0x07806958   Section        0  guilin.o(i.Guilin_Aditional_Workaround)
    i.HandleDataCmd                          0x07806974   Section        0  ProtocolManager.o(i.HandleDataCmd)
    i.HandleDataHeaderCmd                    0x0780699c   Section        0  ProtocolManager.o(i.HandleDataHeaderCmd)
    i.HandleDisconnect                       0x07806a5c   Section        0  ProtocolManager.o(i.HandleDisconnect)
    i.HandleDoneCmd                          0x07806b08   Section        0  ProtocolManager.o(i.HandleDoneCmd)
    i.HandleGetBadBlock                      0x07806b30   Section        0  ProtocolManager.o(i.HandleGetBadBlock)
    i.HandleGetCrcCmd                        0x07806bc0   Section        0  ProtocolManager.o(i.HandleGetCrcCmd)
    i.HandleGetParametersCmd                 0x07806c78   Section        0  ProtocolManager.o(i.HandleGetParametersCmd)
    i.HandleGetVersionCmd                    0x07806cf8   Section        0  ProtocolManager.o(i.HandleGetVersionCmd)
    i.HandleMessageCmd                       0x07806d30   Section        0  ProtocolManager.o(i.HandleMessageCmd)
    i.HandleProtocolVersionCmd               0x07806e1c   Section        0  ProtocolManager.o(i.HandleProtocolVersionCmd)
    i.HandleRequest                          0x07806ea0   Section        0  ProtocolManager.o(i.HandleRequest)
    i.HandleSelectImageCmd                   0x07806f28   Section        0  ProtocolManager.o(i.HandleSelectImageCmd)
    i.HandleUploadDataCmd                    0x07806f50   Section        0  ProtocolManager.o(i.HandleUploadDataCmd)
    i.HandleUploadDataHeaderCmd              0x07807034   Section        0  ProtocolManager.o(i.HandleUploadDataHeaderCmd)
    i.HandleUploadFlow                       0x07807170   Section        0  ProtocolManager.o(i.HandleUploadFlow)
    i.HandleVerifyImageCmd                   0x078071f0   Section        0  ProtocolManager.o(i.HandleVerifyImageCmd)
    i.I2COLED_DisplayOff                     0x07807224   Section        0  oled.o(i.I2COLED_DisplayOff)
    i.I2COLED_Init                           0x07807228   Section        0  oled.o(i.I2COLED_Init)
    i.I2cInit                                0x0780722c   Section        0  I2C.o(i.I2cInit)
    i.INNERRTCClockOn                        0x07807294   Section        0  adc_pm803.o(i.INNERRTCClockOn)
    INNERRTCClockOn                          0x07807294   ARM Code      80  adc_pm803.o(i.INNERRTCClockOn)
    i.INT_init                               0x078072e8   Section        0  platform_interrupts.o(i.INT_init)
    i.IRQ_Glb_Ena                            0x07807350   Section        0  platform_interrupts.o(i.IRQ_Glb_Ena)
    i.IS_PTRB_OK                             0x07807384   Section        0  usb2_main.o(i.IS_PTRB_OK)
    IS_PTRB_OK                               0x07807384   ARM Code      16  usb2_main.o(i.IS_PTRB_OK)
    i.ImageDownloadWaiting                   0x07807394   Section        0  ProtocolManager.o(i.ImageDownloadWaiting)
    i.InitDefaultPort                        0x078073c4   Section        0  tim.o(i.InitDefaultPort)
    i.InitPort                               0x078073c8   Section        0  ProtocolManager.o(i.InitPort)
    i.InitProtocol                           0x078073cc   Section        0  ProtocolManager.o(i.InitProtocol)
    i.InitSODTimer                           0x0780741c   Section        0  timer.o(i.InitSODTimer)
    i.InitializeFM                           0x0780745c   Section        0  FM.o(i.InitializeFM)
    i.InitializeKeypad                       0x07807498   Section        0  keypad.o(i.InitializeKeypad)
    i.InitializeQSPIDevice                   0x078074e0   Section        0  spi_nor.o(i.InitializeQSPIDevice)
    i.InitializeUSB2Memory                   0x078075f0   Section        0  usb2_memory.o(i.InitializeUSB2Memory)
    i.IrqHandler                             0x078076b8   Section        0  platform_interrupts.o(i.IrqHandler)
    i.LZMA_Decompress                        0x07807708   Section        0  LzmaDecode.o(i.LZMA_Decompress)
    i.LoadAllImages                          0x07807a2c   Section        0  BootMode.o(i.LoadAllImages)
    i.LoadMsaBin                             0x078080f0   Section        0  adc_pm803.o(i.LoadMsaBin)
    LoadMsaBin                               0x078080f0   ARM Code       4  adc_pm803.o(i.LoadMsaBin)
    i.LoadTim                                0x078080f4   Section        0  tim.o(i.LoadTim)
    i.Load_SM_Addr_Instruction               0x07808248   Section        0  RegInstructions.o(i.Load_SM_Addr_Instruction)
    i.Load_SM_Val_Instruction                0x0780828c   Section        0  RegInstructions.o(i.Load_SM_Val_Instruction)
    i.Lshift_SM_Val_Instruction              0x078082b0   Section        0  RegInstructions.o(i.Lshift_SM_Val_Instruction)
    i.LzmaDecode                             0x078082dc   Section        0  LzmaDecode.o(i.LzmaDecode)
    i.LzmaLenDecode                          0x0780862c   Section        0  LzmaDecode.o(i.LzmaLenDecode)
    i.LzmaLiteralDecode                      0x078086a8   Section        0  LzmaDecode.o(i.LzmaLiteralDecode)
    i.LzmaLiteralDecodeMatch                 0x07808738   Section        0  LzmaDecode.o(i.LzmaLiteralDecodeMatch)
    i.MRDAdcValueGet                         0x0780885c   Section        0  adc_pm803.o(i.MRDAdcValueGet)
    MRDAdcValueGet                           0x0780885c   ARM Code     264  adc_pm803.o(i.MRDAdcValueGet)
    i.MSAHold                                0x07808964   Section        0  adc_pm803.o(i.MSAHold)
    MSAHold                                  0x07808964   ARM Code      68  adc_pm803.o(i.MSAHold)
    i.MSAInit                                0x078089ac   Section        0  adc_pm803.o(i.MSAInit)
    MSAInit                                  0x078089ac   ARM Code     152  adc_pm803.o(i.MSAInit)
    i.ModOfTwoNumbers                        0x07808a50   Section        0  misc.o(i.ModOfTwoNumbers)
    i.Mov_SM_SM_Instruction                  0x07808a68   Section        0  RegInstructions.o(i.Mov_SM_SM_Instruction)
    i.MrdBubbleSort                          0x07808aa0   Section        0  adc_pm803.o(i.MrdBubbleSort)
    i.MrdSwap                                0x07808af4   Section        0  adc_pm803.o(i.MrdSwap)
    MrdSwap                                  0x07808af4   ARM Code      56  adc_pm803.o(i.MrdSwap)
    i.No_Battery_Display                     0x07808b2c   Section        0  st7735s.o(i.No_Battery_Display)
    i.OBM_Flush                              0x07808b48   Section        0  mpu.o(i.OBM_Flush)
    i.OLED_Cmd_0_Paras                       0x07808b5c   Section        0  st7735s.o(i.OLED_Cmd_0_Paras)
    i.OLED_Cmd_Gamma_Corr                    0x07808b90   Section        0  st7735s.o(i.OLED_Cmd_Gamma_Corr)
    i.OLED_Color_Set                         0x07808bfc   Section        0  st7735s.o(i.OLED_Color_Set)
    i.OLED_DisplayHeadPicture                0x07808c18   Section        0  st7735s.o(i.OLED_DisplayHeadPicture)
    i.OLED_EraseRectArea                     0x07808cc8   Section        0  st7735s.o(i.OLED_EraseRectArea)
    i.OLED_Pin_Configure                     0x07808d10   Section        0  st7735s.o(i.OLED_Pin_Configure)
    i.ONKEY_Bootup                           0x07808e08   Section        0  st7735s.o(i.ONKEY_Bootup)
    i.OSCR0IntervalInMicro                   0x07808e10   Section        0  timer.o(i.OSCR0IntervalInMicro)
    i.OSCR0IntervalInMilli                   0x07808e1c   Section        0  timer.o(i.OSCR0IntervalInMilli)
    i.OSCR0IntervalInSec                     0x07808e28   Section        0  timer.o(i.OSCR0IntervalInSec)
    i.OrInstruction                          0x07808e38   Section        0  RegInstructions.o(i.OrInstruction)
    i.Or_SM_SM_Instruction                   0x07808e58   Section        0  RegInstructions.o(i.Or_SM_SM_Instruction)
    i.Or_SM_Val_Instruction                  0x07808e9c   Section        0  RegInstructions.o(i.Or_SM_Val_Instruction)
    i.PMIC_Init_ID                           0x07808ec8   Section        0  PlatformConfig.o(i.PMIC_Init_ID)
    i.PM_Handler                             0x07808fcc   Section        0  ProtocolManager.o(i.PM_Handler)
    i.PM_ISR                                 0x078091c0   Section        0  ProtocolManager.o(i.PM_ISR)
    i.PM_ReceiveImage                        0x078092b8   Section        0  ProtocolManager.o(i.PM_ReceiveImage)
    i.PM_VerifyPreamble                      0x07809398   Section        0  ProtocolManager.o(i.PM_VerifyPreamble)
    i.PP_Switch                              0x078093bc   Section        0  FreqChange.o(i.PP_Switch)
    i.ParseBR_ExtraState                     0x07809528   Section        0  PlatformConfig.o(i.ParseBR_ExtraState)
    i.ParseTransferStruct                    0x07809574   Section        0  BootLoader.o(i.ParseTransferStruct)
    i.PerformTIMBasedSetup                   0x0780964c   Section        0  BootLoader.o(i.PerformTIMBasedSetup)
    i.PlatformAPUartIsEnable                 0x0780968c   Section        0  PlatformConfig.o(i.PlatformAPUartIsEnable)
    i.PlatformChargeIsEnable                 0x078096a8   Section        0  PlatformConfig.o(i.PlatformChargeIsEnable)
    i.PlatformChargerConfig                  0x078096c4   Section        0  PlatformConfig.o(i.PlatformChargerConfig)
    i.PlatformCheckForceUSBEnumFlag          0x07809714   Section        0  PlatformConfig.o(i.PlatformCheckForceUSBEnumFlag)
    i.PlatformCheckProductionMode            0x07809738   Section        0  PlatformConfig.o(i.PlatformCheckProductionMode)
    i.PlatformClearForceUSBEnumFlag          0x078097c8   Section        0  PlatformConfig.o(i.PlatformClearForceUSBEnumFlag)
    i.PlatformDDRCfgEnable                   0x078097dc   Section        0  PlatformConfig.o(i.PlatformDDRCfgEnable)
    i.PlatformGetChipID                      0x078097ec   Section        0  PlatformConfig.o(i.PlatformGetChipID)
    i.PlatformGetRevisionID                  0x07809800   Section        0  PlatformConfig.o(i.PlatformGetRevisionID)
    i.PlatformInitFlash                      0x0780987c   Section        0  ProtocolManager.o(i.PlatformInitFlash)
    i.PlatformIsLapwB0                       0x078098e4   Section        0  PlatformConfig.o(i.PlatformIsLapwB0)
    i.PlatformKeypadConfig                   0x0780990c   Section        0  PlatformConfig.o(i.PlatformKeypadConfig)
    i.PlatformOLEDConfig                     0x07809934   Section        0  PlatformConfig.o(i.PlatformOLEDConfig)
    i.PlatformOledEnable                     0x07809998   Section        0  PlatformConfig.o(i.PlatformOledEnable)
    i.PlatformPI2CConfig                     0x078099b4   Section        0  PlatformConfig.o(i.PlatformPI2CConfig)
    i.PlatformPMICType                       0x07809a10   Section        0  PlatformConfig.o(i.PlatformPMICType)
    i.PlatformPPEnable                       0x07809a20   Section        0  PlatformConfig.o(i.PlatformPPEnable)
    i.PlatformPROJECTType                    0x07809a30   Section        0  PlatformConfig.o(i.PlatformPROJECTType)
    i.PlatformPrepareOBMVersion              0x07809a40   Section        0  PlatformConfig.o(i.PlatformPrepareOBMVersion)
    i.PlatformProtectBootBlocks              0x07809a58   Section        0  PlatformConfig.o(i.PlatformProtectBootBlocks)
    i.PlatformSetDdrWtdResetFlag             0x07809a5c   Section        0  PlatformConfig.o(i.PlatformSetDdrWtdResetFlag)
    i.PlatformSetForceUSBEnumFlag            0x07809a74   Section        0  PlatformConfig.o(i.PlatformSetForceUSBEnumFlag)
    i.PlatformSetLTGLWGFlag                  0x07809a8c   Section        0  PlatformConfig.o(i.PlatformSetLTGLWGFlag)
    i.PlatformSetPMICType                    0x07809a9c   Section        0  PlatformConfig.o(i.PlatformSetPMICType)
    i.PlatformSetTR069Flag                   0x07809aac   Section        0  PlatformConfig.o(i.PlatformSetTR069Flag)
    i.PlatformUARTConfig                     0x07809ac4   Section        0  PlatformConfig.o(i.PlatformUARTConfig)
    i.Platform_PortEnable                    0x07809b38   Section        0  PlatformConfig.o(i.Platform_PortEnable)
    i.Platform_USB2_ON_USB2_PHY_Init         0x07809b54   Section        0  PlatformConfig.o(i.Platform_USB2_ON_USB2_PHY_Init)
    i.Platform_USB2_Shutdown                 0x07809be4   Section        0  PlatformConfig.o(i.Platform_USB2_Shutdown)
    i.PrefetchHandler                        0x07809bf8   Section        0  platform_interrupts.o(i.PrefetchHandler)
    i.ProcessDDROps                          0x07809bfc   Section        0  DDR_Cfg.o(i.ProcessDDROps)
    i.ProcessInstructions                    0x07809cac   Section        0  RegInstructions.o(i.ProcessInstructions)
    i.RTCRegisterInit                        0x07809eec   Section        0  adc_pm803.o(i.RTCRegisterInit)
    RTCRegisterInit                          0x07809eec   ARM Code      28  adc_pm803.o(i.RTCRegisterInit)
    i.RangeDecoderBitDecode                  0x07809f0c   Section        0  LzmaDecode.o(i.RangeDecoderBitDecode)
    i.RangeDecoderBitTreeDecode              0x07809fc8   Section        0  LzmaDecode.o(i.RangeDecoderBitTreeDecode)
    i.RangeDecoderDecodeDirectBits           0x0780a06c   Section        0  LzmaDecode.o(i.RangeDecoderDecodeDirectBits)
    i.RangeDecoderInit                       0x0780a0d0   Section        0  LzmaDecode.o(i.RangeDecoderInit)
    i.RangeDecoderReadByte                   0x0780a130   Section        0  LzmaDecode.o(i.RangeDecoderReadByte)
    i.RangeDecoderReverseBitTreeDecode       0x0780a154   Section        0  LzmaDecode.o(i.RangeDecoderReverseBitTreeDecode)
    i.ReadBatVolt                            0x0780a200   Section        0  I2C.o(i.ReadBatVolt)
    i.ReadBitField                           0x0780a298   Section        0  DDR_Cfg.o(i.ReadBitField)
    i.ReadFlash                              0x0780a2b4   Section        0  Flash.o(i.ReadFlash)
    i.ReadInstruction                        0x0780a2f0   Section        0  RegInstructions.o(i.ReadInstruction)
    i.ReadKeypad                             0x0780a318   Section        0  keypad.o(i.ReadKeypad)
    i.ReadReliableDataToUpdateDefaultMap     0x0780a360   Section        0  adc_pm803.o(i.ReadReliableDataToUpdateDefaultMap)
    ReadReliableDataToUpdateDefaultMap       0x0780a360   ARM Code     332  adc_pm803.o(i.ReadReliableDataToUpdateDefaultMap)
    i.ReleasetrbChain                        0x0780a4bc   Section        0  usb2_memory.o(i.ReleasetrbChain)
    i.ResetBBT                               0x0780a4d0   Section        0  Flash.o(i.ResetBBT)
    i.Reset_Reg                              0x0780a4d8   Section        0  I2C.o(i.Reset_Reg)
    i.RestoreDefaultConfig                   0x0780a57c   Section        0  PlatformConfig.o(i.RestoreDefaultConfig)
    i.ReturnPImgPtr                          0x0780a5dc   Section        0  tim.o(i.ReturnPImgPtr)
    i.Rshift_SM_Val_Instruction              0x0780a5fc   Section        0  RegInstructions.o(i.Rshift_SM_Val_Instruction)
    i.SD_TR069_Upgrade                       0x0780a628   Section        0  tr069.o(i.SD_TR069_Upgrade)
    i.SPINOR_Disable4BytesMode               0x0780a874   Section        0  spi_nor.o(i.SPINOR_Disable4BytesMode)
    i.SPINOR_Wipe                            0x0780a880   Section        0  spi_nor.o(i.SPINOR_Wipe)
    i.SPI_OLED_BacklightON                   0x0780a88c   Section        0  st7735s.o(i.SPI_OLED_BacklightON)
    i.SPI_OLED_Reset_SSP                     0x0780a8c0   Section        0  st7735s.o(i.SPI_OLED_Reset_SSP)
    i.ST7735S_I2COLED_Init                   0x0780a914   Section        0  st7735s.o(i.ST7735S_I2COLED_Init)
    i.ST7735S_SPILCD_DiplayOff               0x0780abc4   Section        0  st7735s.o(i.ST7735S_SPILCD_DiplayOff)
    i.ST7735S_SPIOLED_Init                   0x0780ac04   Section        0  st7735s.o(i.ST7735S_SPIOLED_Init)
    i.ScanBBT_Legacy                         0x0780ac9c   Section        0  FM.o(i.ScanBBT_Legacy)
    i.SendAck                                0x0780acfc   Section        0  ProtocolManager.o(i.SendAck)
    i.SendError                              0x0780ad64   Section        0  ProtocolManager.o(i.SendError)
    i.SendResponse                           0x0780add8   Section        0  ProtocolManager.o(i.SendResponse)
    i.SetArea                                0x0780adfc   Section        0  st7735s.o(i.SetArea)
    i.SetBBTState                            0x0780aee4   Section        0  FM.o(i.SetBBTState)
    i.SetTIMPointers                         0x0780af4c   Section        0  tim.o(i.SetTIMPointers)
    i.SetUpUSBDescriptors                    0x0780afdc   Section        0  usb_descriptors.o(i.SetUpUSBDescriptors)
    i.SetUseSpareArea                        0x0780b200   Section        0  Flash.o(i.SetUseSpareArea)
    i.Set_Bitfield_Instruction               0x0780b230   Section        0  RegInstructions.o(i.Set_Bitfield_Instruction)
    i.SetupEnvironment                       0x0780b270   Section        0  BootLoader.o(i.SetupEnvironment)
    i.ShutdownPort                           0x0780b49c   Section        0  PlatformConfig.o(i.ShutdownPort)
    i.SoftwareUpgrade_Done                   0x0780b4b0   Section        0  st7735s.o(i.SoftwareUpgrade_Done)
    i.Store_SM_Addr_Instruction              0x0780b4cc   Section        0  RegInstructions.o(i.Store_SM_Addr_Instruction)
    i.SwiHandler                             0x0780b510   Section        0  platform_interrupts.o(i.SwiHandler)
    i.System_poweroff                        0x0780b514   Section        0  I2C.o(i.System_poweroff)
    i.TIMDownloadMain                        0x0780b59c   Section        0  TIMDownload.o(i.TIMDownloadMain)
    i.Test_If_Not_Zero_And_Set               0x0780b664   Section        0  RegInstructions.o(i.Test_If_Not_Zero_And_Set)
    i.Test_If_Zero_And_Set                   0x0780b6cc   Section        0  RegInstructions.o(i.Test_If_Zero_And_Set)
    i.Test_SM_If_Not_Zero_And_Set            0x0780b730   Section        0  RegInstructions.o(i.Test_SM_If_Not_Zero_And_Set)
    i.Test_SM_If_Zero_And_Set                0x0780b794   Section        0  RegInstructions.o(i.Test_SM_If_Zero_And_Set)
    i.Tr069GetConfig                         0x0780b7f4   Section        0  tr069.o(i.Tr069GetConfig)
    i.TransferFlashType                      0x0780b95c   Section        0  Flash.o(i.TransferFlashType)
    i.USB2D_Endpoint0Transmit                0x0780b978   Section        0  usb2_main.o(i.USB2D_Endpoint0Transmit)
    i.USB2D_EndpointTransmit                 0x0780ba48   Section        0  usb2_main.o(i.USB2D_EndpointTransmit)
    i.USB2D_Endpoint_Setup                   0x0780baec   Section        0  usb2_main.o(i.USB2D_Endpoint_Setup)
    i.USB2D_EnumerationHandler               0x0780bb60   Section        0  usb2_enumeration.o(i.USB2D_EnumerationHandler)
    i.USB2D_GetDescriptor                    0x0780bbb8   Section        0  usb2_enumeration.o(i.USB2D_GetDescriptor)
    i.USB2D_GetStatus                        0x0780bcec   Section        0  usb2_enumeration.o(i.USB2D_GetStatus)
    i.USB2D_ISR                              0x0780bd38   Section        0  usb2_main.o(i.USB2D_ISR)
    i.USB2D_Initialize                       0x0780bdd8   Section        0  usb2_main.o(i.USB2D_Initialize)
    i.USB2D_PM_Call                          0x0780be5c   Section        0  usb2_main.o(i.USB2D_PM_Call)
    i.USB2D_RecieveWrapper                   0x0780be88   Section        0  usb2_main.o(i.USB2D_RecieveWrapper)
    i.USB2D_SendWrapper                      0x0780bf14   Section        0  usb2_main.o(i.USB2D_SendWrapper)
    i.USB2D_SetAddress                       0x0780bf48   Section        0  usb2_enumeration.o(i.USB2D_SetAddress)
    i.USB2D_SetConfig                        0x0780bf98   Section        0  usb2_enumeration.o(i.USB2D_SetConfig)
    i.USB2D_VendorRequest                    0x0780c024   Section        0  usb2_enumeration.o(i.USB2D_VendorRequest)
    i.USBAPI_ISR                             0x0780c0bc   Section        0  usbapi.o(i.USBAPI_ISR)
    i.USBAPI_InitializeDevice                0x0780c0e0   Section        0  usbapi.o(i.USBAPI_InitializeDevice)
    i.UncompressUpdater                      0x0780c168   Section        0  tr069.o(i.UncompressUpdater)
    i.UndefinedHandler                       0x0780c420   Section        0  platform_interrupts.o(i.UndefinedHandler)
    i.UpdateBBT                              0x0780c440   Section        0  FM.o(i.UpdateBBT)
    i.UpdateUSBDeviceConfigDesc              0x0780c640   Section        0  usb_descriptors.o(i.UpdateUSBDeviceConfigDesc)
    i.Update_Battery_State                   0x0780c66c   Section        0  st7735s.o(i.Update_Battery_State)
    i.Update_DDR_Flag_From_CP                0x0780c6d8   Section        0  BootMode.o(i.Update_DDR_Flag_From_CP)
    i.Update_DDR_Flag_To_CP                  0x0780c768   Section        0  BootMode.o(i.Update_DDR_Flag_To_CP)
    i.UsticaBaseRead                         0x0780c7ac   Section        0  ustica.o(i.UsticaBaseRead)
    i.UsticaBaseWrite                        0x0780c7cc   Section        0  ustica.o(i.UsticaBaseWrite)
    i.UsticaChargerInit                      0x0780c7e0   Section        0  ustica.o(i.UsticaChargerInit)
    i.UsticaCheckBatteryConnect              0x0780c854   Section        0  ustica.o(i.UsticaCheckBatteryConnect)
    i.UsticaCheckBootONKey                   0x0780c8bc   Section        0  ustica.o(i.UsticaCheckBootONKey)
    i.UsticaCheckUSBConnect                  0x0780c944   Section        0  ustica.o(i.UsticaCheckUSBConnect)
    i.UsticaCheckWakeup                      0x0780c9a4   Section        0  ustica.o(i.UsticaCheckWakeup)
    i.UsticaEnableSMPL                       0x0780cb04   Section        0  ustica.o(i.UsticaEnableSMPL)
    i.UsticaGetBatInstantVolt                0x0780cbdc   Section        0  ustica.o(i.UsticaGetBatInstantVolt)
    i.UsticaGpadcRead                        0x0780cc90   Section        0  ustica.o(i.UsticaGpadcRead)
    i.UsticaGpadcWrite                       0x0780ccb0   Section        0  ustica.o(i.UsticaGpadcWrite)
    i.UsticaPowerRead                        0x0780ccc4   Section        0  ustica.o(i.UsticaPowerRead)
    i.UsticaPowerWrite                       0x0780cce4   Section        0  ustica.o(i.UsticaPowerWrite)
    i.UsticaPoweroff                         0x0780ccf8   Section        0  ustica.o(i.UsticaPoweroff)
    i.UsticaReadBatVolt                      0x0780cd70   Section        0  ustica.o(i.UsticaReadBatVolt)
    i.UsticaResetReg                         0x0780cdb0   Section        0  ustica.o(i.UsticaResetReg)
    i.UsticaSetMainVoltage                   0x0780ced4   Section        0  ustica.o(i.UsticaSetMainVoltage)
    i.ValidAddress                           0x0780cf0c   Section        0  RegInstructions.o(i.ValidAddress)
    i.VerifyUploadParameters                 0x0780cf14   Section        0  ProtocolManager.o(i.VerifyUploadParameters)
    i.Voltage_set_main                       0x0780cf60   Section        0  I2C.o(i.Voltage_set_main)
    i.WaitForBitClearInstruction             0x0780cfe8   Section        0  RegInstructions.o(i.WaitForBitClearInstruction)
    i.WaitForBitSetInstruction               0x0780d040   Section        0  RegInstructions.o(i.WaitForBitSetInstruction)
    i.WaitForOperationComplete               0x0780d098   Section        0  timer.o(i.WaitForOperationComplete)
    i.Wait_For_Bit_Pattern_Instruction       0x0780d108   Section        0  RegInstructions.o(i.Wait_For_Bit_Pattern_Instruction)
    i.WriteFlash                             0x0780d16c   Section        0  Flash.o(i.WriteFlash)
    i.WriteInstruction                       0x0780d1b0   Section        0  RegInstructions.o(i.WriteInstruction)
    i.WriteRam_IO                            0x0780d1c8   Section        0  st7735s.o(i.WriteRam_IO)
    i.XllpDmacMapDeviceToChannel             0x0780d26c   Section        0  dma.o(i.XllpDmacMapDeviceToChannel)
    i.XllpDmacNoDescriptorFetch              0x0780d294   Section        0  dma.o(i.XllpDmacNoDescriptorFetch)
    i.XllpDmacStartTransfer                  0x0780d2bc   Section        0  dma.o(i.XllpDmacStartTransfer)
    i.XllpDmacStopTransfer                   0x0780d2e4   Section        0  dma.o(i.XllpDmacStopTransfer)
    i.__aeabi_idiv0                          0x0780d30c   Section        0  misc.o(i.__aeabi_idiv0)
    i.__spi_nor_read                         0x0780d310   Section        0  spi_nor.o(i.__spi_nor_read)
    __spi_nor_read                           0x0780d310   ARM Code     172  spi_nor.o(i.__spi_nor_read)
    i.aclk_dfc_switch                        0x0780d3bc   Section        0  FreqChange.o(i.aclk_dfc_switch)
    i.alignChannel                           0x0780d434   Section        0  dma.o(i.alignChannel)
    i.alloc_block                            0x0780d460   Section        0  tinyalloc.o(i.alloc_block)
    alloc_block                              0x0780d460   ARM Code     304  tinyalloc.o(i.alloc_block)
    i.battery_charge                         0x0780d594   Section        0  I2C.o(i.battery_charge)
    i.battery_full                           0x0780d6d8   Section        0  I2C.o(i.battery_full)
    i.battery_process_step1                  0x0780d784   Section        0  I2C.o(i.battery_process_step1)
    i.battery_process_step2                  0x0780da48   Section        0  I2C.o(i.battery_process_step2)
    i.battery_timer                          0x0780dc50   Section        0  I2C.o(i.battery_timer)
    i.bubble                                 0x0780dd20   Section        0  misc.o(i.bubble)
    i.checkReliableDataHeader                0x0780dd68   Section        0  adc_pm803.o(i.checkReliableDataHeader)
    checkReliableDataHeader                  0x0780dd68   ARM Code       8  adc_pm803.o(i.checkReliableDataHeader)
    i.check_BQ24259RegStatus                 0x0780dd70   Section        0  bq24259.o(i.check_BQ24259RegStatus)
    i.check_BatteryConnect                   0x0780dd74   Section        0  I2C.o(i.check_BatteryConnect)
    i.check_Battery_Status                   0x0780de0c   Section        0  I2C.o(i.check_Battery_Status)
    i.check_BootONKey                        0x0780dfb8   Section        0  I2C.o(i.check_BootONKey)
    i.check_ExternalPower                    0x0780e070   Section        0  I2C.o(i.check_ExternalPower)
    i.check_USBConnect                       0x0780e1d0   Section        0  I2C.o(i.check_USBConnect)
    i.check_battery                          0x0780e268   Section        0  I2C.o(i.check_battery)
    i.check_if_DCS_mode                      0x0780e2ec   Section        0  guilin.o(i.check_if_DCS_mode)
    i.check_ota_updater                      0x0780e358   Section        0  tr069.o(i.check_ota_updater)
    i.check_updater_ram                      0x0780e3e8   Section        0  tr069.o(i.check_updater_ram)
    check_updater_ram                        0x0780e3e8   ARM Code      92  tr069.o(i.check_updater_ram)
    i.check_wakeup                           0x0780e49c   Section        0  I2C.o(i.check_wakeup)
    i.compact                                0x0780e544   Section        0  tinyalloc.o(i.compact)
    compact                                  0x0780e544   ARM Code     136  tinyalloc.o(i.compact)
    i.dcache_clean_invalidate_range          0x0780e5d0   Section        0  qspi_host.o(i.dcache_clean_invalidate_range)
    i.dcache_invalidate_range                0x0780e5e8   Section        0  qspi_host.o(i.dcache_invalidate_range)
    i.dck_dfc_switch                         0x0780e600   Section        0  FreqChange.o(i.dck_dfc_switch)
    i.decompress_cp_from_flash_to_ddr        0x0780e6a8   Section        0  BootMode.o(i.decompress_cp_from_flash_to_ddr)
    i.decompress_dsp_from_flash_to_ddr       0x0780e8a0   Section        0  BootMode.o(i.decompress_dsp_from_flash_to_ddr)
    i.do_wdt_reset                           0x0780e9e4   Section        0  wdt.o(i.do_wdt_reset)
    i.enable_PLL                             0x0780e9f4   Section        0  FreqChange.o(i.enable_PLL)
    i.firmware_upgrade                       0x0780ea38   Section        0  tr069.o(i.firmware_upgrade)
    i.free                                   0x0780ee9c   Section        0  tinyalloc.o(i.free)
    i.getAdcValue                            0x0780eea0   Section        0  adc_pm803.o(i.getAdcValue)
    i.getAdcValueFromRtnReg                  0x0780eebc   Section        0  adc_pm803.o(i.getAdcValueFromRtnReg)
    getAdcValueFromRtnReg                    0x0780eebc   ARM Code      16  adc_pm803.o(i.getAdcValueFromRtnReg)
    i.getAdcValueFromRtpReg                  0x0780eed0   Section        0  adc_pm803.o(i.getAdcValueFromRtpReg)
    getAdcValueFromRtpReg                    0x0780eed0   ARM Code      16  adc_pm803.o(i.getAdcValueFromRtpReg)
    i.getProtoBuff                           0x0780eee4   Section        0  ProtocolManager.o(i.getProtoBuff)
    i.getProtocolCmd                         0x0780eef4   Section        0  ProtocolManager.o(i.getProtocolCmd)
    i.getProtocolError                       0x0780ef04   Section        0  ProtocolManager.o(i.getProtocolError)
    i.getProtocolISR                         0x0780ef14   Section        0  ProtocolManager.o(i.getProtocolISR)
    i.getProtocolMsg                         0x0780ef20   Section        0  ProtocolManager.o(i.getProtocolMsg)
    i.getProtocolRsp                         0x0780ef2c   Section        0  ProtocolManager.o(i.getProtocolRsp)
    i.getReliableDataEntry                   0x0780ef38   Section        0  adc_pm803.o(i.getReliableDataEntry)
    getReliableDataEntry                     0x0780ef38   ARM Code     148  adc_pm803.o(i.getReliableDataEntry)
    i.getRtnAdcValue                         0x0780f000   Section        0  adc_pm803.o(i.getRtnAdcValue)
    getRtnAdcValue                           0x0780f000   ARM Code     120  adc_pm803.o(i.getRtnAdcValue)
    i.getRtpAdcValue                         0x0780f07c   Section        0  adc_pm803.o(i.getRtpAdcValue)
    getRtpAdcValue                           0x0780f07c   ARM Code      16  adc_pm803.o(i.getRtpAdcValue)
    i.getZoneByID                            0x0780f08c   Section        0  adc_pm803.o(i.getZoneByID)
    getZoneByID                              0x0780f08c   ARM Code      76  adc_pm803.o(i.getZoneByID)
    i.get_cp_exec_addr                       0x0780f0d8   Section        0  BootMode.o(i.get_cp_exec_addr)
    i.get_rw_cpz_struct_addr                 0x0780f0ec   Section        0  BootMode.o(i.get_rw_cpz_struct_addr)
    i.get_trb                                0x0780f100   Section        0  usb2_memory.o(i.get_trb)
    i.get_trb_ep0                            0x0780f13c   Section        0  usb2_memory.o(i.get_trb_ep0)
    i.get_usb_speed_mode                     0x0780f178   Section        0  usbapi.o(i.get_usb_speed_mode)
    i.guilinOpenST7735sPower                 0x0780f194   Section        0  st7735s.o(i.guilinOpenST7735sPower)
    i.guilin_read_volt_meas_val              0x0780f238   Section        0  guilin.o(i.guilin_read_volt_meas_val)
    i.ilog2                                  0x0780f260   Section        0  spi_nor.o(i.ilog2)
    ilog2                                    0x0780f260   ARM Code      36  spi_nor.o(i.ilog2)
    i.insert_block                           0x0780f284   Section        0  tinyalloc.o(i.insert_block)
    insert_block                             0x0780f284   ARM Code      72  tinyalloc.o(i.insert_block)
    i.loadNonDescriptor                      0x0780f2d0   Section        0  dma.o(i.loadNonDescriptor)
    i.load_dsp_adc                           0x0780f308   Section        0  adc_pm803.o(i.load_dsp_adc)
    i.loadtable_init                         0x0780f4ac   Section        0  BootMode.o(i.loadtable_init)
    i.m_atoi_dec                             0x0780f4c0   Section        0  adc_pm803.o(i.m_atoi_dec)
    m_atoi_dec                               0x0780f4c0   ARM Code      84  adc_pm803.o(i.m_atoi_dec)
    i.m_strlen                               0x0780f514   Section        0  adc_pm803.o(i.m_strlen)
    m_strlen                                 0x0780f514   ARM Code      32  adc_pm803.o(i.m_strlen)
    i.malloc                                 0x0780f534   Section        0  tinyalloc.o(i.malloc)
    i.malloc_init                            0x0780f538   Section        0  tinyalloc.o(i.malloc_init)
    i.memcmp                                 0x0780f53c   Section        0  misc.o(i.memcmp)
    i.memset                                 0x0780f57c   Section        0  misc.o(i.memset)
    i.mpu_debug                              0x0780f594   Section        0  mpu.o(i.mpu_debug)
    i.obm_printf                             0x0780f5d4   Section        0  print.o(i.obm_printf)
    i.print                                  0x0780f5f4   Section        0  print.o(i.print)
    i.printchar                              0x0780f8fc   Section        0  print.o(i.printchar)
    printchar                                0x0780f8fc   ARM Code      40  print.o(i.printchar)
    i.printi                                 0x0780f924   Section        0  print.o(i.printi)
    printi                                   0x0780f924   ARM Code     272  print.o(i.printi)
    i.prints                                 0x0780fa34   Section        0  print.o(i.prints)
    prints                                   0x0780fa34   ARM Code     188  print.o(i.prints)
    i.ql_boot_all_pin_error                  0x0780faf0   Section        0  quec_boot_all_pin_test.o(i.ql_boot_all_pin_error)
    ql_boot_all_pin_error                    0x0780faf0   ARM Code      40  quec_boot_all_pin_test.o(i.ql_boot_all_pin_error)
    i.ql_boot_all_pin_ok                     0x0780fb24   Section        0  quec_boot_all_pin_test.o(i.ql_boot_all_pin_ok)
    ql_boot_all_pin_ok                       0x0780fb24   ARM Code      40  quec_boot_all_pin_test.o(i.ql_boot_all_pin_ok)
    i.ql_boot_all_pin_out_str                0x0780fb54   Section        0  quec_boot_all_pin_test.o(i.ql_boot_all_pin_out_str)
    ql_boot_all_pin_out_str                  0x0780fb54   ARM Code      48  quec_boot_all_pin_test.o(i.ql_boot_all_pin_out_str)
    i.ql_boot_all_pin_test                   0x0780fb88   Section        0  quec_boot_all_pin_test.o(i.ql_boot_all_pin_test)
    i.ql_boot_check_download_key             0x0780fd08   Section        0  quec_boot_platform.o(i.ql_boot_check_download_key)
    i.ql_boot_check_pwrkey                   0x0780fdbc   Section        0  quec_boot_platform.o(i.ql_boot_check_pwrkey)
    i.ql_boot_enter_all_pin_test             0x0780ff50   Section        0  quec_boot_all_pin_test.o(i.ql_boot_enter_all_pin_test)
    ql_boot_enter_all_pin_test               0x0780ff50   ARM Code     492  quec_boot_all_pin_test.o(i.ql_boot_enter_all_pin_test)
    i.ql_boot_get_auto_dload_state           0x07810178   Section        0  quec_boot_platform.o(i.ql_boot_get_auto_dload_state)
    i.ql_boot_get_download_mode              0x07810214   Section        0  quec_boot_platform.o(i.ql_boot_get_download_mode)
    i.ql_boot_get_hot_powerup_state          0x07810230   Section        0  quec_boot_platform.o(i.ql_boot_get_hot_powerup_state)
    i.ql_boot_gpio_get                       0x078102f0   Section        0  quec_boot_all_pin_test.o(i.ql_boot_gpio_get)
    i.ql_boot_gpio_set                       0x07810320   Section        0  quec_boot_all_pin_test.o(i.ql_boot_gpio_set)
    i.ql_boot_gpio_set_input                 0x0781034c   Section        0  quec_boot_all_pin_test.o(i.ql_boot_gpio_set_input)
    i.ql_boot_gpio_set_output                0x078103a0   Section        0  quec_boot_all_pin_test.o(i.ql_boot_gpio_set_output)
    i.ql_boot_pin_set                        0x078103f4   Section        0  quec_boot_all_pin_test.o(i.ql_boot_pin_set)
    ql_boot_pin_set                          0x078103f4   ARM Code      76  quec_boot_all_pin_test.o(i.ql_boot_pin_set)
    i.ql_boot_prv_pwk_type                   0x07810444   Section        0  quec_boot_platform.o(i.ql_boot_prv_pwk_type)
    ql_boot_prv_pwk_type                     0x07810444   ARM Code      68  quec_boot_platform.o(i.ql_boot_prv_pwk_type)
    i.ql_boot_sd_power_on                    0x078104b8   Section        0  quec_boot_all_pin_test.o(i.ql_boot_sd_power_on)
    ql_boot_sd_power_on                      0x078104b8   ARM Code      88  quec_boot_all_pin_test.o(i.ql_boot_sd_power_on)
    i.ql_boot_set_all_pin_test_as_gpio       0x07810540   Section        0  quec_boot_all_pin_test.o(i.ql_boot_set_all_pin_test_as_gpio)
    ql_boot_set_all_pin_test_as_gpio         0x07810540   ARM Code      64  quec_boot_all_pin_test.o(i.ql_boot_set_all_pin_test_as_gpio)
    i.ql_boot_set_auto_download              0x07810584   Section        0  quec_boot_platform.o(i.ql_boot_set_auto_download)
    i.ql_boot_set_gpio_fun                   0x07810640   Section        0  quec_boot_all_pin_test.o(i.ql_boot_set_gpio_fun)
    i.ql_boot_set_hot_powerup_state          0x07810674   Section        0  quec_boot_platform.o(i.ql_boot_set_hot_powerup_state)
    i.ql_boot_update_pmic_reg                0x07810728   Section        0  quec_boot_platform.o(i.ql_boot_update_pmic_reg)
    i.ql_gpio_get_base_reg_addr              0x078108a0   Section        0  quec_boot_all_pin_test.o(i.ql_gpio_get_base_reg_addr)
    ql_gpio_get_base_reg_addr                0x078108a0   ARM Code      68  quec_boot_all_pin_test.o(i.ql_gpio_get_base_reg_addr)
    i.ql_gpio_mfpr_addr                      0x078108ec   Section        0  quec_boot_all_pin_test.o(i.ql_gpio_mfpr_addr)
    ql_gpio_mfpr_addr                        0x078108ec   ARM Code     264  quec_boot_all_pin_test.o(i.ql_gpio_mfpr_addr)
    i.qspi_cmd_done_interrupt                0x07810a30   Section        0  qspi_host.o(i.qspi_cmd_done_interrupt)
    i.qspi_cmd_done_pio                      0x07810a4c   Section        0  qspi_host.o(i.qspi_cmd_done_pio)
    i.qspi_config_interrupt                  0x07810a88   Section        0  qspi_host.o(i.qspi_config_interrupt)
    qspi_config_interrupt                    0x07810a88   ARM Code      28  qspi_host.o(i.qspi_config_interrupt)
    i.qspi_config_lookup_tbl                 0x07810ab4   Section        0  qspi_host.o(i.qspi_config_lookup_tbl)
    qspi_config_lookup_tbl                   0x07810ab4   ARM Code     652  qspi_host.o(i.qspi_config_lookup_tbl)
    i.qspi_config_mfp                        0x07810d40   Section        0  qspi_host.o(i.qspi_config_mfp)
    qspi_config_mfp                          0x07810d40   ARM Code      96  qspi_host.o(i.qspi_config_mfp)
    i.qspi_disable_dma                       0x07810da4   Section        0  qspi_host.o(i.qspi_disable_dma)
    qspi_disable_dma                         0x07810da4   ARM Code      24  qspi_host.o(i.qspi_disable_dma)
    i.qspi_disable_interrupt                 0x07810dc8   Section        0  qspi_host.o(i.qspi_disable_interrupt)
    qspi_disable_interrupt                   0x07810dc8   ARM Code      68  qspi_host.o(i.qspi_disable_interrupt)
    i.qspi_enable_dma                        0x07810e18   Section        0  qspi_host.o(i.qspi_enable_dma)
    qspi_enable_dma                          0x07810e18   ARM Code      24  qspi_host.o(i.qspi_enable_dma)
    i.qspi_enable_interrupt                  0x07810e3c   Section        0  qspi_host.o(i.qspi_enable_interrupt)
    qspi_enable_interrupt                    0x07810e3c   ARM Code      84  qspi_host.o(i.qspi_enable_interrupt)
    i.qspi_enable_xip                        0x07810e9c   Section        0  qspi_host.o(i.qspi_enable_xip)
    i.qspi_enter_mode                        0x07810f30   Section        0  qspi_host.o(i.qspi_enter_mode)
    qspi_enter_mode                          0x07810f30   ARM Code      36  qspi_host.o(i.qspi_enter_mode)
    i.qspi_fill_to_txbuff                    0x07810f5c   Section        0  qspi_host.o(i.qspi_fill_to_txbuff)
    qspi_fill_to_txbuff                      0x07810f5c   ARM Code     200  qspi_host.o(i.qspi_fill_to_txbuff)
    i.qspi_fill_tx_buff                      0x07811058   Section        0  qspi_host.o(i.qspi_fill_tx_buff)
    qspi_fill_tx_buff                        0x07811058   ARM Code     164  qspi_host.o(i.qspi_fill_tx_buff)
    i.qspi_get_cfg                           0x07811100   Section        0  qspi_host.o(i.qspi_get_cfg)
    i.qspi_host_init                         0x0781110c   Section        0  qspi_host.o(i.qspi_host_init)
    i.qspi_init_ahb                          0x07811288   Section        0  qspi_host.o(i.qspi_init_ahb)
    i.qspi_invalid_ahb                       0x07811338   Section        0  qspi_host.o(i.qspi_invalid_ahb)
    qspi_invalid_ahb                         0x07811338   ARM Code      72  qspi_host.o(i.qspi_invalid_ahb)
    i.qspi_irq_handler                       0x07811388   Section        0  qspi_host.o(i.qspi_irq_handler)
    i.qspi_poll_rx_buff                      0x07811654   Section        0  qspi_host.o(i.qspi_poll_rx_buff)
    qspi_poll_rx_buff                        0x07811654   ARM Code     320  qspi_host.o(i.qspi_poll_rx_buff)
    i.qspi_preinit_lookup_tbl                0x078117d4   Section        0  qspi_host.o(i.qspi_preinit_lookup_tbl)
    i.qspi_prepare_recv                      0x07811890   Section        0  qspi_host.o(i.qspi_prepare_recv)
    qspi_prepare_recv                        0x07811890   ARM Code     100  qspi_host.o(i.qspi_prepare_recv)
    i.qspi_prepare_transmit                  0x078118f8   Section        0  qspi_host.o(i.qspi_prepare_transmit)
    qspi_prepare_transmit                    0x078118f8   ARM Code     248  qspi_host.o(i.qspi_prepare_transmit)
    i.qspi_read_from_rxbuff                  0x078119f4   Section        0  qspi_host.o(i.qspi_read_from_rxbuff)
    qspi_read_from_rxbuff                    0x078119f4   ARM Code     156  qspi_host.o(i.qspi_read_from_rxbuff)
    i.qspi_set_func_clk                      0x07811a94   Section        0  qspi_host.o(i.qspi_set_func_clk)
    i.qspi_set_func_clk_fc                   0x07811b54   Section        0  qspi_host.o(i.qspi_set_func_clk_fc)
    qspi_set_func_clk_fc                     0x07811b54   ARM Code     240  qspi_host.o(i.qspi_set_func_clk_fc)
    i.qspi_start_cmd                         0x07811c64   Section        0  qspi_host.o(i.qspi_start_cmd)
    i.qspi_start_dma_xfer                    0x07811f90   Section        0  qspi_host.o(i.qspi_start_dma_xfer)
    i.qspi_update_shared_lut                 0x07812038   Section        0  qspi_host.o(i.qspi_update_shared_lut)
    qspi_update_shared_lut                   0x07812038   ARM Code      44  qspi_host.o(i.qspi_update_shared_lut)
    i.qspi_wait_cmd_done                     0x07812064   Section        0  qspi_host.o(i.qspi_wait_cmd_done)
    qspi_wait_cmd_done                       0x07812064   ARM Code     536  qspi_host.o(i.qspi_wait_cmd_done)
    i.qspi_write_rbct                        0x0781227c   Section        0  qspi_host.o(i.qspi_write_rbct)
    qspi_write_rbct                          0x0781227c   ARM Code      68  qspi_host.o(i.qspi_write_rbct)
    i.qspi_write_sfar                        0x078122c8   Section        0  qspi_host.o(i.qspi_write_sfar)
    qspi_write_sfar                          0x078122c8   ARM Code      64  qspi_host.o(i.qspi_write_sfar)
    i.qspi_writel_check                      0x0781230c   Section        0  qspi_host.o(i.qspi_writel_check)
    qspi_writel_check                        0x0781230c   ARM Code      28  qspi_host.o(i.qspi_writel_check)
    i.qspi_writel_clear                      0x07812328   Section        0  qspi_host.o(i.qspi_writel_clear)
    qspi_writel_clear                        0x07812328   ARM Code      28  qspi_host.o(i.qspi_writel_clear)
    i.qspi_xfer_done                         0x07812344   Section        0  qspi_host.o(i.qspi_xfer_done)
    qspi_xfer_done                           0x07812344   ARM Code     192  qspi_host.o(i.qspi_xfer_done)
    i.readDmaStatusRegister                  0x07812444   Section        0  dma.o(i.readDmaStatusRegister)
    i.release_blocks                         0x07812458   Section        0  tinyalloc.o(i.release_blocks)
    release_blocks                           0x07812458   ARM Code      60  tinyalloc.o(i.release_blocks)
    i.resetProtoBuff                         0x07812498   Section        0  ProtocolManager.o(i.resetProtoBuff)
    i.resetTimer                             0x078124b0   Section        0  bq24259.o(i.resetTimer)
    i.serial_init                            0x078124e0   Section        0  serial.o(i.serial_init)
    i.serial_outnum                          0x07812550   Section        0  serial.o(i.serial_outnum)
    i.serial_outstr                          0x07812594   Section        0  serial.o(i.serial_outstr)
    i.serial_poll                            0x078125c0   Section        0  serial.o(i.serial_poll)
    i.serial_read_byte                       0x078125ec   Section        0  serial.o(i.serial_read_byte)
    i.serial_write                           0x07812614   Section        0  serial.o(i.serial_write)
    i.setProtoBuff                           0x0781263c   Section        0  ProtocolManager.o(i.setProtoBuff)
    i.setProtocolCmd                         0x0781264c   Section        0  ProtocolManager.o(i.setProtocolCmd)
    i.setProtocolError                       0x0781265c   Section        0  ProtocolManager.o(i.setProtocolError)
    i.show_Downlaod                          0x0781266c   Section        0  st7735s.o(i.show_Downlaod)
    i.show_battery_status                    0x078126e8   Section        0  st7735s.o(i.show_battery_status)
    i.show_external_power                    0x0781276c   Section        0  st7735s.o(i.show_external_power)
    i.show_logo                              0x078127f0   Section        0  st7735s.o(i.show_logo)
    i.spi_nor_disable_4byte_mode             0x07812850   Section        0  spi_nor.o(i.spi_nor_disable_4byte_mode)
    spi_nor_disable_4byte_mode               0x07812850   ARM Code     100  spi_nor.o(i.spi_nor_disable_4byte_mode)
    i.spi_nor_do_erase                       0x078128d4   Section        0  spi_nor.o(i.spi_nor_do_erase)
    spi_nor_do_erase                         0x078128d4   ARM Code      20  spi_nor.o(i.spi_nor_do_erase)
    i.spi_nor_do_read                        0x078128ec   Section        0  spi_nor.o(i.spi_nor_do_read)
    spi_nor_do_read                          0x078128ec   ARM Code      16  spi_nor.o(i.spi_nor_do_read)
    i.spi_nor_do_write                       0x07812900   Section        0  spi_nor.o(i.spi_nor_do_write)
    spi_nor_do_write                         0x07812900   ARM Code      16  spi_nor.o(i.spi_nor_do_write)
    i.spi_nor_enable_4byte_mode              0x07812914   Section        0  spi_nor.o(i.spi_nor_enable_4byte_mode)
    spi_nor_enable_4byte_mode                0x07812914   ARM Code     228  spi_nor.o(i.spi_nor_enable_4byte_mode)
    i.spi_nor_enable_quad                    0x07812a84   Section        0  spi_nor.o(i.spi_nor_enable_quad)
    spi_nor_enable_quad                      0x07812a84   ARM Code       8  spi_nor.o(i.spi_nor_enable_quad)
    i.spi_nor_erase                          0x07812a8c   Section        0  spi_nor.o(i.spi_nor_erase)
    i.spi_nor_erase_32k_blk                  0x07812c6c   Section        0  spi_nor.o(i.spi_nor_erase_32k_blk)
    spi_nor_erase_32k_blk                    0x07812c6c   ARM Code      20  spi_nor.o(i.spi_nor_erase_32k_blk)
    i.spi_nor_erase_64k_blk                  0x07812c80   Section        0  spi_nor.o(i.spi_nor_erase_64k_blk)
    spi_nor_erase_64k_blk                    0x07812c80   ARM Code      20  spi_nor.o(i.spi_nor_erase_64k_blk)
    i.spi_nor_erase_all                      0x07812c94   Section        0  spi_nor.o(i.spi_nor_erase_all)
    spi_nor_erase_all                        0x07812c94   ARM Code      60  spi_nor.o(i.spi_nor_erase_all)
    i.spi_nor_erase_block                    0x07812cf4   Section        0  spi_nor.o(i.spi_nor_erase_block)
    spi_nor_erase_block                      0x07812cf4   ARM Code     156  spi_nor.o(i.spi_nor_erase_block)
    i.spi_nor_erase_chip                     0x07812d90   Section        0  spi_nor.o(i.spi_nor_erase_chip)
    spi_nor_erase_chip                       0x07812d90   ARM Code      80  spi_nor.o(i.spi_nor_erase_chip)
    i.spi_nor_erase_sector                   0x07812de0   Section        0  spi_nor.o(i.spi_nor_erase_sector)
    spi_nor_erase_sector                     0x07812de0   ARM Code     160  spi_nor.o(i.spi_nor_erase_sector)
    i.spi_nor_init                           0x07812e80   Section        0  spi_nor.o(i.spi_nor_init)
    i.spi_nor_program_data                   0x078130ec   Section        0  spi_nor.o(i.spi_nor_program_data)
    spi_nor_program_data                     0x078130ec   ARM Code     172  spi_nor.o(i.spi_nor_program_data)
    i.spi_nor_read                           0x07813198   Section        0  spi_nor.o(i.spi_nor_read)
    spi_nor_read                             0x07813198   ARM Code     260  spi_nor.o(i.spi_nor_read)
    i.spi_nor_read_id                        0x078132e8   Section        0  spi_nor.o(i.spi_nor_read_id)
    spi_nor_read_id                          0x078132e8   ARM Code      92  spi_nor.o(i.spi_nor_read_id)
    i.spi_nor_read_status                    0x07813344   Section        0  spi_nor.o(i.spi_nor_read_status)
    spi_nor_read_status                      0x07813344   ARM Code     104  spi_nor.o(i.spi_nor_read_status)
    i.spi_nor_read_status1                   0x078133c4   Section        0  spi_nor.o(i.spi_nor_read_status1)
    spi_nor_read_status1                     0x078133c4   ARM Code      16  spi_nor.o(i.spi_nor_read_status1)
    i.spi_nor_read_status2                   0x078133d4   Section        0  spi_nor.o(i.spi_nor_read_status2)
    spi_nor_read_status2                     0x078133d4   ARM Code      60  spi_nor.o(i.spi_nor_read_status2)
    i.spi_nor_read_status3                   0x07813410   Section        0  spi_nor.o(i.spi_nor_read_status3)
    spi_nor_read_status3                     0x07813410   ARM Code      16  spi_nor.o(i.spi_nor_read_status3)
    i.spi_nor_reset                          0x07813420   Section        0  spi_nor.o(i.spi_nor_reset)
    spi_nor_reset                            0x07813420   ARM Code     148  spi_nor.o(i.spi_nor_reset)
    i.spi_nor_scan_id_table                  0x078134ec   Section        0  spi_nor.o(i.spi_nor_scan_id_table)
    spi_nor_scan_id_table                    0x078134ec   ARM Code     136  spi_nor.o(i.spi_nor_scan_id_table)
    i.spi_nor_set_quad                       0x07813598   Section        0  spi_nor.o(i.spi_nor_set_quad)
    spi_nor_set_quad                         0x07813598   ARM Code     344  spi_nor.o(i.spi_nor_set_quad)
    i.spi_nor_set_rd_wr_op                   0x07813788   Section        0  spi_nor.o(i.spi_nor_set_rd_wr_op)
    spi_nor_set_rd_wr_op                     0x07813788   ARM Code     144  spi_nor.o(i.spi_nor_set_rd_wr_op)
    i.spi_nor_wait                           0x0781384c   Section        0  spi_nor.o(i.spi_nor_wait)
    spi_nor_wait                             0x0781384c   ARM Code      56  spi_nor.o(i.spi_nor_wait)
    i.spi_nor_write                          0x07813884   Section        0  spi_nor.o(i.spi_nor_write)
    spi_nor_write                            0x07813884   ARM Code     216  spi_nor.o(i.spi_nor_write)
    i.spi_nor_write_enable                   0x078139c4   Section        0  spi_nor.o(i.spi_nor_write_enable)
    spi_nor_write_enable                     0x078139c4   ARM Code      76  spi_nor.o(i.spi_nor_write_enable)
    i.spi_nor_write_page                     0x07813a10   Section        0  spi_nor.o(i.spi_nor_write_page)
    spi_nor_write_page                       0x07813a10   ARM Code      88  spi_nor.o(i.spi_nor_write_page)
    i.spi_nor_write_status1                  0x07813a90   Section        0  spi_nor.o(i.spi_nor_write_status1)
    spi_nor_write_status1                    0x07813a90   ARM Code      96  spi_nor.o(i.spi_nor_write_status1)
    i.spi_nor_write_status2                  0x07813b08   Section        0  spi_nor.o(i.spi_nor_write_status2)
    spi_nor_write_status2                    0x07813b08   ARM Code      96  spi_nor.o(i.spi_nor_write_status2)
    i.strcmpl                                0x07813b80   Section        0  misc.o(i.strcmpl)
    i.strlen                                 0x07813be0   Section        0  misc.o(i.strlen)
    i.ta_alloc                               0x07813c08   Section        0  tinyalloc.o(i.ta_alloc)
    i.ta_free                                0x07813c60   Section        0  tinyalloc.o(i.ta_free)
    i.ta_init                                0x07813cbc   Section        0  tinyalloc.o(i.ta_init)
    i.using_bsdiff_upgrade                   0x07813d1c   Section        0  bspatch.o(i.using_bsdiff_upgrade)
    i.usticaOpenST7735sPower                 0x07813d2c   Section        0  st7735s.o(i.usticaOpenST7735sPower)
    i.ustica_read_volt_meas_val              0x07813d70   Section        0  ustica.o(i.ustica_read_volt_meas_val)
    i.wdt_access                             0x07813d94   Section        0  wdt.o(i.wdt_access)
    wdt_access                               0x07813d94   ARM Code      24  wdt.o(i.wdt_access)
    i.wdt_enable                             0x07813db0   Section        0  wdt.o(i.wdt_enable)
    wdt_enable                               0x07813db0   ARM Code      44  wdt.o(i.wdt_enable)
    i.wdt_reset_counter                      0x07813de0   Section        0  wdt.o(i.wdt_reset_counter)
    wdt_reset_counter                        0x07813de0   ARM Code      36  wdt.o(i.wdt_reset_counter)
    i.wdt_set_match                          0x07813e08   Section        0  wdt.o(i.wdt_set_match)
    wdt_set_match                            0x07813e08   ARM Code      48  wdt.o(i.wdt_set_match)
    i.wdt_test                               0x07813e3c   Section        0  wdt.o(i.wdt_test)
    x$div0                                   0x07813edc   Section       12  aeabi_sdiv_div0_cr4.o(x$div0)
    __divbyzero                              0x07813edd   Thumb Code    12  aeabi_sdiv_div0_cr4.o(x$div0)
    x$sdiv                                   0x07813ee8   Section       12  aeabi_sdiv_div0_cr4.o(x$sdiv)
    x$udiv                                   0x07813efc   Section       12  aeabi_sdiv_div0_cr4.o(x$udiv)
    x$udivmod                                0x07813f08   Section       18  aeabi_sdiv_div0_cr4.o(x$udivmod)
    .constdata                               0x07813f1c   Section       61  spi_nor.o(.constdata)
    qspi_ops                                 0x07813f1c   Data          20  spi_nor.o(.constdata)
    __func__                                 0x07813f30   Data          14  spi_nor.o(.constdata)
    __func__                                 0x07813f3e   Data          13  spi_nor.o(.constdata)
    __func__                                 0x07813f4b   Data          14  spi_nor.o(.constdata)
    .constdata                               0x07813f59   Section       17  serial.o(.constdata)
    .constdata                               0x07813f6c   Section      612  PlatformConfig.o(.constdata)
    .constdata                               0x078141d0   Section       37  adc_pm803.o(.constdata)
    __FUNCTION__                             0x078141d0   Data          19  adc_pm803.o(.constdata)
    __FUNCTION__                             0x078141e3   Data          18  adc_pm803.o(.constdata)
    .constdata                               0x078141f5   Section      192  I2C.o(.constdata)
    __FUNCTION__                             0x078141f5   Data          10  I2C.o(.constdata)
    __FUNCTION__                             0x078141ff   Data          16  I2C.o(.constdata)
    __FUNCTION__                             0x0781420f   Data          13  I2C.o(.constdata)
    __FUNCTION__                             0x0781421c   Data          27  I2C.o(.constdata)
    __FUNCTION__                             0x07814237   Data          17  I2C.o(.constdata)
    __FUNCTION__                             0x07814248   Data          12  I2C.o(.constdata)
    __FUNCTION__                             0x07814254   Data          18  I2C.o(.constdata)
    __FUNCTION__                             0x07814266   Data          17  I2C.o(.constdata)
    __FUNCTION__                             0x07814277   Data          21  I2C.o(.constdata)
    __FUNCTION__                             0x0781428c   Data          13  I2C.o(.constdata)
    __FUNCTION__                             0x07814299   Data          16  I2C.o(.constdata)
    __FUNCTION__                             0x078142a9   Data          12  I2C.o(.constdata)
    .constdata                               0x078142b5   Section     1536  oled_lib.o(.constdata)
    .constdata                               0x078148b5   Section      256  oled_lib.o(.constdata)
    .constdata                               0x078149b5   Section      128  oled_lib.o(.constdata)
    .constdata                               0x07814a35   Section       64  oled_lib.o(.constdata)
    .constdata                               0x07814a75   Section      256  oled_lib.o(.constdata)
    .constdata                               0x07814b78   Section       32  qspi_host.o(.constdata)
    __FUNCTION__                             0x07814b84   Data          20  qspi_host.o(.constdata)
    .constdata                               0x07814b98   Section       26  guilin_lite.o(.constdata)
    __FUNCTION__                             0x07814b98   Data          26  guilin_lite.o(.constdata)
    .constdata                               0x07814bb2   Section        9  tinyalloc.o(.constdata)
    __FUNCTION__                             0x07814bb2   Data           9  tinyalloc.o(.constdata)
    .constdata                               0x07814bbb   Section       20  quec_boot_all_pin_test.o(.constdata)
    __FUNCTION__                             0x07814bbb   Data          20  quec_boot_all_pin_test.o(.constdata)
    .constdata                               0x07814bcf   Section      201  quec_boot_platform.o(.constdata)
    __FUNCTION__                             0x07814bcf   Data          24  quec_boot_platform.o(.constdata)
    __FUNCTION__                             0x07814be7   Data          29  quec_boot_platform.o(.constdata)
    __FUNCTION__                             0x07814c04   Data          26  quec_boot_platform.o(.constdata)
    __FUNCTION__                             0x07814c1e   Data          21  quec_boot_platform.o(.constdata)
    __FUNCTION__                             0x07814c33   Data          21  quec_boot_platform.o(.constdata)
    __FUNCTION__                             0x07814c48   Data          30  quec_boot_platform.o(.constdata)
    __FUNCTION__                             0x07814c66   Data          30  quec_boot_platform.o(.constdata)
    __FUNCTION__                             0x07814c84   Data          20  quec_boot_platform.o(.constdata)
    .conststring                             0x07814c98   Section      362  spi_nor.o(.conststring)
    .conststring                             0x07814e04   Section       66  BootMode.o(.conststring)
    .conststring                             0x07814e48   Section       83  adc_pm803.o(.conststring)
    .conststring                             0x07814e9c   Section      170  quec_boot_platform.o(.conststring)
    .data                                    0x07840000   Section     1454  spi_nor.o(.data)
    spi_nor_table                            0x07840000   Data         992  spi_nor.o(.data)
    cmd_table                                0x078403e0   Data         462  spi_nor.o(.data)
    .data                                    0x078405b0   Section        4  timer.o(.data)
    misc_SOD_OSCR0                           0x078405b0   Data           4  timer.o(.data)
    .data                                    0x078405b4   Section      516  tr069.o(.data)
    crc16_table                              0x078405b8   Data         512  tr069.o(.data)
    .data                                    0x078407b8   Section        8  FM.o(.data)
    .data                                    0x078407c0   Section        4  dma.o(.data)
    pDmacHandle                              0x078407c0   Data           4  dma.o(.data)
    .data                                    0x078407c4   Section       20  BootLoader.o(.data)
    .data                                    0x078407d8   Section        4  BootMode.o(.data)
    .data                                    0x078407dc   Section        8  BootMode.o(.data)
    .data                                    0x078407e4   Section       16  DownloadMode.o(.data)
    .data                                    0x078407f4   Section       28  TIMDownload.o(.data)
    .data                                    0x07840810   Section        4  serial.o(.data)
    .data                                    0x07840814   Section        1  FreqChange.o(.data)
    voltageSet                               0x07840814   Data           1  FreqChange.o(.data)
    .data                                    0x07840818   Section        4  mpu.o(.data)
    line                                     0x07840818   Data           4  mpu.o(.data)
    .data                                    0x0784081c   Section       20  PlatformConfig.o(.data)
    NF_CLK_default                           0x0784081c   Data           4  PlatformConfig.o(.data)
    .data                                    0x07840830   Section        1  platform_interrupts.o(.data)
    PortInterrupt                            0x07840830   Data           1  platform_interrupts.o(.data)
    .data                                    0x07840834   Section       20  ProtocolManager.o(.data)
    sProtocolError                           0x07840839   Data           1  ProtocolManager.o(.data)
    pGProtocolCmd                            0x07840844   Data           4  ProtocolManager.o(.data)
    .data                                    0x07840848   Section      130  usb_descriptors.o(.data)
    pTIMUSB_h                                0x07840850   Data           4  usb_descriptors.o(.data)
    .data                                    0x078408ca   Section       10  usb_descriptors.o(.data)
    .data                                    0x078408d4   Section        4  usb2_enumeration.o(.data)
    .data                                    0x078408d8   Section       32  usb2_main.o(.data)
    .data                                    0x078408f8   Section        8  usbapi.o(.data)
    usb_speed_mode                           0x078408f8   Data           4  usbapi.o(.data)
    usb_phy_mode                             0x078408fc   Data           4  usbapi.o(.data)
    .data                                    0x07840900   Section       20  tim.o(.data)
    pGetNextTim_h                            0x07840900   Data           4  tim.o(.data)
    pLastIdFound                             0x07840904   Data           4  tim.o(.data)
    lastIdType                               0x07840908   Data           4  tim.o(.data)
    reservedPackageCount                     0x0784090c   Data           4  tim.o(.data)
    TIMValidationStatus                      0x07840910   Data           4  tim.o(.data)
    .data                                    0x07840914   Section       40  adc_pm803.o(.data)
    .data                                    0x0784093c   Section       16  I2C.o(.data)
    .data                                    0x0784094c   Section        4  charger.o(.data)
    .data                                    0x07840950   Section        2  guilin.o(.data)
    isDcsMode                                0x07840950   Data           1  guilin.o(.data)
    init_var                                 0x07840951   Data           1  guilin.o(.data)
    .data                                    0x07840954   Section       12  bspatch.o(.data)
    Dfota_Image_ID                           0x07840954   Data           4  bspatch.o(.data)
    Dfota_Flash_Start_Address                0x07840958   Data           4  bspatch.o(.data)
    bsdiff_upgrade                           0x0784095c   Data           4  bspatch.o(.data)
    .data                                    0x07840960   Section       12  tinyalloc.o(.data)
    heap                                     0x07840968   Data           4  tinyalloc.o(.data)
    .data                                    0x0784096c   Section       12  st7735s.o(.data)
    .data                                    0x07840978   Section       16  quec_boot_all_pin_test.o(.data)
    g_gpio_reg                               0x07840978   Data          16  quec_boot_all_pin_test.o(.data)
    .data                                    0x07840988   Section      462  quec_boot_all_pin_map.o(.data)
    .bss                                     0x07840b58   Section      360  spi_nor.o(.bss)
    nor_chip                                 0x07840b58   Data         360  spi_nor.o(.bss)
    .bss                                     0x07840cc0   Section       12  RegInstructions.o(.bss)
    .bss                                     0x07840ccc   Section       12  tr069.o(.bss)
    .bss                                     0x07840cd8   Section      152  Flash.o(.bss)
    .bss                                     0x07840d70   Section       68  FM.o(.bss)
    .bss                                     0x07840db8   Section    20688  ProtocolManager.o(.bss)
    GProtocolISR                             0x07844db8   Data          32  ProtocolManager.o(.bss)
    GProtocolRsp                             0x07844dd8   Data          38  ProtocolManager.o(.bss)
    GProtocolMsg                             0x07844e00   Data        4232  ProtocolManager.o(.bss)
    .bss                                     0x07845e88   Section      124  usb_descriptors.o(.bss)
    .bss                                     0x07845f04   Section     1120  usb2_main.o(.bss)
    .bss                                     0x07846364   Section     6484  usb2_memory.o(.bss)
    usb2info                                 0x07846364   Data          24  usb2_memory.o(.bss)
    .bss                                     0x07847cb8   Section       64  usbapi.o(.bss)
    .bss                                     0x07847cf8   Section       20  tim.o(.bss)
    sTim                                     0x07847cf8   Data          20  tim.o(.bss)
    .bss                                     0x07847d0c   Section       68  qspi_host.o(.bss)
    qspi_host                                0x07847d0c   Data          68  qspi_host.o(.bss)
    .bss                                     0x07847d50   Section       64  quec_boot_all_pin_test.o(.bss)
    g_ql_all_pin_rx_buff                     0x07847d50   Data          64  quec_boot_all_pin_test.o(.bss)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$ARM_ISAv7$E$P$J$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$STANDARDLIB$THUMB2LIB$REQ8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    Image$$ER_RW$$ZI$$Length                 0x00007238   Number         0  anon$$obj.o ABSOLUTE
    Load$$RO$$Base                           0x07800000   Number         0  anon$$obj.o ABSOLUTE
    obm_header                               0x07800000   Data         256  version_block.o(IMG_HEADER_INFO)
    strncmp                                  0x07800100   ARM Code     236  strncmp.o(.text)
    __aeabi_memcpy                           0x078001f0   ARM Code       0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x078001f0   ARM Code     136  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x07800258   ARM Code       0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x07800278   ARM Code       0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x07800278   ARM Code       0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x07800278   ARM Code     100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x078002c4   ARM Code       0  rt_memcpy_w.o(.text)
    __aeabi_memclr4                          0x078002dd   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x078002dd   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x078002dd   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x078002e1   Thumb Code     0  rt_memclr_w.o(.text)
    BBU_PI2C_Init                            0x0780032c   ARM Code     152  bbu_PI2C.o(I2C)
    BBU_getPI2C                              0x078003c4   ARM Code     256  bbu_PI2C.o(I2C)
    BBU_putPI2C                              0x078004dc   ARM Code     168  bbu_PI2C.o(I2C)
    BBU_CI2C_Init                            0x07800584   ARM Code     132  bbu_CI2C.o(I2C)
    BBU_getCI2C                              0x07800608   ARM Code     256  bbu_CI2C.o(I2C)
    BBU_putCI2C                              0x07800718   ARM Code     172  bbu_CI2C.o(I2C)
    ExceptionVectors                         0x078007e0   ARM Code       0  bl_StartUp_ttc.o(Init)
    __main                                   0x078007e0   ARM Code       0  bl_StartUp_ttc.o(Init)
    ExecutableVersionInfo                    0x07800804   Data           0  bl_StartUp_ttc.o(Init)
    RedirectionVectors                       0x07800820   Data           0  bl_StartUp_ttc.o(Init)
    ResetHandler                             0x07800840   ARM Code       0  bl_StartUp_ttc.o(Init)
    TransferControl                          0x078009ac   ARM Code       0  bl_StartUp_ttc.o(Init)
    SaveProgramState                         0x07800a74   ARM Code       0  bl_StartUp_ttc.o(Init)
    SetInterruptVector                       0x07800aa4   ARM Code       0  bl_StartUp_ttc.o(Init)
    EnableIrqInterrupts                      0x07800afc   ARM Code       0  bl_StartUp_ttc.o(Init)
    DisableIrqInterrupts                     0x07800b10   ARM Code       0  bl_StartUp_ttc.o(Init)
    raise                                    0x07800b24   ARM Code       0  bl_StartUp_ttc.o(Init)
    ObmXsGetProcessorVersion                 0x07800b28   ARM Code       0  bl_StartUp_ttc.o(Init)
    ObmXsGetProcessorSubVersion              0x07800b30   ARM Code       0  bl_StartUp_ttc.o(Init)
    L1_dcache_cleaninvalid_all               0x07800b38   ARM Code       0  bl_StartUp_ttc.o(Init)
    L1_dcache_clean_flush_all                0x07800b80   ARM Code       0  bl_StartUp_ttc.o(Init)
    FlushCache946                            0x07800bac   ARM Code       0  bl_StartUp_ttc.o(Init)
    FlushIcache                              0x07800bbc   ARM Code       0  bl_StartUp_ttc.o(Init)
    FlushDcache                              0x07800bd0   ARM Code       0  bl_StartUp_ttc.o(Init)
    CleanDcache                              0x07800bdc   ARM Code       0  bl_StartUp_ttc.o(Init)
    asm_sync_barrier                         0x07800be8   ARM Code       0  bl_StartUp_ttc.o(Init)
    IniRWROMBase                             0x07800bfc   Data           4  bl_StartUp_ttc.o(Init)
    IniRWRAMBase                             0x07800c04   Data           4  bl_StartUp_ttc.o(Init)
    IniRWRAMLimit                            0x07800c08   Data           4  bl_StartUp_ttc.o(Init)
    IniZIRAMBase                             0x07800c0c   Data           4  bl_StartUp_ttc.o(Init)
    IniZIRAMLength                           0x07800c10   Data           4  bl_StartUp_ttc.o(Init)
    SetupInterrupts                          0x07800c30   ARM Code       0  platform_StartUp.o(Init)
    LoadPlatformConfig                       0x07800c54   ARM Code       0  platform_StartUp.o(Init)
    OBM_asm_Arm9ReadCP15_DS                  0x07800cc4   ARM Code       0  mpu.o(MPU)
    OBM_asm_Arm9WriteCP15_DS                 0x07800eac   ARM Code       0  mpu.o(MPU)
    CPUCleanDCacheLine                       0x07801094   ARM Code       0  mpu.o(MPU)
    XsCleanDCacheLine                        0x07801094   ARM Code       0  mpu.o(MPU)
    asm_arm946e_CleanDCacheLine              0x07801094   ARM Code       0  mpu.o(MPU)
    CPUInvalidateDCacheLine                  0x078010b8   ARM Code       0  mpu.o(MPU)
    XsInvalidateDCacheLine                   0x078010b8   ARM Code       0  mpu.o(MPU)
    asm_arm946e_InvalidateDCacheLine         0x078010b8   ARM Code       0  mpu.o(MPU)
    CPUDrainBuffers                          0x078010d8   ARM Code       0  mpu.o(MPU)
    OBM_MPUCache_Disable                     0x078010e0   ARM Code       0  mpu.o(MPU)
    dcache_clean_invalidate_all              0x07801120   ARM Code       0  mpu.o(MPU)
    dcache_clean_all                         0x07801244   ARM Code       0  mpu.o(MPU)
    sctlr_get                                0x078012dc   ARM Code       0  mpu.o(MPU)
    sctlr_set                                0x078012e4   ARM Code       0  mpu.o(MPU)
    mpu_get_region_num                       0x078012f0   ARM Code       0  mpu.o(MPU)
    mpu_set_region_num                       0x078012f8   ARM Code       0  mpu.o(MPU)
    mpu_get_region_base_addr                 0x07801304   ARM Code       0  mpu.o(MPU)
    mpu_set_region_base_addr                 0x0780130c   ARM Code       0  mpu.o(MPU)
    mpu_get_region_size                      0x07801318   ARM Code       0  mpu.o(MPU)
    mpu_set_region_size                      0x07801328   ARM Code       0  mpu.o(MPU)
    mpu_get_region_size_enable               0x0780134c   ARM Code       0  mpu.o(MPU)
    mpu_get_region_access_ctrl               0x07801354   ARM Code       0  mpu.o(MPU)
    mpu_set_region_access_ctrl               0x0780135c   ARM Code       0  mpu.o(MPU)
    mpu_enable_region                        0x07801368   ARM Code       0  mpu.o(MPU)
    CORTEX_MPU_Region_Init                   0x07801384   ARM Code       0  mpu.o(MPU)
    CORTEX_MPU_Region_Init_XIP               0x078015cc   ARM Code       0  mpu.o(MPU)
    CORTEX_MPU_Region_Init_Noncache          0x07801824   ARM Code       0  mpu.o(MPU)
    getCPUMode                               0x07801a78   ARM Code      32  platform_arch.o(TEXT)
    ADCForPM803DeInit                        0x07801a98   ARM Code      24  adc_pm803.o(i.ADCForPM803DeInit)
    ADCForPM803Init                          0x07801abc   ARM Code      64  adc_pm803.o(i.ADCForPM803Init)
    AbortHandler                             0x07801b18   ARM Code      12  platform_interrupts.o(i.AbortHandler)
    AddMessageError                          0x07801b34   ARM Code     192  ProtocolManager.o(i.AddMessageError)
    Align_Ptr                                0x07801bf4   ARM Code      20  misc.o(i.Align_Ptr)
    Allocate_USB2_Device                     0x07801c08   ARM Code     136  usb2_memory.o(i.Allocate_USB2_Device)
    AndInstruction                           0x07801c94   ARM Code      32  RegInstructions.o(i.AndInstruction)
    And_SM_SM_Instruction                    0x07801cb4   ARM Code      68  RegInstructions.o(i.And_SM_SM_Instruction)
    And_SM_Val_Instruction                   0x07801cf8   ARM Code      44  RegInstructions.o(i.And_SM_Val_Instruction)
    BQ24259Read                              0x07801d24   ARM Code      28  bq24259.o(i.BQ24259Read)
    BQ24259Write                             0x07801d44   ARM Code      28  bq24259.o(i.BQ24259Write)
    BQ24259_chg_disable_pinmux_config        0x07801d64   ARM Code      52  bq24259.o(i.BQ24259_chg_disable_pinmux_config)
    BQ24259_chg_mode_pinmux_config           0x07801da0   ARM Code      52  bq24259.o(i.BQ24259_chg_mode_pinmux_config)
    BQ24259_chg_otg_pinmux_config            0x07801ddc   ARM Code      52  bq24259.o(i.BQ24259_chg_otg_pinmux_config)
    BQ24259_chg_status_pinmux_config         0x07801e18   ARM Code      44  bq24259.o(i.BQ24259_chg_status_pinmux_config)
    BatCharging_On                           0x07801e4c   ARM Code     112  st7735s.o(i.BatCharging_On)
    Battery_Charing_Display                  0x07801edc   ARM Code      76  st7735s.o(i.Battery_Charing_Display)
    BinarySearch                             0x07801f4c   ARM Code     304  FM.o(i.BinarySearch)
    BootLoaderMain                           0x0780207c   ARM Code     896  BootLoader.o(i.BootLoaderMain)
    BootModeMain                             0x078023fc   ARM Code     156  BootMode.o(i.BootModeMain)
    CacheCleanAndInvalidateMemory            0x078024b0   ARM Code     108  mpu.o(i.CacheCleanAndInvalidateMemory)
    CacheInvalidateMemory                    0x0780251c   ARM Code     108  mpu.o(i.CacheInvalidateMemory)
    CalcImageChecksum                        0x07802588   ARM Code      36  misc.o(i.CalcImageChecksum)
    Charger_Voltage_Set                      0x078025ac   ARM Code       4  bq24259.o(i.Charger_Voltage_Set)
    Charger_init                             0x078025b0   ARM Code     100  I2C.o(i.Charger_init)
    CheckAndConfigureDDR                     0x07802638   ARM Code     216  DDR_Cfg.o(i.CheckAndConfigureDDR)
    CheckAndFillStringIndexTable             0x07802730   ARM Code     156  usb_descriptors.o(i.CheckAndFillStringIndexTable)
    CheckDefaultClocks                       0x078027e8   ARM Code      92  PlatformConfig.o(i.CheckDefaultClocks)
    CheckMemoryReliability                   0x078028b8   ARM Code      60  DDR_Cfg.o(i.CheckMemoryReliability)
    CheckProtocolTimeOut                     0x078028f4   ARM Code      40  ProtocolManager.o(i.CheckProtocolTimeOut)
    CheckReserved                            0x0780291c   ARM Code      60  tim.o(i.CheckReserved)
    ClearFM                                  0x07802960   ARM Code     100  FM.o(i.ClearFM)
    ClearPortInterruptFlag                   0x078029c8   ARM Code      16  platform_interrupts.o(i.ClearPortInterruptFlag)
    Clear_DDR_Flag_Table                     0x078029dc   ARM Code      32  BootMode.o(i.Clear_DDR_Flag_Table)
    Clear_Screen                             0x07802a14   ARM Code      40  st7735s.o(i.Clear_Screen)
    ConfigBQ24259Charger                     0x07802a4c   ARM Code     172  bq24259.o(i.ConfigBQ24259Charger)
    ConfigCharger                            0x07802b10   ARM Code       4  charger.o(i.ConfigCharger)
    ConfigRegRestore                         0x07802b14   ARM Code      36  PlatformConfig.o(i.ConfigRegRestore)
    ConfigureDDRMemory                       0x07802b5c   ARM Code     324  DDR_Cfg.o(i.ConfigureDDRMemory)
    Configure_Flashes                        0x07802cb0   ARM Code     240  Flash.o(i.Configure_Flashes)
    CopyUploadDataIntoBuffer                 0x07802da0   ARM Code      92  ProtocolManager.o(i.CopyUploadDataIntoBuffer)
    CoreIsCa7                                0x07802e00   ARM Code      48  PlatformConfig.o(i.CoreIsCa7)
    CreateBBT_Legacy                         0x07802e84   ARM Code     168  FM.o(i.CreateBBT_Legacy)
    CreateUSBAPIhandle                       0x07802f30   ARM Code      80  usbapi.o(i.CreateUSBAPIhandle)
    DWC3_Complete_Int                        0x078030ac   ARM Code     292  usb2_main.o(i.DWC3_Complete_Int)
    DWC3_Controller_Setup                    0x078031d8   ARM Code     264  usb2_main.o(i.DWC3_Controller_Setup)
    DWC3_EP0_INT                             0x078034b4   ARM Code      44  usb2_main.o(i.DWC3_EP0_INT)
    DWC3_EP0_OUT_START                       0x078034e0   ARM Code      36  usb2_main.o(i.DWC3_EP0_OUT_START)
    DWC3_EP0_STALL_AND_RESTART               0x07803504   ARM Code      72  usb2_main.o(i.DWC3_EP0_STALL_AND_RESTART)
    DWC3_GET_CONN_SPEED                      0x07803860   ARM Code      36  usb2_main.o(i.DWC3_GET_CONN_SPEED)
    DWC3_SEND_EP_CMD                         0x0780390c   ARM Code     456  usb2_main.o(i.DWC3_SEND_EP_CMD)
    DWC3_SET_LINK_STATE                      0x07803ae0   ARM Code     108  usb2_main.o(i.DWC3_SET_LINK_STATE)
    DecompressRegion                         0x07803b5c   ARM Code     172  BootMode.o(i.DecompressRegion)
    Delay                                    0x07803cc8   ARM Code      56  timer.o(i.Delay)
    DetermineModeAndDownload                 0x07803d00   ARM Code     384  DownloadMode.o(i.DetermineModeAndDownload)
    DetermineOperatingMode                   0x07803eac   ARM Code     432  BootLoader.o(i.DetermineOperatingMode)
    DisableBQ24259Charger                    0x0780411c   ARM Code      52  bq24259.o(i.DisableBQ24259Charger)
    DisableCharger                           0x07804154   ARM Code      44  charger.o(i.DisableCharger)
    DisableInt                               0x07804194   ARM Code      16  platform_interrupts.o(i.DisableInt)
    DisablePeripheralIRQInterrupt            0x078041a8   ARM Code      16  platform_interrupts.o(i.DisablePeripheralIRQInterrupt)
    DisplayBatstate                          0x078041b8   ARM Code      68  st7735s.o(i.DisplayBatstate)
    DivideTwoNumbers                         0x07804214   ARM Code      84  misc.o(i.DivideTwoNumbers)
    DownloadModeMain                         0x07804268   ARM Code     264  DownloadMode.o(i.DownloadModeMain)
    DownloadTIMImages                        0x078043cc   ARM Code    1188  TIMDownload.o(i.DownloadTIMImages)
    EnableBQ24259Charger                     0x07804870   ARM Code      52  bq24259.o(i.EnableBQ24259Charger)
    EnableCharger                            0x078048a8   ARM Code      72  charger.o(i.EnableCharger)
    EnableInt                                0x07804908   ARM Code      52  platform_interrupts.o(i.EnableInt)
    EnablePeripheralIRQInterrupt             0x07804940   ARM Code      16  platform_interrupts.o(i.EnablePeripheralIRQInterrupt)
    Enable_SMPL                              0x07804950   ARM Code     112  I2C.o(i.Enable_SMPL)
    EraseAllFlash                            0x078049e4   ARM Code      16  Flash.o(i.EraseAllFlash)
    EraseFlash                               0x078049f4   ARM Code      80  Flash.o(i.EraseFlash)
    External_Power_Display                   0x07804a44   ARM Code      24  st7735s.o(i.External_Power_Display)
    FatalError                               0x07804a60   ARM Code      72  BootLoader.o(i.FatalError)
    FillRam_IO                               0x07804abc   ARM Code     148  st7735s.o(i.FillRam_IO)
    FinalizeFM                               0x07804b54   ARM Code      84  FM.o(i.FinalizeFM)
    FinalizeSetup                            0x07804ba8   ARM Code      40  BootLoader.o(i.FinalizeSetup)
    Finalize_Flashes                         0x07804bd0   ARM Code      92  Flash.o(i.Finalize_Flashes)
    FindBBT_Legacy                           0x07804c2c   ARM Code     600  FM.o(i.FindBBT_Legacy)
    FindFirstPackageTypeInReserved           0x07804e8c   ARM Code     168  tim.o(i.FindFirstPackageTypeInReserved)
    FindImageInTIM                           0x07804f40   ARM Code      80  tim.o(i.FindImageInTIM)
    FindMyConsumerArray                      0x07804f90   ARM Code     152  tim.o(i.FindMyConsumerArray)
    FindNextPackageTypeInReserved            0x0780502c   ARM Code     180  tim.o(i.FindNextPackageTypeInReserved)
    FindPackageInReserved                    0x078050ec   ARM Code     124  tim.o(i.FindPackageInReserved)
    FindVendorRequestInTIM                   0x0780516c   ARM Code     124  usb_descriptors.o(i.FindVendorRequestInTIM)
    FiqHandler                               0x078051f0   ARM Code       4  platform_interrupts.o(i.FiqHandler)
    FuseOverwriteForDownload                 0x078051f4   ARM Code       4  PlatformConfig.o(i.FuseOverwriteForDownload)
    GetBadBlockNum                           0x078051f8   ARM Code      16  FM.o(i.GetBadBlockNum)
    GetBatInstantVolt                        0x07805208   ARM Code     132  I2C.o(i.GetBatInstantVolt)
    GetBatteryPercent                        0x078052b0   ARM Code      28  I2C.o(i.GetBatteryPercent)
    GetBlockSize                             0x078052cc   ARM Code      16  Flash.o(i.GetBlockSize)
    GetCompressedType                        0x078052dc   ARM Code      84  BootMode.o(i.GetCompressedType)
    GetDDRSize                               0x07805384   ARM Code     184  DDR_Cfg.o(i.GetDDRSize)
    GetFMProperties                          0x07805474   ARM Code       8  FM.o(i.GetFMProperties)
    GetFlashProperties                       0x07805480   ARM Code      16  Flash.o(i.GetFlashProperties)
    GetFlashType                             0x07805494   ARM Code      16  Flash.o(i.GetFlashType)
    GetImageReadBackCrcBuffer                0x078054a8   ARM Code       8  TIMDownload.o(i.GetImageReadBackCrcBuffer)
    GetInitRoutine                           0x078054b0   ARM Code     132  Flash.o(i.GetInitRoutine)
    GetIrqStatus                             0x07805538   ARM Code      28  platform_interrupts.o(i.GetIrqStatus)
    GetOSCR0                                 0x07805558   ARM Code      32  timer.o(i.GetOSCR0)
    GetPageSize                              0x0780557c   ARM Code      16  Flash.o(i.GetPageSize)
    GetPartitionOffset                       0x0780558c   ARM Code      40  FM.o(i.GetPartitionOffset)
    GetSMPtr                                 0x078055b4   ARM Code      24  RegInstructions.o(i.GetSMPtr)
    GetTimPointer                            0x078055d0   ARM Code       8  tim.o(i.GetTimPointer)
    GetUSBAPIhandle_BootNum                  0x078055dc   ARM Code      56  usbapi.o(i.GetUSBAPIhandle_BootNum)
    GetUSBAPIhandle_InterruptNum             0x07805618   ARM Code      56  usbapi.o(i.GetUSBAPIhandle_InterruptNum)
    GetUSBIDFuseBits                         0x07805654   ARM Code       8  PlatformConfig.o(i.GetUSBIDFuseBits)
    GetUploadCommand                         0x0780565c   ARM Code      68  ProtocolManager.o(i.GetUploadCommand)
    GetUseSpareArea                          0x078056a0   ARM Code      20  Flash.o(i.GetUseSpareArea)
    Get_DC_Properties                        0x078056b4   ARM Code      64  usb2_memory.o(i.Get_DC_Properties)
    Get_Dispaly_Percent                      0x078056f8   ARM Code      84  st7735s.o(i.Get_Dispaly_Percent)
    Get_TR069_Firmware                       0x07805770   ARM Code     712  tr069.o(i.Get_TR069_Firmware)
    GuilinBaseRead                           0x07805a38   ARM Code      16  guilin.o(i.GuilinBaseRead)
    GuilinBaseWrite                          0x07805a4c   ARM Code      16  guilin.o(i.GuilinBaseWrite)
    GuilinChargerInit                        0x07805a60   ARM Code     100  guilin.o(i.GuilinChargerInit)
    GuilinCheckBatteryConnect                0x07805ad8   ARM Code      84  guilin.o(i.GuilinCheckBatteryConnect)
    GuilinCheckBootONKey                     0x07805b80   ARM Code     124  guilin.o(i.GuilinCheckBootONKey)
    GuilinCheckUSBConnect                    0x07805c08   ARM Code      52  guilin.o(i.GuilinCheckUSBConnect)
    GuilinCheckWakeup                        0x07805c68   ARM Code     228  guilin.o(i.GuilinCheckWakeup)
    GuilinClkInit                            0x07805dc4   ARM Code     176  guilin.o(i.GuilinClkInit)
    GuilinEnableSMPL                         0x07805e88   ARM Code       8  guilin.o(i.GuilinEnableSMPL)
    GuilinGetBatInstantVolt                  0x07805e90   ARM Code     172  guilin.o(i.GuilinGetBatInstantVolt)
    GuilinGpadcRead                          0x07805f44   ARM Code      16  guilin.o(i.GuilinGpadcRead)
    GuilinGpadcWrite                         0x07805f58   ARM Code      16  guilin.o(i.GuilinGpadcWrite)
    GuilinLiteBaseRead                       0x07805f6c   ARM Code      28  guilin_lite.o(i.GuilinLiteBaseRead)
    GuilinLiteBaseWrite                      0x07805f8c   ARM Code      16  guilin_lite.o(i.GuilinLiteBaseWrite)
    GuilinLiteChargerInit                    0x07805fa0   ARM Code       8  guilin_lite.o(i.GuilinLiteChargerInit)
    GuilinLiteCheckBatteryConnect            0x07805fc0   ARM Code      60  guilin_lite.o(i.GuilinLiteCheckBatteryConnect)
    GuilinLiteCheckBootONKey                 0x07806030   ARM Code     124  guilin_lite.o(i.GuilinLiteCheckBootONKey)
    GuilinLiteCheckUSBConnect                0x078060b8   ARM Code      52  guilin_lite.o(i.GuilinLiteCheckUSBConnect)
    GuilinLiteCheckWakeup                    0x07806118   ARM Code     228  guilin_lite.o(i.GuilinLiteCheckWakeup)
    GuilinLiteClkInit                        0x07806274   ARM Code       4  guilin_lite.o(i.GuilinLiteClkInit)
    GuilinLiteDisableWDT                     0x07806278   ARM Code      28  guilin_lite.o(i.GuilinLiteDisableWDT)
    GuilinLiteEnableSMPL                     0x07806294   ARM Code       8  guilin_lite.o(i.GuilinLiteEnableSMPL)
    GuilinLiteGetBatInstantVolt              0x0780629c   ARM Code     156  guilin_lite.o(i.GuilinLiteGetBatInstantVolt)
    GuilinLitePowerRead                      0x07806340   ARM Code      28  guilin_lite.o(i.GuilinLitePowerRead)
    GuilinLitePowerWrite                     0x07806360   ARM Code      16  guilin_lite.o(i.GuilinLitePowerWrite)
    GuilinLitePoweroff                       0x07806374   ARM Code      52  guilin_lite.o(i.GuilinLitePoweroff)
    GuilinLiteReadBatVolt                    0x078063ec   ARM Code      68  guilin_lite.o(i.GuilinLiteReadBatVolt)
    GuilinLiteResetReg                       0x07806430   ARM Code     164  guilin_lite.o(i.GuilinLiteResetReg)
    GuilinLiteSetMainVoltage                 0x07806568   ARM Code      44  guilin_lite.o(i.GuilinLiteSetMainVoltage)
    GuilinLite_Aditional_Workaround          0x078065bc   ARM Code      68  guilin_lite.o(i.GuilinLite_Aditional_Workaround)
    GuilinLite_VBUCK1_CFG                    0x07806600   ARM Code      12  guilin_lite.o(i.GuilinLite_VBUCK1_CFG)
    GuilinLite_VBUCK_Set_VOUT                0x0780660c   ARM Code      96  guilin_lite.o(i.GuilinLite_VBUCK_Set_VOUT)
    GuilinPowerRead                          0x0780668c   ARM Code      16  guilin.o(i.GuilinPowerRead)
    GuilinPowerWrite                         0x078066a0   ARM Code      16  guilin.o(i.GuilinPowerWrite)
    GuilinPoweroff                           0x078066b4   ARM Code      52  guilin.o(i.GuilinPoweroff)
    GuilinReadBatVolt                        0x0780672c   ARM Code      64  guilin.o(i.GuilinReadBatVolt)
    GuilinResetReg                           0x0780676c   ARM Code     176  guilin.o(i.GuilinResetReg)
    GuilinSetMainVoltage                     0x078068ac   ARM Code      96  guilin.o(i.GuilinSetMainVoltage)
    Guilin_Aditional_Workaround              0x07806958   ARM Code      28  guilin.o(i.Guilin_Aditional_Workaround)
    HandleDataCmd                            0x07806974   ARM Code      40  ProtocolManager.o(i.HandleDataCmd)
    HandleDataHeaderCmd                      0x0780699c   ARM Code     192  ProtocolManager.o(i.HandleDataHeaderCmd)
    HandleDisconnect                         0x07806a5c   ARM Code     172  ProtocolManager.o(i.HandleDisconnect)
    HandleDoneCmd                            0x07806b08   ARM Code      40  ProtocolManager.o(i.HandleDoneCmd)
    HandleGetBadBlock                        0x07806b30   ARM Code     140  ProtocolManager.o(i.HandleGetBadBlock)
    HandleGetCrcCmd                          0x07806bc0   ARM Code     180  ProtocolManager.o(i.HandleGetCrcCmd)
    HandleGetParametersCmd                   0x07806c78   ARM Code     128  ProtocolManager.o(i.HandleGetParametersCmd)
    HandleGetVersionCmd                      0x07806cf8   ARM Code      52  ProtocolManager.o(i.HandleGetVersionCmd)
    HandleMessageCmd                         0x07806d30   ARM Code     232  ProtocolManager.o(i.HandleMessageCmd)
    HandleProtocolVersionCmd                 0x07806e1c   ARM Code     132  ProtocolManager.o(i.HandleProtocolVersionCmd)
    HandleRequest                            0x07806ea0   ARM Code     136  ProtocolManager.o(i.HandleRequest)
    HandleSelectImageCmd                     0x07806f28   ARM Code      40  ProtocolManager.o(i.HandleSelectImageCmd)
    HandleUploadDataCmd                      0x07806f50   ARM Code     224  ProtocolManager.o(i.HandleUploadDataCmd)
    HandleUploadDataHeaderCmd                0x07807034   ARM Code     260  ProtocolManager.o(i.HandleUploadDataHeaderCmd)
    HandleUploadFlow                         0x07807170   ARM Code     128  ProtocolManager.o(i.HandleUploadFlow)
    HandleVerifyImageCmd                     0x078071f0   ARM Code      52  ProtocolManager.o(i.HandleVerifyImageCmd)
    I2COLED_DisplayOff                       0x07807224   ARM Code       4  oled.o(i.I2COLED_DisplayOff)
    I2COLED_Init                             0x07807228   ARM Code       4  oled.o(i.I2COLED_Init)
    I2cInit                                  0x0780722c   ARM Code     100  I2C.o(i.I2cInit)
    INT_init                                 0x078072e8   ARM Code     100  platform_interrupts.o(i.INT_init)
    IRQ_Glb_Ena                              0x07807350   ARM Code      48  platform_interrupts.o(i.IRQ_Glb_Ena)
    ImageDownloadWaiting                     0x07807394   ARM Code      48  ProtocolManager.o(i.ImageDownloadWaiting)
    InitDefaultPort                          0x078073c4   ARM Code       4  tim.o(i.InitDefaultPort)
    InitPort                                 0x078073c8   ARM Code       4  ProtocolManager.o(i.InitPort)
    InitProtocol                             0x078073cc   ARM Code      80  ProtocolManager.o(i.InitProtocol)
    InitSODTimer                             0x0780741c   ARM Code      56  timer.o(i.InitSODTimer)
    InitializeFM                             0x0780745c   ARM Code      56  FM.o(i.InitializeFM)
    InitializeKeypad                         0x07807498   ARM Code      64  keypad.o(i.InitializeKeypad)
    InitializeQSPIDevice                     0x078074e0   ARM Code     232  spi_nor.o(i.InitializeQSPIDevice)
    InitializeUSB2Memory                     0x078075f0   ARM Code     172  usb2_memory.o(i.InitializeUSB2Memory)
    IrqHandler                               0x078076b8   ARM Code      80  platform_interrupts.o(i.IrqHandler)
    LZMA_Decompress                          0x07807708   ARM Code     776  LzmaDecode.o(i.LZMA_Decompress)
    LoadAllImages                            0x07807a2c   ARM Code    1712  BootMode.o(i.LoadAllImages)
    LoadTim                                  0x078080f4   ARM Code     332  tim.o(i.LoadTim)
    Load_SM_Addr_Instruction                 0x07808248   ARM Code      68  RegInstructions.o(i.Load_SM_Addr_Instruction)
    Load_SM_Val_Instruction                  0x0780828c   ARM Code      36  RegInstructions.o(i.Load_SM_Val_Instruction)
    Lshift_SM_Val_Instruction                0x078082b0   ARM Code      44  RegInstructions.o(i.Lshift_SM_Val_Instruction)
    LzmaDecode                               0x078082dc   ARM Code     848  LzmaDecode.o(i.LzmaDecode)
    LzmaLenDecode                            0x0780862c   ARM Code     124  LzmaDecode.o(i.LzmaLenDecode)
    LzmaLiteralDecode                        0x078086a8   ARM Code     144  LzmaDecode.o(i.LzmaLiteralDecode)
    LzmaLiteralDecodeMatch                   0x07808738   ARM Code     292  LzmaDecode.o(i.LzmaLiteralDecodeMatch)
    ModOfTwoNumbers                          0x07808a50   ARM Code      24  misc.o(i.ModOfTwoNumbers)
    Mov_SM_SM_Instruction                    0x07808a68   ARM Code      56  RegInstructions.o(i.Mov_SM_SM_Instruction)
    MrdBubbleSort                            0x07808aa0   ARM Code      84  adc_pm803.o(i.MrdBubbleSort)
    No_Battery_Display                       0x07808b2c   ARM Code      24  st7735s.o(i.No_Battery_Display)
    OBM_Flush                                0x07808b48   ARM Code      20  mpu.o(i.OBM_Flush)
    OLED_Cmd_0_Paras                         0x07808b5c   ARM Code      48  st7735s.o(i.OLED_Cmd_0_Paras)
    OLED_Cmd_Gamma_Corr                      0x07808b90   ARM Code     104  st7735s.o(i.OLED_Cmd_Gamma_Corr)
    OLED_Color_Set                           0x07808bfc   ARM Code      24  st7735s.o(i.OLED_Color_Set)
    OLED_DisplayHeadPicture                  0x07808c18   ARM Code     172  st7735s.o(i.OLED_DisplayHeadPicture)
    OLED_EraseRectArea                       0x07808cc8   ARM Code      72  st7735s.o(i.OLED_EraseRectArea)
    OLED_Pin_Configure                       0x07808d10   ARM Code     184  st7735s.o(i.OLED_Pin_Configure)
    ONKEY_Bootup                             0x07808e08   ARM Code       8  st7735s.o(i.ONKEY_Bootup)
    OSCR0IntervalInMicro                     0x07808e10   ARM Code      12  timer.o(i.OSCR0IntervalInMicro)
    OSCR0IntervalInMilli                     0x07808e1c   ARM Code      12  timer.o(i.OSCR0IntervalInMilli)
    OSCR0IntervalInSec                       0x07808e28   ARM Code      12  timer.o(i.OSCR0IntervalInSec)
    OrInstruction                            0x07808e38   ARM Code      32  RegInstructions.o(i.OrInstruction)
    Or_SM_SM_Instruction                     0x07808e58   ARM Code      68  RegInstructions.o(i.Or_SM_SM_Instruction)
    Or_SM_Val_Instruction                    0x07808e9c   ARM Code      44  RegInstructions.o(i.Or_SM_Val_Instruction)
    PMIC_Init_ID                             0x07808ec8   ARM Code     124  PlatformConfig.o(i.PMIC_Init_ID)
    PM_Handler                               0x07808fcc   ARM Code     500  ProtocolManager.o(i.PM_Handler)
    PM_ISR                                   0x078091c0   ARM Code     244  ProtocolManager.o(i.PM_ISR)
    PM_ReceiveImage                          0x078092b8   ARM Code     224  ProtocolManager.o(i.PM_ReceiveImage)
    PM_VerifyPreamble                        0x07809398   ARM Code      32  ProtocolManager.o(i.PM_VerifyPreamble)
    PP_Switch                                0x078093bc   ARM Code     256  FreqChange.o(i.PP_Switch)
    ParseBR_ExtraState                       0x07809528   ARM Code      52  PlatformConfig.o(i.ParseBR_ExtraState)
    ParseTransferStruct                      0x07809574   ARM Code     180  BootLoader.o(i.ParseTransferStruct)
    PerformTIMBasedSetup                     0x0780964c   ARM Code      64  BootLoader.o(i.PerformTIMBasedSetup)
    PlatformAPUartIsEnable                   0x0780968c   ARM Code      24  PlatformConfig.o(i.PlatformAPUartIsEnable)
    PlatformChargeIsEnable                   0x078096a8   ARM Code      24  PlatformConfig.o(i.PlatformChargeIsEnable)
    PlatformChargerConfig                    0x078096c4   ARM Code      72  PlatformConfig.o(i.PlatformChargerConfig)
    PlatformCheckForceUSBEnumFlag            0x07809714   ARM Code      28  PlatformConfig.o(i.PlatformCheckForceUSBEnumFlag)
    PlatformCheckProductionMode              0x07809738   ARM Code      96  PlatformConfig.o(i.PlatformCheckProductionMode)
    PlatformClearForceUSBEnumFlag            0x078097c8   ARM Code      16  PlatformConfig.o(i.PlatformClearForceUSBEnumFlag)
    PlatformDDRCfgEnable                     0x078097dc   ARM Code      12  PlatformConfig.o(i.PlatformDDRCfgEnable)
    PlatformGetChipID                        0x078097ec   ARM Code      16  PlatformConfig.o(i.PlatformGetChipID)
    PlatformGetRevisionID                    0x07809800   ARM Code      92  PlatformConfig.o(i.PlatformGetRevisionID)
    PlatformInitFlash                        0x0780987c   ARM Code      80  ProtocolManager.o(i.PlatformInitFlash)
    PlatformIsLapwB0                         0x078098e4   ARM Code      40  PlatformConfig.o(i.PlatformIsLapwB0)
    PlatformKeypadConfig                     0x0780990c   ARM Code      32  PlatformConfig.o(i.PlatformKeypadConfig)
    PlatformOLEDConfig                       0x07809934   ARM Code      92  PlatformConfig.o(i.PlatformOLEDConfig)
    PlatformOledEnable                       0x07809998   ARM Code      24  PlatformConfig.o(i.PlatformOledEnable)
    PlatformPI2CConfig                       0x078099b4   ARM Code      84  PlatformConfig.o(i.PlatformPI2CConfig)
    PlatformPMICType                         0x07809a10   ARM Code      12  PlatformConfig.o(i.PlatformPMICType)
    PlatformPPEnable                         0x07809a20   ARM Code      12  PlatformConfig.o(i.PlatformPPEnable)
    PlatformPROJECTType                      0x07809a30   ARM Code      12  PlatformConfig.o(i.PlatformPROJECTType)
    PlatformPrepareOBMVersion                0x07809a40   ARM Code      16  PlatformConfig.o(i.PlatformPrepareOBMVersion)
    PlatformProtectBootBlocks                0x07809a58   ARM Code       4  PlatformConfig.o(i.PlatformProtectBootBlocks)
    PlatformSetDdrWtdResetFlag               0x07809a5c   ARM Code      16  PlatformConfig.o(i.PlatformSetDdrWtdResetFlag)
    PlatformSetForceUSBEnumFlag              0x07809a74   ARM Code      16  PlatformConfig.o(i.PlatformSetForceUSBEnumFlag)
    PlatformSetLTGLWGFlag                    0x07809a8c   ARM Code      12  PlatformConfig.o(i.PlatformSetLTGLWGFlag)
    PlatformSetPMICType                      0x07809a9c   ARM Code      12  PlatformConfig.o(i.PlatformSetPMICType)
    PlatformSetTR069Flag                     0x07809aac   ARM Code      16  PlatformConfig.o(i.PlatformSetTR069Flag)
    PlatformUARTConfig                       0x07809ac4   ARM Code      92  PlatformConfig.o(i.PlatformUARTConfig)
    Platform_PortEnable                      0x07809b38   ARM Code      28  PlatformConfig.o(i.Platform_PortEnable)
    Platform_USB2_ON_USB2_PHY_Init           0x07809b54   ARM Code      96  PlatformConfig.o(i.Platform_USB2_ON_USB2_PHY_Init)
    Platform_USB2_Shutdown                   0x07809be4   ARM Code      16  PlatformConfig.o(i.Platform_USB2_Shutdown)
    PrefetchHandler                          0x07809bf8   ARM Code       4  platform_interrupts.o(i.PrefetchHandler)
    ProcessDDROps                            0x07809bfc   ARM Code     176  DDR_Cfg.o(i.ProcessDDROps)
    ProcessInstructions                      0x07809cac   ARM Code     576  RegInstructions.o(i.ProcessInstructions)
    RangeDecoderBitDecode                    0x07809f0c   ARM Code     188  LzmaDecode.o(i.RangeDecoderBitDecode)
    RangeDecoderBitTreeDecode                0x07809fc8   ARM Code     164  LzmaDecode.o(i.RangeDecoderBitTreeDecode)
    RangeDecoderDecodeDirectBits             0x0780a06c   ARM Code     100  LzmaDecode.o(i.RangeDecoderDecodeDirectBits)
    RangeDecoderInit                         0x0780a0d0   ARM Code      92  LzmaDecode.o(i.RangeDecoderInit)
    RangeDecoderReadByte                     0x0780a130   ARM Code      36  LzmaDecode.o(i.RangeDecoderReadByte)
    RangeDecoderReverseBitTreeDecode         0x0780a154   ARM Code     172  LzmaDecode.o(i.RangeDecoderReverseBitTreeDecode)
    ReadBatVolt                              0x0780a200   ARM Code     116  I2C.o(i.ReadBatVolt)
    ReadBitField                             0x0780a298   ARM Code      28  DDR_Cfg.o(i.ReadBitField)
    ReadFlash                                0x0780a2b4   ARM Code      60  Flash.o(i.ReadFlash)
    ReadInstruction                          0x0780a2f0   ARM Code      40  RegInstructions.o(i.ReadInstruction)
    ReadKeypad                               0x0780a318   ARM Code      68  keypad.o(i.ReadKeypad)
    ReleasetrbChain                          0x0780a4bc   ARM Code      20  usb2_memory.o(i.ReleasetrbChain)
    ResetBBT                                 0x0780a4d0   ARM Code       8  Flash.o(i.ResetBBT)
    Reset_Reg                                0x0780a4d8   ARM Code     120  I2C.o(i.Reset_Reg)
    RestoreDefaultConfig                     0x0780a57c   ARM Code      72  PlatformConfig.o(i.RestoreDefaultConfig)
    ReturnPImgPtr                            0x0780a5dc   ARM Code      32  tim.o(i.ReturnPImgPtr)
    Rshift_SM_Val_Instruction                0x0780a5fc   ARM Code      44  RegInstructions.o(i.Rshift_SM_Val_Instruction)
    SD_TR069_Upgrade                         0x0780a628   ARM Code     428  tr069.o(i.SD_TR069_Upgrade)
    SPINOR_Disable4BytesMode                 0x0780a874   ARM Code       8  spi_nor.o(i.SPINOR_Disable4BytesMode)
    SPINOR_Wipe                              0x0780a880   ARM Code       8  spi_nor.o(i.SPINOR_Wipe)
    SPI_OLED_BacklightON                     0x0780a88c   ARM Code      48  st7735s.o(i.SPI_OLED_BacklightON)
    SPI_OLED_Reset_SSP                       0x0780a8c0   ARM Code      76  st7735s.o(i.SPI_OLED_Reset_SSP)
    ST7735S_I2COLED_Init                     0x0780a914   ARM Code     688  st7735s.o(i.ST7735S_I2COLED_Init)
    ST7735S_SPILCD_DiplayOff                 0x0780abc4   ARM Code      32  st7735s.o(i.ST7735S_SPILCD_DiplayOff)
    ST7735S_SPIOLED_Init                     0x0780ac04   ARM Code      96  st7735s.o(i.ST7735S_SPIOLED_Init)
    ScanBBT_Legacy                           0x0780ac9c   ARM Code      96  FM.o(i.ScanBBT_Legacy)
    SendAck                                  0x0780acfc   ARM Code     104  ProtocolManager.o(i.SendAck)
    SendError                                0x0780ad64   ARM Code     116  ProtocolManager.o(i.SendError)
    SendResponse                             0x0780add8   ARM Code      36  ProtocolManager.o(i.SendResponse)
    SetArea                                  0x0780adfc   ARM Code     228  st7735s.o(i.SetArea)
    SetBBTState                              0x0780aee4   ARM Code     104  FM.o(i.SetBBTState)
    SetTIMPointers                           0x0780af4c   ARM Code     140  tim.o(i.SetTIMPointers)
    SetUpUSBDescriptors                      0x0780afdc   ARM Code     492  usb_descriptors.o(i.SetUpUSBDescriptors)
    SetUseSpareArea                          0x0780b200   ARM Code      48  Flash.o(i.SetUseSpareArea)
    Set_Bitfield_Instruction                 0x0780b230   ARM Code      64  RegInstructions.o(i.Set_Bitfield_Instruction)
    SetupEnvironment                         0x0780b270   ARM Code     388  BootLoader.o(i.SetupEnvironment)
    ShutdownPort                             0x0780b49c   ARM Code      20  PlatformConfig.o(i.ShutdownPort)
    SoftwareUpgrade_Done                     0x0780b4b0   ARM Code      24  st7735s.o(i.SoftwareUpgrade_Done)
    Store_SM_Addr_Instruction                0x0780b4cc   ARM Code      68  RegInstructions.o(i.Store_SM_Addr_Instruction)
    SwiHandler                               0x0780b510   ARM Code       4  platform_interrupts.o(i.SwiHandler)
    System_poweroff                          0x0780b514   ARM Code     100  I2C.o(i.System_poweroff)
    TIMDownloadMain                          0x0780b59c   ARM Code     156  TIMDownload.o(i.TIMDownloadMain)
    Test_If_Not_Zero_And_Set                 0x0780b664   ARM Code     104  RegInstructions.o(i.Test_If_Not_Zero_And_Set)
    Test_If_Zero_And_Set                     0x0780b6cc   ARM Code     100  RegInstructions.o(i.Test_If_Zero_And_Set)
    Test_SM_If_Not_Zero_And_Set              0x0780b730   ARM Code     100  RegInstructions.o(i.Test_SM_If_Not_Zero_And_Set)
    Test_SM_If_Zero_And_Set                  0x0780b794   ARM Code      96  RegInstructions.o(i.Test_SM_If_Zero_And_Set)
    Tr069GetConfig                           0x0780b7f4   ARM Code     212  tr069.o(i.Tr069GetConfig)
    TransferFlashType                        0x0780b95c   ARM Code      20  Flash.o(i.TransferFlashType)
    USB2D_Endpoint0Transmit                  0x0780b978   ARM Code     208  usb2_main.o(i.USB2D_Endpoint0Transmit)
    USB2D_EndpointTransmit                   0x0780ba48   ARM Code     164  usb2_main.o(i.USB2D_EndpointTransmit)
    USB2D_Endpoint_Setup                     0x0780baec   ARM Code     116  usb2_main.o(i.USB2D_Endpoint_Setup)
    USB2D_EnumerationHandler                 0x0780bb60   ARM Code      88  usb2_enumeration.o(i.USB2D_EnumerationHandler)
    USB2D_GetDescriptor                      0x0780bbb8   ARM Code     288  usb2_enumeration.o(i.USB2D_GetDescriptor)
    USB2D_GetStatus                          0x0780bcec   ARM Code      72  usb2_enumeration.o(i.USB2D_GetStatus)
    USB2D_ISR                                0x0780bd38   ARM Code     160  usb2_main.o(i.USB2D_ISR)
    USB2D_Initialize                         0x0780bdd8   ARM Code     132  usb2_main.o(i.USB2D_Initialize)
    USB2D_PM_Call                            0x0780be5c   ARM Code      44  usb2_main.o(i.USB2D_PM_Call)
    USB2D_RecieveWrapper                     0x0780be88   ARM Code     132  usb2_main.o(i.USB2D_RecieveWrapper)
    USB2D_SendWrapper                        0x0780bf14   ARM Code      52  usb2_main.o(i.USB2D_SendWrapper)
    USB2D_SetAddress                         0x0780bf48   ARM Code      80  usb2_enumeration.o(i.USB2D_SetAddress)
    USB2D_SetConfig                          0x0780bf98   ARM Code     140  usb2_enumeration.o(i.USB2D_SetConfig)
    USB2D_VendorRequest                      0x0780c024   ARM Code     136  usb2_enumeration.o(i.USB2D_VendorRequest)
    USBAPI_ISR                               0x0780c0bc   ARM Code      36  usbapi.o(i.USBAPI_ISR)
    USBAPI_InitializeDevice                  0x0780c0e0   ARM Code     120  usbapi.o(i.USBAPI_InitializeDevice)
    UncompressUpdater                        0x0780c168   ARM Code     696  tr069.o(i.UncompressUpdater)
    UndefinedHandler                         0x0780c420   ARM Code      12  platform_interrupts.o(i.UndefinedHandler)
    UpdateBBT                                0x0780c440   ARM Code     476  FM.o(i.UpdateBBT)
    UpdateUSBDeviceConfigDesc                0x0780c640   ARM Code      32  usb_descriptors.o(i.UpdateUSBDeviceConfigDesc)
    Update_Battery_State                     0x0780c66c   ARM Code     100  st7735s.o(i.Update_Battery_State)
    Update_DDR_Flag_From_CP                  0x0780c6d8   ARM Code     100  BootMode.o(i.Update_DDR_Flag_From_CP)
    Update_DDR_Flag_To_CP                    0x0780c768   ARM Code      36  BootMode.o(i.Update_DDR_Flag_To_CP)
    UsticaBaseRead                           0x0780c7ac   ARM Code      28  ustica.o(i.UsticaBaseRead)
    UsticaBaseWrite                          0x0780c7cc   ARM Code      16  ustica.o(i.UsticaBaseWrite)
    UsticaChargerInit                        0x0780c7e0   ARM Code     116  ustica.o(i.UsticaChargerInit)
    UsticaCheckBatteryConnect                0x0780c854   ARM Code      52  ustica.o(i.UsticaCheckBatteryConnect)
    UsticaCheckBootONKey                     0x0780c8bc   ARM Code     124  ustica.o(i.UsticaCheckBootONKey)
    UsticaCheckUSBConnect                    0x0780c944   ARM Code      52  ustica.o(i.UsticaCheckUSBConnect)
    UsticaCheckWakeup                        0x0780c9a4   ARM Code     240  ustica.o(i.UsticaCheckWakeup)
    UsticaEnableSMPL                         0x0780cb04   ARM Code     176  ustica.o(i.UsticaEnableSMPL)
    UsticaGetBatInstantVolt                  0x0780cbdc   ARM Code     172  ustica.o(i.UsticaGetBatInstantVolt)
    UsticaGpadcRead                          0x0780cc90   ARM Code      28  ustica.o(i.UsticaGpadcRead)
    UsticaGpadcWrite                         0x0780ccb0   ARM Code      16  ustica.o(i.UsticaGpadcWrite)
    UsticaPowerRead                          0x0780ccc4   ARM Code      28  ustica.o(i.UsticaPowerRead)
    UsticaPowerWrite                         0x0780cce4   ARM Code      16  ustica.o(i.UsticaPowerWrite)
    UsticaPoweroff                           0x0780ccf8   ARM Code      52  ustica.o(i.UsticaPoweroff)
    UsticaReadBatVolt                        0x0780cd70   ARM Code      64  ustica.o(i.UsticaReadBatVolt)
    UsticaResetReg                           0x0780cdb0   ARM Code     204  ustica.o(i.UsticaResetReg)
    UsticaSetMainVoltage                     0x0780ced4   ARM Code      36  ustica.o(i.UsticaSetMainVoltage)
    ValidAddress                             0x0780cf0c   ARM Code       8  RegInstructions.o(i.ValidAddress)
    VerifyUploadParameters                   0x0780cf14   ARM Code      76  ProtocolManager.o(i.VerifyUploadParameters)
    Voltage_set_main                         0x0780cf60   ARM Code     100  I2C.o(i.Voltage_set_main)
    WaitForBitClearInstruction               0x0780cfe8   ARM Code      88  RegInstructions.o(i.WaitForBitClearInstruction)
    WaitForBitSetInstruction                 0x0780d040   ARM Code      88  RegInstructions.o(i.WaitForBitSetInstruction)
    WaitForOperationComplete                 0x0780d098   ARM Code     112  timer.o(i.WaitForOperationComplete)
    Wait_For_Bit_Pattern_Instruction         0x0780d108   ARM Code     100  RegInstructions.o(i.Wait_For_Bit_Pattern_Instruction)
    WriteFlash                               0x0780d16c   ARM Code      68  Flash.o(i.WriteFlash)
    WriteInstruction                         0x0780d1b0   ARM Code      24  RegInstructions.o(i.WriteInstruction)
    WriteRam_IO                              0x0780d1c8   ARM Code     160  st7735s.o(i.WriteRam_IO)
    XllpDmacMapDeviceToChannel               0x0780d26c   ARM Code      36  dma.o(i.XllpDmacMapDeviceToChannel)
    XllpDmacNoDescriptorFetch                0x0780d294   ARM Code      36  dma.o(i.XllpDmacNoDescriptorFetch)
    XllpDmacStartTransfer                    0x0780d2bc   ARM Code      36  dma.o(i.XllpDmacStartTransfer)
    XllpDmacStopTransfer                     0x0780d2e4   ARM Code      36  dma.o(i.XllpDmacStopTransfer)
    __aeabi_idiv0                            0x0780d30c   ARM Code       4  misc.o(i.__aeabi_idiv0)
    aclk_dfc_switch                          0x0780d3bc   ARM Code      84  FreqChange.o(i.aclk_dfc_switch)
    alignChannel                             0x0780d434   ARM Code      40  dma.o(i.alignChannel)
    battery_charge                           0x0780d594   ARM Code     284  I2C.o(i.battery_charge)
    battery_full                             0x0780d6d8   ARM Code      96  I2C.o(i.battery_full)
    battery_process_step1                    0x0780d784   ARM Code     536  I2C.o(i.battery_process_step1)
    battery_process_step2                    0x0780da48   ARM Code     392  I2C.o(i.battery_process_step2)
    battery_timer                            0x0780dc50   ARM Code     148  I2C.o(i.battery_timer)
    bubble                                   0x0780dd20   ARM Code      72  misc.o(i.bubble)
    check_BQ24259RegStatus                   0x0780dd70   ARM Code       4  bq24259.o(i.check_BQ24259RegStatus)
    check_BatteryConnect                     0x0780dd74   ARM Code     116  I2C.o(i.check_BatteryConnect)
    check_Battery_Status                     0x0780de0c   ARM Code     428  I2C.o(i.check_Battery_Status)
    check_BootONKey                          0x0780dfb8   ARM Code     148  I2C.o(i.check_BootONKey)
    check_ExternalPower                      0x0780e070   ARM Code     352  I2C.o(i.check_ExternalPower)
    check_USBConnect                         0x0780e1d0   ARM Code     116  I2C.o(i.check_USBConnect)
    check_battery                            0x0780e268   ARM Code      76  I2C.o(i.check_battery)
    check_if_DCS_mode                        0x0780e2ec   ARM Code      68  guilin.o(i.check_if_DCS_mode)
    check_ota_updater                        0x0780e358   ARM Code     104  tr069.o(i.check_ota_updater)
    check_wakeup                             0x0780e49c   ARM Code     132  I2C.o(i.check_wakeup)
    dcache_clean_invalidate_range            0x0780e5d0   ARM Code      20  qspi_host.o(i.dcache_clean_invalidate_range)
    dcache_invalidate_range                  0x0780e5e8   ARM Code      20  qspi_host.o(i.dcache_invalidate_range)
    dck_dfc_switch                           0x0780e600   ARM Code     128  FreqChange.o(i.dck_dfc_switch)
    decompress_cp_from_flash_to_ddr          0x0780e6a8   ARM Code     312  BootMode.o(i.decompress_cp_from_flash_to_ddr)
    decompress_dsp_from_flash_to_ddr         0x0780e8a0   ARM Code     208  BootMode.o(i.decompress_dsp_from_flash_to_ddr)
    do_wdt_reset                             0x0780e9e4   ARM Code      16  wdt.o(i.do_wdt_reset)
    enable_PLL                               0x0780e9f4   ARM Code      44  FreqChange.o(i.enable_PLL)
    firmware_upgrade                         0x0780ea38   ARM Code    1124  tr069.o(i.firmware_upgrade)
    free                                     0x0780ee9c   ARM Code       4  tinyalloc.o(i.free)
    getAdcValue                              0x0780eea0   ARM Code      28  adc_pm803.o(i.getAdcValue)
    getProtoBuff                             0x0780eee4   ARM Code      12  ProtocolManager.o(i.getProtoBuff)
    getProtocolCmd                           0x0780eef4   ARM Code      12  ProtocolManager.o(i.getProtocolCmd)
    getProtocolError                         0x0780ef04   ARM Code      12  ProtocolManager.o(i.getProtocolError)
    getProtocolISR                           0x0780ef14   ARM Code       8  ProtocolManager.o(i.getProtocolISR)
    getProtocolMsg                           0x0780ef20   ARM Code       8  ProtocolManager.o(i.getProtocolMsg)
    getProtocolRsp                           0x0780ef2c   ARM Code       8  ProtocolManager.o(i.getProtocolRsp)
    get_cp_exec_addr                         0x0780f0d8   ARM Code      16  BootMode.o(i.get_cp_exec_addr)
    get_rw_cpz_struct_addr                   0x0780f0ec   ARM Code      16  BootMode.o(i.get_rw_cpz_struct_addr)
    get_trb                                  0x0780f100   ARM Code      56  usb2_memory.o(i.get_trb)
    get_trb_ep0                              0x0780f13c   ARM Code      56  usb2_memory.o(i.get_trb_ep0)
    get_usb_speed_mode                       0x0780f178   ARM Code      24  usbapi.o(i.get_usb_speed_mode)
    guilinOpenST7735sPower                   0x0780f194   ARM Code     136  st7735s.o(i.guilinOpenST7735sPower)
    guilin_read_volt_meas_val                0x0780f238   ARM Code      40  guilin.o(i.guilin_read_volt_meas_val)
    loadNonDescriptor                        0x0780f2d0   ARM Code      52  dma.o(i.loadNonDescriptor)
    load_dsp_adc                             0x0780f308   ARM Code     232  adc_pm803.o(i.load_dsp_adc)
    loadtable_init                           0x0780f4ac   ARM Code      16  BootMode.o(i.loadtable_init)
    malloc                                   0x0780f534   ARM Code       4  tinyalloc.o(i.malloc)
    malloc_init                              0x0780f538   ARM Code       4  tinyalloc.o(i.malloc_init)
    memcmp                                   0x0780f53c   ARM Code      64  misc.o(i.memcmp)
    memset                                   0x0780f57c   ARM Code      24  misc.o(i.memset)
    mpu_debug                                0x0780f594   ARM Code      44  mpu.o(i.mpu_debug)
    obm_printf                               0x0780f5d4   ARM Code      32  print.o(i.obm_printf)
    print                                    0x0780f5f4   ARM Code     768  print.o(i.print)
    ql_boot_all_pin_test                     0x0780fb88   ARM Code     288  quec_boot_all_pin_test.o(i.ql_boot_all_pin_test)
    ql_boot_check_download_key               0x0780fd08   ARM Code     180  quec_boot_platform.o(i.ql_boot_check_download_key)
    ql_boot_check_pwrkey                     0x0780fdbc   ARM Code     292  quec_boot_platform.o(i.ql_boot_check_pwrkey)
    ql_boot_get_auto_dload_state             0x07810178   ARM Code     108  quec_boot_platform.o(i.ql_boot_get_auto_dload_state)
    ql_boot_get_download_mode                0x07810214   ARM Code      24  quec_boot_platform.o(i.ql_boot_get_download_mode)
    ql_boot_get_hot_powerup_state            0x07810230   ARM Code     116  quec_boot_platform.o(i.ql_boot_get_hot_powerup_state)
    ql_boot_gpio_get                         0x078102f0   ARM Code      48  quec_boot_all_pin_test.o(i.ql_boot_gpio_get)
    ql_boot_gpio_set                         0x07810320   ARM Code      44  quec_boot_all_pin_test.o(i.ql_boot_gpio_set)
    ql_boot_gpio_set_input                   0x0781034c   ARM Code      84  quec_boot_all_pin_test.o(i.ql_boot_gpio_set_input)
    ql_boot_gpio_set_output                  0x078103a0   ARM Code      84  quec_boot_all_pin_test.o(i.ql_boot_gpio_set_output)
    ql_boot_set_auto_download                0x07810584   ARM Code     148  quec_boot_platform.o(i.ql_boot_set_auto_download)
    ql_boot_set_gpio_fun                     0x07810640   ARM Code      52  quec_boot_all_pin_test.o(i.ql_boot_set_gpio_fun)
    ql_boot_set_hot_powerup_state            0x07810674   ARM Code     100  quec_boot_platform.o(i.ql_boot_set_hot_powerup_state)
    ql_boot_update_pmic_reg                  0x07810728   ARM Code     316  quec_boot_platform.o(i.ql_boot_update_pmic_reg)
    qspi_cmd_done_interrupt                  0x07810a30   ARM Code      28  qspi_host.o(i.qspi_cmd_done_interrupt)
    qspi_cmd_done_pio                        0x07810a4c   ARM Code      60  qspi_host.o(i.qspi_cmd_done_pio)
    qspi_enable_xip                          0x07810e9c   ARM Code     100  qspi_host.o(i.qspi_enable_xip)
    qspi_get_cfg                             0x07811100   ARM Code       8  qspi_host.o(i.qspi_get_cfg)
    qspi_host_init                           0x0781110c   ARM Code     320  qspi_host.o(i.qspi_host_init)
    qspi_init_ahb                            0x07811288   ARM Code     140  qspi_host.o(i.qspi_init_ahb)
    qspi_irq_handler                         0x07811388   ARM Code     668  qspi_host.o(i.qspi_irq_handler)
    qspi_preinit_lookup_tbl                  0x078117d4   ARM Code     132  qspi_host.o(i.qspi_preinit_lookup_tbl)
    qspi_set_func_clk                        0x07811a94   ARM Code     140  qspi_host.o(i.qspi_set_func_clk)
    qspi_start_cmd                           0x07811c64   ARM Code     724  qspi_host.o(i.qspi_start_cmd)
    qspi_start_dma_xfer                      0x07811f90   ARM Code     132  qspi_host.o(i.qspi_start_dma_xfer)
    readDmaStatusRegister                    0x07812444   ARM Code      16  dma.o(i.readDmaStatusRegister)
    resetProtoBuff                           0x07812498   ARM Code      16  ProtocolManager.o(i.resetProtoBuff)
    resetTimer                               0x078124b0   ARM Code      48  bq24259.o(i.resetTimer)
    serial_init                              0x078124e0   ARM Code     108  serial.o(i.serial_init)
    serial_outnum                            0x07812550   ARM Code      64  serial.o(i.serial_outnum)
    serial_outstr                            0x07812594   ARM Code      44  serial.o(i.serial_outstr)
    serial_poll                              0x078125c0   ARM Code      40  serial.o(i.serial_poll)
    serial_read_byte                         0x078125ec   ARM Code      36  serial.o(i.serial_read_byte)
    serial_write                             0x07812614   ARM Code      36  serial.o(i.serial_write)
    setProtoBuff                             0x0781263c   ARM Code      12  ProtocolManager.o(i.setProtoBuff)
    setProtocolCmd                           0x0781264c   ARM Code      12  ProtocolManager.o(i.setProtocolCmd)
    setProtocolError                         0x0781265c   ARM Code      12  ProtocolManager.o(i.setProtocolError)
    show_Downlaod                            0x0781266c   ARM Code     100  st7735s.o(i.show_Downlaod)
    show_battery_status                      0x078126e8   ARM Code     100  st7735s.o(i.show_battery_status)
    show_external_power                      0x0781276c   ARM Code     100  st7735s.o(i.show_external_power)
    show_logo                                0x078127f0   ARM Code      80  st7735s.o(i.show_logo)
    spi_nor_erase                            0x07812a8c   ARM Code     308  spi_nor.o(i.spi_nor_erase)
    spi_nor_init                             0x07812e80   ARM Code     468  spi_nor.o(i.spi_nor_init)
    strcmpl                                  0x07813b80   ARM Code      96  misc.o(i.strcmpl)
    strlen                                   0x07813be0   ARM Code      40  misc.o(i.strlen)
    ta_alloc                                 0x07813c08   ARM Code      52  tinyalloc.o(i.ta_alloc)
    ta_free                                  0x07813c60   ARM Code      88  tinyalloc.o(i.ta_free)
    ta_init                                  0x07813cbc   ARM Code      92  tinyalloc.o(i.ta_init)
    using_bsdiff_upgrade                     0x07813d1c   ARM Code      12  bspatch.o(i.using_bsdiff_upgrade)
    usticaOpenST7735sPower                   0x07813d2c   ARM Code      64  st7735s.o(i.usticaOpenST7735sPower)
    ustica_read_volt_meas_val                0x07813d70   ARM Code      36  ustica.o(i.ustica_read_volt_meas_val)
    wdt_test                                 0x07813e3c   ARM Code     100  wdt.o(i.wdt_test)
    __aeabi_idiv                             0x07813ee9   Thumb Code    12  aeabi_sdiv_div0_cr4.o(x$sdiv)
    Inline ARM to Thumb Veneer to __aeabi_uidiv 0x07813ef4   ARM Code       8  aeabi_sdiv_div0_cr4.o(x$udiv)
    __aeabi_uidiv                            0x07813efd   Thumb Code    12  aeabi_sdiv_div0_cr4.o(x$udiv)
    __aeabi_uidivmod                         0x07813f09   Thumb Code    18  aeabi_sdiv_div0_cr4.o(x$udivmod)
    digits                                   0x07813f59   Data          17  serial.o(.constdata)
    keypad_pins                              0x07813f6c   Data          24  PlatformConfig.o(.constdata)
    io_pins                                  0x07813f84   Data         204  PlatformConfig.o(.constdata)
    common_pins                              0x07814050   Data          36  PlatformConfig.o(.constdata)
    cs0_common_pins                          0x07814074   Data          48  PlatformConfig.o(.constdata)
    apuart_pins                              0x078140a4   Data          36  PlatformConfig.o(.constdata)
    cpuart_pins                              0x078140c8   Data          36  PlatformConfig.o(.constdata)
    oled_pins                                0x078140ec   Data          48  PlatformConfig.o(.constdata)
    charger_pins                             0x0781411c   Data          48  PlatformConfig.o(.constdata)
    pi2c_pins                                0x0781414c   Data         132  PlatformConfig.o(.constdata)
    BatteryState                             0x078142b5   Data        1536  oled_lib.o(.constdata)
    logo                                     0x078148b5   Data         256  oled_lib.o(.constdata)
    Battery_Status                           0x078149b5   Data         128  oled_lib.o(.constdata)
    External_Power_Status                    0x07814a35   Data          64  oled_lib.o(.constdata)
    download_Status                          0x07814a75   Data         256  oled_lib.o(.constdata)
    qspi_cfg_info                            0x07814b78   Data          12  qspi_host.o(.constdata)
    Image$$RO$$Limit                         0x07814f48   Number         0  anon$$obj.o ABSOLUTE
    Load$$ER_RW$$Base                        0x07814f48   Number         0  anon$$obj.o ABSOLUTE
    Image$$ER_RW$$Base                       0x07840000   Number         0  anon$$obj.o ABSOLUTE
    LTG_LWG_Version_Flag                     0x078405b4   Data           4  tr069.o(.data)
    FM_SPACE                                 0x078407b8   Data           4  FM.o(.data)
    pFM_SPACE                                0x078407bc   Data           4  FM.o(.data)
    FlashInitDone                            0x078407c4   Data           1  BootLoader.o(.data)
    BoardType                                0x078407c5   Data           1  BootLoader.o(.data)
    quec_bootup                              0x078407c6   Data           1  BootLoader.o(.data)
    nextImageSize                            0x078407c8   Data           4  BootLoader.o(.data)
    Jig_status                               0x078407cc   Data           4  BootLoader.o(.data)
    BoardStr                                 0x078407d0   Data           8  BootLoader.o(.data)
    power_up_flag                            0x078407d8   Data           4  BootMode.o(.data)
    MPUFlag                                  0x078407dc   Data           4  BootMode.o(.data)
    pLoadTable                               0x078407e0   Data           4  BootMode.o(.data)
    battery_connect                          0x078407e4   Data           1  DownloadMode.o(.data)
    external_power                           0x078407e5   Data           1  DownloadMode.o(.data)
    isDownload                               0x078407e8   Data           4  DownloadMode.o(.data)
    time_count_enable                        0x078407ec   Data           4  DownloadMode.o(.data)
    upload_nand_spare                        0x078407f0   Data           4  DownloadMode.o(.data)
    Reset_flag                               0x078407f4   Data           4  TIMDownload.o(.data)
    FirstCheck                               0x078407f8   Data           4  TIMDownload.o(.data)
    Flash_size                               0x078407fc   Data           4  TIMDownload.o(.data)
    pSkipAddress                             0x07840800   Data           4  TIMDownload.o(.data)
    All_FF_flag                              0x07840804   Data           4  TIMDownload.o(.data)
    pRead                                    0x07840808   Data           8  TIMDownload.o(.data)
    UartBaseAddress                          0x07840810   Data           4  serial.o(.data)
    lBR_Estate                               0x07840820   Data           4  PlatformConfig.o(.data)
    PlatformConfig                           0x07840824   Data          12  PlatformConfig.o(.data)
    upload_config_flash_flag                 0x07840834   Data           1  ProtocolManager.o(.data)
    upload_get_times_flag                    0x07840835   Data           1  ProtocolManager.o(.data)
    upload_times                             0x07840836   Data           1  ProtocolManager.o(.data)
    ResetUE                                  0x07840837   Data           1  ProtocolManager.o(.data)
    ResetDelay                               0x07840838   Data           1  ProtocolManager.o(.data)
    pRXbuff                                  0x0784083c   Data           4  ProtocolManager.o(.data)
    preambleString                           0x07840840   Data           4  ProtocolManager.o(.data)
    UsbStringDesc_SerialNumStr               0x07840848   Data           2  usb_descriptors.o(.data)
    UsbStringDesc_DefaultStr                 0x0784084a   Data           2  usb_descriptors.o(.data)
    UsbStringDesc_LangID                     0x0784084c   Data           4  usb_descriptors.o(.data)
    UsbStringDesc_ManufacturerStr            0x07840854   Data           8  usb_descriptors.o(.data)
    USB2DeviceDesc                           0x0784085c   Data          18  usb_descriptors.o(.data)
    USB1ConfigDesc                           0x0784086e   Data          32  usb_descriptors.o(.data)
    USB2ConfigDesc                           0x0784088e   Data          32  usb_descriptors.o(.data)
    UsbStringDesc_ProductStr                 0x078408ae   Data          10  usb_descriptors.o(.data)
    UsbStringDesc_InterfaceStr               0x078408b8   Data          18  usb_descriptors.o(.data)
    USB2DevQualDesc                          0x078408ca   Data          10  usb_descriptors.o(.data)
    usbstatus_value                          0x078408d4   Data           4  usb2_enumeration.o(.data)
    usbcmd_cur_cmd_result                    0x078408d8   Data           2  usb2_main.o(.data)
    usbep1_data_count                        0x078408da   Data           2  usb2_main.o(.data)
    usbcmd_setlink_count                     0x078408dc   Data           2  usb2_main.o(.data)
    usbcmd_setlink_fail_count                0x078408de   Data           2  usb2_main.o(.data)
    usbep0_nrdy_fail_count                   0x078408e0   Data           2  usb2_main.o(.data)
    usbep0_data_nrdy_count                   0x078408e2   Data           2  usb2_main.o(.data)
    usbcmd_logging_ptr                       0x078408e4   Data           4  usb2_main.o(.data)
    usbrecv_len_ptr                          0x078408e8   Data           4  usb2_main.o(.data)
    usbep0_trb_ptr                           0x078408ec   Data           4  usb2_main.o(.data)
    usbep1_trb_ptr                           0x078408f0   Data           4  usb2_main.o(.data)
    usbep0_nrdy_ptr                          0x078408f4   Data           4  usb2_main.o(.data)
    vbat_pm803_def                           0x07840914   Data          40  adc_pm803.o(.data)
    absolute_timer                           0x0784093c   Data           4  I2C.o(.data)
    resume_timer                             0x07840940   Data           4  I2C.o(.data)
    resume_flag                              0x07840944   Data           4  I2C.o(.data)
    ONKey_pressed                            0x07840948   Data           4  I2C.o(.data)
    charger_status                           0x0784094c   Data           4  charger.o(.data)
    TA_HEAP_START                            0x07840960   Data           4  tinyalloc.o(.data)
    TA_HEAP_LIMIT                            0x07840964   Data           4  tinyalloc.o(.data)
    st7735s_spilcd_unstable                  0x0784096c   Data           1  st7735s.o(.data)
    st7735s_battery_base_percent             0x0784096d   Data           1  st7735s.o(.data)
    st7735s_battery_display_stage            0x0784096e   Data           1  st7735s.o(.data)
    default_BG_COLOR                         0x07840970   Data           2  st7735s.o(.data)
    default_TEXT_COLOR                       0x07840972   Data           2  st7735s.o(.data)
    RAM_DataBuf                              0x07840974   Data           4  st7735s.o(.data)
    g_ql_all_pin_map_cfg_table               0x07840988   Data         462  quec_boot_all_pin_map.o(.data)
    Image$$ER_RW$$Limit                      0x07840b58   Number         0  anon$$obj.o ABSOLUTE
    Image$$ER_RW$$ZI$$Base                   0x07840b58   Number         0  anon$$obj.o ABSOLUTE
    ScratchMemoryArray                       0x07840cc0   Data          12  RegInstructions.o(.bss)
    TR069Info                                0x07840ccc   Data          12  tr069.o(.bss)
    FlashProp                                0x07840cd8   Data         152  Flash.o(.bss)
    FMProperties                             0x07840d70   Data          68  FM.o(.bss)
    receiveBuff                              0x07840db8   Data        8192  ProtocolManager.o(.bss)
    transmitBuff                             0x07842db8   Data        8192  ProtocolManager.o(.bss)
    USBDescriptors                           0x07845e88   Data          96  usb_descriptors.o(.bss)
    USBStringIndexTable                      0x07845ee8   Data          28  usb_descriptors.o(.bss)
    usbcmd_logging_array                     0x07845f04   Data         512  usb2_main.o(.bss)
    usbrecv_len_table                        0x07846104   Data          64  usb2_main.o(.bss)
    usbep0_trb_table                         0x07846144   Data         256  usb2_main.o(.bss)
    usbep1_trb_table                         0x07846244   Data         256  usb2_main.o(.bss)
    usbep0_nrdy_table                        0x07846344   Data          32  usb2_main.o(.bss)
    QueueHeadSpace                           0x0784637c   Data        1040  usb2_memory.o(.bss)
    trb_space                                0x0784678c   Data        1056  usb2_memory.o(.bss)
    trb_ep0_space                            0x07846bac   Data         160  usb2_memory.o(.bss)
    ep0_out_buf                              0x07846c4c   Data        1028  usb2_memory.o(.bss)
    event_buff_space                         0x07847050   Data        2064  usb2_memory.o(.bss)
    DCProps                                  0x07847860   Data        1112  usb2_memory.o(.bss)
    USBAPI_ARRAY                             0x07847cb8   Data          64  usbapi.o(.bss)



==============================================================================

Memory Map of the image

  Image entry point : Not specified.

  Load Region LOAD (Base: 0x07800000, Size: 0x00015aa0, Max: 0xffffffff, ABSOLUTE)

    Execution Region RO (Base: 0x07800000, Size: 0x00014f48, Max: 0xffffffff, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07800000   0x00000100   Data   RO          380    IMG_HEADER_INFO     version_block.o
    0x07800100   0x000000f0   Code   RO         5277    .text               c_2.l(strncmp.o)
    0x078001f0   0x00000088   Code   RO         5279    .text               c_2.l(rt_memcpy_v6.o)
    0x07800278   0x00000064   Code   RO         5281    .text               c_2.l(rt_memcpy_w.o)
    0x078002dc   0x0000004e   Code   RO         5283    .text               c_2.l(rt_memclr_w.o)
    0x0780032a   0x00000002   PAD
    0x0780032c   0x00000258   Code   RO          399    I2C                 bbu_PI2C.o
    0x07800584   0x00000240   Code   RO          401    I2C                 bbu_CI2C.o
    0x078007c4   0x0000001c   PAD
    0x078007e0   0x00000450   Code   RO          379  * Init                bl_StartUp_ttc.o
    0x07800c30   0x00000094   Code   RO          398    Init                platform_StartUp.o
    0x07800cc4   0x00000db4   Code   RO          378    MPU                 mpu.o
    0x07801a78   0x00000020   Code   RO          396  * TEXT                platform_arch.o
    0x07801a98   0x00000024   Code   RO         3285    i.ADCForPM803DeInit  adc_pm803.o
    0x07801abc   0x0000005c   Code   RO         3286    i.ADCForPM803Init   adc_pm803.o
    0x07801b18   0x0000001c   Code   RO         2279    i.AbortHandler      platform_interrupts.o
    0x07801b34   0x000000c0   Code   RO         2386    i.AddMessageError   ProtocolManager.o
    0x07801bf4   0x00000014   Code   RO          421    i.Align_Ptr         misc.o
    0x07801c08   0x0000008c   Code   RO         3003    i.Allocate_USB2_Device  usb2_memory.o
    0x07801c94   0x00000020   Code   RO          617    i.AndInstruction    RegInstructions.o
    0x07801cb4   0x00000044   Code   RO          618    i.And_SM_SM_Instruction  RegInstructions.o
    0x07801cf8   0x0000002c   Code   RO          619    i.And_SM_Val_Instruction  RegInstructions.o
    0x07801d24   0x00000020   Code   RO         4825    i.BQ24259Read       bq24259.o
    0x07801d44   0x00000020   Code   RO         4826    i.BQ24259Write      bq24259.o
    0x07801d64   0x0000003c   Code   RO         4827    i.BQ24259_chg_disable_pinmux_config  bq24259.o
    0x07801da0   0x0000003c   Code   RO         4828    i.BQ24259_chg_mode_pinmux_config  bq24259.o
    0x07801ddc   0x0000003c   Code   RO         4829    i.BQ24259_chg_otg_pinmux_config  bq24259.o
    0x07801e18   0x00000034   Code   RO         4830    i.BQ24259_chg_status_pinmux_config  bq24259.o
    0x07801e4c   0x00000090   Code   RO         4587    i.BatCharging_On    st7735s.o
    0x07801edc   0x00000070   Code   RO         4588    i.Battery_Charing_Display  st7735s.o
    0x07801f4c   0x00000130   Code   RO         1115    i.BinarySearch      FM.o
    0x0780207c   0x00000380   Code   RO         1405    i.BootLoaderMain    BootLoader.o
    0x078023fc   0x000000b4   Code   RO         1493    i.BootModeMain      BootMode.o
    0x078024b0   0x0000006c   Code   RO         1863    i.CacheCleanAndInvalidateMemory  mpu.o
    0x0780251c   0x0000006c   Code   RO         1865    i.CacheInvalidateMemory  mpu.o
    0x07802588   0x00000024   Code   RO          422    i.CalcImageChecksum  misc.o
    0x078025ac   0x00000004   Code   RO         4831    i.Charger_Voltage_Set  bq24259.o
    0x078025b0   0x00000088   Code   RO         3435    i.Charger_init      I2C.o
    0x07802638   0x000000f8   Code   RO         3126    i.CheckAndConfigureDDR  DDR_Cfg.o
    0x07802730   0x000000b8   Code   RO         2667    i.CheckAndFillStringIndexTable  usb_descriptors.o
    0x078027e8   0x00000068   Code   RO         1933    i.CheckDefaultClocks  PlatformConfig.o
    0x07802850   0x00000068   Code   RO         3287    i.CheckForceDownload  adc_pm803.o
    0x078028b8   0x0000003c   Code   RO         3127    i.CheckMemoryReliability  DDR_Cfg.o
    0x078028f4   0x00000028   Code   RO         2387    i.CheckProtocolTimeOut  ProtocolManager.o
    0x0780291c   0x00000044   Code   RO         3174    i.CheckReserved     tim.o
    0x07802960   0x00000068   Code   RO         1117    i.ClearFM           FM.o
    0x078029c8   0x00000014   Code   RO         2280    i.ClearPortInterruptFlag  platform_interrupts.o
    0x078029dc   0x00000038   Code   RO         1494    i.Clear_DDR_Flag_Table  BootMode.o
    0x07802a14   0x00000038   Code   RO         4589    i.Clear_Screen      st7735s.o
    0x07802a4c   0x000000c4   Code   RO         4832    i.ConfigBQ24259Charger  bq24259.o
    0x07802b10   0x00000004   Code   RO         3590    i.ConfigCharger     charger.o
    0x07802b14   0x00000024   Code   RO         1935    i.ConfigRegRestore  PlatformConfig.o
    0x07802b38   0x00000024   Code   RO         1938    i.ConfigRegWrite    PlatformConfig.o
    0x07802b5c   0x00000154   Code   RO         3128    i.ConfigureDDRMemory  DDR_Cfg.o
    0x07802cb0   0x000000f0   Code   RO          903    i.Configure_Flashes  Flash.o
    0x07802da0   0x00000060   Code   RO         2389    i.CopyUploadDataIntoBuffer  ProtocolManager.o
    0x07802e00   0x00000030   Code   RO         1940    i.CoreIsCa7         PlatformConfig.o
    0x07802e30   0x00000054   Code   RO          830    i.Crc_Check_16      tr069.o
    0x07802e84   0x000000ac   Code   RO         1118    i.CreateBBT_Legacy  FM.o
    0x07802f30   0x00000054   Code   RO         3059    i.CreateUSBAPIhandle  usbapi.o
    0x07802f84   0x000000a0   Code   RO         2778    i.DO_DWC3_GADGET_WAKEUP  usb2_main.o
    0x07803024   0x00000088   Code   RO         2779    i.DWC3_CONNDONE_INT  usb2_main.o
    0x078030ac   0x0000012c   Code   RO         2780    i.DWC3_Complete_Int  usb2_main.o
    0x078031d8   0x0000011c   Code   RO         2781    i.DWC3_Controller_Setup  usb2_main.o
    0x078032f4   0x00000064   Code   RO         2782    i.DWC3_DEV_INT      usb2_main.o
    0x07803358   0x00000028   Code   RO         2783    i.DWC3_DISCONNECT_INT  usb2_main.o
    0x07803380   0x0000006c   Code   RO         2784    i.DWC3_EP0_COMPLETE_DATA  usb2_main.o
    0x078033ec   0x00000044   Code   RO         2785    i.DWC3_EP0_COMPLETE_STATUS  usb2_main.o
    0x07803430   0x00000084   Code   RO         2786    i.DWC3_EP0_HANDLE_SETUP  usb2_main.o
    0x078034b4   0x0000002c   Code   RO         2787    i.DWC3_EP0_INT      usb2_main.o
    0x078034e0   0x00000024   Code   RO         2788    i.DWC3_EP0_OUT_START  usb2_main.o
    0x07803504   0x00000048   Code   RO         2789    i.DWC3_EP0_STALL_AND_RESTART  usb2_main.o
    0x0780354c   0x000000e0   Code   RO         2790    i.DWC3_EP0_XFERNOTREADY  usb2_main.o
    0x0780362c   0x000000f8   Code   RO         2791    i.DWC3_EP0_XFER_COMPLETE  usb2_main.o
    0x07803724   0x00000048   Code   RO         2792    i.DWC3_EP_INT       usb2_main.o
    0x0780376c   0x000000a0   Code   RO         2793    i.DWC3_EP_SET_CFG   usb2_main.o
    0x0780380c   0x0000002c   Code   RO         2794    i.DWC3_EP_SET_XFER_RESOURCE  usb2_main.o
    0x07803838   0x00000028   Code   RO         2795    i.DWC3_EP_START_CFG  usb2_main.o
    0x07803860   0x00000024   Code   RO         2796    i.DWC3_GET_CONN_SPEED  usb2_main.o
    0x07803884   0x0000000c   Code   RO         2797    i.DWC3_LINKSTS_CHANGE_INT  usb2_main.o
    0x07803890   0x0000001c   Code   RO         2798    i.DWC3_PROCESS_EVENT_ENTRY  usb2_main.o
    0x078038ac   0x0000000c   Code   RO         2713    i.DWC3_REG_READ     usb2_enumeration.o
    0x078038b8   0x0000000c   Code   RO         2799    i.DWC3_REG_READ     usb2_main.o
    0x078038c4   0x0000000c   Code   RO         2714    i.DWC3_REG_WRITE    usb2_enumeration.o
    0x078038d0   0x0000000c   Code   RO         2800    i.DWC3_REG_WRITE    usb2_main.o
    0x078038dc   0x00000030   Code   RO         2801    i.DWC3_RESET_INT    usb2_main.o
    0x0780390c   0x000001d4   Code   RO         2802    i.DWC3_SEND_EP_CMD  usb2_main.o
    0x07803ae0   0x0000006c   Code   RO         2803    i.DWC3_SET_LINK_STATE  usb2_main.o
    0x07803b4c   0x0000000c   Code   RO         2805    i.DWC3_SUSPEND_INT  usb2_main.o
    0x07803b58   0x00000004   Code   RO         2806    i.DWC3_WAKEUP_INT   usb2_main.o
    0x07803b5c   0x0000016c   Code   RO         1495    i.DecompressRegion  BootMode.o
    0x07803cc8   0x00000038   Code   RO          543    i.Delay             timer.o
    0x07803d00   0x000001ac   Code   RO         1592    i.DetermineModeAndDownload  DownloadMode.o
    0x07803eac   0x00000270   Code   RO         1406    i.DetermineOperatingMode  BootLoader.o
    0x0780411c   0x00000038   Code   RO         4833    i.DisableBQ24259Charger  bq24259.o
    0x07804154   0x00000040   Code   RO         3591    i.DisableCharger    charger.o
    0x07804194   0x00000014   Code   RO         2281    i.DisableInt        platform_interrupts.o
    0x078041a8   0x00000010   Code   RO         2282    i.DisablePeripheralIRQInterrupt  platform_interrupts.o
    0x078041b8   0x0000005c   Code   RO         4590    i.DisplayBatstate   st7735s.o
    0x07804214   0x00000054   Code   RO          427    i.DivideTwoNumbers  misc.o
    0x07804268   0x00000164   Code   RO         1593    i.DownloadModeMain  DownloadMode.o
    0x078043cc   0x000004a4   Code   RO         1614    i.DownloadTIMImages  TIMDownload.o
    0x07804870   0x00000038   Code   RO         4834    i.EnableBQ24259Charger  bq24259.o
    0x078048a8   0x00000060   Code   RO         3592    i.EnableCharger     charger.o
    0x07804908   0x00000038   Code   RO         2283    i.EnableInt         platform_interrupts.o
    0x07804940   0x00000010   Code   RO         2284    i.EnablePeripheralIRQInterrupt  platform_interrupts.o
    0x07804950   0x00000094   Code   RO         3436    i.Enable_SMPL       I2C.o
    0x078049e4   0x00000010   Code   RO          905    i.EraseAllFlash     Flash.o
    0x078049f4   0x00000050   Code   RO          906    i.EraseFlash        Flash.o
    0x07804a44   0x0000001c   Code   RO         4591    i.External_Power_Display  st7735s.o
    0x07804a60   0x0000005c   Code   RO         1407    i.FatalError        BootLoader.o
    0x07804abc   0x00000098   Code   RO         4592    i.FillRam_IO        st7735s.o
    0x07804b54   0x00000054   Code   RO         1120    i.FinalizeFM        FM.o
    0x07804ba8   0x00000028   Code   RO         1408    i.FinalizeSetup     BootLoader.o
    0x07804bd0   0x0000005c   Code   RO          907    i.Finalize_Flashes  Flash.o
    0x07804c2c   0x00000260   Code   RO         1121    i.FindBBT_Legacy    FM.o
    0x07804e8c   0x000000b4   Code   RO         3176    i.FindFirstPackageTypeInReserved  tim.o
    0x07804f40   0x00000050   Code   RO         3177    i.FindImageInTIM    tim.o
    0x07804f90   0x0000009c   Code   RO         3180    i.FindMyConsumerArray  tim.o
    0x0780502c   0x000000c0   Code   RO         3181    i.FindNextPackageTypeInReserved  tim.o
    0x078050ec   0x00000080   Code   RO         3182    i.FindPackageInReserved  tim.o
    0x0780516c   0x00000084   Code   RO         2668    i.FindVendorRequestInTIM  usb_descriptors.o
    0x078051f0   0x00000004   Code   RO         2285    i.FiqHandler        platform_interrupts.o
    0x078051f4   0x00000004   Code   RO         1941    i.FuseOverwriteForDownload  PlatformConfig.o
    0x078051f8   0x00000010   Code   RO         1122    i.GetBadBlockNum    FM.o
    0x07805208   0x000000a8   Code   RO         3437    i.GetBatInstantVolt  I2C.o
    0x078052b0   0x0000001c   Code   RO         3438    i.GetBatteryPercent  I2C.o
    0x078052cc   0x00000010   Code   RO          908    i.GetBlockSize      Flash.o
    0x078052dc   0x000000a8   Code   RO         1496    i.GetCompressedType  BootMode.o
    0x07805384   0x000000f0   Code   RO         3130    i.GetDDRSize        DDR_Cfg.o
    0x07805474   0x0000000c   Code   RO         1123    i.GetFMProperties   FM.o
    0x07805480   0x00000014   Code   RO          911    i.GetFlashProperties  Flash.o
    0x07805494   0x00000014   Code   RO          912    i.GetFlashType      Flash.o
    0x078054a8   0x00000008   Code   RO         1615    i.GetImageReadBackCrcBuffer  TIMDownload.o
    0x078054b0   0x00000088   Code   RO          913    i.GetInitRoutine    Flash.o
    0x07805538   0x00000020   Code   RO         2286    i.GetIrqStatus      platform_interrupts.o
    0x07805558   0x00000024   Code   RO          547    i.GetOSCR0          timer.o
    0x0780557c   0x00000010   Code   RO          914    i.GetPageSize       Flash.o
    0x0780558c   0x00000028   Code   RO         1124    i.GetPartitionOffset  FM.o
    0x078055b4   0x0000001c   Code   RO          620    i.GetSMPtr          RegInstructions.o
    0x078055d0   0x0000000c   Code   RO         3184    i.GetTimPointer     tim.o
    0x078055dc   0x0000003c   Code   RO         3060    i.GetUSBAPIhandle_BootNum  usbapi.o
    0x07805618   0x0000003c   Code   RO         3061    i.GetUSBAPIhandle_InterruptNum  usbapi.o
    0x07805654   0x00000008   Code   RO         1942    i.GetUSBIDFuseBits  PlatformConfig.o
    0x0780565c   0x00000044   Code   RO         2390    i.GetUploadCommand  ProtocolManager.o
    0x078056a0   0x00000014   Code   RO          917    i.GetUseSpareArea   Flash.o
    0x078056b4   0x00000044   Code   RO         3004    i.Get_DC_Properties  usb2_memory.o
    0x078056f8   0x00000058   Code   RO         4595    i.Get_Dispaly_Percent  st7735s.o
    0x07805750   0x00000020   Code   RO         3005    i.Get_New_DCProps   usb2_memory.o
    0x07805770   0x000002c8   Code   RO          831    i.Get_TR069_Firmware  tr069.o
    0x07805a38   0x00000014   Code   RO         4018    i.GuilinBaseRead    guilin.o
    0x07805a4c   0x00000014   Code   RO         4019    i.GuilinBaseWrite   guilin.o
    0x07805a60   0x00000078   Code   RO         4020    i.GuilinChargerInit  guilin.o
    0x07805ad8   0x000000a8   Code   RO         4021    i.GuilinCheckBatteryConnect  guilin.o
    0x07805b80   0x00000088   Code   RO         4022    i.GuilinCheckBootONKey  guilin.o
    0x07805c08   0x00000060   Code   RO         4023    i.GuilinCheckUSBConnect  guilin.o
    0x07805c68   0x0000015c   Code   RO         4024    i.GuilinCheckWakeup  guilin.o
    0x07805dc4   0x000000c4   Code   RO         4025    i.GuilinClkInit     guilin.o
    0x07805e88   0x00000008   Code   RO         4026    i.GuilinEnableSMPL  guilin.o
    0x07805e90   0x000000b4   Code   RO         4027    i.GuilinGetBatInstantVolt  guilin.o
    0x07805f44   0x00000014   Code   RO         4028    i.GuilinGpadcRead   guilin.o
    0x07805f58   0x00000014   Code   RO         4029    i.GuilinGpadcWrite  guilin.o
    0x07805f6c   0x00000020   Code   RO         4159    i.GuilinLiteBaseRead  guilin_lite.o
    0x07805f8c   0x00000014   Code   RO         4160    i.GuilinLiteBaseWrite  guilin_lite.o
    0x07805fa0   0x00000020   Code   RO         4161    i.GuilinLiteChargerInit  guilin_lite.o
    0x07805fc0   0x00000070   Code   RO         4162    i.GuilinLiteCheckBatteryConnect  guilin_lite.o
    0x07806030   0x00000088   Code   RO         4163    i.GuilinLiteCheckBootONKey  guilin_lite.o
    0x078060b8   0x00000060   Code   RO         4164    i.GuilinLiteCheckUSBConnect  guilin_lite.o
    0x07806118   0x0000015c   Code   RO         4165    i.GuilinLiteCheckWakeup  guilin_lite.o
    0x07806274   0x00000004   Code   RO         4166    i.GuilinLiteClkInit  guilin_lite.o
    0x07806278   0x0000001c   Code   RO         4167    i.GuilinLiteDisableWDT  guilin_lite.o
    0x07806294   0x00000008   Code   RO         4168    i.GuilinLiteEnableSMPL  guilin_lite.o
    0x0780629c   0x000000a4   Code   RO         4169    i.GuilinLiteGetBatInstantVolt  guilin_lite.o
    0x07806340   0x00000020   Code   RO         4172    i.GuilinLitePowerRead  guilin_lite.o
    0x07806360   0x00000014   Code   RO         4173    i.GuilinLitePowerWrite  guilin_lite.o
    0x07806374   0x00000078   Code   RO         4174    i.GuilinLitePoweroff  guilin_lite.o
    0x078063ec   0x00000044   Code   RO         4175    i.GuilinLiteReadBatVolt  guilin_lite.o
    0x07806430   0x00000138   Code   RO         4176    i.GuilinLiteResetReg  guilin_lite.o
    0x07806568   0x00000054   Code   RO         4177    i.GuilinLiteSetMainVoltage  guilin_lite.o
    0x078065bc   0x00000044   Code   RO         4178    i.GuilinLite_Aditional_Workaround  guilin_lite.o
    0x07806600   0x0000000c   Code   RO         4181    i.GuilinLite_VBUCK1_CFG  guilin_lite.o
    0x0780660c   0x00000080   Code   RO         4183    i.GuilinLite_VBUCK_Set_VOUT  guilin_lite.o
    0x0780668c   0x00000014   Code   RO         4031    i.GuilinPowerRead   guilin.o
    0x078066a0   0x00000014   Code   RO         4032    i.GuilinPowerWrite  guilin.o
    0x078066b4   0x00000078   Code   RO         4033    i.GuilinPoweroff    guilin.o
    0x0780672c   0x00000040   Code   RO         4034    i.GuilinReadBatVolt  guilin.o
    0x0780676c   0x00000140   Code   RO         4035    i.GuilinResetReg    guilin.o
    0x078068ac   0x000000ac   Code   RO         4036    i.GuilinSetMainVoltage  guilin.o
    0x07806958   0x0000001c   Code   RO         4037    i.Guilin_Aditional_Workaround  guilin.o
    0x07806974   0x00000028   Code   RO         2391    i.HandleDataCmd     ProtocolManager.o
    0x0780699c   0x000000c0   Code   RO         2392    i.HandleDataHeaderCmd  ProtocolManager.o
    0x07806a5c   0x000000ac   Code   RO         2393    i.HandleDisconnect  ProtocolManager.o
    0x07806b08   0x00000028   Code   RO         2394    i.HandleDoneCmd     ProtocolManager.o
    0x07806b30   0x00000090   Code   RO         2395    i.HandleGetBadBlock  ProtocolManager.o
    0x07806bc0   0x000000b8   Code   RO         2396    i.HandleGetCrcCmd   ProtocolManager.o
    0x07806c78   0x00000080   Code   RO         2397    i.HandleGetParametersCmd  ProtocolManager.o
    0x07806cf8   0x00000038   Code   RO         2398    i.HandleGetVersionCmd  ProtocolManager.o
    0x07806d30   0x000000ec   Code   RO         2399    i.HandleMessageCmd  ProtocolManager.o
    0x07806e1c   0x00000084   Code   RO         2400    i.HandleProtocolVersionCmd  ProtocolManager.o
    0x07806ea0   0x00000088   Code   RO         2401    i.HandleRequest     ProtocolManager.o
    0x07806f28   0x00000028   Code   RO         2402    i.HandleSelectImageCmd  ProtocolManager.o
    0x07806f50   0x000000e4   Code   RO         2403    i.HandleUploadDataCmd  ProtocolManager.o
    0x07807034   0x0000013c   Code   RO         2404    i.HandleUploadDataHeaderCmd  ProtocolManager.o
    0x07807170   0x00000080   Code   RO         2405    i.HandleUploadFlow  ProtocolManager.o
    0x078071f0   0x00000034   Code   RO         2406    i.HandleVerifyImageCmd  ProtocolManager.o
    0x07807224   0x00000004   Code   RO          403    i.I2COLED_DisplayOff  oled.o
    0x07807228   0x00000004   Code   RO          404    i.I2COLED_Init      oled.o
    0x0780722c   0x00000068   Code   RO         3440    i.I2cInit           I2C.o
    0x07807294   0x00000054   Code   RO         3288    i.INNERRTCClockOn   adc_pm803.o
    0x078072e8   0x00000068   Code   RO         2288    i.INT_init          platform_interrupts.o
    0x07807350   0x00000034   Code   RO         2289    i.IRQ_Glb_Ena       platform_interrupts.o
    0x07807384   0x00000010   Code   RO         2807    i.IS_PTRB_OK        usb2_main.o
    0x07807394   0x00000030   Code   RO         2407    i.ImageDownloadWaiting  ProtocolManager.o
    0x078073c4   0x00000004   Code   RO         3185    i.InitDefaultPort   tim.o
    0x078073c8   0x00000004   Code   RO         2408    i.InitPort          ProtocolManager.o
    0x078073cc   0x00000050   Code   RO         2409    i.InitProtocol      ProtocolManager.o
    0x0780741c   0x00000040   Code   RO          549    i.InitSODTimer      timer.o
    0x0780745c   0x0000003c   Code   RO         1126    i.InitializeFM      FM.o
    0x07807498   0x00000048   Code   RO          776    i.InitializeKeypad  keypad.o
    0x078074e0   0x00000110   Code   RO            1    i.InitializeQSPIDevice  spi_nor.o
    0x078075f0   0x000000c8   Code   RO         3006    i.InitializeUSB2Memory  usb2_memory.o
    0x078076b8   0x00000050   Code   RO         2290    i.IrqHandler        platform_interrupts.o
    0x07807708   0x00000324   Code   RO         4515    i.LZMA_Decompress   LzmaDecode.o
    0x07807a2c   0x000006c4   Code   RO         1497    i.LoadAllImages     BootMode.o
    0x078080f0   0x00000004   Code   RO         3289    i.LoadMsaBin        adc_pm803.o
    0x078080f4   0x00000154   Code   RO         3186    i.LoadTim           tim.o
    0x07808248   0x00000044   Code   RO          621    i.Load_SM_Addr_Instruction  RegInstructions.o
    0x0780828c   0x00000024   Code   RO          622    i.Load_SM_Val_Instruction  RegInstructions.o
    0x078082b0   0x0000002c   Code   RO          623    i.Lshift_SM_Val_Instruction  RegInstructions.o
    0x078082dc   0x00000350   Code   RO         4516    i.LzmaDecode        LzmaDecode.o
    0x0780862c   0x0000007c   Code   RO         4517    i.LzmaLenDecode     LzmaDecode.o
    0x078086a8   0x00000090   Code   RO         4518    i.LzmaLiteralDecode  LzmaDecode.o
    0x07808738   0x00000124   Code   RO         4519    i.LzmaLiteralDecodeMatch  LzmaDecode.o
    0x0780885c   0x00000108   Code   RO         3290    i.MRDAdcValueGet    adc_pm803.o
    0x07808964   0x00000048   Code   RO         3291    i.MSAHold           adc_pm803.o
    0x078089ac   0x000000a4   Code   RO         3292    i.MSAInit           adc_pm803.o
    0x07808a50   0x00000018   Code   RO          429    i.ModOfTwoNumbers   misc.o
    0x07808a68   0x00000038   Code   RO          624    i.Mov_SM_SM_Instruction  RegInstructions.o
    0x07808aa0   0x00000054   Code   RO         3293    i.MrdBubbleSort     adc_pm803.o
    0x07808af4   0x00000038   Code   RO         3294    i.MrdSwap           adc_pm803.o
    0x07808b2c   0x0000001c   Code   RO         4596    i.No_Battery_Display  st7735s.o
    0x07808b48   0x00000014   Code   RO         1866    i.OBM_Flush         mpu.o
    0x07808b5c   0x00000034   Code   RO         4597    i.OLED_Cmd_0_Paras  st7735s.o
    0x07808b90   0x0000006c   Code   RO         4599    i.OLED_Cmd_Gamma_Corr  st7735s.o
    0x07808bfc   0x0000001c   Code   RO         4600    i.OLED_Color_Set    st7735s.o
    0x07808c18   0x000000b0   Code   RO         4601    i.OLED_DisplayHeadPicture  st7735s.o
    0x07808cc8   0x00000048   Code   RO         4602    i.OLED_EraseRectArea  st7735s.o
    0x07808d10   0x000000f8   Code   RO         4603    i.OLED_Pin_Configure  st7735s.o
    0x07808e08   0x00000008   Code   RO         4604    i.ONKEY_Bootup      st7735s.o
    0x07808e10   0x0000000c   Code   RO          550    i.OSCR0IntervalInMicro  timer.o
    0x07808e1c   0x0000000c   Code   RO          551    i.OSCR0IntervalInMilli  timer.o
    0x07808e28   0x00000010   Code   RO          552    i.OSCR0IntervalInSec  timer.o
    0x07808e38   0x00000020   Code   RO          625    i.OrInstruction     RegInstructions.o
    0x07808e58   0x00000044   Code   RO          626    i.Or_SM_SM_Instruction  RegInstructions.o
    0x07808e9c   0x0000002c   Code   RO          627    i.Or_SM_Val_Instruction  RegInstructions.o
    0x07808ec8   0x00000104   Code   RO         1945    i.PMIC_Init_ID      PlatformConfig.o
    0x07808fcc   0x000001f4   Code   RO         2410    i.PM_Handler        ProtocolManager.o
    0x078091c0   0x000000f8   Code   RO         2411    i.PM_ISR            ProtocolManager.o
    0x078092b8   0x000000e0   Code   RO         2412    i.PM_ReceiveImage   ProtocolManager.o
    0x07809398   0x00000024   Code   RO         2413    i.PM_VerifyPreamble  ProtocolManager.o
    0x078093bc   0x0000016c   Code   RO         1695    i.PP_Switch         FreqChange.o
    0x07809528   0x0000004c   Code   RO         1946    i.ParseBR_ExtraState  PlatformConfig.o
    0x07809574   0x000000d8   Code   RO         1409    i.ParseTransferStruct  BootLoader.o
    0x0780964c   0x00000040   Code   RO         1410    i.PerformTIMBasedSetup  BootLoader.o
    0x0780968c   0x0000001c   Code   RO         1947    i.PlatformAPUartIsEnable  PlatformConfig.o
    0x078096a8   0x0000001c   Code   RO         1948    i.PlatformChargeIsEnable  PlatformConfig.o
    0x078096c4   0x00000050   Code   RO         1949    i.PlatformChargerConfig  PlatformConfig.o
    0x07809714   0x00000024   Code   RO         1950    i.PlatformCheckForceUSBEnumFlag  PlatformConfig.o
    0x07809738   0x00000090   Code   RO         1952    i.PlatformCheckProductionMode  PlatformConfig.o
    0x078097c8   0x00000014   Code   RO         1954    i.PlatformClearForceUSBEnumFlag  PlatformConfig.o
    0x078097dc   0x00000010   Code   RO         1957    i.PlatformDDRCfgEnable  PlatformConfig.o
    0x078097ec   0x00000014   Code   RO         1958    i.PlatformGetChipID  PlatformConfig.o
    0x07809800   0x0000007c   Code   RO         1959    i.PlatformGetRevisionID  PlatformConfig.o
    0x0780987c   0x00000068   Code   RO         2414    i.PlatformInitFlash  ProtocolManager.o
    0x078098e4   0x00000028   Code   RO         1962    i.PlatformIsLapwB0  PlatformConfig.o
    0x0780990c   0x00000028   Code   RO         1963    i.PlatformKeypadConfig  PlatformConfig.o
    0x07809934   0x00000064   Code   RO         1966    i.PlatformOLEDConfig  PlatformConfig.o
    0x07809998   0x0000001c   Code   RO         1967    i.PlatformOledEnable  PlatformConfig.o
    0x078099b4   0x0000005c   Code   RO         1968    i.PlatformPI2CConfig  PlatformConfig.o
    0x07809a10   0x00000010   Code   RO         1969    i.PlatformPMICType  PlatformConfig.o
    0x07809a20   0x00000010   Code   RO         1970    i.PlatformPPEnable  PlatformConfig.o
    0x07809a30   0x00000010   Code   RO         1971    i.PlatformPROJECTType  PlatformConfig.o
    0x07809a40   0x00000018   Code   RO         1972    i.PlatformPrepareOBMVersion  PlatformConfig.o
    0x07809a58   0x00000004   Code   RO         1973    i.PlatformProtectBootBlocks  PlatformConfig.o
    0x07809a5c   0x00000018   Code   RO         1974    i.PlatformSetDdrWtdResetFlag  PlatformConfig.o
    0x07809a74   0x00000018   Code   RO         1975    i.PlatformSetForceUSBEnumFlag  PlatformConfig.o
    0x07809a8c   0x00000010   Code   RO         1976    i.PlatformSetLTGLWGFlag  PlatformConfig.o
    0x07809a9c   0x00000010   Code   RO         1977    i.PlatformSetPMICType  PlatformConfig.o
    0x07809aac   0x00000018   Code   RO         1978    i.PlatformSetTR069Flag  PlatformConfig.o
    0x07809ac4   0x00000074   Code   RO         1979    i.PlatformUARTConfig  PlatformConfig.o
    0x07809b38   0x0000001c   Code   RO         1980    i.Platform_PortEnable  PlatformConfig.o
    0x07809b54   0x00000090   Code   RO         1981    i.Platform_USB2_ON_USB2_PHY_Init  PlatformConfig.o
    0x07809be4   0x00000014   Code   RO         1982    i.Platform_USB2_Shutdown  PlatformConfig.o
    0x07809bf8   0x00000004   Code   RO         2291    i.PrefetchHandler   platform_interrupts.o
    0x07809bfc   0x000000b0   Code   RO         3131    i.ProcessDDROps     DDR_Cfg.o
    0x07809cac   0x00000240   Code   RO          628    i.ProcessInstructions  RegInstructions.o
    0x07809eec   0x00000020   Code   RO         3295    i.RTCRegisterInit   adc_pm803.o
    0x07809f0c   0x000000bc   Code   RO         4520    i.RangeDecoderBitDecode  LzmaDecode.o
    0x07809fc8   0x000000a4   Code   RO         4521    i.RangeDecoderBitTreeDecode  LzmaDecode.o
    0x0780a06c   0x00000064   Code   RO         4522    i.RangeDecoderDecodeDirectBits  LzmaDecode.o
    0x0780a0d0   0x00000060   Code   RO         4523    i.RangeDecoderInit  LzmaDecode.o
    0x0780a130   0x00000024   Code   RO         4524    i.RangeDecoderReadByte  LzmaDecode.o
    0x0780a154   0x000000ac   Code   RO         4525    i.RangeDecoderReverseBitTreeDecode  LzmaDecode.o
    0x0780a200   0x00000098   Code   RO         3441    i.ReadBatVolt       I2C.o
    0x0780a298   0x0000001c   Code   RO         3132    i.ReadBitField      DDR_Cfg.o
    0x0780a2b4   0x0000003c   Code   RO          919    i.ReadFlash         Flash.o
    0x0780a2f0   0x00000028   Code   RO          629    i.ReadInstruction   RegInstructions.o
    0x0780a318   0x00000048   Code   RO          777    i.ReadKeypad        keypad.o
    0x0780a360   0x0000015c   Code   RO         3296    i.ReadReliableDataToUpdateDefaultMap  adc_pm803.o
    0x0780a4bc   0x00000014   Code   RO         3007    i.ReleasetrbChain   usb2_memory.o
    0x0780a4d0   0x00000008   Code   RO          921    i.ResetBBT          Flash.o
    0x0780a4d8   0x000000a4   Code   RO         3442    i.Reset_Reg         I2C.o
    0x0780a57c   0x00000060   Code   RO         1983    i.RestoreDefaultConfig  PlatformConfig.o
    0x0780a5dc   0x00000020   Code   RO         3188    i.ReturnPImgPtr     tim.o
    0x0780a5fc   0x0000002c   Code   RO          630    i.Rshift_SM_Val_Instruction  RegInstructions.o
    0x0780a628   0x0000024c   Code   RO          833    i.SD_TR069_Upgrade  tr069.o
    0x0780a874   0x0000000c   Code   RO            2    i.SPINOR_Disable4BytesMode  spi_nor.o
    0x0780a880   0x0000000c   Code   RO            3    i.SPINOR_Wipe       spi_nor.o
    0x0780a88c   0x00000034   Code   RO         4605    i.SPI_OLED_BacklightON  st7735s.o
    0x0780a8c0   0x00000054   Code   RO         4606    i.SPI_OLED_Reset_SSP  st7735s.o
    0x0780a914   0x000002b0   Code   RO         4607    i.ST7735S_I2COLED_Init  st7735s.o
    0x0780abc4   0x00000040   Code   RO         4608    i.ST7735S_SPILCD_DiplayOff  st7735s.o
    0x0780ac04   0x00000098   Code   RO         4609    i.ST7735S_SPIOLED_Init  st7735s.o
    0x0780ac9c   0x00000060   Code   RO         1130    i.ScanBBT_Legacy    FM.o
    0x0780acfc   0x00000068   Code   RO         2416    i.SendAck           ProtocolManager.o
    0x0780ad64   0x00000074   Code   RO         2417    i.SendError         ProtocolManager.o
    0x0780add8   0x00000024   Code   RO         2418    i.SendResponse      ProtocolManager.o
    0x0780adfc   0x000000e8   Code   RO         4610    i.SetArea           st7735s.o
    0x0780aee4   0x00000068   Code   RO         1131    i.SetBBTState       FM.o
    0x0780af4c   0x00000090   Code   RO         3189    i.SetTIMPointers    tim.o
    0x0780afdc   0x00000224   Code   RO         2669    i.SetUpUSBDescriptors  usb_descriptors.o
    0x0780b200   0x00000030   Code   RO          926    i.SetUseSpareArea   Flash.o
    0x0780b230   0x00000040   Code   RO          631    i.Set_Bitfield_Instruction  RegInstructions.o
    0x0780b270   0x0000022c   Code   RO         1411    i.SetupEnvironment  BootLoader.o
    0x0780b49c   0x00000014   Code   RO         1985    i.ShutdownPort      PlatformConfig.o
    0x0780b4b0   0x0000001c   Code   RO         4611    i.SoftwareUpgrade_Done  st7735s.o
    0x0780b4cc   0x00000044   Code   RO          632    i.Store_SM_Addr_Instruction  RegInstructions.o
    0x0780b510   0x00000004   Code   RO         2293    i.SwiHandler        platform_interrupts.o
    0x0780b514   0x00000088   Code   RO         3443    i.System_poweroff   I2C.o
    0x0780b59c   0x000000c8   Code   RO         1616    i.TIMDownloadMain   TIMDownload.o
    0x0780b664   0x00000068   Code   RO          633    i.Test_If_Not_Zero_And_Set  RegInstructions.o
    0x0780b6cc   0x00000064   Code   RO          634    i.Test_If_Zero_And_Set  RegInstructions.o
    0x0780b730   0x00000064   Code   RO          635    i.Test_SM_If_Not_Zero_And_Set  RegInstructions.o
    0x0780b794   0x00000060   Code   RO          636    i.Test_SM_If_Zero_And_Set  RegInstructions.o
    0x0780b7f4   0x00000168   Code   RO          834    i.Tr069GetConfig    tr069.o
    0x0780b95c   0x0000001c   Code   RO          927    i.TransferFlashType  Flash.o
    0x0780b978   0x000000d0   Code   RO         2808    i.USB2D_Endpoint0Transmit  usb2_main.o
    0x0780ba48   0x000000a4   Code   RO         2809    i.USB2D_EndpointTransmit  usb2_main.o
    0x0780baec   0x00000074   Code   RO         2810    i.USB2D_Endpoint_Setup  usb2_main.o
    0x0780bb60   0x00000058   Code   RO         2715    i.USB2D_EnumerationHandler  usb2_enumeration.o
    0x0780bbb8   0x00000134   Code   RO         2716    i.USB2D_GetDescriptor  usb2_enumeration.o
    0x0780bcec   0x0000004c   Code   RO         2717    i.USB2D_GetStatus   usb2_enumeration.o
    0x0780bd38   0x000000a0   Code   RO         2811    i.USB2D_ISR         usb2_main.o
    0x0780bdd8   0x00000084   Code   RO         2812    i.USB2D_Initialize  usb2_main.o
    0x0780be5c   0x0000002c   Code   RO         2813    i.USB2D_PM_Call     usb2_main.o
    0x0780be88   0x0000008c   Code   RO         2814    i.USB2D_RecieveWrapper  usb2_main.o
    0x0780bf14   0x00000034   Code   RO         2815    i.USB2D_SendWrapper  usb2_main.o
    0x0780bf48   0x00000050   Code   RO         2718    i.USB2D_SetAddress  usb2_enumeration.o
    0x0780bf98   0x0000008c   Code   RO         2719    i.USB2D_SetConfig   usb2_enumeration.o
    0x0780c024   0x00000098   Code   RO         2720    i.USB2D_VendorRequest  usb2_enumeration.o
    0x0780c0bc   0x00000024   Code   RO         3062    i.USBAPI_ISR        usbapi.o
    0x0780c0e0   0x00000088   Code   RO         3063    i.USBAPI_InitializeDevice  usbapi.o
    0x0780c168   0x000002b8   Code   RO          835    i.UncompressUpdater  tr069.o
    0x0780c420   0x00000020   Code   RO         2294    i.UndefinedHandler  platform_interrupts.o
    0x0780c440   0x00000200   Code   RO         1132    i.UpdateBBT         FM.o
    0x0780c640   0x0000002c   Code   RO         2670    i.UpdateUSBDeviceConfigDesc  usb_descriptors.o
    0x0780c66c   0x0000006c   Code   RO         4615    i.Update_Battery_State  st7735s.o
    0x0780c6d8   0x00000090   Code   RO         1499    i.Update_DDR_Flag_From_CP  BootMode.o
    0x0780c768   0x00000044   Code   RO         1500    i.Update_DDR_Flag_To_CP  BootMode.o
    0x0780c7ac   0x00000020   Code   RO         3855    i.UsticaBaseRead    ustica.o
    0x0780c7cc   0x00000014   Code   RO         3856    i.UsticaBaseWrite   ustica.o
    0x0780c7e0   0x00000074   Code   RO         3857    i.UsticaChargerInit  ustica.o
    0x0780c854   0x00000068   Code   RO         3858    i.UsticaCheckBatteryConnect  ustica.o
    0x0780c8bc   0x00000088   Code   RO         3859    i.UsticaCheckBootONKey  ustica.o
    0x0780c944   0x00000060   Code   RO         3860    i.UsticaCheckUSBConnect  ustica.o
    0x0780c9a4   0x00000160   Code   RO         3861    i.UsticaCheckWakeup  ustica.o
    0x0780cb04   0x000000d8   Code   RO         3862    i.UsticaEnableSMPL  ustica.o
    0x0780cbdc   0x000000b4   Code   RO         3863    i.UsticaGetBatInstantVolt  ustica.o
    0x0780cc90   0x00000020   Code   RO         3864    i.UsticaGpadcRead   ustica.o
    0x0780ccb0   0x00000014   Code   RO         3865    i.UsticaGpadcWrite  ustica.o
    0x0780ccc4   0x00000020   Code   RO         3867    i.UsticaPowerRead   ustica.o
    0x0780cce4   0x00000014   Code   RO         3868    i.UsticaPowerWrite  ustica.o
    0x0780ccf8   0x00000078   Code   RO         3869    i.UsticaPoweroff    ustica.o
    0x0780cd70   0x00000040   Code   RO         3870    i.UsticaReadBatVolt  ustica.o
    0x0780cdb0   0x00000124   Code   RO         3871    i.UsticaResetReg    ustica.o
    0x0780ced4   0x00000038   Code   RO         3872    i.UsticaSetMainVoltage  ustica.o
    0x0780cf0c   0x00000008   Code   RO          637    i.ValidAddress      RegInstructions.o
    0x0780cf14   0x0000004c   Code   RO         2420    i.VerifyUploadParameters  ProtocolManager.o
    0x0780cf60   0x00000088   Code   RO         3444    i.Voltage_set_main  I2C.o
    0x0780cfe8   0x00000058   Code   RO          638    i.WaitForBitClearInstruction  RegInstructions.o
    0x0780d040   0x00000058   Code   RO          639    i.WaitForBitSetInstruction  RegInstructions.o
    0x0780d098   0x00000070   Code   RO          553    i.WaitForOperationComplete  timer.o
    0x0780d108   0x00000064   Code   RO          640    i.Wait_For_Bit_Pattern_Instruction  RegInstructions.o
    0x0780d16c   0x00000044   Code   RO          928    i.WriteFlash        Flash.o
    0x0780d1b0   0x00000018   Code   RO          641    i.WriteInstruction  RegInstructions.o
    0x0780d1c8   0x000000a4   Code   RO         4616    i.WriteRam_IO       st7735s.o
    0x0780d26c   0x00000028   Code   RO         1246    i.XllpDmacMapDeviceToChannel  dma.o
    0x0780d294   0x00000028   Code   RO         1247    i.XllpDmacNoDescriptorFetch  dma.o
    0x0780d2bc   0x00000028   Code   RO         1248    i.XllpDmacStartTransfer  dma.o
    0x0780d2e4   0x00000028   Code   RO         1249    i.XllpDmacStopTransfer  dma.o
    0x0780d30c   0x00000004   Code   RO          431    i.__aeabi_idiv0     misc.o
    0x0780d310   0x000000ac   Code   RO            4    i.__spi_nor_read    spi_nor.o
    0x0780d3bc   0x00000078   Code   RO         1696    i.aclk_dfc_switch   FreqChange.o
    0x0780d434   0x0000002c   Code   RO         1251    i.alignChannel      dma.o
    0x0780d460   0x00000134   Code   RO         4406    i.alloc_block       tinyalloc.o
    0x0780d594   0x00000144   Code   RO         3445    i.battery_charge    I2C.o
    0x0780d6d8   0x000000ac   Code   RO         3446    i.battery_full      I2C.o
    0x0780d784   0x000002c4   Code   RO         3447    i.battery_process_step1  I2C.o
    0x0780da48   0x00000208   Code   RO         3448    i.battery_process_step2  I2C.o
    0x0780dc50   0x000000d0   Code   RO         3451    i.battery_timer     I2C.o
    0x0780dd20   0x00000048   Code   RO          432    i.bubble            misc.o
    0x0780dd68   0x00000008   Code   RO         3297    i.checkReliableDataHeader  adc_pm803.o
    0x0780dd70   0x00000004   Code   RO         4837    i.check_BQ24259RegStatus  bq24259.o
    0x0780dd74   0x00000098   Code   RO         3452    i.check_BatteryConnect  I2C.o
    0x0780de0c   0x000001ac   Code   RO         3453    i.check_Battery_Status  I2C.o
    0x0780dfb8   0x000000b8   Code   RO         3454    i.check_BootONKey   I2C.o
    0x0780e070   0x00000160   Code   RO         3455    i.check_ExternalPower  I2C.o
    0x0780e1d0   0x00000098   Code   RO         3456    i.check_USBConnect  I2C.o
    0x0780e268   0x00000084   Code   RO         3457    i.check_battery     I2C.o
    0x0780e2ec   0x0000006c   Code   RO         4038    i.check_if_DCS_mode  guilin.o
    0x0780e358   0x00000090   Code   RO          837    i.check_ota_updater  tr069.o
    0x0780e3e8   0x000000b4   Code   RO          838    i.check_updater_ram  tr069.o
    0x0780e49c   0x000000a8   Code   RO         3458    i.check_wakeup      I2C.o
    0x0780e544   0x0000008c   Code   RO         4407    i.compact           tinyalloc.o
    0x0780e5d0   0x00000018   Code   RO         3633    i.dcache_clean_invalidate_range  qspi_host.o
    0x0780e5e8   0x00000018   Code   RO         3634    i.dcache_invalidate_range  qspi_host.o
    0x0780e600   0x000000a8   Code   RO         1697    i.dck_dfc_switch    FreqChange.o
    0x0780e6a8   0x000001f8   Code   RO         1501    i.decompress_cp_from_flash_to_ddr  BootMode.o
    0x0780e8a0   0x00000144   Code   RO         1502    i.decompress_dsp_from_flash_to_ddr  BootMode.o
    0x0780e9e4   0x00000010   Code   RO         1726    i.do_wdt_reset      wdt.o
    0x0780e9f4   0x00000044   Code   RO         1698    i.enable_PLL        FreqChange.o
    0x0780ea38   0x00000464   Code   RO          839    i.firmware_upgrade  tr069.o
    0x0780ee9c   0x00000004   Code   RO         4409    i.free              tinyalloc.o
    0x0780eea0   0x0000001c   Code   RO         3298    i.getAdcValue       adc_pm803.o
    0x0780eebc   0x00000014   Code   RO         3299    i.getAdcValueFromRtnReg  adc_pm803.o
    0x0780eed0   0x00000014   Code   RO         3300    i.getAdcValueFromRtpReg  adc_pm803.o
    0x0780eee4   0x00000010   Code   RO         2421    i.getProtoBuff      ProtocolManager.o
    0x0780eef4   0x00000010   Code   RO         2422    i.getProtocolCmd    ProtocolManager.o
    0x0780ef04   0x00000010   Code   RO         2423    i.getProtocolError  ProtocolManager.o
    0x0780ef14   0x0000000c   Code   RO         2424    i.getProtocolISR    ProtocolManager.o
    0x0780ef20   0x0000000c   Code   RO         2425    i.getProtocolMsg    ProtocolManager.o
    0x0780ef2c   0x0000000c   Code   RO         2426    i.getProtocolRsp    ProtocolManager.o
    0x0780ef38   0x000000c8   Code   RO         3301    i.getReliableDataEntry  adc_pm803.o
    0x0780f000   0x0000007c   Code   RO         3302    i.getRtnAdcValue    adc_pm803.o
    0x0780f07c   0x00000010   Code   RO         3303    i.getRtpAdcValue    adc_pm803.o
    0x0780f08c   0x0000004c   Code   RO         3304    i.getZoneByID       adc_pm803.o
    0x0780f0d8   0x00000014   Code   RO         1503    i.get_cp_exec_addr  BootMode.o
    0x0780f0ec   0x00000014   Code   RO         1504    i.get_rw_cpz_struct_addr  BootMode.o
    0x0780f100   0x0000003c   Code   RO         3008    i.get_trb           usb2_memory.o
    0x0780f13c   0x0000003c   Code   RO         3009    i.get_trb_ep0       usb2_memory.o
    0x0780f178   0x0000001c   Code   RO         3066    i.get_usb_speed_mode  usbapi.o
    0x0780f194   0x000000a4   Code   RO         4617    i.guilinOpenST7735sPower  st7735s.o
    0x0780f238   0x00000028   Code   RO         4039    i.guilin_read_volt_meas_val  guilin.o
    0x0780f260   0x00000024   Code   RO            5    i.ilog2             spi_nor.o
    0x0780f284   0x0000004c   Code   RO         4410    i.insert_block      tinyalloc.o
    0x0780f2d0   0x00000038   Code   RO         1254    i.loadNonDescriptor  dma.o
    0x0780f308   0x000001a4   Code   RO         3305    i.load_dsp_adc      adc_pm803.o
    0x0780f4ac   0x00000014   Code   RO         1505    i.loadtable_init    BootMode.o
    0x0780f4c0   0x00000054   Code   RO         3306    i.m_atoi_dec        adc_pm803.o
    0x0780f514   0x00000020   Code   RO         3307    i.m_strlen          adc_pm803.o
    0x0780f534   0x00000004   Code   RO         4411    i.malloc            tinyalloc.o
    0x0780f538   0x00000004   Code   RO         4412    i.malloc_init       tinyalloc.o
    0x0780f53c   0x00000040   Code   RO          433    i.memcmp            misc.o
    0x0780f57c   0x00000018   Code   RO          436    i.memset            misc.o
    0x0780f594   0x00000040   Code   RO         1867    i.mpu_debug         mpu.o
    0x0780f5d4   0x00000020   Code   RO          797    i.obm_printf        print.o
    0x0780f5f4   0x00000308   Code   RO          798    i.print             print.o
    0x0780f8fc   0x00000028   Code   RO          799    i.printchar         print.o
    0x0780f924   0x00000110   Code   RO          800    i.printi            print.o
    0x0780fa34   0x000000bc   Code   RO          801    i.prints            print.o
    0x0780faf0   0x00000034   Code   RO         4915    i.ql_boot_all_pin_error  libql_bootloader.a(quec_boot_all_pin_test.o)
    0x0780fb24   0x00000030   Code   RO         4916    i.ql_boot_all_pin_ok  libql_bootloader.a(quec_boot_all_pin_test.o)
    0x0780fb54   0x00000034   Code   RO         4917    i.ql_boot_all_pin_out_str  libql_bootloader.a(quec_boot_all_pin_test.o)
    0x0780fb88   0x00000180   Code   RO         4918    i.ql_boot_all_pin_test  libql_bootloader.a(quec_boot_all_pin_test.o)
    0x0780fd08   0x000000b4   Code   RO         5178    i.ql_boot_check_download_key  libquectel-bootloader-app.a(quec_boot_platform.o)
    0x0780fdbc   0x00000194   Code   RO         5179    i.ql_boot_check_pwrkey  libquectel-bootloader-app.a(quec_boot_platform.o)
    0x0780ff50   0x00000228   Code   RO         4919    i.ql_boot_enter_all_pin_test  libql_bootloader.a(quec_boot_all_pin_test.o)
    0x07810178   0x0000009c   Code   RO         5180    i.ql_boot_get_auto_dload_state  libquectel-bootloader-app.a(quec_boot_platform.o)
    0x07810214   0x0000001c   Code   RO         5181    i.ql_boot_get_download_mode  libquectel-bootloader-app.a(quec_boot_platform.o)
    0x07810230   0x000000c0   Code   RO         5182    i.ql_boot_get_hot_powerup_state  libquectel-bootloader-app.a(quec_boot_platform.o)
    0x078102f0   0x00000030   Code   RO         4920    i.ql_boot_gpio_get  libql_bootloader.a(quec_boot_all_pin_test.o)
    0x07810320   0x0000002c   Code   RO         4921    i.ql_boot_gpio_set  libql_bootloader.a(quec_boot_all_pin_test.o)
    0x0781034c   0x00000054   Code   RO         4922    i.ql_boot_gpio_set_input  libql_bootloader.a(quec_boot_all_pin_test.o)
    0x078103a0   0x00000054   Code   RO         4923    i.ql_boot_gpio_set_output  libql_bootloader.a(quec_boot_all_pin_test.o)
    0x078103f4   0x00000050   Code   RO         4924    i.ql_boot_pin_set   libql_bootloader.a(quec_boot_all_pin_test.o)
    0x07810444   0x00000074   Code   RO         5183    i.ql_boot_prv_pwk_type  libquectel-bootloader-app.a(quec_boot_platform.o)
    0x078104b8   0x00000088   Code   RO         4925    i.ql_boot_sd_power_on  libql_bootloader.a(quec_boot_all_pin_test.o)
    0x07810540   0x00000044   Code   RO         4926    i.ql_boot_set_all_pin_test_as_gpio  libql_bootloader.a(quec_boot_all_pin_test.o)
    0x07810584   0x000000bc   Code   RO         5184    i.ql_boot_set_auto_download  libquectel-bootloader-app.a(quec_boot_platform.o)
    0x07810640   0x00000034   Code   RO         4927    i.ql_boot_set_gpio_fun  libql_bootloader.a(quec_boot_all_pin_test.o)
    0x07810674   0x000000b4   Code   RO         5185    i.ql_boot_set_hot_powerup_state  libquectel-bootloader-app.a(quec_boot_platform.o)
    0x07810728   0x00000178   Code   RO         5186    i.ql_boot_update_pmic_reg  libquectel-bootloader-app.a(quec_boot_platform.o)
    0x078108a0   0x0000004c   Code   RO         4928    i.ql_gpio_get_base_reg_addr  libql_bootloader.a(quec_boot_all_pin_test.o)
    0x078108ec   0x00000144   Code   RO         4929    i.ql_gpio_mfpr_addr  libql_bootloader.a(quec_boot_all_pin_test.o)
    0x07810a30   0x0000001c   Code   RO         3636    i.qspi_cmd_done_interrupt  qspi_host.o
    0x07810a4c   0x0000003c   Code   RO         3637    i.qspi_cmd_done_pio  qspi_host.o
    0x07810a88   0x0000002c   Code   RO         3638    i.qspi_config_interrupt  qspi_host.o
    0x07810ab4   0x0000028c   Code   RO         3639    i.qspi_config_lookup_tbl  qspi_host.o
    0x07810d40   0x00000064   Code   RO         3640    i.qspi_config_mfp   qspi_host.o
    0x07810da4   0x00000024   Code   RO         3641    i.qspi_disable_dma  qspi_host.o
    0x07810dc8   0x00000050   Code   RO         3642    i.qspi_disable_interrupt  qspi_host.o
    0x07810e18   0x00000024   Code   RO         3643    i.qspi_enable_dma   qspi_host.o
    0x07810e3c   0x00000060   Code   RO         3644    i.qspi_enable_interrupt  qspi_host.o
    0x07810e9c   0x00000094   Code   RO         3645    i.qspi_enable_xip   qspi_host.o
    0x07810f30   0x0000002c   Code   RO         3646    i.qspi_enter_mode   qspi_host.o
    0x07810f5c   0x000000fc   Code   RO         3647    i.qspi_fill_to_txbuff  qspi_host.o
    0x07811058   0x000000a8   Code   RO         3648    i.qspi_fill_tx_buff  qspi_host.o
    0x07811100   0x0000000c   Code   RO         3649    i.qspi_get_cfg      qspi_host.o
    0x0781110c   0x0000017c   Code   RO         3650    i.qspi_host_init    qspi_host.o
    0x07811288   0x000000b0   Code   RO         3651    i.qspi_init_ahb     qspi_host.o
    0x07811338   0x00000050   Code   RO         3652    i.qspi_invalid_ahb  qspi_host.o
    0x07811388   0x000002cc   Code   RO         3653    i.qspi_irq_handler  qspi_host.o
    0x07811654   0x00000180   Code   RO         3654    i.qspi_poll_rx_buff  qspi_host.o
    0x078117d4   0x000000bc   Code   RO         3655    i.qspi_preinit_lookup_tbl  qspi_host.o
    0x07811890   0x00000068   Code   RO         3656    i.qspi_prepare_recv  qspi_host.o
    0x078118f8   0x000000fc   Code   RO         3657    i.qspi_prepare_transmit  qspi_host.o
    0x078119f4   0x000000a0   Code   RO         3658    i.qspi_read_from_rxbuff  qspi_host.o
    0x07811a94   0x000000c0   Code   RO         3659    i.qspi_set_func_clk  qspi_host.o
    0x07811b54   0x00000110   Code   RO         3660    i.qspi_set_func_clk_fc  qspi_host.o
    0x07811c64   0x0000032c   Code   RO         3661    i.qspi_start_cmd    qspi_host.o
    0x07811f90   0x000000a8   Code   RO         3662    i.qspi_start_dma_xfer  qspi_host.o
    0x07812038   0x0000002c   Code   RO         3663    i.qspi_update_shared_lut  qspi_host.o
    0x07812064   0x00000218   Code   RO         3664    i.qspi_wait_cmd_done  qspi_host.o
    0x0781227c   0x0000004c   Code   RO         3665    i.qspi_write_rbct   qspi_host.o
    0x078122c8   0x00000044   Code   RO         3666    i.qspi_write_sfar   qspi_host.o
    0x0781230c   0x0000001c   Code   RO         3667    i.qspi_writel_check  qspi_host.o
    0x07812328   0x0000001c   Code   RO         3668    i.qspi_writel_clear  qspi_host.o
    0x07812344   0x00000100   Code   RO         3669    i.qspi_xfer_done    qspi_host.o
    0x07812444   0x00000014   Code   RO         1256    i.readDmaStatusRegister  dma.o
    0x07812458   0x00000040   Code   RO         4414    i.release_blocks    tinyalloc.o
    0x07812498   0x00000018   Code   RO         2427    i.resetProtoBuff    ProtocolManager.o
    0x078124b0   0x00000030   Code   RO         4838    i.resetTimer        bq24259.o
    0x078124e0   0x00000070   Code   RO         1643    i.serial_init       serial.o
    0x07812550   0x00000044   Code   RO         1644    i.serial_outnum     serial.o
    0x07812594   0x0000002c   Code   RO         1645    i.serial_outstr     serial.o
    0x078125c0   0x0000002c   Code   RO         1646    i.serial_poll       serial.o
    0x078125ec   0x00000028   Code   RO         1648    i.serial_read_byte  serial.o
    0x07812614   0x00000028   Code   RO         1649    i.serial_write      serial.o
    0x0781263c   0x00000010   Code   RO         2428    i.setProtoBuff      ProtocolManager.o
    0x0781264c   0x00000010   Code   RO         2429    i.setProtocolCmd    ProtocolManager.o
    0x0781265c   0x00000010   Code   RO         2430    i.setProtocolError  ProtocolManager.o
    0x0781266c   0x0000007c   Code   RO         4619    i.show_Downlaod     st7735s.o
    0x078126e8   0x00000084   Code   RO         4621    i.show_battery_status  st7735s.o
    0x0781276c   0x00000084   Code   RO         4622    i.show_external_power  st7735s.o
    0x078127f0   0x00000060   Code   RO         4623    i.show_logo         st7735s.o
    0x07812850   0x00000084   Code   RO            7    i.spi_nor_disable_4byte_mode  spi_nor.o
    0x078128d4   0x00000018   Code   RO            8    i.spi_nor_do_erase  spi_nor.o
    0x078128ec   0x00000014   Code   RO            9    i.spi_nor_do_read   spi_nor.o
    0x07812900   0x00000014   Code   RO           10    i.spi_nor_do_write  spi_nor.o
    0x07812914   0x00000170   Code   RO           11    i.spi_nor_enable_4byte_mode  spi_nor.o
    0x07812a84   0x00000008   Code   RO           12    i.spi_nor_enable_quad  spi_nor.o
    0x07812a8c   0x000001e0   Code   RO           13    i.spi_nor_erase     spi_nor.o
    0x07812c6c   0x00000014   Code   RO           14    i.spi_nor_erase_32k_blk  spi_nor.o
    0x07812c80   0x00000014   Code   RO           15    i.spi_nor_erase_64k_blk  spi_nor.o
    0x07812c94   0x00000060   Code   RO           16    i.spi_nor_erase_all  spi_nor.o
    0x07812cf4   0x0000009c   Code   RO           17    i.spi_nor_erase_block  spi_nor.o
    0x07812d90   0x00000050   Code   RO           18    i.spi_nor_erase_chip  spi_nor.o
    0x07812de0   0x000000a0   Code   RO           19    i.spi_nor_erase_sector  spi_nor.o
    0x07812e80   0x0000026c   Code   RO           20    i.spi_nor_init      spi_nor.o
    0x078130ec   0x000000ac   Code   RO           21    i.spi_nor_program_data  spi_nor.o
    0x07813198   0x00000150   Code   RO           22    i.spi_nor_read      spi_nor.o
    0x078132e8   0x0000005c   Code   RO           23    i.spi_nor_read_id   spi_nor.o
    0x07813344   0x00000080   Code   RO           24    i.spi_nor_read_status  spi_nor.o
    0x078133c4   0x00000010   Code   RO           25    i.spi_nor_read_status1  spi_nor.o
    0x078133d4   0x0000003c   Code   RO           26    i.spi_nor_read_status2  spi_nor.o
    0x07813410   0x00000010   Code   RO           27    i.spi_nor_read_status3  spi_nor.o
    0x07813420   0x000000cc   Code   RO           28    i.spi_nor_reset     spi_nor.o
    0x078134ec   0x000000ac   Code   RO           30    i.spi_nor_scan_id_table  spi_nor.o
    0x07813598   0x000001f0   Code   RO           31    i.spi_nor_set_quad  spi_nor.o
    0x07813788   0x000000c4   Code   RO           32    i.spi_nor_set_rd_wr_op  spi_nor.o
    0x0781384c   0x00000038   Code   RO           35    i.spi_nor_wait      spi_nor.o
    0x07813884   0x00000140   Code   RO           36    i.spi_nor_write     spi_nor.o
    0x078139c4   0x0000004c   Code   RO           37    i.spi_nor_write_enable  spi_nor.o
    0x07813a10   0x00000080   Code   RO           38    i.spi_nor_write_page  spi_nor.o
    0x07813a90   0x00000078   Code   RO           39    i.spi_nor_write_status1  spi_nor.o
    0x07813b08   0x00000078   Code   RO           40    i.spi_nor_write_status2  spi_nor.o
    0x07813b80   0x00000060   Code   RO          437    i.strcmpl           misc.o
    0x07813be0   0x00000028   Code   RO          439    i.strlen            misc.o
    0x07813c08   0x00000058   Code   RO         4415    i.ta_alloc          tinyalloc.o
    0x07813c60   0x0000005c   Code   RO         4418    i.ta_free           tinyalloc.o
    0x07813cbc   0x00000060   Code   RO         4419    i.ta_init           tinyalloc.o
    0x07813d1c   0x00000010   Code   RO         4334    i.using_bsdiff_upgrade  bspatch.o
    0x07813d2c   0x00000044   Code   RO         4624    i.usticaOpenST7735sPower  st7735s.o
    0x07813d70   0x00000024   Code   RO         3877    i.ustica_read_volt_meas_val  ustica.o
    0x07813d94   0x0000001c   Code   RO         1727    i.wdt_access        wdt.o
    0x07813db0   0x00000030   Code   RO         1728    i.wdt_enable        wdt.o
    0x07813de0   0x00000028   Code   RO         1729    i.wdt_reset_counter  wdt.o
    0x07813e08   0x00000034   Code   RO         1730    i.wdt_set_match     wdt.o
    0x07813e3c   0x000000a0   Code   RO         1731    i.wdt_test          wdt.o
    0x07813edc   0x0000000c   Code   RO         5295    x$div0              c_2.l(aeabi_sdiv_div0_cr4.o)
    0x07813ee8   0x0000000c   Code   RO         5296    x$sdiv              c_2.l(aeabi_sdiv_div0_cr4.o)
    0x07813ef4   0x00000008   Ven    RO         5298    x$udiv              c_2.l(aeabi_sdiv_div0_cr4.o)
    0x07813efc   0x0000000c   Code   RO         5298    x$udiv              c_2.l(aeabi_sdiv_div0_cr4.o)
    0x07813f08   0x00000012   Code   RO         5299    x$udivmod           c_2.l(aeabi_sdiv_div0_cr4.o)
    0x07813f1a   0x00000002   PAD
    0x07813f1c   0x0000003d   Data   RO           42    .constdata          spi_nor.o
    0x07813f59   0x00000011   Data   RO         1650    .constdata          serial.o
    0x07813f6a   0x00000002   PAD
    0x07813f6c   0x00000264   Data   RO         1986    .constdata          PlatformConfig.o
    0x078141d0   0x00000025   Data   RO         3308    .constdata          adc_pm803.o
    0x078141f5   0x000000c0   Data   RO         3459    .constdata          I2C.o
    0x078142b5   0x00000600   Data   RO         3619    .constdata          oled_lib.o
    0x078148b5   0x00000100   Data   RO         3620    .constdata          oled_lib.o
    0x078149b5   0x00000080   Data   RO         3622    .constdata          oled_lib.o
    0x07814a35   0x00000040   Data   RO         3623    .constdata          oled_lib.o
    0x07814a75   0x00000100   Data   RO         3624    .constdata          oled_lib.o
    0x07814b75   0x00000003   PAD
    0x07814b78   0x00000020   Data   RO         3671    .constdata          qspi_host.o
    0x07814b98   0x0000001a   Data   RO         4184    .constdata          guilin_lite.o
    0x07814bb2   0x00000009   Data   RO         4423    .constdata          tinyalloc.o
    0x07814bbb   0x00000014   Data   RO         4931    .constdata          libql_bootloader.a(quec_boot_all_pin_test.o)
    0x07814bcf   0x000000c9   Data   RO         5188    .constdata          libquectel-bootloader-app.a(quec_boot_platform.o)
    0x07814c98   0x0000016a   Data   RO           43    .conststring        spi_nor.o
    0x07814e02   0x00000002   PAD
    0x07814e04   0x00000042   Data   RO         1507    .conststring        BootMode.o
    0x07814e46   0x00000002   PAD
    0x07814e48   0x00000053   Data   RO         3309    .conststring        adc_pm803.o
    0x07814e9b   0x00000001   PAD
    0x07814e9c   0x000000aa   Data   RO         5189    .conststring        libquectel-bootloader-app.a(quec_boot_platform.o)


    Execution Region ER_RW (Base: 0x07840000, Size: 0x00007d90, Max: 0xffffffff, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07840000   0x000005ae   Data   RW           44    .data               spi_nor.o
    0x078405ae   0x00000002   PAD
    0x078405b0   0x00000004   Data   RW          554    .data               timer.o
    0x078405b4   0x00000204   Data   RW          843    .data               tr069.o
    0x078407b8   0x00000008   Data   RW         1134    .data               FM.o
    0x078407c0   0x00000004   Data   RW         1257    .data               dma.o
    0x078407c4   0x00000014   Data   RW         1413    .data               BootLoader.o
    0x078407d8   0x00000004   Data   RW         1508    .data               BootMode.o
    0x078407dc   0x00000008   Data   RW         1509    .data               BootMode.o
    0x078407e4   0x00000010   Data   RW         1594    .data               DownloadMode.o
    0x078407f4   0x0000001c   Data   RW         1618    .data               TIMDownload.o
    0x07840810   0x00000004   Data   RW         1651    .data               serial.o
    0x07840814   0x00000001   Data   RW         1699    .data               FreqChange.o
    0x07840815   0x00000003   PAD
    0x07840818   0x00000004   Data   RW         1868    .data               mpu.o
    0x0784081c   0x00000014   Data   RW         1989    .data               PlatformConfig.o
    0x07840830   0x00000001   Data   RW         2295    .data               platform_interrupts.o
    0x07840831   0x00000003   PAD
    0x07840834   0x00000014   Data   RW         2432    .data               ProtocolManager.o
    0x07840848   0x00000082   Data   RW         2673    .data               usb_descriptors.o
    0x078408ca   0x0000000a   Data   RW         2674    .data               usb_descriptors.o
    0x078408d4   0x00000004   Data   RW         2721    .data               usb2_enumeration.o
    0x078408d8   0x00000020   Data   RW         2818    .data               usb2_main.o
    0x078408f8   0x00000008   Data   RW         3069    .data               usbapi.o
    0x07840900   0x00000014   Data   RW         3192    .data               tim.o
    0x07840914   0x00000028   Data   RW         3310    .data               adc_pm803.o
    0x0784093c   0x00000010   Data   RW         3460    .data               I2C.o
    0x0784094c   0x00000004   Data   RW         3593    .data               charger.o
    0x07840950   0x00000002   Data   RW         4040    .data               guilin.o
    0x07840952   0x00000002   PAD
    0x07840954   0x0000000c   Data   RW         4337    .data               bspatch.o
    0x07840960   0x0000000c   Data   RW         4424    .data               tinyalloc.o
    0x0784096c   0x0000000c   Data   RW         4625    .data               st7735s.o
    0x07840978   0x00000010   Data   RW         4932    .data               libql_bootloader.a(quec_boot_all_pin_test.o)
    0x07840988   0x000001ce   Data   RW         5266    .data               libql_bootloader.a(quec_boot_all_pin_map.o)
    0x07840b56   0x00000002   PAD
    0x07840b58   0x00000168   Zero   RW           41    .bss                spi_nor.o
    0x07840cc0   0x0000000c   Zero   RW          642    .bss                RegInstructions.o
    0x07840ccc   0x0000000c   Zero   RW          840    .bss                tr069.o
    0x07840cd8   0x00000098   Zero   RW          930    .bss                Flash.o
    0x07840d70   0x00000044   Zero   RW         1133    .bss                FM.o
    0x07840db4   0x00000004   PAD
    0x07840db8   0x000050d0   Zero   RW         2431    .bss                ProtocolManager.o
    0x07845e88   0x0000007c   Zero   RW         2671    .bss                usb_descriptors.o
    0x07845f04   0x00000460   Zero   RW         2817    .bss                usb2_main.o
    0x07846364   0x00001954   Zero   RW         3010    .bss                usb2_memory.o
    0x07847cb8   0x00000040   Zero   RW         3068    .bss                usbapi.o
    0x07847cf8   0x00000014   Zero   RW         3191    .bss                tim.o
    0x07847d0c   0x00000044   Zero   RW         3670    .bss                qspi_host.o
    0x07847d50   0x00000040   Zero   RW         4930    .bss                libql_bootloader.a(quec_boot_all_pin_test.o)


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      2488        744          0         20          0       9837   BootLoader.o
      3600       1292         66         12          0      15834   BootMode.o
      1092        104          0          0          0       4889   DDR_Cfg.o
       784        136          0         16          0       3325   DownloadMode.o
      2112         60          0          8         68      10462   FM.o
       868         20          0          0        152      20087   Flash.o
       720        208          0          1          0       2452   FreqChange.o
      4672        940        192         16          0      16603   I2C.o
      2968        388          0          0          0      14564   LzmaDecode.o
      1972        468        612         20          0      21953   PlatformConfig.o
      4352        156          0         20      20688      28698   ProtocolManager.o
      2020          4          0          0         12      18932   RegInstructions.o
      1396        100          0         28          0       4871   TIMDownload.o
      2368        376        120         40          0      19845   adc_pm803.o
       576         16          0          0          0        100   bbu_CI2C.o
       600         24          0          0          0        100   bbu_PI2C.o
      1104        152          0          0          0          0   bl_StartUp_ttc.o
       660         72          0          0          0       5923   bq24259.o
        16          4          0         12          0        703   bspatch.o
       164         44          0          4          0       1574   charger.o
       280         28          0          4          0      30499   dma.o
      2224        660          0          2          0      12758   guilin.o
      1824        564         26          0          0      12360   guilin_lite.o
       144         12          0          0          0       1088   keypad.o
       464          0          0          0          0       6491   misc.o
       300         20          0          4          0       2304   mpu.o
      3508         76          0          0          0          0   mpu.o
         8          0          0          0          0        804   oled.o
         0          0       2240          0          0       1077   oled_lib.o
       148          8          0          0          0          0   platform_StartUp.o
        32          0          0          0          0         68   platform_arch.o
       468         60          0          1          0       7448   platform_interrupts.o
      1308          8          0          0          0       4949   print.o
      6724       1456         32          0         68      33732   qspi_host.o
       348         20         17          4          0       4843   serial.o
      5416       1180        423       1454        360      88715   spi_nor.o
      3680        560          0         12          0      21091   st7735s.o
      1336         56          0         20         20       8961   tim.o
       308         16          0          4          0       4776   timer.o
       876         60          9         12          0       7203   tinyalloc.o
      3888       1568          0        516         12       9009   tr069.o
       868         40          0          4          0      24248   usb2_enumeration.o
      4040         68          0         32       1120      25923   usb2_main.o
       580         48          0          0       6484      23758   usb2_memory.o
       908        104          0        140        124      12541   usb_descriptors.o
       404         32          0          8         64       4557   usbapi.o
      1924        468          0          0          0      11052   ustica.o
         0          0        256          0          0       5538   version_block.o
       344         76          0          0          0       2706   wdt.o

    ----------------------------------------------------------------------
     76912      <USER>       <GROUP>       2424      29176     569251   Object Totals
         0          0          0          0          0          0   (incl. Generated)
        28          0         10         10          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        62          0          0          0          0        280   aeabi_sdiv_div0_cr4.o
        78          0          0          0          0         80   rt_memclr_w.o
       136          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
       240          4          0          0          0         80   strncmp.o
         0          0          0        462          0        909   quec_boot_all_pin_map.o
      2084        304         20         16         64      20628   quec_boot_all_pin_test.o
      1820        468        371          0          0       9409   quec_boot_platform.o

    ----------------------------------------------------------------------
      4524        <USER>        <GROUP>        480         64      31534   Library Totals
         4          0          2          2          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       616          4          0          0          0        588   c_2.l
      2084        304         20        478         64      21537   libql_bootloader.a
      1820        468        371          0          0       9409   libquectel-bootloader-app.a

    ----------------------------------------------------------------------
      4524        <USER>        <GROUP>        480         64      31534   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     81436      13272       4396       2904      29240     570781   Grand Totals
     81436      13272       4396       2904      29240     570781   ELF Image Totals
     81436      13272       4396       2904          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                85832 (  83.82kB)
    Total RW  Size (RW Data + ZI Data)             32144 (  31.39kB)
    Total ROM Size (Code + RO Data + RW Data)      88736 (  86.66kB)

==============================================================================

