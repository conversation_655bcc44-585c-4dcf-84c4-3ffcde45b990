# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.19

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: UPDATER
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================
# Object build statements for EXECUTABLE target Lapwing_updater


#############################################
# Order-only phony target for Lapwing_updater

build cmake_object_order_depends_target_Lapwing_updater: phony || CMakeFiles\Lapwing_updater.dir

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\plat_api.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\plat_api.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\plat_api.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\plat_api.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\pmic.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\pmic.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\pmic.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\pmic.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\FreqChange.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\FreqChange.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\FreqChange.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\FreqChange.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\uart.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\uart.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\uart.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\uart.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\Flash.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\Flash.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\Flash.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\Flash.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\qspi_host.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\qspi_host.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\qspi_host.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\qspi_host.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\spi_nand.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\spi_nand.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\spi_nand.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\spi_nand.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\spi_nor.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\spi_nor.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\spi_nor.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\spi_nor.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\FM.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\FM.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\FM.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\FM.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\FM_ext.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\FM_ext.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\FM_ext.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\FM_ext.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\system.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\system.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\system.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\system.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzmaDec.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\LzmaDec.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzmaDec.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzmaDec.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzmaEnc.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\LzmaEnc.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzmaEnc.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzmaEnc.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzFind.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\LzFind.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzFind.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzFind.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzmaLib.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\LzmaLib.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzmaLib.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzmaLib.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\tinyalloc.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\tinyalloc.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\tinyalloc.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\tinyalloc.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\tim.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\tim.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\tim.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\tim.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\updater_table.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\updater_table.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\updater_table.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\updater_table.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\utilities.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\utilities.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\utilities.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\utilities.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\asr_lzma.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\asr_lzma.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\asr_lzma.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\asr_lzma.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\guilin.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\guilin.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\guilin.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\guilin.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\guilin_lite.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\guilin_lite.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\guilin_lite.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\guilin_lite.o.rsp

build CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\adc_pm803.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\common\src\adc_pm803.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\adc_pm803.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\adc_pm803.o.rsp

build CMakeFiles\Lapwing_updater.dir\src\StartUp.o: ASM_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\updater\src\StartUp.s || cmake_object_order_depends_target_Lapwing_updater
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\src\StartUp.o.d
  FLAGS = --cpu=Cortex-R4 --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\src\StartUp.o.rsp

build CMakeFiles\Lapwing_updater.dir\src\bspatch.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\updater\src\bspatch.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\src\bspatch.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\src\bspatch.o.rsp

build CMakeFiles\Lapwing_updater.dir\src\version_block.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\updater\src\version_block.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\src\version_block.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\src\version_block.o.rsp

build CMakeFiles\Lapwing_updater.dir\src\main.o: C_COMPILER__Lapwing_updater_ D$:\xy695\PLT\startup\updater\src\main.c || cmake_object_order_depends_target_Lapwing_updater
  DEFINES = -DCOPYIMAGESTOFLASH -DCRANE_MCU_DONGLE -DFOTA_ASRDM -DLAPWING -DSMALL_CODE_UPDATER -DSUPPORT_GUILIN -DSUPPORT_GUILIN_LITE
  DEP_FILE = CMakeFiles\Lapwing_updater.dir\src\main.o.d
  FLAGS = --cpu Cortex-R5 -g -O2 --split_sections --gnu --diag_suppress 268,167,1296,68,61 --apcs /inter --no_unaligned_access
  INCLUDES = -ID:\xy695\PLT\startup\updater\inc -ID:\xy695\PLT\startup\updater\..\common\inc -ID:\xy695\PLT\startup\updater\..\common\inc\fs -ID:\xy695\PLT\startup\updater\..\common\inc\fs\fat -ID:\xy695\PLT\startup\updater\..\common\inc\lfs -ID:\xy695\PLT\startup\updater\..\common\inc\lfs\lfs_v2 -ID:\xy695\PLT\startup\updater\..\common\inc\usb
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  OBJECT_FILE_DIR = CMakeFiles\Lapwing_updater.dir\src
  RSP_FILE = CMakeFiles\Lapwing_updater.dir\src\main.o.rsp


# =============================================================================
# Link build statements for EXECUTABLE target Lapwing_updater


#############################################
# Link the executable bin\Lapwing_updater.elf

build bin\Lapwing_updater.elf: C_EXECUTABLE_LINKER__Lapwing_updater_ CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\plat_api.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\pmic.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\FreqChange.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\uart.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\Flash.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\qspi_host.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\spi_nand.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\spi_nor.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\FM.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\FM_ext.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\system.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzmaDec.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzmaEnc.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzFind.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\LzmaLib.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\tinyalloc.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\tim.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\updater_table.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\utilities.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\asr_lzma.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\guilin.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\guilin_lite.o CMakeFiles\Lapwing_updater.dir\D_\xy695\PLT\startup\common\src\adc_pm803.o CMakeFiles\Lapwing_updater.dir\src\StartUp.o CMakeFiles\Lapwing_updater.dir\src\bspatch.o CMakeFiles\Lapwing_updater.dir\src\version_block.o CMakeFiles\Lapwing_updater.dir\src\main.o
  FLAGS = --cpu Cortex-R5
  LINK_FLAGS = --elf --scatter=D:/xy695/PLT/startup/updater/build/Lapwing_updater_scatter.sct --map --symbols --info=sizes,totals
  OBJECT_DIR = CMakeFiles\Lapwing_updater.dir
  POST_BUILD = cmd.exe /C "cd /D D:\xy695\output\Lapwing_updater && "F:\DS-5 v5.26.0\sw\ARMCompiler5.06u4\bin\fromelf.exe" --bin bin/Lapwing_updater.elf --output bin/Lapwing_updater.bin && D:\xy695\ENV\misc\windows-x86\tree.exe -L 1 -hs -C -D --dirsfirst --noreport --nolinks D:/xy695/output/Lapwing_updater/bin && "F:\Program Files (x86)\Git\usr\bin\perl.exe" D:/xy695/PLT/startup/updater/../common/tool/external_ext_bin_size_update.pl bin/Lapwing_updater.bin UPD_SIZE && "F:\Program Files (x86)\Git\usr\bin\perl.exe" D:/xy695/PLT/startup/updater/../common/tool/external_ext_bin_append.pl UPDATERAPPEND bin/Lapwing_updater.bin UPD_SIZE D:/xy695/PLT/startup/updater/../common/lib/dsp_ADC_lzma.bin ADC_SIZE y && "F:\Program Files (x86)\Git\usr\bin\perl.exe" D:/xy695/PLT/startup/updater/../common/tool/external_ext_bin_size_update.pl bin/Lapwing_updater.bin EXT_SIZE && "F:\Program Files (x86)\Git\usr\bin\perl.exe" D:/xy695/PLT/startup/updater/../common/tool/external_append_pad_crc.pl bin/Lapwing_updater.bin"
  PRE_LINK = cd .
  TARGET_FILE = bin\Lapwing_updater.elf
  TARGET_PDB = Lapwing_updater.elf.dbg
  RSP_FILE = CMakeFiles\Lapwing_updater.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\xy695\output\Lapwing_updater && D:\xy695\ENV\cmake\windows-x86\bin\cmake-gui.exe -SD:\xy695\PLT\startup\updater -BD:\xy695\output\Lapwing_updater"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\xy695\output\Lapwing_updater && D:\xy695\ENV\cmake\windows-x86\bin\cmake.exe --regenerate-during-build -SD:\xy695\PLT\startup\updater -BD:\xy695\output\Lapwing_updater"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util

# =============================================================================
# Target aliases.

build Lapwing_updater: phony bin\Lapwing_updater.elf

build Lapwing_updater.elf: phony bin\Lapwing_updater.elf

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/xy695/output/Lapwing_updater

build all: phony bin\Lapwing_updater.elf

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles\3.19.2\CMakeASMCompiler.cmake CMakeFiles\3.19.2\CMakeCCompiler.cmake CMakeFiles\3.19.2\CMakeSystem.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeASMInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeCInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeCommonLanguageInclude.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeGenericSystem.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeInitializeConfigs.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeLanguageInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeSystemSpecificInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeSystemSpecificInitialize.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Compiler\ARMCC-ASM.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Compiler\ARMCC-C.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Compiler\ARMCC.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Platform\Generic.cmake D$:\xy695\PLT\startup\updater\CMakeLists.txt D$:\xy695\PLT\startup\updater\build\CMakeLists_updater_toolchain_mcu.cmake D$:\xy695\PLT\startup\updater\build\Lapwing_updater_config.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles\3.19.2\CMakeASMCompiler.cmake CMakeFiles\3.19.2\CMakeCCompiler.cmake CMakeFiles\3.19.2\CMakeSystem.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeASMInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeCInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeCommonLanguageInclude.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeGenericSystem.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeInitializeConfigs.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeLanguageInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeSystemSpecificInformation.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\CMakeSystemSpecificInitialize.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Compiler\ARMCC-ASM.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Compiler\ARMCC-C.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Compiler\ARMCC.cmake D$:\xy695\ENV\cmake\windows-x86\share\cmake-3.19\Modules\Platform\Generic.cmake D$:\xy695\PLT\startup\updater\CMakeLists.txt D$:\xy695\PLT\startup\updater\build\CMakeLists_updater_toolchain_mcu.cmake D$:\xy695\PLT\startup\updater\build\Lapwing_updater_config.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
