//PPC Version : V2.1.9.30
//PPL Source File Name : pcac-lzma_Lzmalib.ppp
//PPL Source File Name : L:/PLT/pcac/lzma/src/Lzmalib.c
typedef unsigned int size_t ;
typedef unsigned char uint8_t ;
typedef unsigned short uint16_t ;
typedef unsigned int uint32_t ;
typedef unsigned long long uint64_t ;
typedef int SRes ;
typedef int ptrdiff_t ;
typedef int Int32 ;
typedef unsigned int UInt32 ;
typedef UInt32 SizeT ;
typedef long long int Int64 ;
typedef unsigned long long int UInt64 ;
typedef void * CLzmaEncHandle ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
