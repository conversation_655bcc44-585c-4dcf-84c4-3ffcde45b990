//PPC Version : V2.1.9.30
//PPL Source File Name : pcac-httpclient_libhttpclient.ppp
//PPL Source File Name : L:/PLT/pcac/httpclient/src/libhttpclient.c
typedef unsigned int size_t ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_INT_IFC ,	 
 DIAG_EXT_IFC	 
 } DIAG_IF_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_UART_SPEED_921600 ,	 
 DIAG_UART_SPEED_1842000 ,	 
 DIAG_UART_SPEED_3000000 ,	 
 DIAG_UART_DISABLE ,	 
 } DIAG_UART_SPEED_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_MEM_ENABLE ,	 
 DIAG_MEM_DISABLE	 
 } DIAG_MEM_SWITCH;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_DEV_USB ,	 
 DIAG_DEV_SD ,	 
 DIAG_DEV_FS ,	 
 DIAG_DEV_UART ,	 
 DIAG_DEV_SPI ,	 
 DIAG_DEV_NONE	 
 } DIAG_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 SULOG_DEV_USB ,	 
 SULOG_DEV_SD	 
 } SULOG_DEV_TYPE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 DIAG_IF_TYPE useIntIf ; /* use internal interface or external interface */	 
 DIAG_UART_SPEED_TYPE useHighSpeedUART ; /* high speed UART ( 460800 ) or low speed UART ( 115200 ) */	 
 DIAG_MEM_SWITCH diagMemSwitch ;	 
 DIAG_DEV_TYPE diagDevType ;	 
 SULOG_DEV_TYPE sulogDevType ;	 
 BOOL sdlAutoDelete ;	 
 } diagCfgDataS;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 dbID ;	 
 UINT32 filterBitLength ;	 
 UINT32 reserved [ 6 ] ;	 
 } DIAG_Nvm_Filter_File_Header_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MASS_STORAGE_DISABLE = 0x0 ,	 
 MASS_STORAGE_ENABLE = 0x1	 
 } MassStorage_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* MIFI driver*/	 
 USB_GENERIC_MIFI_DRIVER = 0x0 ,	 
 USB_MARVELL_MIFI_DRIVER = 0x1 ,	 
 USB_ASR_MIFI_DRIVER = 0x2 ,	 
 USB_GENERIC_MOD_DRIVER = 0x10 ,	 
 USB_GENERIC_MOD_ECM_DRIVER = 0x12 ,	 
 USB_DIAG_UAC_DRIVER = 0x14 ,	 
 USB_RNDIS_ONLY_DRIVER = 0x15 ,	 
	 
 /* MBIM driver*/	 
 USB_MBIM_ONLY_DRIVER = 0x40 ,	 
 USB_MBIM_GENERIC_DRIVER = 0x41 ,	 
 USB_MBIM_MAX_DRIVER = 0x4F ,	 
	 
 /* Other driver*/	 
 USB_CDROM_ONLY_DRIVER = 0x91 ,	 
 USB_CDROM_DIAG_DRIVER = 0x92 ,	 
 USB_DIAG_ONLY_DRIVER = 0x93 ,	 
 USB_MODEM_ONLY_DRIVER = 0x94 ,	 
 USB_MODEM_DIAG_DRIVER = 0x95 ,	 
	 
 USB_MAX_DRIVER = 0xFF	 
 } Usb_driver_typeE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_AUTO_INSTALL_DISABLE = 0x0 ,	 
 USB_AUTO_INSTALL_ENABLE = 0x1	 
 } Usb_auto_install_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_OS_DETECT_DISABLE = 0x0 ,	 
 USB_OS_DETECT_ENABLE = 0x1	 
 } Usb_OS_detect_type;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Usb_driver_typeE usb_driver ;	 
 MassStorage_ConfigE mass_storage ;	 
 Usb_auto_install_type auto_install ;	 
 Usb_OS_detect_type os_detect ;	 
 } Usb_DriverS;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 YMODEM_DISABLE = 0x0 ,	 
 YMODEM_ENABLE = 0x1	 
 } Ymodem_Dump_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Ymodem_Dump_config config ;	 
 } Ymodem_Dump_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MBIM_ONLY_DESP = 0x0 ,	 
 MBIM_ATDIAG_DESP = 0x1	 
 } MBIM_DESP_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 MBIM_DESP_config config ;	 
 } MBIM_CFG_type;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 EEH_DUMP_DEV_USB_SD ,	 
 EEH_DUMP_DEV_SPI	 
 } EEH_DUMP_DEV_TYPE;

typedef va_list __gnuc_va_list ;
typedef unsigned int size_t ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0 = 0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 , GPIO_PIN_33 , GPIO_PIN_34 , GPIO_PIN_35 , GPIO_PIN_36 , GPIO_PIN_37 , GPIO_PIN_38 , GPIO_PIN_39 ,	 
	 
 GPIO_PIN_40 , GPIO_PIN_41 , GPIO_PIN_42 , GPIO_PIN_43 , GPIO_PIN_44 , GPIO_PIN_45 , GPIO_PIN_46 , GPIO_PIN_47 ,	 
 GPIO_PIN_48 , GPIO_PIN_49 , GPIO_PIN_50 , GPIO_PIN_51 , GPIO_PIN_52 , GPIO_PIN_53 , GPIO_PIN_54 , GPIO_PIN_55 ,	 
 GPIO_PIN_56 , GPIO_PIN_57 , GPIO_PIN_58 , GPIO_PIN_59 , GPIO_PIN_60 , GPIO_PIN_61 , GPIO_PIN_62 , GPIO_PIN_63 ,	 
	 
 GPIO_PIN_64 , GPIO_PIN_65 , GPIO_PIN_66 , GPIO_PIN_67 , GPIO_PIN_68 , GPIO_PIN_69 , GPIO_PIN_70 , GPIO_PIN_71 ,	 
 GPIO_PIN_72 , GPIO_PIN_73 , GPIO_PIN_74 , GPIO_PIN_75 , GPIO_PIN_76 , GPIO_PIN_77 , GPIO_PIN_78 , GPIO_PIN_79 ,	 
 GPIO_PIN_80 , GPIO_PIN_81 , GPIO_PIN_82 , GPIO_PIN_83 , GPIO_PIN_84 , GPIO_PIN_85 , GPIO_PIN_86 , GPIO_PIN_87 ,	 
 GPIO_PIN_88 , GPIO_PIN_89 , GPIO_PIN_90 , GPIO_PIN_91 , GPIO_PIN_92 , GPIO_PIN_93 , GPIO_PIN_94 , GPIO_PIN_95 ,	 
	 
 GPIO_PIN_96 , GPIO_PIN_97 , GPIO_PIN_98 , GPIO_PIN_99 , GPIO_PIN_100 , GPIO_PIN_101 , GPIO_PIN_102 , GPIO_PIN_103 ,	 
 GPIO_PIN_104 , GPIO_PIN_105 , GPIO_PIN_106 , GPIO_PIN_107 , GPIO_PIN_108 , GPIO_PIN_109 , GPIO_PIN_110 , GPIO_PIN_111 ,	 
 GPIO_PIN_112 , GPIO_PIN_113 , GPIO_PIN_114 , GPIO_PIN_115 , GPIO_PIN_116 , GPIO_PIN_117 , GPIO_PIN_118 , GPIO_PIN_119 ,	 
 GPIO_PIN_120 , GPIO_PIN_121 , GPIO_PIN_122 , GPIO_PIN_123 , GPIO_PIN_124 , GPIO_PIN_125 , GPIO_PIN_126 , GPIO_PIN_127 ,	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PMU_POR = 1 ,	 
 PMU_EMR ,	 
 PMU_WDTR = ( PMU_EMR+2 )	 
 } PMU_LastResetStatus;

typedef UINT8 UART_Activity ;
//ICAT EXPORTED STRUCT 
 typedef struct /* This is structure of the UART Configuration */ 
 {	 
 UART_OpMode opMode ; /* fifo mode , non fifo mode or DMA for basic interface*/	 
 UART_TriggerLevel triggerLevel ; /* the trigger level interrupt on 1 , 8 , 16 , 32 */	 
 UART_BaudRates baudRate ; /* the rate of the transmit and the receive up to 111520 ( default - 9600 ) .*/	 
 UART_WordLen numDataBits ; /* 5 , 6 , 7 , or 8 number of data bits in the UART data frame ( default - 8 ) . */	 
 UART_StopBits stopBits ; /* 1 , 1.500000 or 2 stop bits in the UART data frame ( default - 1 ) . */	 
 UART_ParityTBits parityBitType ; /* Even , Odd or no-parity bit type in the UART data frame ( default - Non ) . */	 
 UART_InterfaceType interfaceType ; /* number of interface that the UART driver supplies ( default - UART_IF_TYPE_L2 ) */	 
 BOOL modemSignal ; /* enable operate modem - TRUE , disable modem - FALSE */	 
 BOOL flowControl ; /* enable Auto flow Control - TRUE , disable Auto flow Control - FALSE */	 
 UINT8 sleepMode ; /* enable sleep mode - TRUE , more fine control - see UARTSleepMode enum */	 
 BOOL auto_baud ; /* enable auto_baud , auto-baud-rate detection within the UART ( default - FALSE ) */	 
 UART_SIRConfigure sirIrDA ;	 
 } UARTConfiguration;

//ICAT EXPORTED ENUM 
 typedef enum // change the order -1 to + 
 {	 
 UART_RC_OK = 1 , /* 1 - no errors */	 
	 
 UART_RC_PORT_NUM_ERROR = -100 , /* -100 - Error in the UART port number */	 
 UART_RC_NO_DATA_TO_READ , /* -99 - Eror no data to read from the FIFO UART */	 
 UART_RC_ILLEGAL_BAUD_RATE , /* -98 - Error in the UART Bayd Rate */	 
 UART_RC_UART_PARITY_BITS_ERROR , /* -97 - Error in parity bit */	 
 UART_RC_UART_ONE_STOP_BIT_ERROR , /* -96 - Error in one stop bit */	 
 UART_RC_ONE_HALF_OR_TWO_STOP_BIT_ERROR , /* -95 - Error in two stop bit */	 
 UART_RC_BAD_INTERFACE_TYPE , /* -94 - Error in the Interface Type */	 
 UART_RC_UART_NOT_AVAILABLE , /* -93 - Error in try to open UART that is open */	 
 UART_RC_NO_DATA_TO_WRITE , /* -92 - Error No data to writ the len = 0 */	 
 UART_RC_NOT_ALL_BYTE_WRITTEN , /* -91 - Error Not all the Byte write to the UART FIFO */	 
 UART_RC_ISR_ALREADY_BIND , /* -90 - Error try to bind ISR for Basic Interface */	 
 UART_RC_WRONG_ISR_UNBIND , /* -89 - Error in the UnBind ISR for Basic Interface */	 
 UART_RC_FIFO_NOT_EMPTY , /* -88 - Error , the UART FIFO not empty */	 
 UART_RC_UART_OPEN , /* -87 - Error try chance the configurr when the UART open */	 
 UART_RC_GPIO_ERR , /* -86 - Error in the Configure of the GPIO */	 
 UART_RC_IRDA_CONFIG_ERR /* -85 - Illegal IrDA configuration */	 
 } UART_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAM_LOG = 0x00 ,	 
 USB_RAM_LOG = 0x01 ,	 
 MAX_RAM_LOG = 0x02	 
 } Ram_Log_Type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAMLOG_ENABLE = 0x00 ,	 
 USB_RAMLOG_ENABLE = 0x01	 
 } RamLog_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 LOG_DISABLE = 0x0 ,	 
 UART_LOG_ENABLE = 0x1 ,	 
 ACAT_LOG_ENABLE = 0x2	 
 } Log_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MSG_DISABLE = 0x0 ,	 
 ACAT_MSG_ENABLE = 0x1	 
 } Msg_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RTI_LOG_DISABLE = 0x0 ,	 
 RTI_LOG_ENABLE = 0x1	 
 } RTI_ConfigE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Log_ConfigE log_cfg ;	 
 Msg_ConfigE msg_cfg ;	 
 RTI_ConfigE rti_cfg ;	 
 RamLog_ConfigE ramlog ;	 
 } Log_ConfigS;

typedef void ( *UARTNotifyInterrupt ) ( UART_Port ) ;
typedef void ( *UsbLogPrint_t ) ( const char * , ... ) ;
typedef UINT8 u8_t ;
typedef UINT16 u16_t ;
typedef UINT32 u32_t ;
typedef UINT64 u64_t ;
typedef INT8 s8_t ;
typedef INT16 s16_t ;
typedef INT32 s32_t ;
typedef void* msg_t ;
typedef u32_t mem_ptr_t ;
typedef unsigned long long uint64_t ;
typedef signed int ptrdiff_t ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef long double max_align_t ;
typedef union {
 ip4_addr_t ip4 ;
 ip6_addr_t ip6 ;
 } ipX_addr_t ;
typedef unsigned int size_t ;
typedef unsigned int clock_t ;
typedef unsigned int time_t ;
typedef s8_t err_t ;
typedef u16_t mem_size_t ;
typedef unsigned int size_t ;
typedef time_t mbedtls_time_t ;
typedef signed char int8_t ;
typedef signed short int int16_t ;
typedef signed int int32_t ;
typedef signed __int64 int64_t ;
typedef unsigned char uint8_t ;
typedef unsigned short int uint16_t ;
typedef unsigned int uint32_t ;
typedef unsigned __int64 uint64_t ;
typedef signed char int_least8_t ;
typedef signed short int int_least16_t ;
typedef signed int int_least32_t ;
typedef signed __int64 int_least64_t ;
typedef unsigned char uint_least8_t ;
typedef unsigned short int uint_least16_t ;
typedef unsigned int uint_least32_t ;
typedef unsigned __int64 uint_least64_t ;
typedef signed int int_fast8_t ;
typedef signed int int_fast16_t ;
typedef signed int int_fast32_t ;
typedef signed __int64 int_fast64_t ;
typedef unsigned int uint_fast8_t ;
typedef unsigned int uint_fast16_t ;
typedef unsigned int uint_fast32_t ;
typedef unsigned __int64 uint_fast64_t ;
typedef signed int intptr_t ;
typedef unsigned int uintptr_t ;
typedef signed long long intmax_t ;
typedef unsigned long long uintmax_t ;
typedef unsigned short wchar_t ;
typedef int64_t mbedtls_ms_time_t ;
typedef int mbedtls_iso_c_forbids_empty_translation_units ;
typedef int32_t mbedtls_mpi_sint ;
typedef uint32_t mbedtls_mpi_uint ;
typedef uint64_t mbedtls_t_udbl ;
typedef void mbedtls_ecp_restart_ctx ;
typedef mbedtls_ecp_keypair mbedtls_ecdsa_context ;
typedef void mbedtls_ecdsa_restart_ctx ;
typedef void mbedtls_pk_restart_ctx ;
typedef int ( *mbedtls_pk_rsa_alt_decrypt_func ) ( void *ctx , size_t *olen ,
 const unsigned char *input , unsigned char *output ,
 size_t output_max_len ) ;
typedef int ( *mbedtls_pk_rsa_alt_sign_func ) ( void *ctx ,
 int ( *f_rng ) ( void * , unsigned char * , size_t ) ,
 void *p_rng ,
 mbedtls_md_type_t md_alg , unsigned int hashlen ,
 const unsigned char *hash , unsigned char *sig ) ;
typedef size_t ( *mbedtls_pk_rsa_alt_key_len_func ) ( void *ctx ) ;
typedef mbedtls_asn1_buf mbedtls_x509_buf ;
typedef mbedtls_asn1_bitstring mbedtls_x509_bitstring ;
typedef mbedtls_asn1_named_data mbedtls_x509_name ;
typedef mbedtls_asn1_sequence mbedtls_x509_sequence ;
typedef void mbedtls_x509_crt_restart_ctx ;
typedef int ( *mbedtls_x509_crt_ext_cb_t ) ( void *p_ctx ,
 mbedtls_x509_crt const *crt ,
 mbedtls_x509_buf const *oid ,
 int critical ,
 const unsigned char *p ,
 const unsigned char *end ) ;
typedef int ( *mbedtls_x509_crt_ca_cb_t ) ( void *p_ctx ,
 mbedtls_x509_crt const *child ,
 mbedtls_x509_crt **candidate_cas ) ;
typedef int32_t psa_status_t ;
typedef uint16_t psa_key_type_t ;
typedef uint8_t psa_ecc_family_t ;
typedef uint8_t psa_dh_family_t ;
typedef uint32_t psa_algorithm_t ;
typedef uint32_t psa_key_lifetime_t ;
typedef uint8_t psa_key_persistence_t ;
typedef uint32_t psa_key_location_t ;
typedef uint32_t psa_key_id_t ;
typedef psa_key_id_t mbedtls_svc_key_id_t ;
typedef uint32_t psa_key_usage_t ;
typedef uint16_t psa_key_derivation_step_t ;
typedef union {
 unsigned dummy ;
 mbedtls_psa_hash_operation_t mbedtls_ctx ;



 } psa_driver_hash_context_t ;
typedef union {
 unsigned dummy ;
 mbedtls_psa_cipher_operation_t mbedtls_ctx ;




 } psa_driver_cipher_context_t ;
typedef union {
 unsigned dummy ;
 mbedtls_psa_mac_operation_t mbedtls_ctx ;




 } psa_driver_mac_context_t ;
typedef union {
 unsigned dummy ;
 mbedtls_psa_aead_operation_t mbedtls_ctx ;



 } psa_driver_aead_context_t ;
typedef union {
 unsigned dummy ;
 mbedtls_psa_sign_hash_interruptible_operation_t mbedtls_ctx ;
 } psa_driver_sign_hash_interruptible_context_t ;
typedef union {
 unsigned dummy ;
 mbedtls_psa_verify_hash_interruptible_operation_t mbedtls_ctx ;
 } psa_driver_verify_hash_interruptible_context_t ;
typedef union {
 unsigned dummy ;
 mbedtls_psa_pake_operation_t mbedtls_ctx ;




 } psa_driver_pake_context_t ;
typedef union {
 unsigned dummy ;
 # 49 " L: / PLT / pcac / mbedTLS / include / psa / crypto_driver_contexts_key_derivation.h "
 } psa_driver_key_derivation_context_t ;
typedef uint16_t psa_key_bits_t ;
typedef mbedtls_svc_key_id_t psa_key_handle_t ;
typedef uint64_t psa_drv_slot_number_t ;
typedef uint8_t psa_pake_role_t ;
typedef uint8_t psa_pake_step_t ;
typedef uint8_t psa_pake_primitive_type_t ;
typedef uint8_t psa_pake_family_t ;
typedef uint32_t psa_pake_primitive_t ;
typedef int mbedtls_ssl_send_t ( void *ctx ,
 const unsigned char *buf ,
 size_t len ) ;
typedef int mbedtls_ssl_recv_t ( void *ctx ,
 unsigned char *buf ,
 size_t len ) ;
typedef int mbedtls_ssl_recv_timeout_t ( void *ctx ,
 unsigned char *buf ,
 size_t len ,
 uint32_t timeout ) ;
typedef void mbedtls_ssl_set_timer_t ( void *ctx ,
 uint32_t int_ms ,
 uint32_t fin_ms ) ;
typedef int mbedtls_ssl_get_timer_t ( void *ctx ) ;
typedef int mbedtls_ssl_cache_get_t ( void *data ,
 unsigned char const *session_id ,
 size_t session_id_len ,
 mbedtls_ssl_session *session ) ;
typedef int mbedtls_ssl_cache_set_t ( void *data ,
 unsigned char const *session_id ,
 size_t session_id_len ,
 const mbedtls_ssl_session *session ) ;
typedef void mbedtls_ssl_export_keys_t ( void *p_expkey ,
 mbedtls_ssl_key_export_type type ,
 const unsigned char *secret ,
 size_t secret_len ,
 const unsigned char client_random [ 32 ] ,
 const unsigned char server_random [ 32 ] ,
 mbedtls_tls_prf_types tls_prf_type ) ;
typedef int ( *mbedtls_ssl_hs_cb_t ) ( mbedtls_ssl_context *ssl ) ;
typedef union {
 uintptr_t n ;
 void *p ;
 } mbedtls_ssl_user_data_t ;
typedef int mbedtls_ssl_ticket_write_t ( void *p_ticket ,
 const mbedtls_ssl_session *session ,
 unsigned char *start ,
 const unsigned char *end ,
 size_t *tlen ,
 uint32_t *lifetime ) ;
typedef int mbedtls_ssl_ticket_parse_t ( void *p_ticket ,
 mbedtls_ssl_session *session ,
 unsigned char *buf ,
 size_t len ) ;
typedef int mbedtls_ssl_cookie_write_t ( void *ctx ,
 unsigned char **p , unsigned char *end ,
 const unsigned char *info , size_t ilen ) ;
typedef int mbedtls_ssl_cookie_check_t ( void *ctx ,
 const unsigned char *cookie , size_t clen ,
 const unsigned char *info , size_t ilen ) ;
typedef UINT32 sys_prot_t ;
typedef void * sys_mutex_t ;
typedef void * sys_sem_t ;
typedef void * sys_mbox_t ;
typedef void * sys_thread_t ;
typedef void ( *eventCallback ) ( void *pArgs , int evt ) ;
typedef void ( *printCallback ) ( char *printstr ) ;
typedef void ( *lwip_thread_fn ) ( void *pArgs ) ;
typedef err_t ( *lwip_run_entry ) ( void *pArgs ) ;
typedef err_t ( *pbuf_bypass_fn ) ( void *p , void *inp ) ;
typedef void* ( *alloc_fn ) ( size_t size ) ;
typedef void ( *free_fn ) ( pmsg* msg ) ;
typedef void ( * socket_callback ) ( int s , int evt , u16_t len ) ;
typedef u32_t socklen_t ;
typedef int ( *netif_lowoutput_fn ) ( u8_t *data , u32_t len , void *msg ) ;
typedef void ( *tcp_err_fn ) ( void *arg , err_t err ) ;
typedef u32_t tcpwnd_size_t ;
typedef u16_t tcpflags_t ;
typedef unsigned int time_t ;
typedef u32_t in_addr_t ;
typedef u32_t bool ;
typedef void ( * sys_timeout_handler ) ( void *arg ) ;
typedef void ( *tcpip_init_done_fn ) ( void *arg ) ;
typedef void ( *tcpip_callback_fn ) ( void *ctx ) ;
typedef void ( *tcpip_select_fn ) ( int event , void *ctx , void *arg ) ;
typedef lwip_mq* mq_t ;
typedef void ( *lwip_nwst_callback_fn ) ( int event ) ;
typedef int ( *client_response_cb ) ( char *buffer , int size , int nitems , void *private_data ) ;
typedef void http_client ;
typedef int ( *mbedtls_entropy_f_source_ptr ) ( void *data , unsigned char *output , size_t len ,
 size_t *olen ) ;
DIAG_FILTER ( MIFI , HTTPCLI , 18 , DIAG_INFORMATION)  
 diagPrintf ( " [ value ] %s " , print_buf );

DIAG_FILTER ( MIFI , HTTPCLI , 19 , DIAG_INFORMATION)  
 diagPrintf ( " Strdup failed! Cannot malloc memory " );

DIAG_FILTER ( MIFI , HTTPCLI , 20 , DIAG_INFORMATION)  
 diagPrintf ( " Task running ( %lx ) ... " , msg );

DIAG_FILTER ( MIFI , HTTPCLI , 21 , DIAG_INFORMATION)  
 diagPrintf ( " Handle task stop ( %d ) " , timer_flag );

DIAG_FILTER ( MIFI , HTTPCLI , 22 , DIAG_INFORMATION)  
 diagPrintf ( " Handle task start ( %d ) " , timer_flag );

DIAG_FILTER ( MIFI , HTTPCLI , 23 , DIAG_INFORMATION)  
 diagPrintf ( " !!! UNKNOW COMMAND !!! " );

DIAG_FILTER ( MIFI , HTTPCLI , 24 , DIAG_INFORMATION)  
 diagPrintf ( " Create task " );

DIAG_FILTER ( MIFI , HTTPCLI , 130 , DIAG_INFORMATION)  
 diagPrintf ( " !!! FAILED %s:%d !!! " , __func__ , 244 );

DIAG_FILTER ( MIFI , HTTPCLI , 131 , DIAG_INFORMATION)  
 diagPrintf ( " !!! COMMNAND LOSE %d@%lx !!! " , command->command_id , __return_address ( ) );

DIAG_FILTER ( MIFI , HTTPCLI , 132 , DIAG_INFORMATION)  
 diagPrintf ( " Cannot get memory! " );

DIAG_FILTER ( MIFI , HTTPCLI , 133 , DIAG_INFORMATION)  
 diagPrintf ( " % failed! [ %d ] " , __func__ , res );

DIAG_FILTER ( MIFI , HTTPCLI , 25 , DIAG_INFORMATION)  
 diagPrintf ( " build_new_socket: Clear ssl context " );

DIAG_FILTER ( MIFI , HTTPCLI , 26 , DIAG_INFORMATION)  
 diagPrintf ( " build_new_socket use_ip6 ( %d ) " , client->server . use_ip6 );

DIAG_FILTER ( MIFI , HTTPCLI , 27 , DIAG_INFORMATION)  
 diagPrintf ( " ip6 bind port [ %d ] success " , client->port );

DIAG_FILTER ( MIFI , HTTPCLI , 28 , DIAG_INFORMATION)  
 diagPrintf ( " ip4 bind port [ %d ] success " , client->port );

DIAG_FILTER ( MIFI , HTTPCLI , 29 , DIAG_INFORMATION)  
 diagPrintf ( " Socket connect successful " );

DIAG_FILTER ( MIFI , HTTPCLI , 142 , DIAG_INFORMATION)  
 diagPrintf ( " %s failed [ %d ] [ %d ] " , __func__ , res , ret );

DIAG_FILTER ( MIFI , HTTPCLI , 30 , DIAG_INFORMATION)  
 diagPrintf ( " %s , connect success " , __FUNCTION__ );

DIAG_FILTER ( MIFI , HTTPCLI , 143 , DIAG_INFORMATION)  
 diagPrintf ( " %s , connect failed " , __FUNCTION__ );

DIAG_FILTER ( MIFI , HTTPCLI , 31 , DIAG_INFORMATION)  
 diagPrintf ( " connect EINPROGRESS " );

DIAG_FILTER ( MIFI , HTTPCLI , 144 , DIAG_INFORMATION)  
 diagPrintf ( " select error %d " , lwip_errno );

DIAG_FILTER ( MIFI , HTTPCLI , 145 , DIAG_INFORMATION)  
 diagPrintf ( " select time out " );

DIAG_FILTER ( MIFI , HTTPCLI , 32 , DIAG_INFORMATION)  
 diagPrintf ( " connect may be reset " );

DIAG_FILTER ( MIFI , HTTPCLI , 33 , DIAG_INFORMATION)  
 diagPrintf ( " get SO_ERROR= %d " , value );

DIAG_FILTER ( MIFI , HTTPCLI , 34 , DIAG_INFORMATION)  
 diagPrintf ( " select return %d , value [ %d ] " , res , value );

DIAG_FILTER ( MIFI , HTTPCLI , 141 , DIAG_INFORMATION)  
 diagPrintf ( " %s failed , [ %d ] [ %d ] " , __func__ , res , lwip_errno );

DIAG_FILTER ( MIFI , HTTPCLI , 35 , DIAG_INFORMATION)  
 diagPrintf ( " Set url version:2016-02-04 1918 " );

DIAG_FILTER ( MIFI , HTTPCLI , 36 , DIAG_INFORMATION)  
 diagPrintf ( " Set url raw data: " );

DIAG_FILTER ( MIFI , HTTPCLI , 37 , DIAG_INFORMATION)  
 diagPrintf ( " User use special http port %d " , client->server . port );

DIAG_FILTER ( MIFI , HTTPCLI , 38 , DIAG_INFORMATION)  
 diagPrintf ( " User does not use special http port " );

DIAG_FILTER ( MIFI , HTTPCLI , 39 , DIAG_INFORMATION)  
 diagPrintf ( " User use special http port %d " , client->server . port );

DIAG_FILTER ( MIFI , HTTPCLI , 40 , DIAG_INFORMATION)  
 diagPrintf ( " Http client parse address result: " );

DIAG_FILTER ( MIFI , HTTPCLI , 41 , DIAG_INFORMATION)  
 diagPrintf ( " host: " );

DIAG_FILTER ( MIFI , HTTPCLI , 42 , DIAG_INFORMATION)  
 diagPrintf ( " path: " );

DIAG_FILTER ( MIFI , HTTPCLI , 400 , DIAG_INFORMATION)  
 diagPrintf ( " active_stop ( line %d ) " , 726 );

DIAG_FILTER ( MIFI , HTTPCLI , 43 , DIAG_INFORMATION)  
 diagPrintf ( " Get %s: %s " , client->server . host , ipaddr_ntoa ( ( ip_addr_t* ) & ( temp ) ) );

DIAG_FILTER ( MIFI , HTTPCLI , 44 , DIAG_INFORMATION)  
 diagPrintf ( " Get IPV6:%s: %s " , client->server . host , ip6addr_ntoa ( ( ip6_addr_t* ) & ( client->server . ip6 . sin6_addr ) ) );

DIAG_FILTER ( MIFI , HTTPCLI , 147 , DIAG_INFORMATION)  
 diagPrintf ( " error! DNS type ( %d ) , query_status error ( %d ) !\n " , client->dns_type , client->dns_query_status );

DIAG_FILTER ( MIFI , HTTPCLI , 148 , DIAG_INFORMATION)  
 diagPrintf ( " error! DNS type ( %d ) , query_status error ( %d ) !\n " , client->dns_type , client->dns_query_status );

DIAG_FILTER ( MIFI , HTTPCLI , 149 , DIAG_INFORMATION)  
 diagPrintf ( " error! DNS type ( %d ) , query_status error ( %d ) !\n " , client->dns_type , client->dns_query_status );

DIAG_FILTER ( MIFI , HTTPCLI , 150 , DIAG_INFORMATION)  
 diagPrintf ( " error! DNS type ( %d ) , query_status ( %d ) !\n " , client->dns_type , client->dns_query_status );

DIAG_FILTER ( MIFI , HTTPCLI , 151 , DIAG_INFORMATION)  
 diagPrintf ( " DNS getaddrinfo failed ( %d ) ! query again!\n " , ret );

DIAG_FILTER ( MIFI , HTTPCLI , 45 , DIAG_INFORMATION)  
 diagPrintf ( " DNS type ( %d ) , query_status ( %d ) !\n " , client->dns_type , client->dns_query_status );

DIAG_FILTER ( MIFI , HTTPCLI , 152 , DIAG_INFORMATION)  
 diagPrintf ( " DNS getaddrinfo faield?! query again!\n " );

DIAG_FILTER ( MIFI , HTTPCLI , 46 , DIAG_INFORMATION)  
 diagPrintf ( " DNS type ( %d ) , query_status ( %d ) !\n " , client->dns_type , client->dns_query_status );

DIAG_FILTER ( MIFI , HTTPCLI , 47 , DIAG_INFORMATION)  
 diagPrintf ( " DNS getaddrinfo , Get %s: %s " , client->server . host , ipaddr_ntoa ( ( ip_addr_t* ) & ( temp ) ) );

DIAG_FILTER ( MIFI , HTTPCLI , 48 , DIAG_INFORMATION)  
 diagPrintf ( " DNS getaddrinfo , Get IPV6:%s: %s " , client->server . host , ip6addr_ntoa ( ( ip6_addr_t* ) & ( client->server . ip6 . sin6_addr ) ) );

DIAG_FILTER ( MIFI , HTTPCLI , 401 , DIAG_INFORMATION)  
 diagPrintf ( " active_stop ( line %d ) " , 868 );

DIAG_FILTER ( MIFI , HTTPCLI , 153 , DIAG_INFORMATION)  
 diagPrintf ( " Create socket failed ( %d ) " , client->server . fd );

DIAG_FILTER ( MIFI , HTTPCLI , 154 , DIAG_INFORMATION)  
 diagPrintf ( " Set tcp no delay failed ( %d ) " , ret );

DIAG_FILTER ( MIFI , HTTPCLI , set_url_901 , DIAG_INFORMATION)  
 diagPrintf ( " recv_timeout %d " , client->recv_timeout );

DIAG_FILTER ( MIFI , HTTPCLI , 155 , DIAG_INFORMATION)  
 diagPrintf ( " Set tcp recv timeout failed ( %d ) " , ret );

DIAG_FILTER ( MIFI , HTTPCLI , set_url_918 , DIAG_INFORMATION)  
 diagPrintf ( " Set socket nosack_enable " );

DIAG_FILTER ( MIFI , HTTPCLI , set_url_920 , DIAG_INFORMATION)  
 diagPrintf ( " Set socket noscale_enable " );

DIAG_FILTER ( MIFI , HTTPCLI , 156 , DIAG_INFORMATION)  
 diagPrintf ( " bind cid [ %d ] failed ( ip6 %s ) " , client->cid-1 , ip6addr_ntoa ( ( ip6_addr_t* ) & ( local6 . sin6_addr ) ) );

DIAG_FILTER ( MIFI , HTTPCLI , 157 , DIAG_INFORMATION)  
 diagPrintf ( " bind cid [ %d ] success ( ip6 %s ) " , client->cid-1 , ip6addr_ntoa ( ( ip6_addr_t* ) & ( local6 . sin6_addr ) ) );

DIAG_FILTER ( MIFI , HTTPCLI , 158 , DIAG_INFORMATION)  
 diagPrintf ( " bind cid [ %d ] failed ( ip6 have not ready ) " , client->cid-1 );

DIAG_FILTER ( MIFI , HTTPCLI , 159 , DIAG_INFORMATION)  
 diagPrintf ( " bind cid [ %d ] failed ( ip %s ) " , client->cid-1 , ipaddr_ntoa ( ( ip_addr_t* ) & ( local4 . sin_addr . s_addr ) ) );

DIAG_FILTER ( MIFI , HTTPCLI , 49 , DIAG_INFORMATION)  
 diagPrintf ( " bind cid [ %d ] success ( ip %s ) " , client->cid-1 , ipaddr_ntoa ( ( ip_addr_t* ) & ( local4 . sin_addr . s_addr ) ) );

DIAG_FILTER ( MIFI , HTTPCLI , 160 , DIAG_INFORMATION)  
 diagPrintf ( " bind cid [ %d ] failed ( not find netif by cid ) " , client->cid-1 );

DIAG_FILTER ( MIFI , HTTPCLI , 161 , DIAG_INFORMATION)  
 diagPrintf ( " Set socket SO_LINGER failed ( %d ) " , ret );

DIAG_FILTER ( MIFI , HTTPCLI , 162 , DIAG_INFORMATION)  
 diagPrintf ( " ip6 bind port [ %d ] failed " , client->port );

DIAG_FILTER ( MIFI , HTTPCLI , 50 , DIAG_INFORMATION)  
 diagPrintf ( " ip6 bind port [ %d ] success " , client->port );

DIAG_FILTER ( MIFI , HTTPCLI , 163 , DIAG_INFORMATION)  
 diagPrintf ( " ip4 bind port [ %d ] failed " , client->port );

DIAG_FILTER ( MIFI , HTTPCLI , 51 , DIAG_INFORMATION)  
 diagPrintf ( " ip4 bind port [ %d ] success " , client->port );

DIAG_FILTER ( MIFI , HTTPCLI , 52 , DIAG_INFORMATION)  
 diagPrintf ( " Get socket fd %d " , client->server . fd );

DIAG_FILTER ( MIFI , HTTPCLI , 53 , DIAG_INFORMATION)  
 diagPrintf ( " Socket connect failed ( %d ) , use_ip6 ( %d ) , valid_ip_flag ( %d ) " , ret , client->server . use_ip6 , valid_ip_flag );

DIAG_FILTER ( MIFI , HTTPCLI , 54 , DIAG_INFORMATION)  
 diagPrintf ( " DNS getaddrinfo next , Get %s: %s " , client->server . host , ipaddr_ntoa ( ( ip_addr_t* ) & ( temp ) ) );

DIAG_FILTER ( MIFI , HTTPCLI , 55 , DIAG_INFORMATION)  
 diagPrintf ( " DNS getaddrinfo next , Get IPV6:%s: %s " , client->server . host , ip6addr_ntoa ( ( ip6_addr_t* ) & ( client->server . ip6 . sin6_addr ) ) );

DIAG_FILTER ( MIFI , HTTPCLI , 164 , DIAG_INFORMATION)  
 diagPrintf ( " connect failed! query again!\n " );

DIAG_FILTER ( MIFI , HTTPCLI , 56 , DIAG_INFORMATION)  
 diagPrintf ( " DNS type ( %d ) , query_status ( %d ) !\n " , client->dns_type , client->dns_query_status );

DIAG_FILTER ( MIFI , HTTPCLI , 58 , DIAG_INFORMATION)  
 diagPrintf ( " Socket connect successful " );

DIAG_FILTER ( MIFI , HTTPCLI , 146 , DIAG_INFORMATION)  
 diagPrintf ( " %s failed , %d , %d " , __func__ , res , ret );

DIAG_FILTER ( MIFI , HTTPCLI , 59 , DIAG_INFORMATION)  
 diagPrintf ( " set_pdp_cid %d " , arg );

DIAG_FILTER ( MIFI , HTTPCLI , 60 , DIAG_INFORMATION)  
 diagPrintf ( " set_bind port %d " , arg );

DIAG_FILTER ( MIFI , HTTPCLI , 61 , DIAG_INFORMATION)  
 diagPrintf ( " set_dns_type %d " , arg );

DIAG_FILTER ( MIFI , HTTPCLI , 62 , DIAG_INFORMATION)  
 diagPrintf ( " set_proxy_enable %d " , arg );

DIAG_FILTER ( MIFI , HTTPCLI , set_recv_timeout_1198 , DIAG_INFORMATION)  
 diagPrintf ( " set_recv_timeout %d " , arg );

DIAG_FILTER ( MIFI , HTTPCLI , set_socket_nosack_1224 , DIAG_INFORMATION)  
 diagPrintf ( " set_socket_nosack %d " , arg );

DIAG_FILTER ( MIFI , HTTPCLI , set_socket_nosclae_1234 , DIAG_INFORMATION)  
 diagPrintf ( " set_socket_noscale %d " , arg );

DIAG_FILTER ( MIFI , HTTPCLI , set_http_redirect_1270 , DIAG_INFORMATION)  
 diagPrintf ( " set_http_redirect %d " , arg );

DIAG_FILTER ( MIFI , HTTPCLI , 166 , DIAG_INFORMATION)  
 diagPrintf ( " No client! " );

DIAG_FILTER ( MIFI , HTTPCLI , 63 , DIAG_INFORMATION)  
 diagPrintf ( " Get response start line: " );

DIAG_FILTER ( MIFI , HTTPCLI , 64 , DIAG_INFORMATION)  
 diagPrintf ( " Response code %d " , client->response . result_code );

DIAG_FILTER ( MIFI , HTTPCLI , 167 , DIAG_INFORMATION)  
 diagPrintf ( " %s Failed , %d " , __func__ , res );

DIAG_FILTER ( MIFI , HTTPCLI , 173 , DIAG_INFORMATION)  
 diagPrintf ( " Failed @%s:%d " , __func__ , 1417 );

DIAG_FILTER ( MIFI , HTTPCLI , 65 , DIAG_INFORMATION)  
 diagPrintf ( " Get http content length %lu " , client->response . response_data_length );

DIAG_FILTER ( MIFI , HTTPCLI , 66 , DIAG_INFORMATION)  
 diagPrintf ( " Handle cookie: " );

DIAG_FILTER ( MIFI , HTTPCLI , 67 , DIAG_INFORMATION)  
 diagPrintf ( " %s " , data );

DIAG_FILTER ( MIFI , HTTPCLI , 68 , DIAG_INFORMATION)  
 diagPrintf ( " Handle connection setting: %s " , data );

DIAG_FILTER ( MIFI , HTTPCLI , 69 , DIAG_INFORMATION)  
 diagPrintf ( " Headle auth: " );

DIAG_FILTER ( MIFI , HTTPCLI , 70 , DIAG_INFORMATION)  
 diagPrintf ( " HTTP client get auth header , but not match ' Digest ' , maybe is BASE auth , so adjust type " );

DIAG_FILTER ( MIFI , HTTPCLI , 71 , DIAG_INFORMATION)  
 diagPrintf ( " User set auth type is BASE , but handle DIGEST auth response , so adjust auth type " );

DIAG_FILTER ( MIFI , HTTPCLI , 72 , DIAG_INFORMATION)  
 diagPrintf ( " Handle transfer_encoding: %s " , data );

DIAG_FILTER ( MIFI , HTTPCLI , 73 , DIAG_INFORMATION)  
 diagPrintf ( " transfer_encoding is chunked " );

DIAG_FILTER ( MIFI , HTTPCLI , 74 , DIAG_INFORMATION)  
 diagPrintf ( " Enter %s " , __func__ );

DIAG_FILTER ( MIFI , HTTPCLI , 174 , DIAG_INFORMATION)  
 diagPrintf ( " %s Failed %d " , __func__ , res );

DIAG_FILTER ( MIFI , HTTPCLI , 75 , DIAG_INFORMATION)  
 diagPrintf ( " %s old_length [ %d ] , len [ %d ] " , __func__ , old_length , new_length );

DIAG_FILTER ( MIFI , HTTPCLI , 177 , DIAG_INFORMATION)  
 diagPrintf ( " Failed @%s:%d , [ %d ] , [ %s ] " , __func__ , 1665 , old_length , client->response . chunked_string );

DIAG_FILTER ( MIFI , HTTPCLI , 76 , DIAG_INFORMATION)  
 diagPrintf ( " need more @%s:%d , [ %d ] , [ %s ] " , __func__ , 1671 , old_length , client->response . chunked_string );

DIAG_FILTER ( MIFI , HTTPCLI , 178 , DIAG_INFORMATION)  
 diagPrintf ( " Failed @%s:%d , [ %d ] , [ %s ] " , __func__ , 1696 , old_length , client->response . chunked_string );

DIAG_FILTER ( MIFI , HTTPCLI , 179 , DIAG_INFORMATION)  
 diagPrintf ( " Failed @%s:%d , [ %d ] , [ %s ] " , __func__ , 1732 , string , length );

DIAG_FILTER ( MIFI , HTTPCLI , 77 , DIAG_INFORMATION)  
 diagPrintf ( " get chunked_length [ %x ] [ %d ] " , client->response . chunked_length , client->response . chunked_length );

DIAG_FILTER ( MIFI , HTTPCLI , 180 , DIAG_INFORMATION)  
 diagPrintf ( " Failed @%s:%d , [ %d ] , [ %s ] " , __func__ , 1748 , string , length );

DIAG_FILTER ( MIFI , HTTPCLI , 78 , DIAG_INFORMATION)  
 diagPrintf ( " get chunked_length [ %x ] [ %ld ] " , client->response . chunked_length , client->response . chunked_length );

DIAG_FILTER ( MIFI , HTTPCLI , 79 , DIAG_INFORMATION)  
 diagPrintf ( " %s not found , old len [ %d ] [ %s ] " , __func__ , old_length , client->response . chunked_string );

DIAG_FILTER ( MIFI , HTTPCLI , 168 , DIAG_INFORMATION)  
 diagPrintf ( " %s Failed , %d " , __func__ , res );

DIAG_FILTER ( MIFI , HTTPCLI , 80 , DIAG_INFORMATION)  
 diagPrintf ( " Perform with bad fd! " );

DIAG_FILTER ( MIFI , HTTPCLI , 181 , DIAG_INFORMATION)  
 diagPrintf ( " Cliet perform failed , cannnot malloc memory " );

DIAG_FILTER ( MIFI , HTTPCLI , 182 , DIAG_INFORMATION)  
 diagPrintf ( " Bad http request! " );

DIAG_FILTER ( MIFI , HTTPCLI , 81 , DIAG_INFORMATION)  
 diagPrintf ( " Perform ssl_client_init... " );

DIAG_FILTER ( MIFI , HTTPCLI , 183 , DIAG_INFORMATION)  
 diagPrintf ( " http client active stop , %s:%d! " , __func__ , 1821 );

DIAG_FILTER ( MIFI , HTTPCLI , 82 , DIAG_INFORMATION)  
 diagPrintf ( " username: %s " , client_t->server . name );

DIAG_FILTER ( MIFI , HTTPCLI , 83 , DIAG_INFORMATION)  
 diagPrintf ( " password: %s " , client_t->server . password );

DIAG_FILTER ( MIFI , HTTPCLI , 84 , DIAG_INFORMATION)  
 diagPrintf ( " User set use auth type is base " );

DIAG_FILTER ( MIFI , HTTPCLI , 85 , DIAG_INFORMATION)  
 diagPrintf ( " Server set use auth type is digest " );

DIAG_FILTER ( MIFI , HTTPCLI , 86 , DIAG_INFORMATION)  
 diagPrintf ( " Add cookie header: " );

DIAG_FILTER ( MIFI , HTTPCLI , 87 , DIAG_INFORMATION)  
 diagPrintf ( " http buffer overflow... " );

DIAG_FILTER ( MIFI , HTTPCLI , 88 , DIAG_INFORMATION)  
 diagPrintf ( " Need write header length %d " , pos );

DIAG_FILTER ( MIFI , HTTPCLI , 89 , DIAG_INFORMATION)  
 diagPrintf ( " Try send %d ( %d ) " , pos , client_t->server . fd );

DIAG_FILTER ( MIFI , HTTPCLI , 184 , DIAG_INFORMATION)  
 diagPrintf ( " Send socket failed ( %d ) @%s:%d! " , ret , __func__ , 1936 );

DIAG_FILTER ( MIFI , HTTPCLI , 185 , DIAG_INFORMATION)  
 diagPrintf ( " http client active stop , %s:%d! " , __func__ , 1942 );

DIAG_FILTER ( MIFI , HTTPCLI , 90 , DIAG_INFORMATION)  
 diagPrintf ( " Sent %d " , ret );

DIAG_FILTER ( MIFI , HTTPCLI , 91 , DIAG_INFORMATION)  
 diagPrintf ( " Need write data length %d " , pos );

DIAG_FILTER ( MIFI , HTTPCLI , 92 , DIAG_INFORMATION)  
 diagPrintf ( " Try send %d ( %d ) " , pos , client_t->server . fd );

DIAG_FILTER ( MIFI , HTTPCLI , 186 , DIAG_INFORMATION)  
 diagPrintf ( " Send socket failed ( %d ) @%s:%d! " , ret , __func__ , 1970 );

DIAG_FILTER ( MIFI , HTTPCLI , 187 , DIAG_INFORMATION)  
 diagPrintf ( " http client active stop , %s:%d! " , __func__ , 1976 );

DIAG_FILTER ( MIFI , HTTPCLI , 93 , DIAG_INFORMATION)  
 diagPrintf ( " Sent %d " , ret );

DIAG_FILTER ( MIFI , HTTPCLI , 94 , DIAG_INFORMATION)  
 diagPrintf ( " Clear old auth response! " );

DIAG_FILTER ( MIFI , HTTPCLI , http_client_perform_1929 , DIAG_INFORMATION)  
 diagPrintf ( " Record last inform time:%lu " , last_inform );

DIAG_FILTER ( MIFI , HTTPCLI , 95 , DIAG_INFORMATION)  
 diagPrintf ( " %s , begin to recv data... " , __FUNCTION__ );

DIAG_FILTER ( MIFI , HTTPCLI , 188 , DIAG_INFORMATION)  
 diagPrintf ( " http client active stop , %s:%d! " , __func__ , 2018 );

DIAG_FILTER ( MIFI , HTTPCLI , 189 , DIAG_INFORMATION)  
 diagPrintf ( " Socket receive failed ( %d ) tempdata_offset ( %d ) , active_stop [ %d ] ! " , ret , tempdata_offset , client_t->active_stop );

DIAG_FILTER ( MIFI , HTTPCLI , 96 , DIAG_INFORMATION)  
 diagPrintf ( " http client active stop , %s:%d! " , __func__ , 2043 );

DIAG_FILTER ( MIFI , HTTPCLI , 97 , DIAG_INFORMATION)  
 diagPrintf ( " data incomplete " );

DIAG_FILTER ( MIFI , HTTPCLI , 98 , DIAG_INFORMATION)  
 diagPrintf ( " Retry give up , tempdata_offset [ %d ] " , tempdata_offset );

DIAG_FILTER ( MIFI , HTTPCLI , 99 , DIAG_INFORMATION)  
 diagPrintf ( " check_http_chunked_length failed!!! " );

DIAG_FILTER ( MIFI , HTTPCLI , 100 , DIAG_INFORMATION)  
 diagPrintf ( " check_http_chunked_length data incomplete!!! " );

DIAG_FILTER ( MIFI , HTTPCLI , 101 , DIAG_INFORMATION)  
 diagPrintf ( " check_http_chunked_length res [ %d ] " , res );

DIAG_FILTER ( MIFI , HTTPCLI , 102 , DIAG_INFORMATION)  
 diagPrintf ( " check_http_chunked_length complete!!! " );

DIAG_FILTER ( MIFI , HTTPCLI , 1962 , DIAG_INFORMATION)  
 diagPrintf ( " http_client_perform: calling response_cb , start [ %x ] , size [ %d ] , n_items [ %d ] , private_cb_data [ %x ] " , start , ret , client_t->response . response_data_length , client_t->rsp_cb_data );

DIAG_FILTER ( MIFI , HTTPCLI , 103 , DIAG_INFORMATION)  
 diagPrintf ( " pos [ %ld ] , response_data_length [ %ld ] , chunked %ld " , pos , client_t->response . response_data_length , client_t->chunked );

DIAG_FILTER ( MIFI , HTTPCLI , 1982 , DIAG_INFORMATION)  
 diagPrintf ( " http_client_perform result code %d " , client_t->response . result_code );

DIAG_FILTER ( MIFI , HTTPCLI , 104 , DIAG_INFORMATION)  
 diagPrintf ( " Server Unauthorized. Try again... " );

DIAG_FILTER ( MIFI , HTTPCLI , 105 , DIAG_INFORMATION)  
 diagPrintf ( " Server has redirections " );

DIAG_FILTER ( MIFI , HTTPCLI , 190 , DIAG_INFORMATION)  
 diagPrintf ( " ERROR , get response code 307 , but not handle location " );

DIAG_FILTER ( MIFI , HTTPCLI , 2071 , DIAG_INFORMATION)  
 diagPrintf ( " free old ssl context context! " );

DIAG_FILTER ( MIFI , HTTPCLI , http_client_perform__2148 , DIAG_INFORMATION)  
 diagPrintf ( " closed directly ( for redirections ) " );

DIAG_FILTER ( MIFI , HTTPCLI , 191 , DIAG_INFORMATION)  
 diagPrintf ( " Try build socket failed ( for redirections ) " );

DIAG_FILTER ( MIFI , HTTPCLI , 193 , DIAG_INFORMATION)  
 diagPrintf ( " %s failed , [ %d ] " , __func__ , error );

DIAG_FILTER ( MIFI , HTTPCLI , 2028 , DIAG_INFORMATION)  
 diagPrintf ( " http_client_perform ret %d " , ret );

DIAG_FILTER ( MIFI , HTTPCLI , 106 , DIAG_INFORMATION)  
 diagPrintf ( " http_client_shutdown " );

DIAG_FILTER ( MIFI , HTTPCLI , 107 , DIAG_INFORMATION)  
 diagPrintf ( " http_client_shutdown: Clear ssl context " );

DIAG_FILTER ( MIFI , HTTPCLI , http_client_stop_2124 , DIAG_INFORMATION)  
 diagPrintf ( " http_client_stop " );

DIAG_FILTER ( MIFI , HTTPCLI , http_client_stop_2131 , DIAG_INFORMATION)  
 diagPrintf ( " Set tcp rst trigger failed ( %d ) " , ret );

DIAG_FILTER ( MIFI , HTTPCLI , 109 , DIAG_INFORMATION)  
 diagPrintf ( " %s: invalid http ctx " , __func__ );

DIAG_FILTER ( MIFI , HTTPCLI , 110 , DIAG_INFORMATION)  
 diagPrintf ( " %s: invalid http socket " , __func__ );

DIAG_FILTER ( MIFI , HTTPCLI , 111 , DIAG_INFORMATION)  
 diagPrintf ( " %s:http will be closed by server side " , __func__ );

DIAG_FILTER ( MIFI , HTTPCLI , 112 , DIAG_INFORMATION)  
 diagPrintf ( " %s:get IPPROTO_TCP state %d " , __func__ , info . tcpi_state );

