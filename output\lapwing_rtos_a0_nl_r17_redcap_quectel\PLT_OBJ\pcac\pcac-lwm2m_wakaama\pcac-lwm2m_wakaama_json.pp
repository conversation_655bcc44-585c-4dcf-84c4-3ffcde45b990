//PPC Version : V2.1.9.30
//PPL Source File Name : pcac-lwm2m_wakaama_json.ppp
//PPL Source File Name : L:/PLT/pcac/lwm2m_wakaama/data/json.c
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
typedef unsigned int size_t ;
typedef unsigned int size_t ;
typedef signed char int8_t ;
typedef signed short int int16_t ;
typedef signed int int32_t ;
typedef signed __int64 int64_t ;
typedef unsigned char uint8_t ;
typedef unsigned short int uint16_t ;
typedef unsigned int uint32_t ;
typedef unsigned __int64 uint64_t ;
typedef signed char int_least8_t ;
typedef signed short int int_least16_t ;
typedef signed int int_least32_t ;
typedef signed __int64 int_least64_t ;
typedef unsigned char uint_least8_t ;
typedef unsigned short int uint_least16_t ;
typedef unsigned int uint_least32_t ;
typedef unsigned __int64 uint_least64_t ;
typedef signed int int_fast8_t ;
typedef signed int int_fast16_t ;
typedef signed int int_fast32_t ;
typedef signed __int64 int_fast64_t ;
typedef unsigned int uint_fast8_t ;
typedef unsigned int uint_fast16_t ;
typedef unsigned int uint_fast32_t ;
typedef unsigned __int64 uint_fast64_t ;
typedef signed int intptr_t ;
typedef unsigned int uintptr_t ;
typedef signed long long intmax_t ;
typedef unsigned long long uintmax_t ;
typedef signed int ptrdiff_t ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef long double max_align_t ;
typedef unsigned int size_t ;
typedef unsigned int clock_t ;
typedef unsigned int time_t ;
typedef uint8_t ( *lwm2m_read_callback_t ) ( lwm2m_context_t * contextP , uint16_t instanceId , int * numDataP , lwm2m_data_t ** dataArrayP , lwm2m_object_t * objectP ) ;
typedef uint8_t ( *lwm2m_discover_callback_t ) ( lwm2m_context_t * contextP , uint16_t instanceId , int * numDataP , lwm2m_data_t ** dataArrayP , lwm2m_object_t * objectP ) ;
typedef uint8_t ( *lwm2m_write_callback_t ) ( lwm2m_context_t * contextP , uint16_t instanceId , int numData , lwm2m_data_t * dataArray , lwm2m_object_t * objectP , lwm2m_write_type_t writeType ) ;
typedef uint8_t ( *lwm2m_execute_callback_t ) ( lwm2m_context_t * contextP , uint16_t instanceId , uint16_t resourceId , uint8_t * buffer , int length , lwm2m_object_t * objectP ) ;
typedef uint8_t ( *lwm2m_create_callback_t ) ( lwm2m_context_t * contextP , uint16_t instanceId , int numData , lwm2m_data_t * dataArray , lwm2m_object_t * objectP ) ;
typedef uint8_t ( *lwm2m_delete_callback_t ) ( lwm2m_context_t * contextP , uint16_t instanceId , lwm2m_object_t * objectP ) ;
typedef uint8_t lwm2m_binding_t ;
typedef union _block_data_identifier_
 {
 char * uri ; / / resource string if block1
 int32_t mid ; / / mid of the last request if block2 eg the mid for the expected block
 } block_data_identifier_t ;
typedef void ( *lwm2m_result_callback_t ) ( lwm2m_context_t *contextP , uint16_t clientID , lwm2m_uri_t *uriP , int status ,
 block_info_t *block_info , lwm2m_media_type_t format , uint8_t *data ,
 size_t dataLength , void *userData ) ;
typedef void ( *lwm2m_transaction_callback_t ) ( lwm2m_context_t * contextP , lwm2m_transaction_t * transacP , void * message ) ;
typedef unsigned short wchar_t ;
