//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-fatfs_diskio.ppp
//PPL Source File Name : L:/PLT/softutil/fatfs/src/diskio.c
typedef unsigned int size_t ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
typedef unsigned int size_t ;
typedef unsigned int clock_t ;
typedef unsigned int time_t ;
typedef unsigned int UINT ;
typedef unsigned char BYTE ;
typedef unsigned short WORD ;
typedef unsigned short WCHAR ;
typedef unsigned long DWORD ;
typedef unsigned long long QWORD ;
typedef char TCHAR ;
typedef QWORD FSIZE_t ;
typedef DWORD LBA_t ;
typedef BYTE DSTATUS ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
typedef int ( tpl_parse_t ) ( UINT8 , const unsigned char * , unsigned ) ;
