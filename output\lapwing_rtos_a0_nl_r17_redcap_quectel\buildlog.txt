QUECTEL TARGET CMAKE CONFIG :quectel_build/config/feature_opt.cmake
-- Build WIFI TYPE AIC8800DW
-- VARIANT_LIST_EXT : PSM_SUPPORT;PPP_SUPPORT;WEBUI_SUPPORT;BIP_SUPPORT;CMUX_SUPPORT;WIFI_SUPPORT;TR069_SUPPORT;LFS_SUPPORT;ETHERNET_SUPPORT;MRD_SUPPROT_LZOP;IMSSMS_ONLY;PMICONKEY_SUPPORT;AT_OVER_AP_UART;LAPWING_SINGLE_SIM;AICWIFI
===============================================
VARIANT_LIST=NEZHA3;PLAT_AQUILAC;PLAT_TEST;PMD2NONE;PHS_SW_DEMO;PHS_SW_DEMO_PM;SRCNUCLEUS;FULL_SYSTEM;PDFLT;PLAT_TEST;NOAGPS;ACDATATUNNEL;SAC;PV2;DIAGOSHMEM;NVM;PHS_SW_DEMO_TTC_PM;PHS_SW_DEMO_TTC;LAPWING_MCU_DONGLE;DIET;2CHIP;12MBGB15;PV2COM;FLAVOR_COM;RELD;ACIPC;MIPS_RAM;ISPT;SILICON_PV2;SILICON_SEAGULL;SILICON_TTC_CORE_SEAGULL;PCAC_INCLUDE;OSA_NU_XSCALE;CPIMS;UARTNEWVERSION;DIP_CHN;DDR_STRESS;RF_TYPE_LAPWING;SOC_TYPE_LAPWING;REL7;PHSUPDATE;DIGRF3;CRL;UPGRADE_CRL_MODEM;CONFIG_L1C_SINGLE_SIM;L1WLAN_SUPPORT;PSM_SUPPORT;PPP_SUPPORT;WEBUI_SUPPORT;BIP_SUPPORT;CMUX_SUPPORT;WIFI_SUPPORT;TR069_SUPPORT;LFS_SUPPORT;ETHERNET_SUPPORT;MRD_SUPPROT_LZOP;IMSSMS_ONLY;PMICONKEY_SUPPORT;AT_OVER_AP_UART;LAPWING_SINGLE_SIM;AICWIFI
TARGET_DFLAGS_CPSDK_REMOULD=VOLTE_ENABLE;LFS_FILE_SYS;PSM_ENABLE;PPP_ENABLE;WEBUI_SUPPORT;CRANE_WEBUI_SUPPORT;TR069_SUPPORT;AICWIFI_SUPPORT;CONFIG_SDIO_ADMA;PROTECT_TX_ADMA;AICWIFI_100M;BIP_FUNC_SUPPORT;CMUX_ENABLE;WIFI_SUPPORT;MRD_SUPPROT_LZOP;ETHERNET_SUPPORT;PMICONKEY_ENABLE;AT_OVER_AP_UART;SINGLE_SIM;DDR_STRESS;DIP_CHN
TARGET_DFLAGS=ENV_XSCALE;NR_FPGA_BOARD;CA_LONG_IPC_MSG;NEZHA3;NEZHA3_1826;UPGRADE_PLMS;UPGRADE_PLMS_SR;LTE_GSMMULTIBCCH;GPLC_LTE_RSSI_SCAN;UPGRADE_PLMS_3G;UPGRADE_PLMS_L1;UPGRADE_FG_PLMS;FG_PLMS_URR;UPGRADE_L1A_FG_PLMS;UPGRADE_PLMS_STAGE_2;UPGRADE_MBCCH;MULTI_BCCH_READY_IND;URR_MRAT_ICS_SEARCH;UPGRADE_ICS;MRAT_NAS;UPGRADE_PLMS_SEARCH_API;ICS_MBCCH;ICS_MBCCH_2G_RSSI;PLAT_AQUILAC;PHS_SW_DEMO;PHS_SW_DEMO_TTC;PHS_SW_DEMO_TTC_PM;FULL_SYSTEM;_DDR_INIT_;_TAVOR_HARBELL_;UPGRADE_ARBEL_PLATFORM;_TAVOR_B0_SILICON_;TAVOR;FLAVOR_DUALCORE;DEBUG_D2_MOR_REG_RESEREVED_ENABLE;_DIAG_USE_COMMSTACK_;PM_DEBUG_MODE_ENABLED;PM_D2FULL_MODE;PM_EXT_DBG_INT_ARR;FEATURE_WB_AMR_PS;MACRO_FOR_LWG;HL_LWG;PLAT_TEST;_FDI_USE_OSA_;PLAT_USE_THREADX;ENABLE_MAC_TX_DATA_LOGGING;DISABLE_NVRAM_ACCESS;LTE_W_PS;UPGRADE_HERMON_DUAL;INTEL_2CHIP_PLAT;I_2CHIP_PLAT;FLAVOR_DDR12MB_GB1MB5;FEATURE_SHMEM;ACIPC_ENABLE_NEW_CALLBACK_MECHANISM;RELIABLE_DATA;MAP_NSS;ENABLE_ACIPC;_DATAOMSL_ENABLED_;USB_CABLE_DETECTION_VIA_PMIC;MIPS_TEST;MIPS_TEST_RAM;FLAVOR_DIET_RAM;NVM_INCLUDE;MSL_POOL_MEM;OSA_QUEUE_NAMES;_DIAG_DISABLE_USB_;OSA_NUCLEUS;OSA_USED;PM_D2NONE_MODE;MSL_INCLUDE;INTEL_HERMON_SAC;FLAVOR_COM;SILICON_PV2;SILICON_SEAGULL;SILICON_TTC_CORE_SEAGULL;PCAC_INCLUDE;CONFIG_KESTREL_A0;UPGRADE_LTE_ONLY;LAPWING;LAPWING_RTOS;LAPWING_MCU_DONGLE;PLAT_LAPWING;YMODEM_EEH_DUMP;ENABLE_ACIPC_BM_DATA_PATH;LWIP_IPNETBUF_SUPPORT;PCAC_AT_CLIENT;TBD;SUPPORT_CM_DUSTER_DIALER;TFT_QUERY_IND;RAM_DISK_FILE_SYSTEM;NTP;UART_NEW_VERSION;ENABLE_EXTEND_MY_AT;SUPPORT_PMIC_RTC;FAT32_FILE_SYSTEM;SOC_TYPE_LAPWING;RF_TYPE_LAPWING;L1_DCXO_ENABLED;FRBD_DSDS_L1;L1V_NEW_RSSI;TDL1C_SPY_ENABLE;DLM_TAVOR;_TAVOR_DIAG_;INTEL_UPGRADE_EE_HANDLER_SUPPORT;L1_DUAL_MODE;INTEL_UPGRADE_DUAL_RAT;INTEL_UPGRADE_GPRS_CIPHER_FLUSH;UPGRADE_ENHANCED_QUAD_BAND;UPGRDE_TAVOR_COMMUNICATION;RUN_WIRELESS_MODEM;MSL_INCLUDE;L1_SW_UPDATE_FOR_DIGRF;PHS_L1_SW_UPDATE_R7;UPGRADE_LTE;FRBD_CALIB_NVM;FRBD_AGC_CALIB;FRBD_FDT_CALIB;HSPA_MPR;CAPT_PARAMS_OPTIMIZE;WB_FAST_CALIBRATION_USE_LEGACY_TIMER;L1_RX_DIV_SUPPORT;ENABLE_OOS_HANDLING;TAVOR_D2_WB_L1_SUPPORT;L1_DDR_HIGH_FREQ;UPGRADE_DIGRF3G_SUPPORT;NO_APLP=0;INTEL_UPGRADE_UNIFIED_VOICE_TASK;INTEL_UPGRADE_R99;__TARGET_FEATURE_DOUBLEWORD;WHOLE_UMTS_STACK;USE_TTPCOM_CSR_BLUETOOTH_AUDIO_GAIN_CONTROL;UPGRADE_EDGE;UPGRADE_R4_FS1;INTEL_UPGRADE_GSM_CRL_IF;UPGRADE_EGPRS_M;INTEL_UPGRADE_EGPRS_M;INTEL_UPGRADE_RF_PARAMS_IN_CF_TDS;INTEL_UPGRADE_2SAMPLES_PER_SYMBOL;GPRS_MULTISLOT_CLASS=12;EGPRS_MULTISLOT_CLASS=12;MARVELL_UPGRADE_BSIC_REDESIGN;LTE_ONLY_MODE;REMOVE_GSM;REMOVE_WB;L1_WIFI_LOCATION;UPGRADE_NR_WIFI;SUPPORT_WIFI_DSP_RESET;VOLTE_ENABLE;LFS_FILE_SYS;PSM_ENABLE;PPP_ENABLE;WEBUI_SUPPORT;CRANE_WEBUI_SUPPORT;TR069_SUPPORT;AICWIFI_SUPPORT;CONFIG_SDIO_ADMA;PROTECT_TX_ADMA;AICWIFI_100M;BIP_FUNC_SUPPORT;CMUX_ENABLE;WIFI_SUPPORT;MRD_SUPPROT_LZOP;ETHERNET_SUPPORT;PMICONKEY_ENABLE;AT_OVER_AP_UART;SINGLE_SIM;DDR_STRESS;DIP_CHN
===============================================
===============================================
GLOBAL_PS_MODE_CONFIG=NL
GLOBAL_PS_MODE_NR_ENABLED=1
GLOBAL_PS_MODE_LTE_ENABLED=1
GLOBAL_PS_MODE_WCDMA_ENABLED=0
GLOBAL_PS_MODE_GSM_ENABLED=0
TARGET_PS_MODE_DFLAGS=GLOBAL_PS_MODE_NR_ENABLED;GLOBAL_PS_MODE_LTE_ENABLED
===============================================
PS_BLD_FILE=lapwing_nl_R17_audio_off.bld
PS_INC_FILE=modemhermon_nrlte.inc
PS_TARGET_CFLAGS=
L1C_CONFIG_FILE=inc_L1_Lapwing_NL_SS.cmake
===============================================
SCT_FILE_PRE_NAME=Seagull_40MB_GB15_LAPWING_RFS
TARGET_ARMLINK_OPT=--predefine="-DQUECTEL_PROJECT_CUST"
OUTPUT_NAME=LAPWING_RTOS_NL_R17_REDCAP
VARIANT_LIST_L1C=RF_TYPE_LAPWING;SOC_TYPE_LAPWING;REL7;PHSUPDATE;DIGRF3;CRL;UPGRADE_CRL_MODEM;CONFIG_L1C_SINGLE_SIM;L1WLAN_SUPPORT
INPUT_VARIANT_LIST=
VARIANT_LIST(contain VARIANT_LIST_L1C/INPUT_VARIANT_LIST)=NEZHA3;PLAT_AQUILAC;PLAT_TEST;PMD2NONE;PHS_SW_DEMO;PHS_SW_DEMO_PM;SRCNUCLEUS;FULL_SYSTEM;PDFLT;PLAT_TEST;NOAGPS;ACDATATUNNEL;SAC;PV2;DIAGOSHMEM;NVM;PHS_SW_DEMO_TTC_PM;PHS_SW_DEMO_TTC;LAPWING_MCU_DONGLE;DIET;2CHIP;12MBGB15;PV2COM;FLAVOR_COM;RELD;ACIPC;MIPS_RAM;ISPT;SILICON_PV2;SILICON_SEAGULL;SILICON_TTC_CORE_SEAGULL;PCAC_INCLUDE;OSA_NU_XSCALE;CPIMS;UARTNEWVERSION;DIP_CHN;DDR_STRESS;RF_TYPE_LAPWING;SOC_TYPE_LAPWING;REL7;PHSUPDATE;DIGRF3;CRL;UPGRADE_CRL_MODEM;CONFIG_L1C_SINGLE_SIM;L1WLAN_SUPPORT;PSM_SUPPORT;PPP_SUPPORT;WEBUI_SUPPORT;BIP_SUPPORT;CMUX_SUPPORT;WIFI_SUPPORT;TR069_SUPPORT;LFS_SUPPORT;ETHERNET_SUPPORT;MRD_SUPPROT_LZOP;IMSSMS_ONLY;PMICONKEY_SUPPORT;AT_OVER_AP_UART;LAPWING_SINGLE_SIM;AICWIFI
TARGET_VARIANT_FULL=PMD2NONE;PHS_SW_DEMO;PHS_SW_DEMO_PM;SRCNUCLEUS;FULL_SYSTEM;PDFLT;PLAT_TEST;PV2;DIAGOSHMEM;NVM
TARGET_ASMFLAGS=SHELL:--predefine "_NEZHA3_1826_ SETL {TRUE}";SHELL:--predefine "SEAGULL_CORTEX SETL {TRUE}";SHELL:--predefine "HL_LWG SETL {TRUE}";SHELL:--predefine "_PLAT_AQUILAC_ SETL {TRUE}";SHELL:--predefine "_PLAT_TEST_ SETL {TRUE}";SHELL:--predefine "L1_DUAL_MODE SETL {TRUE}";SHELL:--predefine "ASM_MIPS_TEST_RAM SETL {TRUE}";SHELL:--predefine "DATA_COLLECTOR_IMPL SETL {TRUE}";SHELL:--predefine "ISPT_OVER_SSP SETL {TRUE}";SHELL:--predefine "NO_APLP SETL {FALSE}";SHELL:--predefine "FLAVOR_COM SETL {TRUE}";SHELL:--predefine "SILICON_PV2 SETL {TRUE}";SHELL:--predefine "SILICON_SEAGULL SETL {TRUE}";SHELL:--predefine "SILICON_TTC_CORE_SEAGULL SETL {TRUE}";SHELL:--predefine "PCAC_INCLUDE SETL {TRUE}";--keep;--fpu;None;--apcs;/inter;--diag_suppress;1658
TARGET_CFLAGS=-g;--diag_suppress;2084,1,2,9,61,68,170,175,177,550,1296,2548,2795,4421,6319,9931,9933,3732,2803;--diag_error=warning;--gnu;--loose_implicit_cast;-DDATA_COLLECTOR_IMPL;-DISPT_OVER_SSP;-DDIAG_SSP_DOUBLE_BUFFER_USE_DYNAMIC_ALLOCATION;--feedback=L:/PLT/tavor/Arbel/build/feedbackLinkOpt.txt
TARGET_DFLAGS_L1C=SOC_TYPE_LAPWING;RF_TYPE_LAPWING;L1_DCXO_ENABLED;FRBD_DSDS_L1;L1V_NEW_RSSI;TDL1C_SPY_ENABLE;DLM_TAVOR;_TAVOR_DIAG_;INTEL_UPGRADE_EE_HANDLER_SUPPORT;L1_DUAL_MODE;INTEL_UPGRADE_DUAL_RAT;INTEL_UPGRADE_GPRS_CIPHER_FLUSH;UPGRADE_ENHANCED_QUAD_BAND;UPGRDE_TAVOR_COMMUNICATION;RUN_WIRELESS_MODEM;MSL_INCLUDE;L1_SW_UPDATE_FOR_DIGRF;PHS_L1_SW_UPDATE_R7;UPGRADE_LTE;FRBD_CALIB_NVM;FRBD_AGC_CALIB;FRBD_FDT_CALIB;HSPA_MPR;CAPT_PARAMS_OPTIMIZE;WB_FAST_CALIBRATION_USE_LEGACY_TIMER;L1_RX_DIV_SUPPORT;ENABLE_OOS_HANDLING;TAVOR_D2_WB_L1_SUPPORT;L1_DDR_HIGH_FREQ;UPGRADE_DIGRF3G_SUPPORT;NO_APLP=0;INTEL_UPGRADE_UNIFIED_VOICE_TASK;INTEL_UPGRADE_R99;__TARGET_FEATURE_DOUBLEWORD;WHOLE_UMTS_STACK;USE_TTPCOM_CSR_BLUETOOTH_AUDIO_GAIN_CONTROL;UPGRADE_EDGE;UPGRADE_R4_FS1;INTEL_UPGRADE_GSM_CRL_IF;UPGRADE_EGPRS_M;INTEL_UPGRADE_EGPRS_M;INTEL_UPGRADE_RF_PARAMS_IN_CF_TDS;INTEL_UPGRADE_2SAMPLES_PER_SYMBOL;GPRS_MULTISLOT_CLASS=12;EGPRS_MULTISLOT_CLASS=12;MARVELL_UPGRADE_BSIC_REDESIGN;LTE_ONLY_MODE;REMOVE_GSM;REMOVE_WB;L1_WIFI_LOCATION;UPGRADE_NR_WIFI;SUPPORT_WIFI_DSP_RESET
TARGET_DFLAGS_CPSDK_REMOULD=VOLTE_ENABLE;LFS_FILE_SYS;PSM_ENABLE;PPP_ENABLE;WEBUI_SUPPORT;CRANE_WEBUI_SUPPORT;TR069_SUPPORT;AICWIFI_SUPPORT;CONFIG_SDIO_ADMA;PROTECT_TX_ADMA;AICWIFI_100M;BIP_FUNC_SUPPORT;CMUX_ENABLE;WIFI_SUPPORT;MRD_SUPPROT_LZOP;ETHERNET_SUPPORT;PMICONKEY_ENABLE;AT_OVER_AP_UART;SINGLE_SIM;DDR_STRESS;DIP_CHN
TARGET_DFLAGS(contain TARGET_DFLAGS_L1C/TARGET_DFLAGS_CPSDK_REMOULD)=ENV_XSCALE;NR_FPGA_BOARD;CA_LONG_IPC_MSG;NEZHA3;NEZHA3_1826;UPGRADE_PLMS;UPGRADE_PLMS_SR;LTE_GSMMULTIBCCH;GPLC_LTE_RSSI_SCAN;UPGRADE_PLMS_3G;UPGRADE_PLMS_L1;UPGRADE_FG_PLMS;FG_PLMS_URR;UPGRADE_L1A_FG_PLMS;UPGRADE_PLMS_STAGE_2;UPGRADE_MBCCH;MULTI_BCCH_READY_IND;URR_MRAT_ICS_SEARCH;UPGRADE_ICS;MRAT_NAS;UPGRADE_PLMS_SEARCH_API;ICS_MBCCH;ICS_MBCCH_2G_RSSI;PLAT_AQUILAC;PHS_SW_DEMO;PHS_SW_DEMO_TTC;PHS_SW_DEMO_TTC_PM;FULL_SYSTEM;_DDR_INIT_;_TAVOR_HARBELL_;UPGRADE_ARBEL_PLATFORM;_TAVOR_B0_SILICON_;TAVOR;FLAVOR_DUALCORE;DEBUG_D2_MOR_REG_RESEREVED_ENABLE;_DIAG_USE_COMMSTACK_;PM_DEBUG_MODE_ENABLED;PM_D2FULL_MODE;PM_EXT_DBG_INT_ARR;FEATURE_WB_AMR_PS;MACRO_FOR_LWG;HL_LWG;PLAT_TEST;_FDI_USE_OSA_;PLAT_USE_THREADX;ENABLE_MAC_TX_DATA_LOGGING;DISABLE_NVRAM_ACCESS;LTE_W_PS;UPGRADE_HERMON_DUAL;INTEL_2CHIP_PLAT;I_2CHIP_PLAT;FLAVOR_DDR12MB_GB1MB5;FEATURE_SHMEM;ACIPC_ENABLE_NEW_CALLBACK_MECHANISM;RELIABLE_DATA;MAP_NSS;ENABLE_ACIPC;_DATAOMSL_ENABLED_;USB_CABLE_DETECTION_VIA_PMIC;MIPS_TEST;MIPS_TEST_RAM;FLAVOR_DIET_RAM;NVM_INCLUDE;MSL_POOL_MEM;OSA_QUEUE_NAMES;_DIAG_DISABLE_USB_;OSA_NUCLEUS;OSA_USED;PM_D2NONE_MODE;MSL_INCLUDE;INTEL_HERMON_SAC;FLAVOR_COM;SILICON_PV2;SILICON_SEAGULL;SILICON_TTC_CORE_SEAGULL;PCAC_INCLUDE;CONFIG_KESTREL_A0;UPGRADE_LTE_ONLY;LAPWING;LAPWING_RTOS;LAPWING_MCU_DONGLE;PLAT_LAPWING;YMODEM_EEH_DUMP;ENABLE_ACIPC_BM_DATA_PATH;LWIP_IPNETBUF_SUPPORT;PCAC_AT_CLIENT;TBD;SUPPORT_CM_DUSTER_DIALER;TFT_QUERY_IND;RAM_DISK_FILE_SYSTEM;NTP;UART_NEW_VERSION;ENABLE_EXTEND_MY_AT;SUPPORT_PMIC_RTC;FAT32_FILE_SYSTEM;SOC_TYPE_LAPWING;RF_TYPE_LAPWING;L1_DCXO_ENABLED;FRBD_DSDS_L1;L1V_NEW_RSSI;TDL1C_SPY_ENABLE;DLM_TAVOR;_TAVOR_DIAG_;INTEL_UPGRADE_EE_HANDLER_SUPPORT;L1_DUAL_MODE;INTEL_UPGRADE_DUAL_RAT;INTEL_UPGRADE_GPRS_CIPHER_FLUSH;UPGRADE_ENHANCED_QUAD_BAND;UPGRDE_TAVOR_COMMUNICATION;RUN_WIRELESS_MODEM;MSL_INCLUDE;L1_SW_UPDATE_FOR_DIGRF;PHS_L1_SW_UPDATE_R7;UPGRADE_LTE;FRBD_CALIB_NVM;FRBD_AGC_CALIB;FRBD_FDT_CALIB;HSPA_MPR;CAPT_PARAMS_OPTIMIZE;WB_FAST_CALIBRATION_USE_LEGACY_TIMER;L1_RX_DIV_SUPPORT;ENABLE_OOS_HANDLING;TAVOR_D2_WB_L1_SUPPORT;L1_DDR_HIGH_FREQ;UPGRADE_DIGRF3G_SUPPORT;NO_APLP=0;INTEL_UPGRADE_UNIFIED_VOICE_TASK;INTEL_UPGRADE_R99;__TARGET_FEATURE_DOUBLEWORD;WHOLE_UMTS_STACK;USE_TTPCOM_CSR_BLUETOOTH_AUDIO_GAIN_CONTROL;UPGRADE_EDGE;UPGRADE_R4_FS1;INTEL_UPGRADE_GSM_CRL_IF;UPGRADE_EGPRS_M;INTEL_UPGRADE_EGPRS_M;INTEL_UPGRADE_RF_PARAMS_IN_CF_TDS;INTEL_UPGRADE_2SAMPLES_PER_SYMBOL;GPRS_MULTISLOT_CLASS=12;EGPRS_MULTISLOT_CLASS=12;MARVELL_UPGRADE_BSIC_REDESIGN;LTE_ONLY_MODE;REMOVE_GSM;REMOVE_WB;L1_WIFI_LOCATION;UPGRADE_NR_WIFI;SUPPORT_WIFI_DSP_RESET;VOLTE_ENABLE;LFS_FILE_SYS;PSM_ENABLE;PPP_ENABLE;WEBUI_SUPPORT;CRANE_WEBUI_SUPPORT;TR069_SUPPORT;AICWIFI_SUPPORT;CONFIG_SDIO_ADMA;PROTECT_TX_ADMA;AICWIFI_100M;BIP_FUNC_SUPPORT;CMUX_ENABLE;WIFI_SUPPORT;MRD_SUPPROT_LZOP;ETHERNET_SUPPORT;PMICONKEY_ENABLE;AT_OVER_AP_UART;SINGLE_SIM;DDR_STRESS;DIP_CHN;QUECTEL_PROJECT_CUST;EXT_AT_MODEM_SUPPORT;TSEN_SUPPORT;USB_REMOTEWAKEUP;GLOBAL_PS_MODE_NR_ENABLED;GLOBAL_PS_MODE_LTE_ENABLED
===============================================
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/3g_ps.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/CRD.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/aslte.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/asnr.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/commond.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/dpd.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/nsab_d.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/sac.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/usbd.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/utd.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/agpstp.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/diag.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/drat.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/genlib.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-aam.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-ccu.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-commpm.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-intc.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-pm.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-pmu.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-rm.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-telephony_modem.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-timer.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/ims-ims_client.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/ims-ims_client_smsonly.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/l1wlan.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/ltel1a.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/os-threadx.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/pcac-ci_stub.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/pcac-dial.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/pcac-lwipv4v6.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/pcac-pcaclib.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/softutil-TickManager.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/softutil-aic_wifi_priv.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/softutil-csw_memory.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/softutil-fatsys.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/tavor-Arbel.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/volte.lib
[PREBUILD_DETECT][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/wlanhost.lib
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/3g_ps.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/CRD.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/aslte.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/asnr.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/commond.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/dpd.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/nsab_d.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/sac.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/usbd.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/utd.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/agpstp.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/diag.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/drat.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/genlib.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-aam.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-ccu.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-commpm.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-intc.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-pm.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-pmu.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-rm.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-telephony_modem.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-timer.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/ims-ims_client.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/ims-ims_client_smsonly.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/l1wlan.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/ltel1a.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/os-threadx.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/pcac-ci_stub.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/pcac-dial.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/pcac-lwipv4v6.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/pcac-pcaclib.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/softutil-TickManager.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/softutil-aic_wifi_priv.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/softutil-csw_memory.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/softutil-fatsys.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/tavor-Arbel.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/volte.pp
[PREBUILD_DETECT][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/wlanhost.pp
[PREBUILD_REMOVE][OBJ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/wlanhost.lib
[PREBUILD_REMOVE][PP ] L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/wlanhost.pp
[PREBUILD_DETECT][PS][BINGO]aslte.lib
[PREBUILD_DETECT][PS][BINGO]commond.lib
[PREBUILD_DETECT][PS][BINGO]dpd.lib
[PREBUILD_DETECT][PS][BINGO]nsab_d.lib
[PREBUILD_DETECT][PS][BINGO]usbd.lib
[PREBUILD_DETECT][PS][BINGO]utd.lib
[PREBUILD_DETECT][PS][BINGO]sac.lib
[PREBUILD_DETECT][PS][BINGO]asnr.lib
[PREBUILD_DETECT][PS] ALL LIB DETECTED
[PREBUILD][SKIP] 3g_ps.lib
[PREBUILD][SKIP] hop-pm.lib
[PREBUILD][SKIP] hop-rm.lib
[PREBUILD][SKIP] hop-aam.lib
[PREBUILD][SKIP] hop-commpm.lib
[PREBUILD][SKIP] hop-ccu.lib
[PREBUILD][SKIP] hop-timer.lib
[PREBUILD][SKIP] hop-pmu.lib
[PREBUILD][SKIP] hop-telephony_modem.lib
[PREBUILD][SKIP] hop-intc.lib
[PREBUILD][SKIP] CRD.lib
[PREBUILD][SKIP] genlib.lib
[PREBUILD][SKIP] softutil-csw_memory.lib
[PREBUILD][SKIP] softutil-TickManager.lib
[PREBUILD][SKIP] agpstp.lib
[PREBUILD][SKIP] os-threadx.lib
[PREBUILD][SKIP] diag.lib
[PREBUILD][SKIP] ltel1a.lib
[PREBUILD][SKIP] pcac-ci_stub.lib
[PREBUILD][SKIP] pcac-dial.lib
[PREBUILD][SKIP] pcac-lwipv4v6.lib
[PREBUILD][SKIP] pcac-pcaclib.lib
-- cmake config L:/PLT/quectel/app
-- cmake config L:/PLT/quectel/open/example
[PREBUILD][SKIP] ims-ims_client.lib
[PREBUILD][SKIP] ims-ims_client_smsonly.lib
[PREBUILD][SKIP] tavor-Arbel.lib
-- No build type selected, default to RelWithDebInfo
[PREBUILD][SKIP] drat.lib
[PREBUILD][SKIP] l1wlan.lib
39b326e58a43ceef81d56e7dcd92710b  D:/xy695/output/lapwing_rtos_a0_nl_r17_redcap_quectel/PLT_OBJ/hal.pp
37b03234a8cf7bb5b4b3b9dcbd986d64  D:/xy695/output/lapwing_rtos_a0_nl_r17_redcap_quectel/PLT_OBJ/CrossPlatformSW.pp
bfd9abdba962cf5086e3da2bbbf4c0f7  D:/xy695/output/lapwing_rtos_a0_nl_r17_redcap_quectel/PLT_OBJ/csw.pp
88c4b255cd5b45af8a8239bac156d77c  D:/xy695/output/lapwing_rtos_a0_nl_r17_redcap_quectel/PLT_OBJ/aud_sw.pp
ffc6b7b8ad9ff697dc182923641681de  D:/xy695/output/lapwing_rtos_a0_nl_r17_redcap_quectel/PLT_OBJ/softutil.pp
b53d702a642d53ac4190ccb23f00b5e3  D:/xy695/output/lapwing_rtos_a0_nl_r17_redcap_quectel/PLT_OBJ/os.pp
aac9cee89c089f994dd0f0b4207f1b8c  D:/xy695/output/lapwing_rtos_a0_nl_r17_redcap_quectel/PLT_OBJ/pcac.pp
930366fb655ed68187b111d7d98e8a85  D:/xy695/output/lapwing_rtos_a0_nl_r17_redcap_quectel/PLT_OBJ/quectel.pp
968a10496fd683c627db74be523781b9  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/3g_ps.pp
dd8538eef6aee14b40ee311d64091882  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/CRD.pp
b470d602b9a867723f5039d37efe3e3b  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/aslte.pp
d04b7b06a26065f30a15dbbe9b03d322  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/asnr.pp
ca770935248aa48dc12d9fd434af438f  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/commond.pp
b94ad5f5a4c354de8ec702a11b03640a  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/dpd.pp
370b61d238ec7a1a843d2f1bf04c1725  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/nsab_d.pp
981703441f9e0074265d99fb10ec8843  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/sac.pp
9ddb30608d90d903f74b25b519923b69  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/usbd.pp
dd1edc2e8834cc6f35afa83a20a292bf  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/PS/utd.pp
0201fbaf629b4f6aa0b428eb1ace4c00  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/agpstp.pp
9e7240e68c3e139a2a3c6334739371d3  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/diag.pp
664f1a679dab3c62ccfb60dd399325af  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/drat.pp
38b30fa502fb9930111afdf42dc610a7  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/genlib.pp
62724c388aa695ad6d88dd7000baf69d  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-aam.pp
0f2b690c20b9206dfbaaf71fbd4cb40a  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-ccu.pp
1587b13c05be273d105c9d05478f2984  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-commpm.pp
f61d8f51d04f93f6b0b05b348048698e  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-intc.pp
6ebf5ffd889618d9a1eb616c95c86e7c  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-pm.pp
82ca6c23f4bb843f65df9549069b0c69  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-pmu.pp
d506988a5855723e47d34d40b20e6045  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-rm.pp
363f849e3f277f656fd1cd92f2d43218  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-telephony_modem.pp
017e1e30b189652c0937856af47ab101  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/hop-timer.pp
473e91b4dcb708b6a37c38e81c08951f  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/ims-ims_client_smsonly.pp
b7bb1d29b4f9611371e1a0b31f3b2f55  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/l1wlan.pp
4fe0c1f4f162617fd7cac74b163155d7  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/ltel1a.pp
dee7f3255cf2ec5946196f48cffc4994  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/os-threadx.pp
1ba58193e4fd9bdc9f55d73b935a8b9e  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/pcac-ci_stub.pp
2814998ca39f40a4a3236ee52c92f186  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/pcac-dial.pp
dd3033cd49c71067a7dd360f166811e0  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/pcac-lwipv4v6.pp
462bb306bd2066348717bea33406312d  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/pcac-pcaclib.pp
ab8fbf6332f6e8b3fa9c1557ece98760  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/softutil-TickManager.pp
3ceb2051d4137977637be63cbc6d26e5  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/softutil-aic_wifi_priv.pp
c1688b1213d62c7bd941768a6344fb46  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/softutil-csw_memory.pp
e088d4deb73017e2544b2fc92f116afa  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/softutil-fatsys.pp
6cb6e999f5a78abef26db97fa0014efa  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/tavor-Arbel.pp
7738ac1399ccf47fd0129e8b15e23565  L:/PREBUILD/lapwing_rtos_a0_nl_r17_redcap/volte.pp
-- Configuring done
-- Generating done
-- Build files have been written to: D:/xy695/output/lapwing_rtos_a0_nl_r17_redcap_quectel
[35mHOST PROCESSOR = 12[0m
[1/4][2.121s]D:/xy695/output/lapwing_rtos_a0_nl_r17_redcap_quectel/ALL_PP_FILES_HASH_RECORDER.new
[2/4][2.275s]diagDB.c_GEN
[3/3][43.335s]Linking C executable bin\LAPWING_RTOS_NL_R17_REDCAP.elf
|INPUT   |bin/LAPWING_RTOS_NL_R17_REDCAP.cponly_lzma.bin                        
|MARK    |NAME    |EXEADDR .LOADADDR.LENGTH  .CPZLADDR|COMPRESS STASTIC       |
|--------|--------|--------.--------.--------.--------|-----------------------|
|RW_CPZ_1|CODE_PS |06003000.06003000.000e6fb0.06003000|000e6fb0->0007b000
|RW_CPZ_2|CODEPSB |060f3000.060f3000.00100bec.0607e000|00100bec->0005d000
|RW_CPZ_3|CODEPSC |061fb000.061fb000.000dcca0.060db000|000dcca0->00075000
|RW_CPZ_4|CODEPSD |062db000.062db000.000e89c8.06150000|000e89c8->00079000
|RW_CPZ_5|CODEPSE |063cb000.063cb000.00062204.061c9000|00062204->00035000
|RW_CPZ_6|CODE_PL |0643b000.0643b000.000ca694.061fe000|000ca694->00069000
|RW_CPZ_7|CODEPLB |06513000.06513000.001127e0.06267000|001127e0->0008f000
|RW_CPZ_8|CODEPLC |06685000.06685000.000e818c.062f6000|000e818c->00076000
|RW_CPZ_9|CODEPLD |06775000.06775000.001700b8.0636c000|001700b8->00065000
|RW_CPZ_A|REMAIN_ |06a52000.069f3e84.00049e7c.063d1000|00049e7c->00012000
|RW_CPZ_B|NON_OTA |068ea000.068ea000.0005832c.063e3000|0005832c->0002c000
|RW_CPZ_C|CODEAPP |0699a000.0699a000.00059e84.0640f000|00059e84->00033000
|--------|--------|--------.--------.--------.--------|-----------------------|
|                                                    |0x00a3dd00 -> 0x00442000|
|                                                    |10.241(MB) ->  4.258(MB)|
|-----------------------------------------------------------------------------|
|INPUT   |bin/LAPWING_RTOS_NL_R17_REDCAP.cponly.bin                             
|MARK    |NAME    |EXEADDR .LOADADDR.LENGTH  .CPZLADDR|COMPRESS STASTIC       |
|--------|--------|--------.--------.--------.--------|-----------------------|
|RW_CPZ_1|CODE_PS |06003000.06003000.000e6fb0.06003000|00000000->000e7000
|RW_CPZ_2|CODEPSB |060f3000.060f3000.00100bec.060ea000|00000000->00101000
|RW_CPZ_3|CODEPSC |061fb000.061fb000.000dcca0.061eb000|00000000->000dd000
|RW_CPZ_4|CODEPSD |062db000.062db000.000e89c8.062c8000|00000000->000e9000
|RW_CPZ_5|CODEPSE |063cb000.063cb000.00062204.063b1000|00000000->00063000
|RW_CPZ_6|CODE_PL |0643b000.0643b000.000ca694.06414000|00000000->000cb000
|RW_CPZ_7|CODEPLB |06513000.06513000.001127e0.064df000|00000000->00113000
|RW_CPZ_8|CODEPLC |06685000.06685000.000e818c.065f2000|00000000->000e9000
|RW_CPZ_9|CODEPLD |06775000.06775000.001700b8.066db000|00000000->00171000
|RW_CPZ_A|REMAIN_ |06a52000.069f3e84.00049e7c.0684c000|00000000->0004a000
|RW_CPZ_B|NON_OTA |068ea000.068ea000.0005832c.06896000|00000000->00059000
|RW_CPZ_C|CODEAPP |0699a000.0699a000.00059e84.068ef000|00000000->0005a000
|--------|--------|--------.--------.--------.--------|-----------------------|
|                                                    |0x00a3dd00 -> 0x00949000|
|                                                    |10.241(MB) ->  9.285(MB)|
|-----------------------------------------------------------------------------|
|bin/ReliableData.bin|
|L:/PLT/ims/ims_dualsim_lteonly_sms/Settings_preferences.xml|
D:/xy695/output/lapwing_rtos_a0_nl_r17_redcap_quectel/bin
|-- [4.8M May 27 15:40]  LAPWING_RTOS_NL_R17_DIAG.mdb
|-- [ 27M May 27 15:42]  LAPWING_RTOS_NL_R17_NVM.mdb
|-- [9.3M May 27 15:50]  LAPWING_RTOS_NL_R17_REDCAP.cponly.bin
|-- [4.3M May 27 15:50]  LAPWING_RTOS_NL_R17_REDCAP.cponly_lzma.bin
|-- [171M May 27 15:50]  LAPWING_RTOS_NL_R17_REDCAP.elf
|-- [9.2M May 27 15:50]  LAPWING_RTOS_NL_R17_REDCAP.elf.map
|-- [9.9M May 27 15:39]  LAPWING_RTOS_NL_R17_REDCAP.hsiupdlibdev.i
|-- [ 19M May 27 15:39]  LAPWING_RTOS_NL_R17_REDCAP.mdb.txt
|-- [ 64K May 27 15:50]  ReliableData.bin
|-- [684K May 27 15:50]  feedbackLinkOpt.txt.update
`-- [ 166 May 27 15:39]  lapwing_rtos_a0_nl_r17_redcap_quectel_config.cmake
