//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_IMEI.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/IMEI.c
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
typedef union {
 rw_region_item compress_rw_region_list [ 64 ] ;

 UINT8 filer [ 2048 ] ;
 } LOADTABLE_AREA_RW_CPZ_INFO ;
//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_stdlib_util.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/stdlib_util.c
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_RmiFunctions.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/RmiFunctions.c
typedef unsigned int size_t ;
typedef va_list __gnuc_va_list ;
typedef unsigned int size_t ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0 = 0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 , GPIO_PIN_33 , GPIO_PIN_34 , GPIO_PIN_35 , GPIO_PIN_36 , GPIO_PIN_37 , GPIO_PIN_38 , GPIO_PIN_39 ,	 
	 
 GPIO_PIN_40 , GPIO_PIN_41 , GPIO_PIN_42 , GPIO_PIN_43 , GPIO_PIN_44 , GPIO_PIN_45 , GPIO_PIN_46 , GPIO_PIN_47 ,	 
 GPIO_PIN_48 , GPIO_PIN_49 , GPIO_PIN_50 , GPIO_PIN_51 , GPIO_PIN_52 , GPIO_PIN_53 , GPIO_PIN_54 , GPIO_PIN_55 ,	 
 GPIO_PIN_56 , GPIO_PIN_57 , GPIO_PIN_58 , GPIO_PIN_59 , GPIO_PIN_60 , GPIO_PIN_61 , GPIO_PIN_62 , GPIO_PIN_63 ,	 
	 
 GPIO_PIN_64 , GPIO_PIN_65 , GPIO_PIN_66 , GPIO_PIN_67 , GPIO_PIN_68 , GPIO_PIN_69 , GPIO_PIN_70 , GPIO_PIN_71 ,	 
 GPIO_PIN_72 , GPIO_PIN_73 , GPIO_PIN_74 , GPIO_PIN_75 , GPIO_PIN_76 , GPIO_PIN_77 , GPIO_PIN_78 , GPIO_PIN_79 ,	 
 GPIO_PIN_80 , GPIO_PIN_81 , GPIO_PIN_82 , GPIO_PIN_83 , GPIO_PIN_84 , GPIO_PIN_85 , GPIO_PIN_86 , GPIO_PIN_87 ,	 
 GPIO_PIN_88 , GPIO_PIN_89 , GPIO_PIN_90 , GPIO_PIN_91 , GPIO_PIN_92 , GPIO_PIN_93 , GPIO_PIN_94 , GPIO_PIN_95 ,	 
	 
 GPIO_PIN_96 , GPIO_PIN_97 , GPIO_PIN_98 , GPIO_PIN_99 , GPIO_PIN_100 , GPIO_PIN_101 , GPIO_PIN_102 , GPIO_PIN_103 ,	 
 GPIO_PIN_104 , GPIO_PIN_105 , GPIO_PIN_106 , GPIO_PIN_107 , GPIO_PIN_108 , GPIO_PIN_109 , GPIO_PIN_110 , GPIO_PIN_111 ,	 
 GPIO_PIN_112 , GPIO_PIN_113 , GPIO_PIN_114 , GPIO_PIN_115 , GPIO_PIN_116 , GPIO_PIN_117 , GPIO_PIN_118 , GPIO_PIN_119 ,	 
 GPIO_PIN_120 , GPIO_PIN_121 , GPIO_PIN_122 , GPIO_PIN_123 , GPIO_PIN_124 , GPIO_PIN_125 , GPIO_PIN_126 , GPIO_PIN_127 ,	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 short data0 ;	 
 short data1 ;	 
 short data2 ;	 
 } DummyStruct;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 DUMMY_ZERO = 0 ,	 
 DUMMY_ONE ,	 
 DUMMY_TWO ,	 
 DUMMY_THREE	 
 } DummyEnum;

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , restartCmd 
 void restartCmd ( void* pv ) 
 {	 
 // doRestart ( ) ;	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , ArmRead16 
 void armRead16 ( void* p ) 
 {	 
 UINT32 addr=* ( UINT32* ) p ;	 
 UINT32 val ;	 
 val=* ( UINT16* ) addr ;	 
DIAG_FILTER ( SW_PLAT , CONTROL , ArmRead16Reply , DIAG_INFORMATION)  
 diagPrintf ( " Read from %lx ( 16 bit ) : %x " , addr , val );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , ArmRead32 
 void armRead32 ( void* p ) 
 {	 
 UINT32 addr=* ( UINT32* ) p ;	 
 UINT32 val ;	 
 val=* ( UINT32* ) addr ;	 
DIAG_FILTER ( SW_PLAT , CONTROL , ArmRead32Reply , DIAG_INFORMATION)  
 diagPrintf ( " Read from %lx ( 32 bit ) : %lx " , addr , val );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , ArmWrite16 
 void armWrite16 ( void* p ) 
 {	 
 UINT32 addr=* ( UINT32* ) p ;	 
 UINT32 val=* ( ( UINT32* ) p+1 ) ;	 
 * ( UINT16* ) addr=val ;	 
DIAG_FILTER ( SW_PLAT , CONTROL , ArmWrite16Reply , DIAG_INFORMATION)  
 diagPrintf ( " Written to %lx ( 16 bit ) : %x " , addr , val );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , ArmWrite32 
 void armWrite32 ( void* p ) 
 {	 
 UINT32 addr=* ( UINT32* ) p ;	 
 UINT32 val=* ( ( UINT32* ) p+1 ) ;	 
 * ( UINT32* ) addr=val ;	 
DIAG_FILTER ( SW_PLAT , CONTROL , ArmWrite32Reply , DIAG_INFORMATION)  
 diagPrintf ( " Written to %lx ( 32 bit ) : %lx " , addr , val );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , memTest16 
 void memTest16 ( void* p ) 
 {	 
 UINT32 addr=* ( UINT32* ) p ;	 
 UINT32 size=* ( ( UINT32* ) p+1 ) ;	 
 UINT32 i , val ;	 
 UINT32 failureCount=0 ;	 
 UINT16 shift=0xa5a5 ;	 
	 
 if ( size>0x800000 ) size=0x800000 ;	 
 else size&=~1 ; // even	 
 addr&=~1 ;	 
	 
DIAG_FILTER ( SW_PLAT , CONTROL , memTestReplyWrite , DIAG_INFORMATION)  
 diagTextPrintf ( " Mem Test: Write " );

	 
	 
 for ( i=0 ; i<size ; i+=2 )	 
 {		 
 * ( UINT16* ) ( addr+i ) = ( UINT16 ) ( addr+i+shift ) ;		 
 }	 
	 
DIAG_FILTER ( SW_PLAT , CONTROL , memTestReplyRead , DIAG_INFORMATION)  
 diagTextPrintf ( " Mem Test: Read Back " );

	 
	 
 for ( i=0 ; i<size ; i+=2 )	 
 {		 
 val=* ( UINT16* ) ( addr+i ) ;		 
 if ( val!= ( UINT16 ) ( addr+i+shift ) ) failureCount++ ;		 
 }	 
	 
DIAG_FILTER ( SW_PLAT , CONTROL , memTestReply , DIAG_INFORMATION)  
 diagPrintf ( " Mem Test Done , %d failures " , failureCount );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , AAAP , getPlpVersion 
 void getPlpVersion ( void *p ) 
 {	 
 // extern const char plpVersion [ ] ;	 
 const char plpVersion [ ] = " Not available " ;	 
DIAG_FILTER ( SW_PLAT , AAAP , plpVersionReport , DIAG_INFORMATION)  
 diagPrintf ( " PLP version: %s " , plpVersion );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , AAAP , holdPLP 
 void holdPLPCmd ( void *p ) 
 {	 
 // holdPLP ( ) ;	 
DIAG_FILTER ( SW_PLAT , AAAP , holdPLP_reply , DIAG_INFORMATION)  
 diagTextPrintf ( " PLP is held in reset " );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , malloc 
 void exportedMalloc ( UINT32 *p , int len ) 
 {	 
 if ( len>=sizeof ( UINT32 ) )	 
 {		 
 UINT32 size=*p ;		 
 UINT32 addr= ( UINT32 ) malloc ( size ) ;		 
DIAG_FILTER ( SW_PLAT , CONTROL , malloc_reply , DIAG_INFORMATION)  
 diagPrintf ( " Allocated: 0x%lx " , addr );

		 
		 
 /*mischecked by coverity*/		 
 /*coverity [ leaked_storage ] */		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , free 
 void exportedFree ( UINT32 *p , int len ) 
 {	 
 if ( len>=sizeof ( UINT32 ) )	 
 {		 
 UINT32 addr=*p ;		 
 free ( ( void * ) addr ) ;		 
DIAG_FILTER ( SW_PLAT , CONTROL , free_reply , DIAG_INFORMATION)  
 diagPrintf ( " Deallocated: 0x%lx " , addr );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , ICacheParityErrorCount 
 void ICacheParityErrorCount ( void *p ) 
 {	 
 extern UINT32 ICParityCount ;	 
DIAG_FILTER ( SW_PLAT , CONTROL , ICacheParityErrorCount_rep , DIAG_INFORMATION)  
 diagPrintf ( " IC parity error numer: %d " , ICParityCount );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , GetSteppingID 
 void expGetSteppingID ( void *p ) 
 {	 
 CPU_Version id=GetCpuVersion ( ) ;	 
 char name [ ] = " Bx " ;	 
 switch ( id )	 
 {		 
 case CPU_HERMON_B0 :		 
 case CPU_HERMON_B1 :		 
 case CPU_HERMON_B2 :		 
 case CPU_HERMON_B3 :		 
 name [ 1 ] = ' 0 ' + ( id-CPU_HERMON_B0 ) ;		 
 break ;		 
 default:		 
 strcpy ( name , " ?? " ) ;		 
 }	 
DIAG_FILTER ( SW_PLAT , CONTROL , GetSteppingID_rep , DIAG_INFORMATION)  
 diagPrintf ( " Hermon stepping is: %s " , name );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , writeUINT8 
 void writeUINT8 ( volatile void *base ) 
 {	 
 UINT32 *desAdd = ( UINT32* ) ( * ( UINT32* ) ( base ) ) ;	 
 UINT8 val2Write = * ( ( UINT8* ) base+4 ) ;	 
	 
 *desAdd = ( *desAdd & 0xffffff00 ) | val2Write ;	 
	 
DIAG_FILTER ( SW_PLAT , CONTROL , writeUINT8 , writeUINT8a)  
 diagPrintf ( " address %lx contain %lx " , desAdd , *desAdd );

	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , readUINT32burst 
 void readUINT32 ( volatile void *base , void *byteAmount ) 
 {	 
 UINT32 numOfUINT32 , i , *baseAdd ;	 
 numOfUINT32 = * ( ( UINT32* ) base+1 ) ;	 
 baseAdd = ( UINT32* ) ( * ( UINT32* ) base ) ;	 
	 
 if ( ( UINT32 ) byteAmount==4 )	 
 numOfUINT32=1 ; /* in case only address is given*/	 
	 
 for ( i=numOfUINT32 ; i>0 ; i-- )	 
 {		 
DIAG_FILTER ( SW_PLAT , CONTROL , readUINT32 , readUINT32a)  
 diagPrintf ( " address %lx contain %lx " , baseAdd , *baseAdd );

		 
 baseAdd++ ;		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , readUINT16burst 
 void readUINT16 ( volatile void *base , void *byteAmount ) 
 {	 
 UINT32 numOfUINT16 , i ;	 
 UINT16 *baseAdd ;	 
	 
 numOfUINT16 = * ( ( UINT32* ) base+1 ) ;	 
 baseAdd = ( UINT16 * ) ( * ( UINT32* ) base ) ;	 
	 
 if ( ( UINT32 ) byteAmount==4 )	 
 numOfUINT16=1 ; /* in case only address is given*/	 
	 
 for ( i=numOfUINT16 ; i>0 ; i-- )	 
 {		 
DIAG_FILTER ( SW_PLAT , CONTROL , readUINT16 , readUINT16a)  
 diagPrintf ( " address %lx contain %x " , baseAdd , *baseAdd );

		 
 baseAdd++ ;		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , readUINT8burst 
 void readUINT8 ( volatile void *base , void *byteAmount ) 
 {	 
 UINT32 numOfUINT8 , i ;	 
 UINT8 *baseAdd ;	 
	 
 numOfUINT8 = * ( ( UINT32* ) base+1 ) ;	 
 baseAdd = ( UINT8 * ) ( * ( UINT32* ) base ) ;	 
	 
 if ( ( UINT32 ) byteAmount==4 )	 
 numOfUINT8=1 ; /* in case only address is given*/	 
	 
 for ( i=numOfUINT8 ; i>0 ; i-- )	 
 {		 
DIAG_FILTER ( SW_PLAT , CONTROL , readUINT8 , readUINT8a)  
 diagPrintf ( " address %lx contain %x " , baseAdd , *baseAdd );

		 
 baseAdd++ ;		 
 }	 
 }

//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_fstdio_wrap.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/fstdio_wrap.c
typedef unsigned int size_t ;
typedef unsigned int size_t ;
typedef FILE* ( *fopen_t ) ( const char * , const char * ) ;
typedef int ( *fclose_t ) ( FILE* ) ;
typedef size_t ( *fread_t ) ( void * , size_t , size_t , FILE* ) ;
typedef size_t ( *fwrite_t ) ( const void * , size_t , size_t , FILE* ) ;
typedef int ( *fprintf_t ) ( FILE* , const char * , ... ) ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_FatSysWrapper.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/FatSysWrapper.c
typedef unsigned int size_t ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef UINT32 Attribs_Mmu ;
typedef void ( * mmuNotifyEventFunc ) ( MMU_ERROR_STATUS mmuErrorHandle ) ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0 = 0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 , GPIO_PIN_33 , GPIO_PIN_34 , GPIO_PIN_35 , GPIO_PIN_36 , GPIO_PIN_37 , GPIO_PIN_38 , GPIO_PIN_39 ,	 
	 
 GPIO_PIN_40 , GPIO_PIN_41 , GPIO_PIN_42 , GPIO_PIN_43 , GPIO_PIN_44 , GPIO_PIN_45 , GPIO_PIN_46 , GPIO_PIN_47 ,	 
 GPIO_PIN_48 , GPIO_PIN_49 , GPIO_PIN_50 , GPIO_PIN_51 , GPIO_PIN_52 , GPIO_PIN_53 , GPIO_PIN_54 , GPIO_PIN_55 ,	 
 GPIO_PIN_56 , GPIO_PIN_57 , GPIO_PIN_58 , GPIO_PIN_59 , GPIO_PIN_60 , GPIO_PIN_61 , GPIO_PIN_62 , GPIO_PIN_63 ,	 
	 
 GPIO_PIN_64 , GPIO_PIN_65 , GPIO_PIN_66 , GPIO_PIN_67 , GPIO_PIN_68 , GPIO_PIN_69 , GPIO_PIN_70 , GPIO_PIN_71 ,	 
 GPIO_PIN_72 , GPIO_PIN_73 , GPIO_PIN_74 , GPIO_PIN_75 , GPIO_PIN_76 , GPIO_PIN_77 , GPIO_PIN_78 , GPIO_PIN_79 ,	 
 GPIO_PIN_80 , GPIO_PIN_81 , GPIO_PIN_82 , GPIO_PIN_83 , GPIO_PIN_84 , GPIO_PIN_85 , GPIO_PIN_86 , GPIO_PIN_87 ,	 
 GPIO_PIN_88 , GPIO_PIN_89 , GPIO_PIN_90 , GPIO_PIN_91 , GPIO_PIN_92 , GPIO_PIN_93 , GPIO_PIN_94 , GPIO_PIN_95 ,	 
	 
 GPIO_PIN_96 , GPIO_PIN_97 , GPIO_PIN_98 , GPIO_PIN_99 , GPIO_PIN_100 , GPIO_PIN_101 , GPIO_PIN_102 , GPIO_PIN_103 ,	 
 GPIO_PIN_104 , GPIO_PIN_105 , GPIO_PIN_106 , GPIO_PIN_107 , GPIO_PIN_108 , GPIO_PIN_109 , GPIO_PIN_110 , GPIO_PIN_111 ,	 
 GPIO_PIN_112 , GPIO_PIN_113 , GPIO_PIN_114 , GPIO_PIN_115 , GPIO_PIN_116 , GPIO_PIN_117 , GPIO_PIN_118 , GPIO_PIN_119 ,	 
 GPIO_PIN_120 , GPIO_PIN_121 , GPIO_PIN_122 , GPIO_PIN_123 , GPIO_PIN_124 , GPIO_PIN_125 , GPIO_PIN_126 , GPIO_PIN_127 ,	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

typedef union {
 rw_region_item compress_rw_region_list [ 64 ] ;

 UINT8 filer [ 2048 ] ;
 } LOADTABLE_AREA_RW_CPZ_INFO ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef unsigned int size_t ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef unsigned char BYTE ;
typedef unsigned short int WORD ;
typedef unsigned long DWORD ;
typedef WORD * WORD_PTR ;
typedef DWORD * DWORD_PTR ;
typedef BYTE BYTE_BITMASK ;
typedef VOID_PTR * VOID_PTR_PTR ;
typedef WORD FLASH_DATA_WIDTH ;
typedef char FDI_TCHAR ;
typedef OSASemaRef SEM_ID ;
typedef int SEM_STATUS ;
typedef SEM_MTX * SEM_MTX_ID ;
typedef WORD IDTYPE ;
typedef DATA_LOOKUP * DATA_LOOKUP_PTR ;
typedef DATA_LOCATION * DATA_LOC_PTR ;
typedef OPEN_PARAM * OPEN_STREAM_PTR ;
typedef COMMAND * COMMAND_PTR ;
typedef UNIT_HEADER * UNIT_HDR_PTR ;
typedef MULTI_INSTANCE * MULTI_INST_PTR ;
typedef BLOCK_INFO * BLK_INFO_PTR ;
typedef COMMAND_CONTROL * CMD_CNTRL_PTR ;
typedef WORD FILE_ID ;
typedef unsigned int size_t ;
typedef unsigned int clock_t ;
typedef unsigned int time_t ;
typedef va_list __gnuc_va_list ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_INT_IFC ,	 
 DIAG_EXT_IFC	 
 } DIAG_IF_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_UART_SPEED_921600 ,	 
 DIAG_UART_SPEED_1842000 ,	 
 DIAG_UART_SPEED_3000000 ,	 
 DIAG_UART_DISABLE ,	 
 } DIAG_UART_SPEED_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_MEM_ENABLE ,	 
 DIAG_MEM_DISABLE	 
 } DIAG_MEM_SWITCH;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_DEV_USB ,	 
 DIAG_DEV_SD ,	 
 DIAG_DEV_FS ,	 
 DIAG_DEV_UART ,	 
 DIAG_DEV_SPI ,	 
 DIAG_DEV_NONE	 
 } DIAG_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 SULOG_DEV_USB ,	 
 SULOG_DEV_SD	 
 } SULOG_DEV_TYPE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 DIAG_IF_TYPE useIntIf ; /* use internal interface or external interface */	 
 DIAG_UART_SPEED_TYPE useHighSpeedUART ; /* high speed UART ( 460800 ) or low speed UART ( 115200 ) */	 
 DIAG_MEM_SWITCH diagMemSwitch ;	 
 DIAG_DEV_TYPE diagDevType ;	 
 SULOG_DEV_TYPE sulogDevType ;	 
 BOOL sdlAutoDelete ;	 
 } diagCfgDataS;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 dbID ;	 
 UINT32 filterBitLength ;	 
 UINT32 reserved [ 6 ] ;	 
 } DIAG_Nvm_Filter_File_Header_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MASS_STORAGE_DISABLE = 0x0 ,	 
 MASS_STORAGE_ENABLE = 0x1	 
 } MassStorage_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* MIFI driver*/	 
 USB_GENERIC_MIFI_DRIVER = 0x0 ,	 
 USB_MARVELL_MIFI_DRIVER = 0x1 ,	 
 USB_ASR_MIFI_DRIVER = 0x2 ,	 
 USB_GENERIC_MOD_DRIVER = 0x10 ,	 
 USB_GENERIC_MOD_ECM_DRIVER = 0x12 ,	 
 USB_DIAG_UAC_DRIVER = 0x14 ,	 
 USB_RNDIS_ONLY_DRIVER = 0x15 ,	 
	 
 /* MBIM driver*/	 
 USB_MBIM_ONLY_DRIVER = 0x40 ,	 
 USB_MBIM_GENERIC_DRIVER = 0x41 ,	 
 USB_MBIM_MAX_DRIVER = 0x4F ,	 
	 
 /* Other driver*/	 
 USB_CDROM_ONLY_DRIVER = 0x91 ,	 
 USB_CDROM_DIAG_DRIVER = 0x92 ,	 
 USB_DIAG_ONLY_DRIVER = 0x93 ,	 
 USB_MODEM_ONLY_DRIVER = 0x94 ,	 
 USB_MODEM_DIAG_DRIVER = 0x95 ,	 
	 
 USB_MAX_DRIVER = 0xFF	 
 } Usb_driver_typeE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_AUTO_INSTALL_DISABLE = 0x0 ,	 
 USB_AUTO_INSTALL_ENABLE = 0x1	 
 } Usb_auto_install_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_OS_DETECT_DISABLE = 0x0 ,	 
 USB_OS_DETECT_ENABLE = 0x1	 
 } Usb_OS_detect_type;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Usb_driver_typeE usb_driver ;	 
 MassStorage_ConfigE mass_storage ;	 
 Usb_auto_install_type auto_install ;	 
 Usb_OS_detect_type os_detect ;	 
 } Usb_DriverS;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 YMODEM_DISABLE = 0x0 ,	 
 YMODEM_ENABLE = 0x1	 
 } Ymodem_Dump_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Ymodem_Dump_config config ;	 
 } Ymodem_Dump_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MBIM_ONLY_DESP = 0x0 ,	 
 MBIM_ATDIAG_DESP = 0x1	 
 } MBIM_DESP_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 MBIM_DESP_config config ;	 
 } MBIM_CFG_type;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 EEH_DUMP_DEV_USB_SD ,	 
 EEH_DUMP_DEV_SPI	 
 } EEH_DUMP_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PMU_POR = 1 ,	 
 PMU_EMR ,	 
 PMU_WDTR = ( PMU_EMR+2 )	 
 } PMU_LastResetStatus;

typedef UINT8 UART_Activity ;
//ICAT EXPORTED STRUCT 
 typedef struct /* This is structure of the UART Configuration */ 
 {	 
 UART_OpMode opMode ; /* fifo mode , non fifo mode or DMA for basic interface*/	 
 UART_TriggerLevel triggerLevel ; /* the trigger level interrupt on 1 , 8 , 16 , 32 */	 
 UART_BaudRates baudRate ; /* the rate of the transmit and the receive up to 111520 ( default - 9600 ) .*/	 
 UART_WordLen numDataBits ; /* 5 , 6 , 7 , or 8 number of data bits in the UART data frame ( default - 8 ) . */	 
 UART_StopBits stopBits ; /* 1 , 1.500000 or 2 stop bits in the UART data frame ( default - 1 ) . */	 
 UART_ParityTBits parityBitType ; /* Even , Odd or no-parity bit type in the UART data frame ( default - Non ) . */	 
 UART_InterfaceType interfaceType ; /* number of interface that the UART driver supplies ( default - UART_IF_TYPE_L2 ) */	 
 BOOL modemSignal ; /* enable operate modem - TRUE , disable modem - FALSE */	 
 BOOL flowControl ; /* enable Auto flow Control - TRUE , disable Auto flow Control - FALSE */	 
 UINT8 sleepMode ; /* enable sleep mode - TRUE , more fine control - see UARTSleepMode enum */	 
 BOOL auto_baud ; /* enable auto_baud , auto-baud-rate detection within the UART ( default - FALSE ) */	 
 UART_SIRConfigure sirIrDA ;	 
 } UARTConfiguration;

//ICAT EXPORTED ENUM 
 typedef enum // change the order -1 to + 
 {	 
 UART_RC_OK = 1 , /* 1 - no errors */	 
	 
 UART_RC_PORT_NUM_ERROR = -100 , /* -100 - Error in the UART port number */	 
 UART_RC_NO_DATA_TO_READ , /* -99 - Eror no data to read from the FIFO UART */	 
 UART_RC_ILLEGAL_BAUD_RATE , /* -98 - Error in the UART Bayd Rate */	 
 UART_RC_UART_PARITY_BITS_ERROR , /* -97 - Error in parity bit */	 
 UART_RC_UART_ONE_STOP_BIT_ERROR , /* -96 - Error in one stop bit */	 
 UART_RC_ONE_HALF_OR_TWO_STOP_BIT_ERROR , /* -95 - Error in two stop bit */	 
 UART_RC_BAD_INTERFACE_TYPE , /* -94 - Error in the Interface Type */	 
 UART_RC_UART_NOT_AVAILABLE , /* -93 - Error in try to open UART that is open */	 
 UART_RC_NO_DATA_TO_WRITE , /* -92 - Error No data to writ the len = 0 */	 
 UART_RC_NOT_ALL_BYTE_WRITTEN , /* -91 - Error Not all the Byte write to the UART FIFO */	 
 UART_RC_ISR_ALREADY_BIND , /* -90 - Error try to bind ISR for Basic Interface */	 
 UART_RC_WRONG_ISR_UNBIND , /* -89 - Error in the UnBind ISR for Basic Interface */	 
 UART_RC_FIFO_NOT_EMPTY , /* -88 - Error , the UART FIFO not empty */	 
 UART_RC_UART_OPEN , /* -87 - Error try chance the configurr when the UART open */	 
 UART_RC_GPIO_ERR , /* -86 - Error in the Configure of the GPIO */	 
 UART_RC_IRDA_CONFIG_ERR /* -85 - Illegal IrDA configuration */	 
 } UART_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAM_LOG = 0x00 ,	 
 USB_RAM_LOG = 0x01 ,	 
 MAX_RAM_LOG = 0x02	 
 } Ram_Log_Type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAMLOG_ENABLE = 0x00 ,	 
 USB_RAMLOG_ENABLE = 0x01	 
 } RamLog_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 LOG_DISABLE = 0x0 ,	 
 UART_LOG_ENABLE = 0x1 ,	 
 ACAT_LOG_ENABLE = 0x2	 
 } Log_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MSG_DISABLE = 0x0 ,	 
 ACAT_MSG_ENABLE = 0x1	 
 } Msg_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RTI_LOG_DISABLE = 0x0 ,	 
 RTI_LOG_ENABLE = 0x1	 
 } RTI_ConfigE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Log_ConfigE log_cfg ;	 
 Msg_ConfigE msg_cfg ;	 
 RTI_ConfigE rti_cfg ;	 
 RamLog_ConfigE ramlog ;	 
 } Log_ConfigS;

typedef void ( *UARTNotifyInterrupt ) ( UART_Port ) ;
typedef void ( *UsbLogPrint_t ) ( const char * , ... ) ;
typedef volatile unsigned long VUINT32_T ;
typedef unsigned long UINT32_T ;
typedef volatile unsigned int VUINT_T ;
typedef unsigned int UINT_T ;
typedef int INT_T ;
typedef unsigned short UINT16_T , USHORT ;
typedef volatile unsigned short VUINT16_T ;
typedef unsigned char UINT8_T ;
typedef char INT8_T ;
typedef unsigned int UINT , *PUINT ;
typedef unsigned long long UINT64 , *PUINT64 ;
typedef unsigned char UCHAR , BYTE , *PUCHAR ;
typedef int INT , *PINT ;
typedef long long INT64 , *PINT64 ;
typedef char CHAR , *PCHAR ;
typedef unsigned int size_t ;
typedef volatile UINT VUINT , *PVUINT ;
typedef volatile UINT64 VUINT64 , *PVUINT64 ;
typedef volatile unsigned int VUINT32 , *PVUINT32 ;
typedef volatile UINT16 VUINT16 , *PVUINT16 ;
typedef volatile UINT8 VUINT8 , *PVUINT8 ;
typedef volatile UCHAR VUCHAR , *PVUCHAR ;
typedef volatile INT VINT , *PVINT ;
typedef volatile INT64 VINT64 , *PVINT64 ;
typedef volatile int VINT32 , *PVINT32 ;
typedef volatile INT16 VINT16 , *PVINT16 ;
typedef volatile char VINT8 , *PVINT8 ;
typedef volatile CHAR VCHAR , *PVCHAR ;
typedef UINT16 WORD ;
typedef signed char int8_t ;
typedef unsigned char uint8_t ;
typedef signed short int16_t ;
typedef unsigned short uint16_t ;
typedef signed int int32_t ;
typedef unsigned int uint32_t ;
typedef signed long long int int64_t ;
typedef unsigned long long int uint64_t ;
typedef UINT32 rti_uint32_t ;
typedef UINT64 rti_uint64_t ;
typedef TX_THREAD* rti_thread_t ;
//ICAT EXPORTED STRUCT 
 typedef struct {	 
 UINT32 product_id ;	 
 UINT8 data [ 20 ] ;	 
 } InfoForBoardTracking_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 rti_mode_none = 0x00 ,	 
 rti_check_mode = 0x01 ,	 
 rti_timer_mode = 0x02 ,	 
 rti_log2acat_mode = 0x03 ,	 
 rti_psoff_mode = 0x04 ,	 
 rti_uarttrace_mode = 0x05 ,	 
 rti_rfuarttest_mode = 0xFF ,	 
	 
 rti_urtlog_mode = 0x100 ,	 
 rti_usbtrace_mode = 0x101 ,	 
 rti_muxtrace_mode = 0x102 ,	 
 rti_fsyslog_mode = 0x103 ,	 
 rti_mode_max = 0xFFFF	 
 } rti_mode;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_DISABLE=0 ,	 
 RTI_EN_VER1=1 ,	 
 RTI_EN_VER2=2	 
 } RTI_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_QUE_DISABLE=0 ,	 
 RTI_QUE_ENABLE=1	 
 } RTI_QUE_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_EVT_DISABLE=0 ,	 
 RTI_EVT_ENABLE=1	 
 } RTI_EVT_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 TIME_OUT_3MS=0x62 ,	 
 TIME_OUT_4MS=0x83 ,	 
 TIME_OUT_5MS=0xA4 ,	 
 TIME_OUT_6MS=0xC4 ,	 
 TIME_OUT_MAX=0xFF	 
 } Timeout_Threshold;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 RTI_TYPE rtiType ;	 
 RTI_QUE_TYPE rtiQueType ;	 
 RTI_EVT_TYPE rtiEvtType ;	 
	 
 int rtiChange ;	 
 int rtiHT ;	 
 int rtiLT ;	 
	 
 int modeChange ;	 
 int modeHT ;	 
 int modeLT ;	 
	 
 Timeout_Threshold Timeout ;	 
 rti_mode rtiMode ;	 
 } RTICfg_t;

typedef signed char SINT8 ;
typedef signed short SINT16 ;
typedef signed long SINT32 ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef float FLOAT32 ;
typedef double FLOAT64 ;
typedef long double FLOAT80 ;
typedef signed short TCHAR ;
typedef TCHAR * TCHAR_PTR ;
typedef SINT8 * SINT8_PTR ;
typedef SINT16 * SINT16_PTR ;
typedef SINT32 * SINT32_PTR ;
typedef UINT8 * UINT8_PTR ;
typedef UINT16 * UINT16_PTR ;
typedef UINT32 * UINT32_PTR ;
typedef FLOAT32 * FLOAT32_PTR ;
typedef FLOAT64 * FLOAT64_PTR ;
typedef FLOAT80 * FLOAT80_PTR ;
typedef void * VOID_PTR ;
typedef UINT8 BOOL ;
typedef UINT8 BOOLEAN ;
typedef UINT32 FFS_FileDirTime ;
typedef FFS_FileDirTime* FFS_FileDirTimePtr ;
typedef OSATaskRef OS_Task_fdi ;
typedef void ( *OS_TaskEntryPtr ) ( UINT32 , void* ) ;
typedef OSASemaRef OS_Semaphore ;
typedef _OS_Mutex* OS_Mutex_fdi ;
typedef UINT32 OS_IRQ_Disable_Func ( void ) ;
typedef UINT32 OS_IRQ_Enable_Func ( UINT32 irq_level ) ;
typedef void OS_Mem_Free_Func ( void * ptr ) ;
typedef void * OS_Mem_Malloc_Func ( UINT32 size ) ;
typedef BOOL OS_MutexCreate_Func ( OS_Mutex_fdi * mutex_ptr ,
 BOOL owned ,
 const TCHAR * name ) ;
typedef BOOL OS_MutexInit_Func ( OS_Mutex_fdi * mutex_ptr ,
 OS_Mutex_fdi mtx_str ,
 BOOL owned ,
 const TCHAR * name ) ;
typedef BOOL OS_MutexClear_Func ( OS_Mutex_fdi * mutex_ptr ) ;
typedef BOOL OS_MutexDestroy_Func ( OS_Mutex_fdi* mutex_ptr ) ;
typedef BOOL OS_MutexPend_Func ( OS_Mutex_fdi* mutex_ptr , UINT32 timeout ) ;
typedef BOOL OS_MutexRelease_Func ( OS_Mutex_fdi* mutex_ptr ) ;
typedef BOOL OS_SemCreate_Func ( OS_Semaphore * sem_ptr ,
 UINT32 initial_count ,
 const TCHAR * name ) ;
typedef BOOL OS_SemDestroy_Func ( OS_Semaphore * sem_ptr ) ;
typedef BOOL OS_SemPend_Func ( const OS_Semaphore * sem_ptr , UINT32 timeout ) ;
typedef BOOL OS_SemRelease_Func ( const OS_Semaphore * sem_ptr ) ;
typedef BOOL OS_Task_Create_Func ( OS_Task_fdi * task_ptr ,
 const TCHAR * task_name ,
 OS_TaskEntryPtr func_ptr ,
 UINT8 argc ,
 void * argv ) ;
typedef BOOL OS_Task_Destroy_Func ( const OS_Task_fdi * task_ptr ) ;
typedef OS_Task_fdi OS_Task_GetCurrent_Func ( void ) ;
typedef UINT16 OS_Task_GetPriority_Func ( const OS_Task_fdi * task_ptr ) ;
typedef BOOL OS_Task_SetPriority_Func ( const OS_Task_fdi * task_ptr ,
 UINT16 priority ) ;
typedef PLAT_OEM_SubArrayInfo *PLAT_OEM_SubArrayInfoPtr ;
typedef UINT32 LockStatus ;
typedef UINT32 FLASH_ERROR ;
typedef UINT32 FLASH_VolumeHandle ;
typedef UINT32 *FLASH_VolumeHandlePtr ;
typedef signed int ptrdiff_t ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef long double max_align_t ;
typedef signed char D_INT8 ;
typedef unsigned char D_UINT8 ;
typedef short D_INT16 ;
typedef unsigned short D_UINT16 ;
typedef long D_INT32 ;
typedef unsigned long D_UINT32 ;
typedef long long D_INT64 ;
typedef unsigned long long D_UINT64 ;
typedef D_INT32 D_INTPTR ;
typedef D_UINT32 D_UINTPTR ;
typedef D_UINT32 DCLSTATUS ;
typedef D_UINT16 D_BOOL ;
typedef char D_CHAR ;
typedef unsigned char D_UCHAR ;
typedef D_UINT8 D_BUFFER ;
typedef D_UINT64 DCLHRTIMESTAMP ;
typedef D_UINT32 DCLTIMESTAMP ;
typedef void ( *PFNDCLOUTPUTSTRING ) ( void * pUserData , const char * pszString ) ;
typedef char tfsChar ;
typedef int DLP_size_t ;
typedef unsigned int DLP_mode_t ;
typedef long DLP_off_t ;
typedef unsigned short DLP_dev_t ;
typedef unsigned long DLP_ino_t ;
typedef unsigned short DLP_nlink_t ;
typedef unsigned short DLP_uid_t ;
typedef unsigned short DLP_gid_t ;
typedef unsigned long DLP_time_t ;
typedef int DLP_blksize_t ;
typedef unsigned long DLP_blkcnt_t ;
typedef void DLP_DIR ;
typedef int GFSSize_t ;
typedef long GFSOff_t ;
typedef void GFS_DIR ;
typedef int GFS_size_t ;
typedef unsigned int GFS_mode_t ;
typedef long GFS_off_t ;
typedef unsigned short GFS_dev_t ;
typedef unsigned long GFS_ino_t ;
typedef unsigned short GFS_nlink_t ;
typedef unsigned short GFS_uid_t ;
typedef unsigned short GFS_gid_t ;
typedef unsigned long GFS_time_t ;
typedef unsigned long GFS_blkcnt_t ;
typedef unsigned int GFS_fileID ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
typedef void ( *free_cb ) ( void *buf ) ;
typedef void ( *complete_cb ) ( pUsb3Dwc pProps , Usb3DwcEp *pEp , Usb3DwcReqQ *pReq , UINT32 xfer ) ;
typedef unsigned int time_t ;
typedef UINT_T ( *InitFlash_F ) ( UINT8_T FlashNum , FlashBootType_T FlashBootType , UINT8_T *P_DefaultPartitionNum ) ;
typedef UINT_T ( *FinalizeFlash_F ) ( FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *ReadFlash_F ) ( UINT_T FlashOffset , UINT_T LocalBuffer , UINT_T Size , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *WriteFlash_F ) ( UINT_T FlashOffset , UINT_T LocalBuffer , UINT_T Size , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *EraseFlash_F ) ( UINT_T FlashOffset , UINT_T Size , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *ResetFlash_F ) ( FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *WipeFlash_F ) ( FlashBootType_T FlashBootType ) ;
typedef void ( *ChangePartition_F ) ( UINT_T PartitionNum , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *WriteOTP_F ) ( UINT_T FlashOffset , UINT_T LocalBuffer , UINT_T Size ) ;
typedef UINT_T ( *ReadOTP_F ) ( UINT_T FlashOffset , UINT_T LocalBuffer , UINT_T Size ) ;
typedef UINT_T ( *LockOTP_F ) ( void ) ;
typedef UINT_T ( *ConvertToLogicalAddr_F ) ( UINT_T FlashLocation , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *DataSearchFunc_F ) ( UINT_T *pResult , void *pResvHeader , UINT_T pkgID ) ;
typedef UINT_T ( *DataSearchFindNext_F ) ( UINT_T *pResult ) ;
typedef UINT_T ( *FlashInitAfterTimDownload_F ) ( void *pResvHeader , DataSearchFunc_F , DataSearchFunc_F , DataSearchFindNext_F , UINT_T *myPkgIDs ) ;
typedef unsigned int time_t ;
typedef unsigned char BOOL ;
typedef signed char BYTE8 ;
typedef unsigned char UBYTE8 ;
typedef short HWD16 ;
typedef long WORD32 ;
typedef unsigned short UHWD16 ;
typedef unsigned long UWORD32 ;
typedef unsigned long long ULLONG64 ;
typedef char S8 ;
typedef HWD16 S16 ;
typedef WORD32 S32 ;
typedef UBYTE8 U8 ;
typedef UHWD16 U16 ;
typedef UWORD32 U32 ;
typedef void ( *apTYPE_rCallback ) ( UWORD32 ) ;
typedef WORD32 apError ;
typedef UWORD32 fatTYPE_tSector ;
typedef UWORD32 fatTYPE_tCluster ;
typedef UWORD32 fatTYPE_tEntry ;
typedef UWORD32 fatTYPE_tSize ;
typedef signed char int8_t ;
typedef signed short int int16_t ;
typedef signed int int32_t ;
typedef signed __int64 int64_t ;
typedef unsigned char uint8_t ;
typedef unsigned short int uint16_t ;
typedef unsigned int uint32_t ;
typedef unsigned __int64 uint64_t ;
typedef signed char int_least8_t ;
typedef signed short int int_least16_t ;
typedef signed int int_least32_t ;
typedef signed __int64 int_least64_t ;
typedef unsigned char uint_least8_t ;
typedef unsigned short int uint_least16_t ;
typedef unsigned int uint_least32_t ;
typedef unsigned __int64 uint_least64_t ;
typedef signed int int_fast8_t ;
typedef signed int int_fast16_t ;
typedef signed int int_fast32_t ;
typedef signed __int64 int_fast64_t ;
typedef unsigned int uint_fast8_t ;
typedef unsigned int uint_fast16_t ;
typedef unsigned int uint_fast32_t ;
typedef unsigned __int64 uint_fast64_t ;
typedef signed int intptr_t ;
typedef unsigned int uintptr_t ;
typedef signed long long intmax_t ;
typedef unsigned long long uintmax_t ;
typedef unsigned short wchar_t ;
typedef uint32_t lfs_size_t ;
typedef uint32_t lfs_off_t ;
typedef int32_t lfs_ssize_t ;
typedef int32_t lfs_soff_t ;
typedef uint32_t lfs_block_t ;
DIAG_FILTER ( FDI , fatsys , F_Open_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI fopen start: %s: mode: %s " , filename_ptr , mode );

DIAG_FILTER ( FDI , fatsys , F_Open_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI fopen succeed: %s: handle: %d " , filename_ptr , handle );

DIAG_FILTER ( FDI , fatsys , F_Open_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI fopen error: %s , mode: %s " , filename_ptr , mode );

DIAG_FILTER ( FDI , fatsys , F_Close_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI close start , handle: %d " , stream );

DIAG_FILTER ( FDI , fatsys , F_Close_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI close error , handle: %d , errCode: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_Close_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI close succeed , handle: %d " , stream );

DIAG_FILTER ( FDI , fatsys , F_read_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI read error , handle: %d , errCode: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_readEx_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI read error , handle: %d , errCode: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_write_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI write start , handle: %d , size: %ld " , stream , element_size*count );

DIAG_FILTER ( FDI , fatsys , F_write_error0 , DIAG_INFORMATION)  
 diagPrintf ( " FDI write error , handle: %d , element size is 0 " , stream );

DIAG_FILTER ( FDI , fatsys , F_write_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI write error , handle: %d , errCode: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_write_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI write succeed , handle: %d , actual size: %ld " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_writeEx_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI write start , handle: %d , size: %ld " , stream , element_size*count );

DIAG_FILTER ( FDI , fatsys , F_writeEx_zero_size , DIAG_INFORMATION)  
 diagPrintf ( " FDI write error , handle: %d , size is 0 " , stream );

DIAG_FILTER ( FDI , fatsys , F_writeEx_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI write error , handle: %d , errCode: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_writeEx_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI write succeed , handle: %d , actual size: %ld " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_seek_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI seek start , handle: %d , offset:%d , wherefrom: %d " , stream , offset , wherefrom );

DIAG_FILTER ( FDI , fatsys , F_seek_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI seek error , handle: %d , errCode: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_seek_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI seek succeed , handle: %d , res: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_tell_start , DIAG_INFORMATION)  
 diagPrintf ( " Fdi tell start , handle: %d " , stream );

DIAG_FILTER ( FDI , fatsys , F_tell_succeed , DIAG_INFORMATION)  
 diagPrintf ( " Fdi tell succeed , handle: %d " , stream );

DIAG_FILTER ( FDI , fatsys , F_Opendir , DIAG_INFORMATION)  
 diagPrintf ( " Fdi open dir error: %s , status: %d " , dir_name , handle );

DIAG_FILTER ( FDI , fatsys , F_Opendir_succeed , DIAG_INFORMATION)  
 diagPrintf ( " Fdi open dir succeed: %s: handle: %d " , dir_name , handle );

DIAG_FILTER ( FDI , fatsys , F_Closedir , DIAG_INFORMATION)  
 diagPrintf ( " Fdi close dir error: handle:%d , status: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_Closedir_succeed , DIAG_INFORMATION)  
 diagPrintf ( " Fdi close dir succeed:handle: %d , res: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_findfirst_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI findfirst start , filename_ptr: %s " , filename_ptr );

DIAG_FILTER ( FDI , fatsys , F_findfirst_error_0 , DIAG_INFORMATION)  
 diagPrintf ( " FDI findfirst error , ERR_PARAM " );

DIAG_FILTER ( FDI , fatsys , F_findfirst_error_1 , DIAG_INFORMATION)  
 diagPrintf ( " Fdi findfirst error 1 , errCode: %d " , ret );

DIAG_FILTER ( FDI , fatsys , F_findfirst_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI findfirst succeed , the found file: %s " , gFullFileName );

DIAG_FILTER ( FDI , fatsys , FDI_Stat_failure , DIAG_INFORMATION)  
 diagPrintf ( " FDI_Stat failure , ret: %d " , ret );

DIAG_FILTER ( FDI , fatsys , F_findnext_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI findnext start " );

DIAG_FILTER ( FDI , fatsys , F_findnext_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI findnext error errCode: %d " , rec );

DIAG_FILTER ( FDI , fatsys , F_findnext_finished , DIAG_INFORMATION)  
 diagPrintf ( " FDI find process finished: the final file: %s " , gFullFileName );

DIAG_FILTER ( FDI , fatsys , F_findnext_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI findnext succeed: the found file: %s , isDir %d " , gFullFileName , Stat.st_mode & fatTYPE_DIR );

DIAG_FILTER ( FDI , fatsys , F_remove_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI remove start , file: %s " , filename_ptr );

DIAG_FILTER ( FDI , fatsys , F_remove_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI remove error , file: %s , errCode: %d " , filename_ptr , res );

DIAG_FILTER ( FDI , fatsys , F_remove_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI remove succeed , file: %s " , filename_ptr );

DIAG_FILTER ( FDI , fatsys , F_rename_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI rename start , file: %s " , name );

DIAG_FILTER ( FDI , fatsys , F_rename_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI rename error , file: %s , errCode: %d " , name , res );

DIAG_FILTER ( FDI , fatsys , F_rename_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI rename succeed , file: %s " , name );

DIAG_FILTER ( FDI , fatsys , F_mkdir_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI mkdir start , file: %s " , dir_name );

DIAG_FILTER ( FDI , fatsys , F_mkdir_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI mkdir succeed , file: %s " , dir_name );

DIAG_FILTER ( FDI , fatsys , F_eof_start , DIAG_INFORMATION)  
 diagPrintf ( " Fdi eof start , handle: %d " , stream );

DIAG_FILTER ( FDI , fatsys , F_eof_succeed , DIAG_INFORMATION)  
 diagPrintf ( " Fdi eof succeed , handle: %d , res: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_access_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI access start , file: %s , mode %lu " , path , mode );

DIAG_FILTER ( FDI , fatsys , F_access_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI access succeed , file: %s " , path );

//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_FDI_Transport.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/FDI_Transport.c
typedef unsigned int size_t ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef unsigned int size_t ;
typedef UINT32 Attribs_Mmu ;
typedef void ( * mmuNotifyEventFunc ) ( MMU_ERROR_STATUS mmuErrorHandle ) ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0 = 0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 , GPIO_PIN_33 , GPIO_PIN_34 , GPIO_PIN_35 , GPIO_PIN_36 , GPIO_PIN_37 , GPIO_PIN_38 , GPIO_PIN_39 ,	 
	 
 GPIO_PIN_40 , GPIO_PIN_41 , GPIO_PIN_42 , GPIO_PIN_43 , GPIO_PIN_44 , GPIO_PIN_45 , GPIO_PIN_46 , GPIO_PIN_47 ,	 
 GPIO_PIN_48 , GPIO_PIN_49 , GPIO_PIN_50 , GPIO_PIN_51 , GPIO_PIN_52 , GPIO_PIN_53 , GPIO_PIN_54 , GPIO_PIN_55 ,	 
 GPIO_PIN_56 , GPIO_PIN_57 , GPIO_PIN_58 , GPIO_PIN_59 , GPIO_PIN_60 , GPIO_PIN_61 , GPIO_PIN_62 , GPIO_PIN_63 ,	 
	 
 GPIO_PIN_64 , GPIO_PIN_65 , GPIO_PIN_66 , GPIO_PIN_67 , GPIO_PIN_68 , GPIO_PIN_69 , GPIO_PIN_70 , GPIO_PIN_71 ,	 
 GPIO_PIN_72 , GPIO_PIN_73 , GPIO_PIN_74 , GPIO_PIN_75 , GPIO_PIN_76 , GPIO_PIN_77 , GPIO_PIN_78 , GPIO_PIN_79 ,	 
 GPIO_PIN_80 , GPIO_PIN_81 , GPIO_PIN_82 , GPIO_PIN_83 , GPIO_PIN_84 , GPIO_PIN_85 , GPIO_PIN_86 , GPIO_PIN_87 ,	 
 GPIO_PIN_88 , GPIO_PIN_89 , GPIO_PIN_90 , GPIO_PIN_91 , GPIO_PIN_92 , GPIO_PIN_93 , GPIO_PIN_94 , GPIO_PIN_95 ,	 
	 
 GPIO_PIN_96 , GPIO_PIN_97 , GPIO_PIN_98 , GPIO_PIN_99 , GPIO_PIN_100 , GPIO_PIN_101 , GPIO_PIN_102 , GPIO_PIN_103 ,	 
 GPIO_PIN_104 , GPIO_PIN_105 , GPIO_PIN_106 , GPIO_PIN_107 , GPIO_PIN_108 , GPIO_PIN_109 , GPIO_PIN_110 , GPIO_PIN_111 ,	 
 GPIO_PIN_112 , GPIO_PIN_113 , GPIO_PIN_114 , GPIO_PIN_115 , GPIO_PIN_116 , GPIO_PIN_117 , GPIO_PIN_118 , GPIO_PIN_119 ,	 
 GPIO_PIN_120 , GPIO_PIN_121 , GPIO_PIN_122 , GPIO_PIN_123 , GPIO_PIN_124 , GPIO_PIN_125 , GPIO_PIN_126 , GPIO_PIN_127 ,	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

typedef union {
 rw_region_item compress_rw_region_list [ 64 ] ;

 UINT8 filer [ 2048 ] ;
 } LOADTABLE_AREA_RW_CPZ_INFO ;
typedef unsigned char BYTE ;
typedef unsigned short int WORD ;
typedef unsigned long DWORD ;
typedef WORD * WORD_PTR ;
typedef DWORD * DWORD_PTR ;
typedef BYTE BYTE_BITMASK ;
typedef VOID_PTR * VOID_PTR_PTR ;
typedef WORD FLASH_DATA_WIDTH ;
typedef char FDI_TCHAR ;
typedef OSASemaRef SEM_ID ;
typedef int SEM_STATUS ;
typedef SEM_MTX * SEM_MTX_ID ;
typedef WORD IDTYPE ;
typedef DATA_LOOKUP * DATA_LOOKUP_PTR ;
typedef DATA_LOCATION * DATA_LOC_PTR ;
typedef OPEN_PARAM * OPEN_STREAM_PTR ;
typedef COMMAND * COMMAND_PTR ;
typedef UNIT_HEADER * UNIT_HDR_PTR ;
typedef MULTI_INSTANCE * MULTI_INST_PTR ;
typedef BLOCK_INFO * BLK_INFO_PTR ;
typedef COMMAND_CONTROL * CMD_CNTRL_PTR ;
typedef WORD FILE_ID ;
typedef va_list __gnuc_va_list ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
typedef unsigned int size_t ;
typedef unsigned int clock_t ;
typedef unsigned int time_t ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 second ; // Seconds after minute: 0 - 59	 
 UINT8 minute ; // Minutes after hour: 0 - 59	 
 UINT8 hour ; // Hours after midnight: 0 - 23	 
 UINT8 day ; // Day of month: 1 - 31	 
 UINT8 month ; // Month of year: 1 - 12	 
 UINT16 year ; // Calendar year: e.g 2001	 
 } RTC_CalendarTime;

typedef UINT8 RTC_Handle ;
typedef void ( *RTC_ISR ) ( void ) ;
typedef void ( *RTCOnTimeSetNotifyCallback ) ( RTC_CalendarTime* oldTime , RTC_CalendarTime* newTime ) ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_INT_IFC ,	 
 DIAG_EXT_IFC	 
 } DIAG_IF_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_UART_SPEED_921600 ,	 
 DIAG_UART_SPEED_1842000 ,	 
 DIAG_UART_SPEED_3000000 ,	 
 DIAG_UART_DISABLE ,	 
 } DIAG_UART_SPEED_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_MEM_ENABLE ,	 
 DIAG_MEM_DISABLE	 
 } DIAG_MEM_SWITCH;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_DEV_USB ,	 
 DIAG_DEV_SD ,	 
 DIAG_DEV_FS ,	 
 DIAG_DEV_UART ,	 
 DIAG_DEV_SPI ,	 
 DIAG_DEV_NONE	 
 } DIAG_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 SULOG_DEV_USB ,	 
 SULOG_DEV_SD	 
 } SULOG_DEV_TYPE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 DIAG_IF_TYPE useIntIf ; /* use internal interface or external interface */	 
 DIAG_UART_SPEED_TYPE useHighSpeedUART ; /* high speed UART ( 460800 ) or low speed UART ( 115200 ) */	 
 DIAG_MEM_SWITCH diagMemSwitch ;	 
 DIAG_DEV_TYPE diagDevType ;	 
 SULOG_DEV_TYPE sulogDevType ;	 
 BOOL sdlAutoDelete ;	 
 } diagCfgDataS;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 dbID ;	 
 UINT32 filterBitLength ;	 
 UINT32 reserved [ 6 ] ;	 
 } DIAG_Nvm_Filter_File_Header_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MASS_STORAGE_DISABLE = 0x0 ,	 
 MASS_STORAGE_ENABLE = 0x1	 
 } MassStorage_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* MIFI driver*/	 
 USB_GENERIC_MIFI_DRIVER = 0x0 ,	 
 USB_MARVELL_MIFI_DRIVER = 0x1 ,	 
 USB_ASR_MIFI_DRIVER = 0x2 ,	 
 USB_GENERIC_MOD_DRIVER = 0x10 ,	 
 USB_GENERIC_MOD_ECM_DRIVER = 0x12 ,	 
 USB_DIAG_UAC_DRIVER = 0x14 ,	 
 USB_RNDIS_ONLY_DRIVER = 0x15 ,	 
	 
 /* MBIM driver*/	 
 USB_MBIM_ONLY_DRIVER = 0x40 ,	 
 USB_MBIM_GENERIC_DRIVER = 0x41 ,	 
 USB_MBIM_MAX_DRIVER = 0x4F ,	 
	 
 /* Other driver*/	 
 USB_CDROM_ONLY_DRIVER = 0x91 ,	 
 USB_CDROM_DIAG_DRIVER = 0x92 ,	 
 USB_DIAG_ONLY_DRIVER = 0x93 ,	 
 USB_MODEM_ONLY_DRIVER = 0x94 ,	 
 USB_MODEM_DIAG_DRIVER = 0x95 ,	 
	 
 USB_MAX_DRIVER = 0xFF	 
 } Usb_driver_typeE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_AUTO_INSTALL_DISABLE = 0x0 ,	 
 USB_AUTO_INSTALL_ENABLE = 0x1	 
 } Usb_auto_install_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_OS_DETECT_DISABLE = 0x0 ,	 
 USB_OS_DETECT_ENABLE = 0x1	 
 } Usb_OS_detect_type;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Usb_driver_typeE usb_driver ;	 
 MassStorage_ConfigE mass_storage ;	 
 Usb_auto_install_type auto_install ;	 
 Usb_OS_detect_type os_detect ;	 
 } Usb_DriverS;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 YMODEM_DISABLE = 0x0 ,	 
 YMODEM_ENABLE = 0x1	 
 } Ymodem_Dump_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Ymodem_Dump_config config ;	 
 } Ymodem_Dump_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MBIM_ONLY_DESP = 0x0 ,	 
 MBIM_ATDIAG_DESP = 0x1	 
 } MBIM_DESP_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 MBIM_DESP_config config ;	 
 } MBIM_CFG_type;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 EEH_DUMP_DEV_USB_SD ,	 
 EEH_DUMP_DEV_SPI	 
 } EEH_DUMP_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PMU_POR = 1 ,	 
 PMU_EMR ,	 
 PMU_WDTR = ( PMU_EMR+2 )	 
 } PMU_LastResetStatus;

typedef UINT8 UART_Activity ;
//ICAT EXPORTED STRUCT 
 typedef struct /* This is structure of the UART Configuration */ 
 {	 
 UART_OpMode opMode ; /* fifo mode , non fifo mode or DMA for basic interface*/	 
 UART_TriggerLevel triggerLevel ; /* the trigger level interrupt on 1 , 8 , 16 , 32 */	 
 UART_BaudRates baudRate ; /* the rate of the transmit and the receive up to 111520 ( default - 9600 ) .*/	 
 UART_WordLen numDataBits ; /* 5 , 6 , 7 , or 8 number of data bits in the UART data frame ( default - 8 ) . */	 
 UART_StopBits stopBits ; /* 1 , 1.500000 or 2 stop bits in the UART data frame ( default - 1 ) . */	 
 UART_ParityTBits parityBitType ; /* Even , Odd or no-parity bit type in the UART data frame ( default - Non ) . */	 
 UART_InterfaceType interfaceType ; /* number of interface that the UART driver supplies ( default - UART_IF_TYPE_L2 ) */	 
 BOOL modemSignal ; /* enable operate modem - TRUE , disable modem - FALSE */	 
 BOOL flowControl ; /* enable Auto flow Control - TRUE , disable Auto flow Control - FALSE */	 
 UINT8 sleepMode ; /* enable sleep mode - TRUE , more fine control - see UARTSleepMode enum */	 
 BOOL auto_baud ; /* enable auto_baud , auto-baud-rate detection within the UART ( default - FALSE ) */	 
 UART_SIRConfigure sirIrDA ;	 
 } UARTConfiguration;

//ICAT EXPORTED ENUM 
 typedef enum // change the order -1 to + 
 {	 
 UART_RC_OK = 1 , /* 1 - no errors */	 
	 
 UART_RC_PORT_NUM_ERROR = -100 , /* -100 - Error in the UART port number */	 
 UART_RC_NO_DATA_TO_READ , /* -99 - Eror no data to read from the FIFO UART */	 
 UART_RC_ILLEGAL_BAUD_RATE , /* -98 - Error in the UART Bayd Rate */	 
 UART_RC_UART_PARITY_BITS_ERROR , /* -97 - Error in parity bit */	 
 UART_RC_UART_ONE_STOP_BIT_ERROR , /* -96 - Error in one stop bit */	 
 UART_RC_ONE_HALF_OR_TWO_STOP_BIT_ERROR , /* -95 - Error in two stop bit */	 
 UART_RC_BAD_INTERFACE_TYPE , /* -94 - Error in the Interface Type */	 
 UART_RC_UART_NOT_AVAILABLE , /* -93 - Error in try to open UART that is open */	 
 UART_RC_NO_DATA_TO_WRITE , /* -92 - Error No data to writ the len = 0 */	 
 UART_RC_NOT_ALL_BYTE_WRITTEN , /* -91 - Error Not all the Byte write to the UART FIFO */	 
 UART_RC_ISR_ALREADY_BIND , /* -90 - Error try to bind ISR for Basic Interface */	 
 UART_RC_WRONG_ISR_UNBIND , /* -89 - Error in the UnBind ISR for Basic Interface */	 
 UART_RC_FIFO_NOT_EMPTY , /* -88 - Error , the UART FIFO not empty */	 
 UART_RC_UART_OPEN , /* -87 - Error try chance the configurr when the UART open */	 
 UART_RC_GPIO_ERR , /* -86 - Error in the Configure of the GPIO */	 
 UART_RC_IRDA_CONFIG_ERR /* -85 - Illegal IrDA configuration */	 
 } UART_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAM_LOG = 0x00 ,	 
 USB_RAM_LOG = 0x01 ,	 
 MAX_RAM_LOG = 0x02	 
 } Ram_Log_Type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAMLOG_ENABLE = 0x00 ,	 
 USB_RAMLOG_ENABLE = 0x01	 
 } RamLog_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 LOG_DISABLE = 0x0 ,	 
 UART_LOG_ENABLE = 0x1 ,	 
 ACAT_LOG_ENABLE = 0x2	 
 } Log_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MSG_DISABLE = 0x0 ,	 
 ACAT_MSG_ENABLE = 0x1	 
 } Msg_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RTI_LOG_DISABLE = 0x0 ,	 
 RTI_LOG_ENABLE = 0x1	 
 } RTI_ConfigE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Log_ConfigE log_cfg ;	 
 Msg_ConfigE msg_cfg ;	 
 RTI_ConfigE rti_cfg ;	 
 RamLog_ConfigE ramlog ;	 
 } Log_ConfigS;

typedef void ( *UARTNotifyInterrupt ) ( UART_Port ) ;
typedef void ( *UsbLogPrint_t ) ( const char * , ... ) ;
typedef volatile unsigned long VUINT32_T ;
typedef unsigned long UINT32_T ;
typedef volatile unsigned int VUINT_T ;
typedef unsigned int UINT_T ;
typedef int INT_T ;
typedef unsigned short UINT16_T , USHORT ;
typedef volatile unsigned short VUINT16_T ;
typedef unsigned char UINT8_T ;
typedef char INT8_T ;
typedef unsigned int UINT , *PUINT ;
typedef unsigned long long UINT64 , *PUINT64 ;
typedef unsigned char UCHAR , BYTE , *PUCHAR ;
typedef int INT , *PINT ;
typedef long long INT64 , *PINT64 ;
typedef char CHAR , *PCHAR ;
typedef unsigned int size_t ;
typedef volatile UINT VUINT , *PVUINT ;
typedef volatile UINT64 VUINT64 , *PVUINT64 ;
typedef volatile unsigned int VUINT32 , *PVUINT32 ;
typedef volatile UINT16 VUINT16 , *PVUINT16 ;
typedef volatile UINT8 VUINT8 , *PVUINT8 ;
typedef volatile UCHAR VUCHAR , *PVUCHAR ;
typedef volatile INT VINT , *PVINT ;
typedef volatile INT64 VINT64 , *PVINT64 ;
typedef volatile int VINT32 , *PVINT32 ;
typedef volatile INT16 VINT16 , *PVINT16 ;
typedef volatile char VINT8 , *PVINT8 ;
typedef volatile CHAR VCHAR , *PVCHAR ;
typedef UINT16 WORD ;
typedef signed char int8_t ;
typedef unsigned char uint8_t ;
typedef signed short int16_t ;
typedef unsigned short uint16_t ;
typedef signed int int32_t ;
typedef unsigned int uint32_t ;
typedef signed long long int int64_t ;
typedef unsigned long long int uint64_t ;
typedef UINT32 rti_uint32_t ;
typedef UINT64 rti_uint64_t ;
typedef TX_THREAD* rti_thread_t ;
//ICAT EXPORTED STRUCT 
 typedef struct {	 
 UINT32 product_id ;	 
 UINT8 data [ 20 ] ;	 
 } InfoForBoardTracking_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 rti_mode_none = 0x00 ,	 
 rti_check_mode = 0x01 ,	 
 rti_timer_mode = 0x02 ,	 
 rti_log2acat_mode = 0x03 ,	 
 rti_psoff_mode = 0x04 ,	 
 rti_uarttrace_mode = 0x05 ,	 
 rti_rfuarttest_mode = 0xFF ,	 
	 
 rti_urtlog_mode = 0x100 ,	 
 rti_usbtrace_mode = 0x101 ,	 
 rti_muxtrace_mode = 0x102 ,	 
 rti_fsyslog_mode = 0x103 ,	 
 rti_mode_max = 0xFFFF	 
 } rti_mode;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_DISABLE=0 ,	 
 RTI_EN_VER1=1 ,	 
 RTI_EN_VER2=2	 
 } RTI_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_QUE_DISABLE=0 ,	 
 RTI_QUE_ENABLE=1	 
 } RTI_QUE_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_EVT_DISABLE=0 ,	 
 RTI_EVT_ENABLE=1	 
 } RTI_EVT_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 TIME_OUT_3MS=0x62 ,	 
 TIME_OUT_4MS=0x83 ,	 
 TIME_OUT_5MS=0xA4 ,	 
 TIME_OUT_6MS=0xC4 ,	 
 TIME_OUT_MAX=0xFF	 
 } Timeout_Threshold;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 RTI_TYPE rtiType ;	 
 RTI_QUE_TYPE rtiQueType ;	 
 RTI_EVT_TYPE rtiEvtType ;	 
	 
 int rtiChange ;	 
 int rtiHT ;	 
 int rtiLT ;	 
	 
 int modeChange ;	 
 int modeHT ;	 
 int modeLT ;	 
	 
 Timeout_Threshold Timeout ;	 
 rti_mode rtiMode ;	 
 } RTICfg_t;

typedef signed char int8_t ;
typedef signed short int int16_t ;
typedef signed int int32_t ;
typedef signed __int64 int64_t ;
typedef unsigned char uint8_t ;
typedef unsigned short int uint16_t ;
typedef unsigned int uint32_t ;
typedef unsigned __int64 uint64_t ;
typedef signed char int_least8_t ;
typedef signed short int int_least16_t ;
typedef signed int int_least32_t ;
typedef signed __int64 int_least64_t ;
typedef unsigned char uint_least8_t ;
typedef unsigned short int uint_least16_t ;
typedef unsigned int uint_least32_t ;
typedef unsigned __int64 uint_least64_t ;
typedef signed int int_fast8_t ;
typedef signed int int_fast16_t ;
typedef signed int int_fast32_t ;
typedef signed __int64 int_fast64_t ;
typedef unsigned int uint_fast8_t ;
typedef unsigned int uint_fast16_t ;
typedef unsigned int uint_fast32_t ;
typedef unsigned __int64 uint_fast64_t ;
typedef signed int intptr_t ;
typedef unsigned int uintptr_t ;
typedef signed long long intmax_t ;
typedef unsigned long long uintmax_t ;
typedef unsigned short wchar_t ;
typedef uint32_t lfs_size_t ;
typedef uint32_t lfs_off_t ;
typedef int32_t lfs_ssize_t ;
typedef int32_t lfs_soff_t ;
typedef uint32_t lfs_block_t ;
typedef FILE_ID FDI_FID ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 char fileName [ 256 ] ; // name of file located in Flash ( or to be create )	 
 char mode [ 256 ] ; // Opening mode ( e.g. open for readonly )	 
 } FOpenInputStruct;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 FDI_FID fileID ; // an ID of an already open file in Flash memory.	 
 char fileName [ 256 ] ; // name of file located in Flash memory.	 
 } FOpenReturnStruct;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 FDI_FID fileID ; // File ID of the file being handled.	 
 UINT16 PacketSize ; // size of packet being transfered.	 
 char writeBuf [ 1024 ] ; // buffer of bytes to be written	 
 } WriteInputStruct;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 FDI_FID fileID ; // File ID of the file being handled.	 
 UINT16 PacketSize ; // size of packet being transfered.	 
 UINT32 filePos ; // offset to start to write	 
 UINT32 remainBytes ; // offset to start to write	 
 char writeBuf [ 1024 *2 ] ; // buffer of bytes to be written	 
 } WriteInputStruct_Ex;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 FDI_FID fileID ; // File ID of the file being handled.	 
 UINT16 PacketOK ; // Contian the error code see FDI Doc ( e.g. ERR_OK )	 
 UINT16 PacketSize ; // size of packet being transfered.	 
 UINT32 fileCurPos ; // current posof file	 
 UINT32 reserved ; // size to write from current filepos	 
 } WriteReturnStruct_Ex;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 FDI_FID fileID ;	 
 UINT16 bufferSize ;	 
 } ReadInputStruct;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT16 PacketOK ; // Contian the error code see FDI Doc ( e.g. ERR_OK )	 
 UINT16 LastPacket ; // equal to 1 if it ' s the last packet , 0 - otherwise.	 
 UINT16 PacketSize ; // size in bytes of the buffer being read from file.	 
 char readBuf [ 1024 ] ; // buffer of bytes contains the Flash file data.	 
 } ReadReturnStruct;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 FDI_FID fileID ;	 
 UINT16 bufferSize ;	 
 UINT32 filePos ; // offset to start to read	 
 UINT32 sizetoRead ; // size to read from current filepos	 
 UINT32 dataBlockSize ;	 
 } ReadInputStruct_Ex;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 FDI_FID fileID ;	 
 UINT16 PacketOK ; // Contian the error code see FDI Doc ( e.g. ERR_OK )	 
 UINT16 LastPacket ; // equal to 1 if it ' s the last packet , 0 - otherwise.	 
 UINT16 PacketSize ; // size in bytes of the buffer being read from file.	 
 UINT32 filePos ; // offset to start to read	 
 char readBuf [ 1024 *4 ] ; // buffer of bytes contains the Flash file data.	 
 } ReadReturnStruct_Ex;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 char fInfoList [ 1024 ] ; // buffer contain file properties information ( name , size , date etc. )	 
 UINT16 fListLen ; // Number of files describe in the buffer ( fInfoList )	 
 UINT8 LastPacket ; // Flag which intecates whether this is the last structure contain file -	 
 } FNamesReturnStruct;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 char fOldName [ 256 ] ;	 
 char fNewName [ 256 ] ;	 
 } RenameInputStruct;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 char fileName [ 256 ] ;	 
 UINT32 newMode ;	 
 } ChangModeStruct;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 FDI_FID fileID ;	 
 long offset ;	 
 int whereFrom ;	 
 } FSeekStruct;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 unsigned long long fdvSize ;	 
 } FdvSizeStruct;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 fdvAvailableSpace ;	 
 } FdvAvailableSpaceStruct;

DIAG_FILTER ( FDI , Transport , FdvAvailableSize , DIAG_INFORMATION)  
 diagStructPrintf ( " %S { FdvAvailableSpaceStruct } " , ptr , size );

DIAG_FILTER ( FDI , Transport , F_NameList_Packet , DIAG_INFORMATION)  
 diagStructPrintf ( " %S { FNamesReturnStruct } " , ptr , size );

DIAG_FILTER ( FDI , TRANSPORT , FDI_Transport_Is_Valid_Fid , DIAG_INFORMATION)  
 diagPrintf ( " Invalid FDI transport File ( ID: %d ) " , ( UINT16 ) hFileID );

DIAG_FILTER ( FDI , Transport , FDI_Transport_Clear_Fid_Error , DIAG_INFORMATION)  
 diagPrintf ( " FDI clear transport id ( ID: %d ) ( Error Code: %d ) " , ( UINT16 ) hFileID , retVal );

DIAG_FILTER ( FDI , Transport , FDI_Transport_Clear_Fid_Success , DIAG_INFORMATION)  
 diagPrintf ( " FDI clear transport id ( ID: %d ) succesfully " , ( UINT16 ) hFileID );

DIAG_FILTER ( FDI , TRANSPORT , FDI_Transport_Record_Fid , DIAG_INFORMATION)  
 diagPrintf ( " Fail to record FDI transport File ( ID: %d ) " , ( UINT16 ) hFileID );

//ICAT EXPORTED FUNCTION - Diag , Utils , SetRTC 
 void _SetRTC ( void *pDateAmdTime ) 
 {	 
 RTC_CalendarTime* pstDateAndTime = ( RTC_CalendarTime* ) pDateAmdTime ;	 
 RTC_ReturnCode rc ;	 
	 
 RTCAlarmReset ( ) ; // Clear the alarm - needed for the next code line.	 
 rc = RTCDateAndTimeSet ( pstDateAndTime ) ;	 
 if ( rc == RTC_RC_OK )	 
 {		 
DIAG_FILTER ( Diag , Utils , SetRTC_Success , DIAG_INFORMATION)  
 diagPrintf ( " RTC was successfully set. " );

		 
 }	 
 else if ( rc == RTC_RC_MAKETIME_FAILURE )	 
 {		 
DIAG_FILTER ( Diag , Utils , SetRTC_Failure , DIAG_INFORMATION)  
 diagPrintf ( " RTC set failed , check the input parameter validity. " );

		 
 }	 
 else if ( rc == RTC_RC_ALARM_ALREADY_SET )	 
 {		 
DIAG_FILTER ( Diag , Utils , RTC_Already_Set , DIAG_INFORMATION)  
 diagPrintf ( " RTC was already set. " );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - Diag , Utils , ReadRTC 
 void _ReadRTC ( void ) 
 {	 
 RTC_CalendarTime stCalendarTime ;	 
 RTC_ReturnCode rc ;	 
	 
 rc = RTCCurrentDateAndTimeGet ( &stCalendarTime ) ;	 
 if ( rc == RTC_RC_OK )	 
 {		 
 char sCalanderTime [ 256 ] ;		 
 sprintf ( sCalanderTime , " The RTC Value is: %d / %02d / %02d , %d:%02d:%02d " ,		 
 stCalendarTime.day , stCalendarTime.month , stCalendarTime.year ,		 
 stCalendarTime.hour , stCalendarTime.minute , stCalendarTime.second ) ;		 
DIAG_FILTER ( Diag , Utils , ReadRTC_Success , DIAG_INFORMATION)  
 diagPrintf ( " %s " , sCalanderTime );

		 
 }	 
 else	 
 {		 
DIAG_FILTER ( Diag , Utils , ReadRTC_Failure , DIAG_INFORMATION)  
 diagPrintf ( " Failed to read RTC " );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , Fopen 
 void _Fopen ( void* fInOpenStruct ) 
 {	 
 FOpenInputStruct* fOpenInSt = ( FOpenInputStruct* ) fInOpenStruct ;	 
	 
	 
	 
 FILE_ID hFileID = FDI_fopen ( fOpenInSt->fileName , fOpenInSt->mode ) ;	 
	 
 if ( hFileID == 0 )	 
 {		 
DIAG_FILTER ( FDI , Transport , F_Open_Error , DIAG_INFORMATION)  
 diagPrintf ( " fopen %s error ( Error Code: %d ) " , fOpenInSt->fileName , hFileID );

		 
 }	 
 else	 
 {		 
 FOpenReturnStruct retOpenStruct ;		 
 retOpenStruct.fileID = ( FDI_FID ) hFileID ;		 
 memset ( retOpenStruct.fileName , 0 , 256 ) ;		 
 strcpy ( retOpenStruct.fileName , fOpenInSt->fileName ) ;		 
		 
DIAG_FILTER ( FDI , Transport , F_Open_Return_FileID , DIAG_INFORMATION)  
 diagStructPrintf ( " %S { FOpenReturnStruct } " , ( void* ) &retOpenStruct , sizeof ( FOpenReturnStruct ) );

		 
		 
 FDI_Transport_Record_Fid ( hFileID ) ;		 
 }	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , Fclose 
 void _Fclose ( void *fileID ) 
 {	 
 UINT8 retVal = 1 ;	 
 FILE_ID hFileID = * ( FDI_FID * ) fileID ;	 
	 
 if ( !FDI_Transport_Is_Valid_Fid ( hFileID ) )	 
 {		 
DIAG_FILTER ( FDI , Transport , F_Close_Invalid_Error , DIAG_INFORMATION)  
 diagPrintf ( " Receive Invalid File ( ID: %d ) close command " , ( UINT16 ) hFileID );

		 
 return ;		 
 }	 
	 
	 
	 
	 
 retVal = FDI_fclose ( hFileID ) ;	 
	 
 if ( retVal != 0 )	 
 {		 
DIAG_FILTER ( FDI , Transport , F_Close_Error , DIAG_INFORMATION)  
 diagPrintf ( " File ( ID: %d ) close error ( Error Code: %d ) " , ( UINT16 ) hFileID , retVal );

		 
 }	 
 else	 
 {		 
DIAG_FILTER ( FDI , Transport , F_Close_Success , DIAG_INFORMATION)  
 diagPrintf ( " File ( ID: %d ) was succesfully closed " , ( UINT16 ) hFileID );

		 
 }	 
	 
 FDI_Transport_Remove_Fid ( hFileID ) ;	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , Fread 
 void _Fread ( void *pReadStruct ) 
 {	 
 ERR_CODE errFdi ;	 
 ReadReturnStruct* pRetReadBuf ;	 
 size_t nElementsToRead , nActualReadElemenets ;	 
	 
 FILE_ID hFileID = ( FILE_ID ) ( ( ( ReadInputStruct* ) pReadStruct ) ->fileID ) ;	 
 nElementsToRead = ( size_t ) ( ( ( ReadInputStruct* ) pReadStruct ) ->bufferSize ) ;	 
	 
 if ( nElementsToRead > 1024 )	 
 {		 
DIAG_FILTER ( FDI , Transport , F_Read_Bad_Input , DIAG_INFORMATION)  
 diagPrintf ( " Number of packets to read exceeded the maximal number ( %d ) " , ( UINT16 ) 1024 );

		 
 return ;		 
 }	 
	 
 if ( !FDI_Transport_Is_Valid_Fid ( hFileID ) )	 
 {		 
DIAG_FILTER ( FDI , Transport , F_Read_Invalid_Error , DIAG_INFORMATION)  
 diagPrintf ( " Receive Invalid File ( ID: %d ) _FreadEx command " , ( UINT16 ) hFileID );

		 
 return ;		 
 }	 
	 
 pRetReadBuf = ( ReadReturnStruct* ) ( malloc ( sizeof ( ReadReturnStruct ) ) ) ;	 
	 
 if ( pRetReadBuf )	 
 {		 
		 
		 
		 
 nActualReadElemenets = FDI_fread ( & ( pRetReadBuf->readBuf ) , sizeof ( UINT8 ) , nElementsToRead , hFileID ) ;		 
		 
 errFdi = FDI_ferror ( hFileID ) ; // If any error occured errFdi will contain the error code.		 
 pRetReadBuf->PacketOK = ( UINT16 ) errFdi ; // Update the return struct to contain any error information.		 
 pRetReadBuf->PacketSize = nActualReadElemenets ;		 
 if ( ( errFdi == ERR_EOF ) || ( nActualReadElemenets < nElementsToRead ) )		 
 {			 
 pRetReadBuf->PacketOK = ( UINT16 ) ( ERR_EOF ) ;			 
 pRetReadBuf->LastPacket = 1 ;			 
 }		 
 else if ( errFdi == ERR_NONE )		 
 {			 
 pRetReadBuf->LastPacket = 0 ;			 
 }		 
 else		 
 {			 
DIAG_FILTER ( FDI , Transport , F_Read_Error , DIAG_INFORMATION)  
 diagPrintf ( " File read error ( ID: %d , Error number: %d ) " , ( UINT16 ) hFileID , ( UINT16 ) errFdi );

			 
 free ( pRetReadBuf ) ;			 
 pRetReadBuf = 0 ;			 
 return ;			 
 }		 
DIAG_FILTER ( FDI , Transport , F_Read_Packet , DIAG_INFORMATION)  
 diagStructPrintf ( " %S { ReadReturnStruct } " , ( void* ) pRetReadBuf , sizeof ( ReadReturnStruct ) );

		 
 free ( pRetReadBuf ) ;		 
 pRetReadBuf = 0 ;		 
 }	 
 else	 
 { // Unsuccessful allocation		 
DIAG_FILTER ( FDI , Transport , F_Read_Malloc_Error , DIAG_INFORMATION)  
 diagPrintf ( " Malloc in Fread failed " );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , FreadEx 
 void _FreadEx ( void *pReadStruct ) 
 {	 
 ERR_CODE errFdi ;	 
 ReadReturnStruct_Ex* pRetReadBuf ;	 
 size_t nElementsToRead , nActualReadElemenets , nSizetoread , nWholeBytes ;	 
 UINT32 filepostoreadstart ;	 
 UINT32 packagecount = 0 ;	 
 FILE_ID hFileID = ( FILE_ID ) ( ( ( ReadInputStruct_Ex* ) pReadStruct ) ->fileID ) ;	 
	 
 nElementsToRead = ( size_t ) ( ( ( ReadInputStruct_Ex* ) pReadStruct ) ->bufferSize ) ;	 
 nWholeBytes = nSizetoread = ( size_t ) ( ( ( ReadInputStruct_Ex* ) pReadStruct ) ->sizetoRead ) ;	 
 filepostoreadstart = ( ( ReadInputStruct_Ex* ) pReadStruct ) ->filePos ;	 
	 
 if ( !FDI_Transport_Is_Valid_Fid ( hFileID ) )	 
 {		 
DIAG_FILTER ( FDI , Transport , F_ReadEx_Invalid_Error , DIAG_INFORMATION)  
 diagPrintf ( " Receive Invalid File ( ID: %d ) _FreadEx command " , ( UINT16 ) hFileID );

		 
 return ;		 
 }	 
	 
 pRetReadBuf = ( ReadReturnStruct_Ex* ) ( malloc ( sizeof ( ReadReturnStruct_Ex ) ) ) ;	 
 if ( !pRetReadBuf )	 
 {		 
 // Unsuccessful allocation		 
 { if ( ( log_config . log_cfg != LOG_DISABLE ) || assertFlag ) { ErrorLogPrintf_Extend ( ERR_RAM_LOG , " %08x " " %s: memory exhaust " " \r\n " , get_current_TStimer_tick ( ) , __FUNCTION__ ) ; } } ;		 
DIAG_FILTER ( FDI , Transport , F_Read_Malloc_Error , DIAG_INFORMATION)  
 diagPrintf ( " Malloc in Fread failed " );

		 
 return ;		 
 }	 
	 
	 
	 
	 
 FDI_fseek ( hFileID , filepostoreadstart , 0 ) ;	 
	 
 while ( nSizetoread>0 )	 
 {		 
 memset ( pRetReadBuf , 0 , sizeof ( ReadReturnStruct_Ex ) ) ;		 
 pRetReadBuf->fileID = hFileID ;		 
 if ( nElementsToRead > ( 1024 *4 ) )		 
 {			 
 pRetReadBuf->filePos = filepostoreadstart ;			 
DIAG_FILTER ( FDI , Transport , F_Read_Bad_Input , DIAG_INFORMATION)  
 diagStructPrintf ( " %S { ReadReturnStruct_Ex } " , ( void* ) pRetReadBuf , sizeof ( ReadReturnStruct_Ex ) );

			 
			 
 free ( pRetReadBuf ) ;			 
 pRetReadBuf = 0 ;			 
 return ;			 
 }		 
		 
 if ( pRetReadBuf )		 
 {			 
 if ( nSizetoread>=nElementsToRead )			 
 {				 
 // CPUartLogPrintf ( " new FDI Read :%s --- FDI_freadEx ( nElementsToRead ) , nSizetoread = %d " , __FUNCTION__ , nSizetoread ) ;				 
				 
				 
				 
 nActualReadElemenets = FDI_freadEx ( & ( pRetReadBuf->readBuf ) , sizeof ( UINT8 ) , nElementsToRead , hFileID , filepostoreadstart ) ;				 
				 
				 
 }			 
 else			 
 {				 
 // CPUartLogPrintf ( " new FDI Read :%s --- FDI_freadEx ( nSizetoread ) , nSizetoread = %d " , __FUNCTION__ , nSizetoread ) ;				 
				 
				 
				 
 nActualReadElemenets = FDI_freadEx ( & ( pRetReadBuf->readBuf ) , sizeof ( UINT8 ) , nSizetoread , hFileID , filepostoreadstart ) ;				 
				 
 }			 
			 
			 
 errFdi = FDI_ferror ( hFileID ) ; // If any error occured errFdi will contain the error code.			 
 pRetReadBuf->PacketOK = ( UINT16 ) errFdi ; // Update the return struct to contain any error information.			 
 pRetReadBuf->PacketSize = nActualReadElemenets ;			 
			 
			 
 if ( ( errFdi == ERR_EOF ) || ( nActualReadElemenets < nElementsToRead ) )			 
 {				 
 pRetReadBuf->PacketOK = ( UINT16 ) ( ERR_EOF ) ;				 
 pRetReadBuf->LastPacket = 1 ;				 
 }			 
 else if ( errFdi == ERR_NONE )			 
 {				 
 pRetReadBuf->LastPacket = 0 ;				 
 }			 
 else			 
 {				 
 pRetReadBuf->filePos = filepostoreadstart ;				 
DIAG_FILTER ( FDI , Transport , F_Read_Error , DIAG_INFORMATION)  
 diagStructPrintf ( " %S { ReadReturnStruct_Ex } " , ( void* ) pRetReadBuf , sizeof ( ReadReturnStruct_Ex ) );

				 
 free ( pRetReadBuf ) ;				 
 pRetReadBuf = 0 ;				 
 return ;				 
 }			 
			 
 nSizetoread -= nActualReadElemenets ;			 
 pRetReadBuf->filePos = filepostoreadstart ;			 
DIAG_FILTER ( FDI , Transport , F_Read_PacketEx , DIAG_INFORMATION)  
 diagStructPrintf ( " %S { ReadReturnStruct_Ex } " , ( void* ) pRetReadBuf , sizeof ( ReadReturnStruct_Ex ) );

			 
 filepostoreadstart += nActualReadElemenets ;			 
			 
 if ( pRetReadBuf->LastPacket == 1 ) {				 
 free ( pRetReadBuf ) ;				 
 pRetReadBuf = 0 ;				 
 return ;				 
 }			 
			 
			 
 }		 
		 
 packagecount++ ;		 
		 
 if ( packagecount%8==0 )		 
 {			 
 OsaTaskSleep ( 5 , 0 ) ;			 
 }		 
 }	 
	 
 if ( pRetReadBuf )	 
 {		 
 free ( pRetReadBuf ) ;		 
 pRetReadBuf = 0 ;		 
 }	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , Fwrite 
 void _Fwrite ( void * pWriteStruct ) 
 {	 
 ERR_CODE errFdi ;	 
 FILE_ID hFileID = ( FILE_ID ) ( ( ( WriteInputStruct* ) pWriteStruct ) ->fileID ) ;	 
 UINT32 numberOfElementsWritten = 0 ;	 
	 
 if ( !FDI_Transport_Is_Valid_Fid ( hFileID ) )	 
 {		 
DIAG_FILTER ( FDI , Transport , F_Write_Invalid_Error , DIAG_INFORMATION)  
 diagPrintf ( " Receive Invalid File ( ID: %d ) FwriteEx command " , ( UINT16 ) hFileID );

		 
 return ;		 
 }	 
	 
	 
 numberOfElementsWritten = FDI_fwrite ( ( ( ( WriteInputStruct* ) pWriteStruct ) ->writeBuf ) ,	 
 sizeof ( char ) ,	 
 ( ( ( WriteInputStruct* ) pWriteStruct ) ->PacketSize ) ,	 
 hFileID ) ;	 
	 
	 
 if ( numberOfElementsWritten == ( ( ( WriteInputStruct* ) pWriteStruct ) ->PacketSize ) )	 
 {		 
DIAG_FILTER ( FDI , Transport , F_Write_Success , DIAG_INFORMATION)  
 diagPrintf ( " The Packet was written successfully ( packet size %d ) " , ( UINT16 ) ( ( ( WriteInputStruct* ) pWriteStruct ) ->PacketSize ) );

		 
 }	 
 else	 
 {		 
 errFdi = FDI_ferror ( hFileID ) ; // If any error occured errFdi will contain the error code.		 
DIAG_FILTER ( FDI , Transport , F_Write_Error , DIAG_INFORMATION)  
 diagPrintf ( " File write error ( ID: %d , Error number: %d ) " , ( UINT16 ) hFileID , ( UINT16 ) errFdi );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , FwriteEx 
 void _FwriteEx ( void * pWriteStruct ) 
 {	 
 ERR_CODE errFdi ;	 
 WriteReturnStruct_Ex* pRetWriteBuf ;	 
 FILE_ID hFileID = ( FILE_ID ) ( ( ( WriteInputStruct_Ex* ) pWriteStruct ) ->fileID ) ;	 
 UINT32 numberOfElementsWritten = 0 ;	 
 UINT32 filepostowrite , retCurfilepos ;	 
 filepostowrite = ( ( WriteInputStruct_Ex* ) pWriteStruct ) ->filePos ;	 
 retCurfilepos = filepostowrite ;	 
	 
 if ( !FDI_Transport_Is_Valid_Fid ( hFileID ) )	 
 {		 
DIAG_FILTER ( FDI , Transport , F_WriteEx_Invalid_Error , DIAG_INFORMATION)  
 diagPrintf ( " Receive Invalid File ( ID: %d ) FwriteEx command " , ( UINT16 ) hFileID );

		 
 return ;		 
 }	 
	 
 pRetWriteBuf = ( WriteReturnStruct_Ex* ) ( malloc ( sizeof ( WriteReturnStruct_Ex ) ) ) ;	 
	 
 if ( !pRetWriteBuf )	 
 {		 
 // Unsuccessful allocation		 
 { if ( ( log_config . log_cfg != LOG_DISABLE ) || assertFlag ) { ErrorLogPrintf_Extend ( ERR_RAM_LOG , " %08x " " %s: memory exhaust " " \r\n " , get_current_TStimer_tick ( ) , __FUNCTION__ ) ; } } ;		 
DIAG_FILTER ( FDI , Transport , F_Write_Malloc_Error , DIAG_INFORMATION)  
 diagPrintf ( " Malloc in Fread failed " );

		 
 return ;		 
 }	 
	 
	 
 numberOfElementsWritten = FDI_fwriteEx ( ( ( ( WriteInputStruct_Ex* ) pWriteStruct ) ->writeBuf ) ,	 
 sizeof ( char ) ,	 
 ( ( ( WriteInputStruct_Ex* ) pWriteStruct ) ->PacketSize ) ,	 
 hFileID , filepostowrite ) ;	 
	 
	 
 pRetWriteBuf->fileID = hFileID ;	 
 pRetWriteBuf->fileCurPos = retCurfilepos + numberOfElementsWritten ;	 
 pRetWriteBuf->PacketSize = numberOfElementsWritten ;	 
	 
 if ( numberOfElementsWritten == ( ( ( WriteInputStruct_Ex* ) pWriteStruct ) ->PacketSize ) )	 
 {		 
DIAG_FILTER ( FDI , Transport , F_Write_Success , DIAG_INFORMATION)  
 diagStructPrintf ( " %S { WriteReturnStruct_Ex } " , ( void* ) pRetWriteBuf , sizeof ( WriteReturnStruct_Ex ) );

		 
 }	 
 else	 
 {		 
 errFdi = FDI_ferror ( hFileID ) ; // If any error occured errFdi will contain the error code.		 
 pRetWriteBuf->PacketOK = ( UINT16 ) errFdi ;		 
DIAG_FILTER ( FDI , Transport , F_Write_Error , DIAG_INFORMATION)  
 diagStructPrintf ( " %S { WriteReturnStruct_Ex } " , ( void* ) pRetWriteBuf , sizeof ( WriteReturnStruct_Ex ) );

		 
 }	 
	 
	 
 free ( pRetWriteBuf ) ;	 
 pRetWriteBuf = 0 ;	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , Format_Flash 
 void _Format ( void ) 
 {	 
 ERR_CODE errFdi ;	 
	 
 errFdi = FDI_Format ( ) ;	 
 if ( errFdi == ERR_NONE ) // No errors occurred.	 
 {		 
DIAG_FILTER ( FDI , Transport , Format_Success , DIAG_INFORMATION)  
 diagPrintf ( " The flash was successfully formated " );

		 
 }	 
 else	 
 {		 
DIAG_FILTER ( FDI , Transport , Format_Error , DIAG_INFORMATION)  
 diagPrintf ( " An error occurred while formating ( %d ) " , errFdi );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , Remove_File 
 void _Remove ( void* fileName ) 
 {	 
 int errFdi ;	 
	 
	 
	 
	 
 errFdi = FDI_remove ( ( char * ) fileName ) ;	 
	 
	 
 if ( errFdi == 0 )	 
 {		 
DIAG_FILTER ( FDI , Transport , Remove_Success , DIAG_INFORMATION)  
 diagPrintf ( " The file was successfully deleted " );

		 
 }	 
 else	 
 {		 
DIAG_FILTER ( FDI , Transport , Remove_Error , DIAG_INFORMATION)  
 diagPrintf ( " An error occurred while deleting file ( %d ) " , ( UINT16 ) errFdi );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , GetFileNameList 
 void _GetFileNameList ( void* wildcard ) 
 {	 
 FILE_INFO file_info = { 0 } ; /* Holds the file information */	 
 char sTemp [ 256 +41 ] = { 0 } ; // max num of digits recieved from DWORD / int ( type of file attributes ) = 10 + file name length + 1 ( NULL terminator )	 
 INT16 ret_val = 0 , nFInfoListLen = 0 ;	 
 FNamesReturnStruct retFNamesStruct = { 0 } ;	 
	 
	 
	 
	 
 FDI_Transport_Clear_Fid ( ) ;	 
	 
	 
	 
	 
 ret_val = FDI_findfirst ( ( char* ) wildcard , &file_info ) ;	 
	 
 if ( ret_val!=0 )	 
 {		 
DIAG_FILTER ( FDI , TRANSPORT , FName_LIST_NOTEXISTS , DIAG_INFORMATION)  
 diagPrintf ( " Currently There are no files on flash matching wildcard - %s " , ( char* ) wildcard );

		 
 return ;		 
 }	 
	 
 if ( ret_val == -1 ) /* Unsuccessful trial */	 
 {		 
DIAG_FILTER ( FDI , Transport , F_Name_List_Error , DIAG_INFORMATION)  
 diagPrintf ( " unsuccessful trial to retrieve files name list " );

		 
 return ;		 
 }	 
	 
 do	 
 {		 
 retFNamesStruct.LastPacket = 1 ;		 
 retFNamesStruct.fListLen = 0 ; /* Length meauered in number of files in the list */		 
 retFNamesStruct.fInfoList [ 0 ] = 0 ;		 
 nFInfoListLen = 0 ;		 
 do		 
 {			 
 nFInfoListLen += sprintf ( sTemp , " %s@%d@%d@%lu@%u@ " , ( char* ) file_info.file_name , file_info.time , file_info.date , file_info.size , file_info.permissions ) ;			 
			 
 /* Check if file properties list will not exceed maximum list buffer length ( +1 counts the ' \n ' at the end ) */			 
 if ( ( nFInfoListLen+1 ) > 1024 )			 
 {				 
 retFNamesStruct.LastPacket = 0 ; /* Indicates on packet which DOES NOT contain all flash files information */				 
 break ;				 
 }			 
 retFNamesStruct.fListLen ++ ; /* counts number of files */			 
 strcat ( retFNamesStruct.fInfoList , sTemp ) ;			 
			 
			 
			 
			 
 ret_val = FDI_findnext ( &file_info ) ;			 
			 
 } while ( ret_val == 0 ) ; /* Loop until we don ' t find anymore files. */		 
		 
DIAG_FILTER ( FDI , Transport , F_NameList_Packet , DIAG_INFORMATION)  
 diagStructPrintf ( " %S { FNamesReturnStruct } " , ( void* ) &retFNamesStruct , sizeof ( FNamesReturnStruct ) );

		 
		 
 } while ( retFNamesStruct.LastPacket != ( UINT8 ) 1 ) ; /* Loop until we don ' t find anymore files. */	 
	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , RenameFile 
 void _RenameFile ( void* pRenameStruct ) 
 {	 
 INT16 ret_val ;	 
 RenameInputStruct* pRenameSt = ( RenameInputStruct* ) pRenameStruct ;	 
	 
	 
	 
	 
 ret_val = FDI_rename ( ( char* ) ( pRenameSt->fOldName ) , ( char* ) ( pRenameSt->fNewName ) ) ;	 
	 
	 
 if ( ret_val == 0 )	 
 {		 
DIAG_FILTER ( FDI , Transport , F_Rename_Succes , DIAG_INFORMATION)  
 diagPrintf ( " The file was successfully renamed to %s " , pRenameSt->fNewName );

		 
 }	 
 else	 
 {		 
DIAG_FILTER ( FDI , Transport , F_Rename_Error , DIAG_INFORMATION)  
 diagPrintf ( " Error occured while trying to change file name ( %d ) " , ret_val );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , FStatus 
 void _FStatus ( void* fileName ) 
 {	 
 INT16 fStatus ;	 
 INT32 ret_val ;	 
	 
 { if ( log_config . log_cfg != LOG_DISABLE ) {DIAG_FILTER ( MIFI , FDI , _FStatus , DIAG_INFORMATION)  
 diagPrintf ( " _FStatus %s " , fileName );

 } } ;	 
	 
 ret_val = FDI_stat ( ( char* ) fileName , ( int * ) &fStatus ) ;	 
 if ( ret_val == 0 )	 
 {		 
DIAG_FILTER ( FDI , Transport , F_Status_Success , DIAG_INFORMATION)  
 diagPrintf ( " ( %d ) " , fStatus );

		 
 }	 
 else	 
 {		 
DIAG_FILTER ( FDI , Transport , F_Status_Error , DIAG_INFORMATION)  
 diagPrintf ( " An error occurred while checking the file status ( %d ) " , ret_val );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , FchangeMode 
 void _FChangeMode ( void* stInChMode ) 
 {	 
 char* fileName = ( char* ) ( ( ( ChangModeStruct* ) stInChMode ) ->fileName ) ;	 
 INT32 newMode = ( INT32 ) ( ( ( ChangModeStruct* ) stInChMode ) ->newMode ) ;	 
	 
 if ( FDI_chmod ( fileName , newMode ) == 0 )	 
 {		 
DIAG_FILTER ( FDI , Transport , F_ChangeMode_Success , DIAG_INFORMATION)  
 diagPrintf ( " mode of %s was successfully changed " , fileName );

		 
 }	 
 else	 
 {		 
DIAG_FILTER ( FDI , Transport , F_ChangeMode_Error , DIAG_INFORMATION)  
 diagPrintf ( " An error occurred while changing %s mode " , fileName );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , Fseek 
 void _Fseek ( void* stFSeek ) 
 {	 
 FSeekStruct* fSeekSt = ( FSeekStruct* ) ( stFSeek ) ;	 
	 
 if ( !FDI_Transport_Is_Valid_Fid ( fSeekSt->fileID ) )	 
 {		 
DIAG_FILTER ( FDI , Transport , Fseek_Invalid_Error , DIAG_INFORMATION)  
 diagPrintf ( " Receive Invalid File ( ID: %d ) _Fseek command " , ( UINT16 ) ( fSeekSt->fileID ) );

		 
 return ;		 
 }	 
	 
	 
	 
	 
 if ( FDI_fseek ( fSeekSt->fileID , fSeekSt->offset , fSeekSt->whereFrom ) == 0 )	 
	 
 {		 
DIAG_FILTER ( FDI , Transport , Fseek_Success , DIAG_INFORMATION)  
 diagPrintf ( " File ( ID: %d ) indicator was offset by %d " , ( UINT16 ) ( fSeekSt->fileID ) , fSeekSt->offset );

		 
 }	 
 else	 
 {		 
DIAG_FILTER ( FDI , Transport , Fseek_Error , DIAG_INFORMATION)  
 diagPrintf ( " Error occured while trying to seek within file ( ID: %d ) " , ( UINT16 ) ( fSeekSt->fileID ) );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , GetMaxFileNameLen 
 void _GetMaxFileNameLength ( ) 
 {	 
DIAG_FILTER ( FDI , Transport , MaxFileNameLen , DIAG_INFORMATION)  
 diagPrintf ( " %d " , ( UINT16 ) ( 128 ) );

	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , GetFdiFdvSize 
 void _GetFdiFdvSize ( void ) 
 {	 
 FdvSizeStruct retFdvSizeStruct ;	 
 unsigned long bytes ;	 
 OSA_STATUS status ;	 
	 
 status = OsaSemaphoreAcquire ( FatSysRef , 0xFFFFFFFF , 0 ) ;	 
 { if ( ! ( status == OS_SUCCESS ) ) { utilsAssertFail ( " status == OS_SUCCESS " , " FDI_Transport.c " , 1282 , 1 ) ; } } ;	 
	 
 bytes = FDI_GetTotalSpaceSize ( ) ;	 
	 
 if ( bytes != 0 )	 
 {		 
 retFdvSizeStruct.fdvSize = bytes ;		 
DIAG_FILTER ( FDI , Transport , FdvSpace , DIAG_INFORMATION)  
 diagStructPrintf ( " %S { FdvSizeStruct } " , ( void* ) &retFdvSizeStruct , sizeof ( FdvSizeStruct ) );

		 
 }	 
 else	 
 {		 
DIAG_FILTER ( FDI , Transport , FdvSpace , DIAG_INFORMATION)  
 diagPrintf ( " error on getting the volume sizes " );

		 
 }	 
	 
 status = OsaSemaphoreRelease ( FatSysRef , 0 ) ;	 
 { if ( ! ( status == OS_SUCCESS ) ) { utilsAssertFail ( " status == OS_SUCCESS " , " FDI_Transport.c " , 1299 , 1 ) ; } } ;	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , GetFdiFdvAvailableSpace 
 void _GetFdiFdvAvailableSpace ( void *volName ) 
 {	 
 FdvAvailableSpaceStruct retAvailableSpaceStruct ;	 
 UINT32 bytes_returned = 0 ;	 
	 
	 
	 
	 
 bytes_returned = FDI_GetFreeSpaceSize ( ) ;	 
	 
	 
 retAvailableSpaceStruct.fdvAvailableSpace = bytes_returned ;	 
 ACATwrapper_print_space ( ( void* ) &retAvailableSpaceStruct , sizeof ( FdvAvailableSpaceStruct ) ) ;	 
 // DIAG_FILTER ( FDI , Transport , FdvAvailableSize_RAM , DIAG_INFORMATION )	 
 // diagStructPrintf ( " %S { FdvAvailableSpaceStruct } " , ( VOID* ) &retAvailableSpaceStruct , sizeof ( FdvAvailableSpaceStruct ) ) ;	 
	 
 return ;	 
 }

//ICAT EXPORTED FUNCTION - FDI , Transport , GetFdiVersion 
 void _GetFdiVersion ( ) 
 {	 
	 
	 
	 
	 
DIAG_FILTER ( FDI , Transport , FDI5 , DIAG_INFORMATION)  
 diagPrintf ( " FDI 5 " );

	 
	 
 }

//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_FDI_Partition.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/FDI_Partition.c
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_INT_IFC ,	 
 DIAG_EXT_IFC	 
 } DIAG_IF_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_UART_SPEED_921600 ,	 
 DIAG_UART_SPEED_1842000 ,	 
 DIAG_UART_SPEED_3000000 ,	 
 DIAG_UART_DISABLE ,	 
 } DIAG_UART_SPEED_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_MEM_ENABLE ,	 
 DIAG_MEM_DISABLE	 
 } DIAG_MEM_SWITCH;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_DEV_USB ,	 
 DIAG_DEV_SD ,	 
 DIAG_DEV_FS ,	 
 DIAG_DEV_UART ,	 
 DIAG_DEV_SPI ,	 
 DIAG_DEV_NONE	 
 } DIAG_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 SULOG_DEV_USB ,	 
 SULOG_DEV_SD	 
 } SULOG_DEV_TYPE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 DIAG_IF_TYPE useIntIf ; /* use internal interface or external interface */	 
 DIAG_UART_SPEED_TYPE useHighSpeedUART ; /* high speed UART ( 460800 ) or low speed UART ( 115200 ) */	 
 DIAG_MEM_SWITCH diagMemSwitch ;	 
 DIAG_DEV_TYPE diagDevType ;	 
 SULOG_DEV_TYPE sulogDevType ;	 
 BOOL sdlAutoDelete ;	 
 } diagCfgDataS;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 dbID ;	 
 UINT32 filterBitLength ;	 
 UINT32 reserved [ 6 ] ;	 
 } DIAG_Nvm_Filter_File_Header_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MASS_STORAGE_DISABLE = 0x0 ,	 
 MASS_STORAGE_ENABLE = 0x1	 
 } MassStorage_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* MIFI driver*/	 
 USB_GENERIC_MIFI_DRIVER = 0x0 ,	 
 USB_MARVELL_MIFI_DRIVER = 0x1 ,	 
 USB_ASR_MIFI_DRIVER = 0x2 ,	 
 USB_GENERIC_MOD_DRIVER = 0x10 ,	 
 USB_GENERIC_MOD_ECM_DRIVER = 0x12 ,	 
 USB_DIAG_UAC_DRIVER = 0x14 ,	 
 USB_RNDIS_ONLY_DRIVER = 0x15 ,	 
	 
 /* MBIM driver*/	 
 USB_MBIM_ONLY_DRIVER = 0x40 ,	 
 USB_MBIM_GENERIC_DRIVER = 0x41 ,	 
 USB_MBIM_MAX_DRIVER = 0x4F ,	 
	 
 /* Other driver*/	 
 USB_CDROM_ONLY_DRIVER = 0x91 ,	 
 USB_CDROM_DIAG_DRIVER = 0x92 ,	 
 USB_DIAG_ONLY_DRIVER = 0x93 ,	 
 USB_MODEM_ONLY_DRIVER = 0x94 ,	 
 USB_MODEM_DIAG_DRIVER = 0x95 ,	 
	 
 USB_MAX_DRIVER = 0xFF	 
 } Usb_driver_typeE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_AUTO_INSTALL_DISABLE = 0x0 ,	 
 USB_AUTO_INSTALL_ENABLE = 0x1	 
 } Usb_auto_install_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_OS_DETECT_DISABLE = 0x0 ,	 
 USB_OS_DETECT_ENABLE = 0x1	 
 } Usb_OS_detect_type;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Usb_driver_typeE usb_driver ;	 
 MassStorage_ConfigE mass_storage ;	 
 Usb_auto_install_type auto_install ;	 
 Usb_OS_detect_type os_detect ;	 
 } Usb_DriverS;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 YMODEM_DISABLE = 0x0 ,	 
 YMODEM_ENABLE = 0x1	 
 } Ymodem_Dump_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Ymodem_Dump_config config ;	 
 } Ymodem_Dump_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MBIM_ONLY_DESP = 0x0 ,	 
 MBIM_ATDIAG_DESP = 0x1	 
 } MBIM_DESP_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 MBIM_DESP_config config ;	 
 } MBIM_CFG_type;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 EEH_DUMP_DEV_USB_SD ,	 
 EEH_DUMP_DEV_SPI	 
 } EEH_DUMP_DEV_TYPE;

typedef unsigned int size_t ;
typedef va_list __gnuc_va_list ;
typedef unsigned int size_t ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0 = 0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 , GPIO_PIN_33 , GPIO_PIN_34 , GPIO_PIN_35 , GPIO_PIN_36 , GPIO_PIN_37 , GPIO_PIN_38 , GPIO_PIN_39 ,	 
	 
 GPIO_PIN_40 , GPIO_PIN_41 , GPIO_PIN_42 , GPIO_PIN_43 , GPIO_PIN_44 , GPIO_PIN_45 , GPIO_PIN_46 , GPIO_PIN_47 ,	 
 GPIO_PIN_48 , GPIO_PIN_49 , GPIO_PIN_50 , GPIO_PIN_51 , GPIO_PIN_52 , GPIO_PIN_53 , GPIO_PIN_54 , GPIO_PIN_55 ,	 
 GPIO_PIN_56 , GPIO_PIN_57 , GPIO_PIN_58 , GPIO_PIN_59 , GPIO_PIN_60 , GPIO_PIN_61 , GPIO_PIN_62 , GPIO_PIN_63 ,	 
	 
 GPIO_PIN_64 , GPIO_PIN_65 , GPIO_PIN_66 , GPIO_PIN_67 , GPIO_PIN_68 , GPIO_PIN_69 , GPIO_PIN_70 , GPIO_PIN_71 ,	 
 GPIO_PIN_72 , GPIO_PIN_73 , GPIO_PIN_74 , GPIO_PIN_75 , GPIO_PIN_76 , GPIO_PIN_77 , GPIO_PIN_78 , GPIO_PIN_79 ,	 
 GPIO_PIN_80 , GPIO_PIN_81 , GPIO_PIN_82 , GPIO_PIN_83 , GPIO_PIN_84 , GPIO_PIN_85 , GPIO_PIN_86 , GPIO_PIN_87 ,	 
 GPIO_PIN_88 , GPIO_PIN_89 , GPIO_PIN_90 , GPIO_PIN_91 , GPIO_PIN_92 , GPIO_PIN_93 , GPIO_PIN_94 , GPIO_PIN_95 ,	 
	 
 GPIO_PIN_96 , GPIO_PIN_97 , GPIO_PIN_98 , GPIO_PIN_99 , GPIO_PIN_100 , GPIO_PIN_101 , GPIO_PIN_102 , GPIO_PIN_103 ,	 
 GPIO_PIN_104 , GPIO_PIN_105 , GPIO_PIN_106 , GPIO_PIN_107 , GPIO_PIN_108 , GPIO_PIN_109 , GPIO_PIN_110 , GPIO_PIN_111 ,	 
 GPIO_PIN_112 , GPIO_PIN_113 , GPIO_PIN_114 , GPIO_PIN_115 , GPIO_PIN_116 , GPIO_PIN_117 , GPIO_PIN_118 , GPIO_PIN_119 ,	 
 GPIO_PIN_120 , GPIO_PIN_121 , GPIO_PIN_122 , GPIO_PIN_123 , GPIO_PIN_124 , GPIO_PIN_125 , GPIO_PIN_126 , GPIO_PIN_127 ,	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PMU_POR = 1 ,	 
 PMU_EMR ,	 
 PMU_WDTR = ( PMU_EMR+2 )	 
 } PMU_LastResetStatus;

typedef UINT8 UART_Activity ;
//ICAT EXPORTED STRUCT 
 typedef struct /* This is structure of the UART Configuration */ 
 {	 
 UART_OpMode opMode ; /* fifo mode , non fifo mode or DMA for basic interface*/	 
 UART_TriggerLevel triggerLevel ; /* the trigger level interrupt on 1 , 8 , 16 , 32 */	 
 UART_BaudRates baudRate ; /* the rate of the transmit and the receive up to 111520 ( default - 9600 ) .*/	 
 UART_WordLen numDataBits ; /* 5 , 6 , 7 , or 8 number of data bits in the UART data frame ( default - 8 ) . */	 
 UART_StopBits stopBits ; /* 1 , 1.500000 or 2 stop bits in the UART data frame ( default - 1 ) . */	 
 UART_ParityTBits parityBitType ; /* Even , Odd or no-parity bit type in the UART data frame ( default - Non ) . */	 
 UART_InterfaceType interfaceType ; /* number of interface that the UART driver supplies ( default - UART_IF_TYPE_L2 ) */	 
 BOOL modemSignal ; /* enable operate modem - TRUE , disable modem - FALSE */	 
 BOOL flowControl ; /* enable Auto flow Control - TRUE , disable Auto flow Control - FALSE */	 
 UINT8 sleepMode ; /* enable sleep mode - TRUE , more fine control - see UARTSleepMode enum */	 
 BOOL auto_baud ; /* enable auto_baud , auto-baud-rate detection within the UART ( default - FALSE ) */	 
 UART_SIRConfigure sirIrDA ;	 
 } UARTConfiguration;

//ICAT EXPORTED ENUM 
 typedef enum // change the order -1 to + 
 {	 
 UART_RC_OK = 1 , /* 1 - no errors */	 
	 
 UART_RC_PORT_NUM_ERROR = -100 , /* -100 - Error in the UART port number */	 
 UART_RC_NO_DATA_TO_READ , /* -99 - Eror no data to read from the FIFO UART */	 
 UART_RC_ILLEGAL_BAUD_RATE , /* -98 - Error in the UART Bayd Rate */	 
 UART_RC_UART_PARITY_BITS_ERROR , /* -97 - Error in parity bit */	 
 UART_RC_UART_ONE_STOP_BIT_ERROR , /* -96 - Error in one stop bit */	 
 UART_RC_ONE_HALF_OR_TWO_STOP_BIT_ERROR , /* -95 - Error in two stop bit */	 
 UART_RC_BAD_INTERFACE_TYPE , /* -94 - Error in the Interface Type */	 
 UART_RC_UART_NOT_AVAILABLE , /* -93 - Error in try to open UART that is open */	 
 UART_RC_NO_DATA_TO_WRITE , /* -92 - Error No data to writ the len = 0 */	 
 UART_RC_NOT_ALL_BYTE_WRITTEN , /* -91 - Error Not all the Byte write to the UART FIFO */	 
 UART_RC_ISR_ALREADY_BIND , /* -90 - Error try to bind ISR for Basic Interface */	 
 UART_RC_WRONG_ISR_UNBIND , /* -89 - Error in the UnBind ISR for Basic Interface */	 
 UART_RC_FIFO_NOT_EMPTY , /* -88 - Error , the UART FIFO not empty */	 
 UART_RC_UART_OPEN , /* -87 - Error try chance the configurr when the UART open */	 
 UART_RC_GPIO_ERR , /* -86 - Error in the Configure of the GPIO */	 
 UART_RC_IRDA_CONFIG_ERR /* -85 - Illegal IrDA configuration */	 
 } UART_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAM_LOG = 0x00 ,	 
 USB_RAM_LOG = 0x01 ,	 
 MAX_RAM_LOG = 0x02	 
 } Ram_Log_Type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAMLOG_ENABLE = 0x00 ,	 
 USB_RAMLOG_ENABLE = 0x01	 
 } RamLog_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 LOG_DISABLE = 0x0 ,	 
 UART_LOG_ENABLE = 0x1 ,	 
 ACAT_LOG_ENABLE = 0x2	 
 } Log_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MSG_DISABLE = 0x0 ,	 
 ACAT_MSG_ENABLE = 0x1	 
 } Msg_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RTI_LOG_DISABLE = 0x0 ,	 
 RTI_LOG_ENABLE = 0x1	 
 } RTI_ConfigE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Log_ConfigE log_cfg ;	 
 Msg_ConfigE msg_cfg ;	 
 RTI_ConfigE rti_cfg ;	 
 RamLog_ConfigE ramlog ;	 
 } Log_ConfigS;

typedef void ( *UARTNotifyInterrupt ) ( UART_Port ) ;
typedef void ( *UsbLogPrint_t ) ( const char * , ... ) ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
typedef volatile unsigned long VUINT32_T ;
typedef unsigned long UINT32_T ;
typedef volatile unsigned int VUINT_T ;
typedef unsigned int UINT_T ;
typedef int INT_T ;
typedef unsigned short UINT16_T , USHORT ;
typedef volatile unsigned short VUINT16_T ;
typedef unsigned char UINT8_T ;
typedef char INT8_T ;
typedef unsigned int UINT , *PUINT ;
typedef unsigned long long UINT64 , *PUINT64 ;
typedef unsigned char UCHAR , BYTE , *PUCHAR ;
typedef int INT , *PINT ;
typedef long long INT64 , *PINT64 ;
typedef char CHAR , *PCHAR ;
typedef unsigned int size_t ;
typedef volatile UINT VUINT , *PVUINT ;
typedef volatile UINT64 VUINT64 , *PVUINT64 ;
typedef volatile unsigned int VUINT32 , *PVUINT32 ;
typedef volatile UINT16 VUINT16 , *PVUINT16 ;
typedef volatile UINT8 VUINT8 , *PVUINT8 ;
typedef volatile UCHAR VUCHAR , *PVUCHAR ;
typedef volatile INT VINT , *PVINT ;
typedef volatile INT64 VINT64 , *PVINT64 ;
typedef volatile int VINT32 , *PVINT32 ;
typedef volatile INT16 VINT16 , *PVINT16 ;
typedef volatile char VINT8 , *PVINT8 ;
typedef volatile CHAR VCHAR , *PVCHAR ;
typedef UINT16 WORD ;
typedef signed char int8_t ;
typedef unsigned char uint8_t ;
typedef signed short int16_t ;
typedef unsigned short uint16_t ;
typedef signed int int32_t ;
typedef unsigned int uint32_t ;
typedef signed long long int int64_t ;
typedef unsigned long long int uint64_t ;
typedef UINT32 rti_uint32_t ;
typedef UINT64 rti_uint64_t ;
typedef UINT32 Attribs_Mmu ;
typedef void ( * mmuNotifyEventFunc ) ( MMU_ERROR_STATUS mmuErrorHandle ) ;
typedef union {
 rw_region_item compress_rw_region_list [ 64 ] ;

 UINT8 filer [ 2048 ] ;
 } LOADTABLE_AREA_RW_CPZ_INFO ;
typedef unsigned char BYTE ;
typedef unsigned short int WORD ;
typedef unsigned long DWORD ;
typedef WORD * WORD_PTR ;
typedef DWORD * DWORD_PTR ;
typedef BYTE BYTE_BITMASK ;
typedef VOID_PTR * VOID_PTR_PTR ;
typedef WORD FLASH_DATA_WIDTH ;
typedef char FDI_TCHAR ;
typedef OSASemaRef SEM_ID ;
typedef int SEM_STATUS ;
typedef SEM_MTX * SEM_MTX_ID ;
typedef WORD IDTYPE ;
typedef DATA_LOOKUP * DATA_LOOKUP_PTR ;
typedef DATA_LOCATION * DATA_LOC_PTR ;
typedef OPEN_PARAM * OPEN_STREAM_PTR ;
typedef COMMAND * COMMAND_PTR ;
typedef UNIT_HEADER * UNIT_HDR_PTR ;
typedef MULTI_INSTANCE * MULTI_INST_PTR ;
typedef BLOCK_INFO * BLK_INFO_PTR ;
typedef COMMAND_CONTROL * CMD_CNTRL_PTR ;
typedef WORD FILE_ID ;
typedef TX_THREAD* rti_thread_t ;
//ICAT EXPORTED STRUCT 
 typedef struct {	 
 UINT32 product_id ;	 
 UINT8 data [ 20 ] ;	 
 } InfoForBoardTracking_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 rti_mode_none = 0x00 ,	 
 rti_check_mode = 0x01 ,	 
 rti_timer_mode = 0x02 ,	 
 rti_log2acat_mode = 0x03 ,	 
 rti_psoff_mode = 0x04 ,	 
 rti_uarttrace_mode = 0x05 ,	 
 rti_rfuarttest_mode = 0xFF ,	 
	 
 rti_urtlog_mode = 0x100 ,	 
 rti_usbtrace_mode = 0x101 ,	 
 rti_muxtrace_mode = 0x102 ,	 
 rti_fsyslog_mode = 0x103 ,	 
 rti_mode_max = 0xFFFF	 
 } rti_mode;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_DISABLE=0 ,	 
 RTI_EN_VER1=1 ,	 
 RTI_EN_VER2=2	 
 } RTI_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_QUE_DISABLE=0 ,	 
 RTI_QUE_ENABLE=1	 
 } RTI_QUE_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_EVT_DISABLE=0 ,	 
 RTI_EVT_ENABLE=1	 
 } RTI_EVT_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 TIME_OUT_3MS=0x62 ,	 
 TIME_OUT_4MS=0x83 ,	 
 TIME_OUT_5MS=0xA4 ,	 
 TIME_OUT_6MS=0xC4 ,	 
 TIME_OUT_MAX=0xFF	 
 } Timeout_Threshold;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 RTI_TYPE rtiType ;	 
 RTI_QUE_TYPE rtiQueType ;	 
 RTI_EVT_TYPE rtiEvtType ;	 
	 
 int rtiChange ;	 
 int rtiHT ;	 
 int rtiLT ;	 
	 
 int modeChange ;	 
 int modeHT ;	 
 int modeLT ;	 
	 
 Timeout_Threshold Timeout ;	 
 rti_mode rtiMode ;	 
 } RTICfg_t;

typedef signed char int8_t ;
typedef signed short int int16_t ;
typedef signed int int32_t ;
typedef signed __int64 int64_t ;
typedef unsigned char uint8_t ;
typedef unsigned short int uint16_t ;
typedef unsigned int uint32_t ;
typedef unsigned __int64 uint64_t ;
typedef signed char int_least8_t ;
typedef signed short int int_least16_t ;
typedef signed int int_least32_t ;
typedef signed __int64 int_least64_t ;
typedef unsigned char uint_least8_t ;
typedef unsigned short int uint_least16_t ;
typedef unsigned int uint_least32_t ;
typedef unsigned __int64 uint_least64_t ;
typedef signed int int_fast8_t ;
typedef signed int int_fast16_t ;
typedef signed int int_fast32_t ;
typedef signed __int64 int_fast64_t ;
typedef unsigned int uint_fast8_t ;
typedef unsigned int uint_fast16_t ;
typedef unsigned int uint_fast32_t ;
typedef unsigned __int64 uint_fast64_t ;
typedef signed int intptr_t ;
typedef unsigned int uintptr_t ;
typedef signed long long intmax_t ;
typedef unsigned long long uintmax_t ;
typedef unsigned short wchar_t ;
typedef uint32_t lfs_size_t ;
typedef uint32_t lfs_off_t ;
typedef int32_t lfs_ssize_t ;
typedef int32_t lfs_soff_t ;
typedef uint32_t lfs_block_t ;
//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_simPin.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/simPin.c
typedef unsigned int size_t ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
typedef union {
 rw_region_item compress_rw_region_list [ 64 ] ;

 UINT8 filer [ 2048 ] ;
 } LOADTABLE_AREA_RW_CPZ_INFO ;
//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_mep.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/mep.c
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
typedef UINT8 MEP_BCD ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 number [ 32 ] ;	 
 UINT8 length ;	 
 } MEP_PASSWORD;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT16 year ;	 
 UINT8 month ;	 
 UINT8 day ;	 
 } MEP_DATE;

typedef UINT16 MEP_MNC ;
typedef UINT16 MEP_MCC ;
typedef UINT16 MEP_AccessTechnologyId ;
typedef UINT8 MEP_Boolean ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 serviceproviderId [ 4 ] ;	 
 } MEP_CODE_SP;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 corporateId [ 4 ] ;	 
 } MEP_CODE_CP;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_SIMPLMN networkIds [ 100 ] ;	 
 MEP_CODE_NS lockNS [ 100 ] ;	 
 MEP_CODE_SP lockSP [ 100 ] ;	 
 MEP_CODE_CP lockCP [ 100 ] ;	 
 MEP_CODE_SIMUSIM SimUsim ;	 
 } MEP_CODE;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_CODE code ;	 
 UINT8 DefaultSiActivated ;	 
 UINT8 DefaultSiEnabled ;	 
 } MEP_CAT_DATA;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_PASSWORD CatPsw [ MEP_MAX_CAT ] ;	 
 MEP_PASSWORD UnblockPsw ;	 
 // #if defined ( SS_IPC_SUPPORT ) && defined ( EXTENED_TRIAL_LIMIT_MEP )	 
	 
	 
 UINT8 TrialLimit [ MEP_MAX_CAT ] ;	 
	 
	 
	 
 } MEP_BLOCK_DATA;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 /*modified for #499867 2014.031800 by yunhail begin*/	 
 UINT32 VersionSize ;	 
 /*modified for #499867 2014.031800 by yunhail end*/	 
 MEP_CAT_DATA data ; // !!! Run F8 And F9 on this field	 
 MEP_BLOCK_DATA blocking ; // !!! Run F8 And F9 on this field	 
 UINT8 signature [ 20 ] ; // This is the output of the F9 function	 
 UINT8 DataIsEncrypted ; // Should be set to FALSE or TRUE -- do not run F8 | F9	 
 } MEP_FIX;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 SiActivated ;	 
 UINT8 SiEnabled ;	 
 UINT8 TrialCounter [ MEP_MAX_CAT ] ;	 
 UINT8 padding [ 15 ] ;	 
 UINT8 signature [ 20 ] ;	 
 } MEP_CHANGEABLE;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 iccid [ 10 ] ;	 
 MEP_DATE date ;	 
 } UDP_ASL_DATA;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UDP_ASL_DATA data [ 10 ] ;	 
 UINT8 NumAslEntries ;	 
 } UDP_ASL;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_PASSWORD psw ;	 
 UDP_ASL asl ;	 
 UINT8 si ;	 
 UINT8 padding [ 15 ] ;	 
 UINT8 signature [ 20 ] ;	 
 } UDP_CHANGEABLE;

typedef void ( *MEP_Callback ) ( MEP_UDP_RC* ) ;
typedef union
 {
 void ( *MEP_PutTC_Callback ) ( MEP_UDP_RC* ) ;
 void ( *MEP_PutSI_Callback ) ( MEP_UDP_RC* ) ;
 void ( *UDP_PutSI_Callback ) ( MEP_UDP_RC* ) ;
 void ( *UDP_PutASL_Callback ) ( MEP_UDP_RC* ) ;
 void ( *UDP_PutPassword_Callback ) ( MEP_UDP_RC* ) ;
 } MEP_UDP_CallBackFuncS ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 ulDebugInfoTag ;	 
 UINT32 ulMrdInfo ;	 
 UINT32 ulMepInfo ;	 
 } MRD_DEBUG_INFO;

typedef unsigned int size_t ;
typedef va_list __gnuc_va_list ;
typedef unsigned int size_t ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0 = 0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 , GPIO_PIN_33 , GPIO_PIN_34 , GPIO_PIN_35 , GPIO_PIN_36 , GPIO_PIN_37 , GPIO_PIN_38 , GPIO_PIN_39 ,	 
	 
 GPIO_PIN_40 , GPIO_PIN_41 , GPIO_PIN_42 , GPIO_PIN_43 , GPIO_PIN_44 , GPIO_PIN_45 , GPIO_PIN_46 , GPIO_PIN_47 ,	 
 GPIO_PIN_48 , GPIO_PIN_49 , GPIO_PIN_50 , GPIO_PIN_51 , GPIO_PIN_52 , GPIO_PIN_53 , GPIO_PIN_54 , GPIO_PIN_55 ,	 
 GPIO_PIN_56 , GPIO_PIN_57 , GPIO_PIN_58 , GPIO_PIN_59 , GPIO_PIN_60 , GPIO_PIN_61 , GPIO_PIN_62 , GPIO_PIN_63 ,	 
	 
 GPIO_PIN_64 , GPIO_PIN_65 , GPIO_PIN_66 , GPIO_PIN_67 , GPIO_PIN_68 , GPIO_PIN_69 , GPIO_PIN_70 , GPIO_PIN_71 ,	 
 GPIO_PIN_72 , GPIO_PIN_73 , GPIO_PIN_74 , GPIO_PIN_75 , GPIO_PIN_76 , GPIO_PIN_77 , GPIO_PIN_78 , GPIO_PIN_79 ,	 
 GPIO_PIN_80 , GPIO_PIN_81 , GPIO_PIN_82 , GPIO_PIN_83 , GPIO_PIN_84 , GPIO_PIN_85 , GPIO_PIN_86 , GPIO_PIN_87 ,	 
 GPIO_PIN_88 , GPIO_PIN_89 , GPIO_PIN_90 , GPIO_PIN_91 , GPIO_PIN_92 , GPIO_PIN_93 , GPIO_PIN_94 , GPIO_PIN_95 ,	 
	 
 GPIO_PIN_96 , GPIO_PIN_97 , GPIO_PIN_98 , GPIO_PIN_99 , GPIO_PIN_100 , GPIO_PIN_101 , GPIO_PIN_102 , GPIO_PIN_103 ,	 
 GPIO_PIN_104 , GPIO_PIN_105 , GPIO_PIN_106 , GPIO_PIN_107 , GPIO_PIN_108 , GPIO_PIN_109 , GPIO_PIN_110 , GPIO_PIN_111 ,	 
 GPIO_PIN_112 , GPIO_PIN_113 , GPIO_PIN_114 , GPIO_PIN_115 , GPIO_PIN_116 , GPIO_PIN_117 , GPIO_PIN_118 , GPIO_PIN_119 ,	 
 GPIO_PIN_120 , GPIO_PIN_121 , GPIO_PIN_122 , GPIO_PIN_123 , GPIO_PIN_124 , GPIO_PIN_125 , GPIO_PIN_126 , GPIO_PIN_127 ,	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
typedef UINT32 Attribs_Mmu ;
typedef void ( * mmuNotifyEventFunc ) ( MMU_ERROR_STATUS mmuErrorHandle ) ;
typedef union {
 rw_region_item compress_rw_region_list [ 64 ] ;

 UINT8 filer [ 2048 ] ;
 } LOADTABLE_AREA_RW_CPZ_INFO ;
typedef unsigned char BYTE ;
typedef unsigned short int WORD ;
typedef unsigned long DWORD ;
typedef WORD * WORD_PTR ;
typedef DWORD * DWORD_PTR ;
typedef BYTE BYTE_BITMASK ;
typedef VOID_PTR * VOID_PTR_PTR ;
typedef WORD FLASH_DATA_WIDTH ;
typedef char FDI_TCHAR ;
typedef OSASemaRef SEM_ID ;
typedef int SEM_STATUS ;
typedef SEM_MTX * SEM_MTX_ID ;
typedef WORD IDTYPE ;
typedef DATA_LOOKUP * DATA_LOOKUP_PTR ;
typedef DATA_LOCATION * DATA_LOC_PTR ;
typedef OPEN_PARAM * OPEN_STREAM_PTR ;
typedef COMMAND * COMMAND_PTR ;
typedef UNIT_HEADER * UNIT_HDR_PTR ;
typedef MULTI_INSTANCE * MULTI_INST_PTR ;
typedef BLOCK_INFO * BLK_INFO_PTR ;
typedef COMMAND_CONTROL * CMD_CNTRL_PTR ;
typedef WORD FILE_ID ;
typedef UINT32 lowTaskEventHandler_t ;
typedef void ( *LowEventFuncPtr ) ( lowTaskEventHandler_t ) ;
typedef unsigned char Ipp8u ;
typedef unsigned short Ipp16u ;
typedef unsigned int Ipp32u ;
typedef signed char Ipp8s ;
typedef signed short Ipp16s ;
typedef signed int Ipp32s ;
typedef float Ipp32f ;
typedef long long Ipp64s ;
typedef unsigned long long Ipp64u ;
typedef double Ipp64f ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
DIAG_FILTER ( SW_PLAT , MEP , CheckSignatureFail , DIAG_ERROR)  
 diagPrintf ( " Error -- Mep signature checking failed " );

DIAG_FILTER ( SW_PLAT , MEP , CheckSignature , DIAG_INFORMATION)  
 diagPrintf ( " Signature checking finish ok " );

DIAG_FILTER ( SW_PLAT , MEP , ippsSHA1MessageDigestFail , DIAG_ERROR)  
 diagPrintf ( " Error -- Integrity checking failed " );

DIAG_FILTER ( SW_PLAT , MEP , ippsSHA1MessageDigest , DIAG_INFORMATION)  
 diagPrintf ( " Integrity checking finish ok " );

DIAG_FILTER ( SW_PLAT , MEP , ippsRijndael128DecryptCBCFail , DIAG_ERROR)  
 diagPrintf ( " Error -- ippsRijndael128 Decrypt CBC " );

DIAG_FILTER ( SW_PLAT , MEP , ippsRijndael128DecryptCBC , DIAG_INFORMATION)  
 diagPrintf ( " Decrypt ippsRijndael128 finish ok " );

DIAG_FILTER ( SW_PLAT , MEP , ippsRijndael128EncryptCBCFail , DIAG_ERROR)  
 diagPrintf ( " Error -- ippsRijndael128 Encrypt CBC " );

DIAG_FILTER ( SW_PLAT , MEP , ippsRijndael128EncryptCBC , DIAG_INFORMATION)  
 diagPrintf ( " Encrypt ippsRijndael128 finish ok " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_MAIN_FIX_DATA , DIAG_INFORMATION)  
 diagPrintf ( " Initialization Mep fix data base " );

DIAG_FILTER ( SW_PLAT , MEP , RunConfidentiality_start , DIAG_INFORMATION)  
 diagPrintf ( " Encrypt mep --- Run Confidentiality started " );

DIAG_FILTER ( SW_PLAT , MEP , RunConfidentiality_ended , DIAG_INFORMATION)  
 diagPrintf ( " Encrypt mep --- Run Confidentiality ended " );

DIAG_FILTER ( SW_PLAT , MEP , RunIntegrity_start , DIAG_INFORMATION)  
 diagPrintf ( " Integrity mep --- Run Integrity started " );

DIAG_FILTER ( SW_PLAT , MEP , RunIntegrity_end , DIAG_INFORMATION)  
 diagPrintf ( " Integrity mep --- Run Integrity ended " );

DIAG_FILTER ( SW_PLAT , MEP , UdpRunConfidentiality_start , DIAG_INFORMATION)  
 diagPrintf ( " Encrypt udp --- Run Confidentiality started " );

DIAG_FILTER ( SW_PLAT , MEP , UdpRunConfidentiality_ended , DIAG_INFORMATION)  
 diagPrintf ( " Encrypt udp --- Run Confidentiality ended " );

DIAG_FILTER ( SW_PLAT , MEP , UdpRunIntegrity_start , DIAG_INFORMATION)  
 diagPrintf ( " Integrity udp --- Run Integrity started " );

DIAG_FILTER ( SW_PLAT , MEP , UdpRunIntegrity_end , DIAG_INFORMATION)  
 diagPrintf ( " Integrity udp --- Run Integrity ended " );

//ICAT EXPORTED STRUCT 
 typedef struct NVM_Header 
 {	 
 unsigned long StructSize ; // the size of the user structure below	 
 unsigned long NumofStructs ; // >1 in case of array of structs ( default is 1 ) .	 
 char StructName [ 64 ] ; // the name of the user structure below	 
 char Date [ 32 ] ; // date updated by the ICAT when the file is saved. Filled by calibration SW.	 
 char time [ 32 ] ; // time updated by the ICAT when the file is saved. Filled by calibration SW.	 
 char Version [ 64 ] ; // user version - this field is updated by the SW eng. Every time they update the UserStruct.	 
 char HW_ID [ 32 ] ; // signifies the board number. Filled by calibration SW.	 
 char CalibVersion [ 32 ] ; // signifies the calibration SW version. Filled by calibration SW.	 
 } NVM_Header_ts;

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE , DIAG_INFORMATION)  
 diagPrintf ( " Start saving mep file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE_HEADER , DIAG_INFORMATION)  
 diagPrintf ( " the mep.nvm header write fail " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE_END , DIAG_INFORMATION)  
 diagPrintf ( " Saving mep file end " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_WRITE_FILE , DIAG_ERROR)  
 diagPrintf ( " Error -- Writing to MEP file failed " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CLOSE_FILE_start , DIAG_INFORMATION)  
 diagPrintf ( " Start closing mep file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CLOSE_FILE , DIAG_INFORMATION)  
 diagPrintf ( " Mep file closed " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE_FAIL , DIAG_ERROR)  
 diagPrintf ( " Error -- Cant open MEP file for save " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE_2 , DIAG_INFORMATION)  
 diagPrintf ( " Start saving mep file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE_HEADER_2 , DIAG_INFORMATION)  
 diagPrintf ( " the mep.nvm header write fail " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE_END_2 , DIAG_INFORMATION)  
 diagPrintf ( " Saving mep file end " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_WRITE_FILE_2 , DIAG_ERROR)  
 diagPrintf ( " Error -- Writing to MEP file failed " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CLOSE_FILE_start_2 , DIAG_INFORMATION)  
 diagPrintf ( " Start closing mep file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CLOSE_FILE_2 , DIAG_INFORMATION)  
 diagPrintf ( " Mep file closed " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_FILE_FAIL_2 , DIAG_ERROR)  
 diagPrintf ( " Error -- Cant open MEP file for save " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_FILE , DIAG_INFORMATION)  
 diagPrintf ( " Start saving udp file " );

DIAG_FILTER ( SW_PLAT , UDP , UDP_SAVE_FILE_HEADER , DIAG_INFORMATION)  
 diagPrintf ( " the UDP.nvm header write fail " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_FILE_END , DIAG_INFORMATION)  
 diagPrintf ( " Saving udp file end " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_WRITE_FILE , DIAG_ERROR)  
 diagPrintf ( " Error -- Writing to UDP file failed " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_CLOSE_FILE_start , DIAG_INFORMATION)  
 diagPrintf ( " Start closing udp file " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_CLOSE_FILE , DIAG_INFORMATION)  
 diagPrintf ( " Udp file closed " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_FILE_FAIL , DIAG_ERROR)  
 diagPrintf ( " Error -- Cant open UDP file for save " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CHANGEABLE_OPEN_RO , DIAG_INFORMATION)  
 diagPrintf ( " Opening MEP Changeable Data file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CHANGEABLE_OPEN_RW , DIAG_INFORMATION)  
 diagPrintf ( " Creating new MEP changeable data file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_DEFAULT_IMEI , DIAG_INFORMATION)  
 diagPrintf ( " Got default IMEI - MEP changeable data file wasn ' t created " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CHANGEABLE_OPEN_RO_2 , DIAG_INFORMATION)  
 diagPrintf ( " Opening MEP Changeable Data file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_CHANGEABLE_OPEN_RW_2 , DIAG_INFORMATION)  
 diagPrintf ( " Creating new MEP changeable data file " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_DEFAULT_IMEI_2 , DIAG_INFORMATION)  
 diagPrintf ( " Got default IMEI - MEP changeable data file wasn ' t created " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_CHANGEABLE_OPEN_RO , DIAG_INFORMATION)  
 diagPrintf ( " Opening UDP Changeable Data file " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_CHANGEABLE_OPEN_RW , DIAG_INFORMATION)  
 diagPrintf ( " Creating new UDP changeable data file " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_DEFAULT_IMEI , DIAG_INFORMATION)  
 diagPrintf ( " Got default IMEI - UDP changeable data file wasn ' t created " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb1 , DIAG_INFORMATION)  
 diagPrintf ( " Udp calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb_end1 , DIAG_INFORMATION)  
 diagPrintf ( " Udp after CB " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb_end2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp after CB " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb1_2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb_end1_2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp after CB " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb2_2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_SAVE_Handler_cb_end2_2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp after CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb , DIAG_INFORMATION)  
 diagPrintf ( " Udp calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb_end , DIAG_INFORMATION)  
 diagPrintf ( " Udp after CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb1 , DIAG_INFORMATION)  
 diagPrintf ( " Mep calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb_end1 , DIAG_INFORMATION)  
 diagPrintf ( " Mep after CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb_2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb_end_2 , DIAG_INFORMATION)  
 diagPrintf ( " Udp after CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb1_2 , DIAG_INFORMATION)  
 diagPrintf ( " Mep calling to CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_SAVE_Handler_cb_end1_2 , DIAG_INFORMATION)  
 diagPrintf ( " Mep after CB " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetCK_CMD , DIAG_INFORMATION)  
 diagPrintf ( " MEP got CK read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetGC_CMD , DIAG_INFORMATION)  
 diagPrintf ( " MEP got GC read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetGC_CMD_2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP 2 got GC read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetTC_CMD , DIAG_INFORMATION)  
 diagPrintf ( " MEP got TC read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetTC_CMD_2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP 2 got TC read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetSI_CMD_2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP 2 got SI read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetTL_CMD , DIAG_INFORMATION)  
 diagPrintf ( " MEP got TL read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetTL_CMD_2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP 2 got TL read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetUPW_CMD , DIAG_INFORMATION)  
 diagPrintf ( " MEP got UPSW read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_GetUPW_CMD_2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP 2 got UPSW read command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutTC_CMD , DIAG_INFORMATION)  
 diagPrintf ( " MEP got TC write command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutTC_CMD1 , DIAG_INFORMATION)  
 diagPrintf ( " MEP_PutTC errorCode=%d " , mep->MepErrorCode );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutTC_CMD2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP_PutTC second para is valid " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutTC_CMD3 , DIAG_INFORMATION)  
 diagPrintf ( " MEP_SaveMepData send sigal fail " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutTC_CMD_2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP 2 got TC write command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutSI_CMD , DIAG_INFORMATION)  
 diagPrintf ( " MEP got SI write command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutSI_CMD1 , DIAG_INFORMATION)  
 diagPrintf ( " MEP_PutSI errorCode=%d " , mep->MepErrorCode );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutSI_CMD_2 , DIAG_INFORMATION)  
 diagPrintf ( " MEP 2 got SI write command " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_GetSI_CMD , DIAG_INFORMATION)  
 diagPrintf ( " UDP got SI read command " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_GetASL_CMD , DIAG_INFORMATION)  
 diagPrintf ( " UDP got ASL read command " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_GetPassword_CMD , DIAG_INFORMATION)  
 diagPrintf ( " UDP got PSW read command " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_PutSI_CMD , DIAG_INFORMATION)  
 diagPrintf ( " UDP got SI write command " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_PutASL_CMD , DIAG_INFORMATION)  
 diagPrintf ( " UDP got ASL write command " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_PutPassword_CMD , DIAG_INFORMATION)  
 diagPrintf ( " UDP got PSW write command " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_MAIN_FIX , DIAG_INFORMATION)  
 diagPrintf ( " Mep fix data base init finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_MAIN_CHANGEABLE , DIAG_INFORMATION)  
 diagPrintf ( " Mep changeable data init finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_MAIN_FIX , DIAG_INFORMATION)  
 diagPrintf ( " Mep fix data base init finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_MAIN_CHANGEABLE , DIAG_INFORMATION)  
 diagPrintf ( " Mep changeable data init finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_MAIN_CHANGEABLE , DIAG_INFORMATION)  
 diagPrintf ( " Udp changeable data init finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_MAIN_CHANGEABLE , DIAG_INFORMATION)  
 diagPrintf ( " Udp changeable data init finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutTC_Callback , DIAG_INFORMATION)  
 diagPrintf ( " MEP_PutTC_Callback finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_PutSI_Callback , DIAG_INFORMATION)  
 diagPrintf ( " MEP_PutSI_Callback finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_PutSI_Callback , DIAG_INFORMATION)  
 diagPrintf ( " UDP_PutSI_Callback finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_PutASL_Callback , DIAG_INFORMATION)  
 diagPrintf ( " UDP_PutASL_Callback finished ok " );

DIAG_FILTER ( SW_PLAT , MEP , UDP_PutPassword_Callback , DIAG_INFORMATION)  
 diagPrintf ( " UDP_PutPassword_Callback finished ok " );

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_read_si 
 void mep_read_si ( void ) 
 {	 
 UINT8 SiActivated , SiEnabled ;	 
	 
	 
 MEP_GetSI ( &SiActivated , &SiEnabled ) ;	 
	 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_si , DIAG_INFORMATION)  
 diagPrintf ( " Mep SiActivated value is = %x and SiEnabled is = %x " , SiActivated , SiEnabled );

	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_write_si 
 void mep_write_si ( void *p ) 
 {	 
 UINT8 *param = ( UINT8 * ) p ;	 
	 
	 
 cb.MEP_PutSI_Callback = MEP_PutSI_Callback ;	 
 MEP_PutSI ( &param [ 0 ] , &param [ 1 ] , &cb ) ;	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_read_tl 
 void mep_read_tl ( void ) 
 {	 
 UINT8 tl ;	 
 MEP_UDP_RC rc ;	 
	 
 MEP_CAT category ;	 
	 
 for ( category = MEP_CAT_NP ; category < MEP_MAX_CAT ; category ++ )	 
 {		 
 rc = MEP_GetTL ( category , &tl ) ;		 
 if ( rc != MEP_UDP_RC_OK )		 
 {			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_tl_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading MEP - TL " );

			 
 return ;			 
 }		 
		 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_tl , DIAG_INFORMATION)  
 diagPrintf ( " Mep TL [ cat: %d ] value is = %x " , category , tl );

		 
 }	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_read_upsw 
 void mep_read_upsw ( void ) 
 {	 
 MEP_PASSWORD Upsw ;	 
 MEP_UDP_RC rc ;	 
	 
	 
 rc = ME_GetUPW ( &Upsw ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_upsw_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading MEP - UPSW " );

		 
 return ;		 
 }	 
	 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_upw , DIAG_INFORMATION)  
 diagPrintf ( " Mep u_psw value is = %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , length = %x " , Upsw.number [ 0 ] , Upsw.number [ 1 ] , Upsw.number [ 2 ] , Upsw.number [ 3 ] , Upsw.number [ 4 ] , Upsw.number [ 5 ] , Upsw.number [ 6 ] , Upsw.number [ 7 ] , Upsw.number [ 8 ] , Upsw.number [ 9 ] , Upsw.number [ 10 ] , Upsw.number [ 11 ] , Upsw.number [ 12 ] , Upsw.number [ 13 ] , Upsw.number [ 14 ] , Upsw.number [ 15 ] , Upsw.length );

	 
	 
	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_read_ck 
 void mep_read_ck ( void ) 
 {	 
 MEP_PASSWORD ck ;	 
 MEP_UDP_RC rc ;	 
 MEP_CAT category ;	 
	 
	 
 for ( category = MEP_CAT_NP ; category < MEP_MAX_CAT ; category++ )	 
 {		 
 rc = MEP_GetCK ( category , &ck ) ;		 
 if ( rc != MEP_UDP_RC_OK )		 
 {			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_ck_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading MEP - CK " );

			 
 return ;			 
 }		 
		 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_ck , DIAG_INFORMATION)  
 diagPrintf ( " Mep CK of category %x value is = %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , length = %x " , category , ck.number [ 0 ] , ck.number [ 1 ] , ck.number [ 2 ] , ck.number [ 3 ] , ck.number [ 4 ] , ck.number [ 5 ] , ck.number [ 6 ] , ck.number [ 7 ] , ck.number [ 8 ] , ck.number [ 9 ] , ck.number [ 10 ] , ck.number [ 11 ] , ck.number [ 12 ] , ck.number [ 13 ] , ck.number [ 14 ] , ck.number [ 15 ] , ck.length );

		 
		 
		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_read_gc 
 void mep_read_gc ( void ) 
 {	 
 MEP_CODE gc ;	 
 MEP_UDP_RC rc ;	 
 MEP_CODE_ID codeId ;	 
 UINT8 i ;	 
	 
 for ( codeId = MEP_CODE_ID_NP ; codeId < MEP_MAX_CODE_ID_CAT ; codeId++ )	 
 {		 
 rc = MEP_GetGC ( codeId , &gc ) ;		 
 if ( rc != MEP_UDP_RC_OK )		 
 {			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_gc_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading MEP - GC " );

			 
 return ;			 
 }		 
		 
 switch ( codeId )		 
 {			 
 case MEP_CODE_ID_NP :			 
 for ( i = 0 ; i < 100 ; i++ )			 
 {				 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_gc_NP , DIAG_INFORMATION)  
 diagPrintf ( " NP code_id value is mcc [ %x ] = %x , mnc [ %x ] = %x , at [ %x ] = %x , ThreeDigitsDecoded [ %x ] = %x " , i , gc.networkIds [ i ] .plmn.mcc , i , gc.networkIds [ i ] .plmn.mnc , i , gc.networkIds [ i ] .plmn.AccessTechnology , i , gc.networkIds [ i ] .mncThreeDigitsDecoding );

				 
				 
 }			 
 break ;			 
 case MEP_CODE_ID_NSP :			 
			 
			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_gc_NSP , DIAG_INFORMATION)  
 diagPrintf ( " NSP code_id value is = %x , " , gc.lockNS [ 0 ] .networkSubsetId [ 0 ] );

			 
			 
 break ;			 
 case MEP_CODE_ID_SP :			 
			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_gc_SP , DIAG_INFORMATION)  
 diagPrintf ( " SP code_id value is = %x " , gc.lockSP [ 0 ] .serviceproviderId [ 0 ] );

			 
			 
 break ;			 
 case MEP_CODE_ID_CP :			 
			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_gc_CP , DIAG_INFORMATION)  
 diagPrintf ( " CP code_id value is = %x " , gc.lockCP [ 0 ] .corporateId [ 0 ] );

			 
			 
 break ;			 
 case MEP_CODE_ID_SIM_LOCK :			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_gc_SIMLOCK , DIAG_INFORMATION)  
 diagPrintf ( " SIM_LOCK code_id length is = %x , contents = %x , %x , %x , %x , %x , %x , %x , %x " , gc.SimUsim.simId.length , gc.SimUsim.simId.contents [ 0 ] , gc.SimUsim.simId.contents [ 1 ] , gc.SimUsim.simId.contents [ 2 ] , gc.SimUsim.simId.contents [ 3 ] , gc.SimUsim.simId.contents [ 4 ] , gc.SimUsim.simId.contents [ 5 ] , gc.SimUsim.simId.contents [ 6 ] , gc.SimUsim.simId.contents [ 7 ] );

			 
			 
			 
 break ;			 
 default:			 
 return ;			 
 }		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_read_tc 
 void mep_read_tc ( void ) 
 {	 
 UINT8 tc ;	 
 MEP_UDP_RC rc ;	 
 MEP_CAT category ;	 
	 
	 
 for ( category = MEP_CAT_NP ; category < MEP_MAX_CAT ; category++ )	 
 {		 
 rc = MEP_GetTC ( category , &tc ) ;		 
 if ( rc != MEP_UDP_RC_OK )		 
 {			 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_tc_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading MEP - TC " );

			 
 return ;			 
 }		 
		 
DIAG_FILTER ( SW_PLAT , MEP , mep_read_gc , DIAG_INFORMATION)  
 diagPrintf ( " Mep TC of category %x value is = %x " , category , tc );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , udp_read_si 
 void udp_read_si ( void ) 
 {	 
 UINT8 SiActivated ;	 
 MEP_UDP_RC rc ;	 
	 
	 
 rc = UDP_GetSI ( &SiActivated ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_si_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading UDP - SI or UDP is not support " );

		 
 return ;		 
 }	 
	 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_si , DIAG_INFORMATION)  
 diagPrintf ( " Udp SiActivated value is = %x " , SiActivated );

	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , udp_read_psw 
 void udp_read_psw ( void ) 
 {	 
 MEP_PASSWORD psw ;	 
 MEP_UDP_RC rc ;	 
	 
	 
 rc = UDP_GetPassword ( &psw ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_psw_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading UDP - PSW or UDP is not support " );

		 
 return ;		 
 }	 
	 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_psw , DIAG_INFORMATION)  
 diagPrintf ( " Udp psw value is = %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x , %x " , psw.number [ 0 ] , psw.number [ 1 ] , psw.number [ 2 ] , psw.number [ 3 ] , psw.number [ 4 ] , psw.number [ 5 ] , psw.number [ 6 ] , psw.number [ 7 ] , psw.number [ 8 ] , psw.number [ 9 ] , psw.number [ 10 ] , psw.number [ 11 ] , psw.number [ 12 ] , psw.number [ 13 ] , psw.number [ 14 ] , psw.number [ 15 ] );

	 
	 
	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , udp_read_asl 
 void udp_read_asl ( void ) 
 {	 
 UDP_ASL asl ;	 
 UINT8 i ;	 
 MEP_UDP_RC rc ;	 
	 
	 
 rc = UDP_GetASL ( &asl ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_asl_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading UDP - ASL or UDP is not support " );

		 
 return ;		 
 }	 
	 
 for ( i = 0 ; i < 10 ; i++ )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_asl , DIAG_INFORMATION)  
 diagPrintf ( " Udp ASL data_%x is = %x , %x , %x , %x , %x , %x , %x , %x , %x , %x Date = %u.%u.%u " , i , asl.data [ i ] .iccid [ 0 ] , asl.data [ i ] .iccid [ 1 ] , asl.data [ i ] .iccid [ 2 ] , asl.data [ i ] .iccid [ 3 ] , asl.data [ i ] .iccid [ 4 ] , asl.data [ i ] .iccid [ 5 ] , asl.data [ i ] .iccid [ 6 ] , asl.data [ i ] .iccid [ 7 ] , asl.data [ i ] .iccid [ 8 ] , asl.data [ i ] .iccid [ 9 ] , asl.data [ i ] .date.day , asl.data [ i ] .date.month , asl.data [ i ] .date.year );

		 
		 
		 
		 
 }	 
	 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_asl_1 , DIAG_INFORMATION)  
 diagPrintf ( " Udp ASL number of entries is = %x " , asl.NumAslEntries );

	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , mep_write_tc 
 void mep_write_tc ( void *p ) 
 {	 
 UINT8 *param = ( UINT8 * ) p ;	 
 MEP_UDP_RC rc ;	 
	 
	 
 cb.MEP_PutTC_Callback = MEP_PutTC_Callback ;	 
 rc = MEP_PutTC ( &param [ 0 ] , ( MEP_CAT ) param [ 1 ] , &cb ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_write_tc_fail , DIAG_ERROR)  
 diagPrintf ( " Error while writing to MEP TC " );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , udp_write_si 
 void udp_write_si ( void *p ) 
 {	 
 UINT8 *param = ( UINT8 * ) p ;	 
 MEP_UDP_RC rc ;	 
	 
	 
 cb.UDP_PutSI_Callback = UDP_PutSI_Callback ;	 
 rc = UDP_PutSI ( &param [ 0 ] , &cb ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_write_si_fail , DIAG_ERROR)  
 diagPrintf ( " Error while writing to Udp si or UDP is not support " );

		 
 return ;		 
 }	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , udp_write_psw 
 void udp_write_psw ( void *p ) 
 {	 
 UINT8 *param = ( UINT8 * ) p ;	 
 MEP_PASSWORD psw ;	 
 UINT8 i ;	 
 MEP_UDP_RC rc ;	 
	 
	 
	 
	 
 rc = UDP_GetPassword ( &psw ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_read_write_psw_fail , DIAG_ERROR)  
 diagPrintf ( " Error while reading Udp psw or UDP is not support " );

		 
 return ;		 
 }	 
	 
 if ( param [ 0 ] == 1 )	 
 {		 
 for ( i = 0 ; i < 32 ; i++ )		 
 psw.number [ i ] = ( psw.number [ i ] + 1 ) ;		 
 }	 
 else	 
 {		 
 for ( i = 0 ; i < 32 ; i++ )		 
 psw.number [ i ] = ( psw.number [ i ] - 1 ) ;		 
 }	 
	 
 cb.UDP_PutPassword_Callback = UDP_PutPassword_Callback ;	 
 rc = UDP_PutPassword ( &psw , &cb ) ;	 
 if ( rc != MEP_UDP_RC_OK )	 
 {		 
DIAG_FILTER ( SW_PLAT , MEP , udp_write_psw_fail , DIAG_ERROR)  
 diagPrintf ( " Error while writing to Udp psw or UDP is not support " );

		 
 return ;		 
 }	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , MEP , MepEncryptTest 
 void MepEncryptTest ( void ) 
 {	 
	 
 }

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_INT_IFC ,	 
 DIAG_EXT_IFC	 
 } DIAG_IF_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_UART_SPEED_921600 ,	 
 DIAG_UART_SPEED_1842000 ,	 
 DIAG_UART_SPEED_3000000 ,	 
 DIAG_UART_DISABLE ,	 
 } DIAG_UART_SPEED_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_MEM_ENABLE ,	 
 DIAG_MEM_DISABLE	 
 } DIAG_MEM_SWITCH;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_DEV_USB ,	 
 DIAG_DEV_SD ,	 
 DIAG_DEV_FS ,	 
 DIAG_DEV_UART ,	 
 DIAG_DEV_SPI ,	 
 DIAG_DEV_NONE	 
 } DIAG_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 SULOG_DEV_USB ,	 
 SULOG_DEV_SD	 
 } SULOG_DEV_TYPE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 DIAG_IF_TYPE useIntIf ; /* use internal interface or external interface */	 
 DIAG_UART_SPEED_TYPE useHighSpeedUART ; /* high speed UART ( 460800 ) or low speed UART ( 115200 ) */	 
 DIAG_MEM_SWITCH diagMemSwitch ;	 
 DIAG_DEV_TYPE diagDevType ;	 
 SULOG_DEV_TYPE sulogDevType ;	 
 BOOL sdlAutoDelete ;	 
 } diagCfgDataS;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 dbID ;	 
 UINT32 filterBitLength ;	 
 UINT32 reserved [ 6 ] ;	 
 } DIAG_Nvm_Filter_File_Header_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MASS_STORAGE_DISABLE = 0x0 ,	 
 MASS_STORAGE_ENABLE = 0x1	 
 } MassStorage_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* MIFI driver*/	 
 USB_GENERIC_MIFI_DRIVER = 0x0 ,	 
 USB_MARVELL_MIFI_DRIVER = 0x1 ,	 
 USB_ASR_MIFI_DRIVER = 0x2 ,	 
 USB_GENERIC_MOD_DRIVER = 0x10 ,	 
 USB_GENERIC_MOD_ECM_DRIVER = 0x12 ,	 
 USB_DIAG_UAC_DRIVER = 0x14 ,	 
 USB_RNDIS_ONLY_DRIVER = 0x15 ,	 
	 
 /* MBIM driver*/	 
 USB_MBIM_ONLY_DRIVER = 0x40 ,	 
 USB_MBIM_GENERIC_DRIVER = 0x41 ,	 
 USB_MBIM_MAX_DRIVER = 0x4F ,	 
	 
 /* Other driver*/	 
 USB_CDROM_ONLY_DRIVER = 0x91 ,	 
 USB_CDROM_DIAG_DRIVER = 0x92 ,	 
 USB_DIAG_ONLY_DRIVER = 0x93 ,	 
 USB_MODEM_ONLY_DRIVER = 0x94 ,	 
 USB_MODEM_DIAG_DRIVER = 0x95 ,	 
	 
 USB_MAX_DRIVER = 0xFF	 
 } Usb_driver_typeE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_AUTO_INSTALL_DISABLE = 0x0 ,	 
 USB_AUTO_INSTALL_ENABLE = 0x1	 
 } Usb_auto_install_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_OS_DETECT_DISABLE = 0x0 ,	 
 USB_OS_DETECT_ENABLE = 0x1	 
 } Usb_OS_detect_type;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Usb_driver_typeE usb_driver ;	 
 MassStorage_ConfigE mass_storage ;	 
 Usb_auto_install_type auto_install ;	 
 Usb_OS_detect_type os_detect ;	 
 } Usb_DriverS;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 YMODEM_DISABLE = 0x0 ,	 
 YMODEM_ENABLE = 0x1	 
 } Ymodem_Dump_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Ymodem_Dump_config config ;	 
 } Ymodem_Dump_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MBIM_ONLY_DESP = 0x0 ,	 
 MBIM_ATDIAG_DESP = 0x1	 
 } MBIM_DESP_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 MBIM_DESP_config config ;	 
 } MBIM_CFG_type;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 EEH_DUMP_DEV_USB_SD ,	 
 EEH_DUMP_DEV_SPI	 
 } EEH_DUMP_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PMU_POR = 1 ,	 
 PMU_EMR ,	 
 PMU_WDTR = ( PMU_EMR+2 )	 
 } PMU_LastResetStatus;

typedef UINT8 UART_Activity ;
//ICAT EXPORTED STRUCT 
 typedef struct /* This is structure of the UART Configuration */ 
 {	 
 UART_OpMode opMode ; /* fifo mode , non fifo mode or DMA for basic interface*/	 
 UART_TriggerLevel triggerLevel ; /* the trigger level interrupt on 1 , 8 , 16 , 32 */	 
 UART_BaudRates baudRate ; /* the rate of the transmit and the receive up to 111520 ( default - 9600 ) .*/	 
 UART_WordLen numDataBits ; /* 5 , 6 , 7 , or 8 number of data bits in the UART data frame ( default - 8 ) . */	 
 UART_StopBits stopBits ; /* 1 , 1.500000 or 2 stop bits in the UART data frame ( default - 1 ) . */	 
 UART_ParityTBits parityBitType ; /* Even , Odd or no-parity bit type in the UART data frame ( default - Non ) . */	 
 UART_InterfaceType interfaceType ; /* number of interface that the UART driver supplies ( default - UART_IF_TYPE_L2 ) */	 
 BOOL modemSignal ; /* enable operate modem - TRUE , disable modem - FALSE */	 
 BOOL flowControl ; /* enable Auto flow Control - TRUE , disable Auto flow Control - FALSE */	 
 UINT8 sleepMode ; /* enable sleep mode - TRUE , more fine control - see UARTSleepMode enum */	 
 BOOL auto_baud ; /* enable auto_baud , auto-baud-rate detection within the UART ( default - FALSE ) */	 
 UART_SIRConfigure sirIrDA ;	 
 } UARTConfiguration;

//ICAT EXPORTED ENUM 
 typedef enum // change the order -1 to + 
 {	 
 UART_RC_OK = 1 , /* 1 - no errors */	 
	 
 UART_RC_PORT_NUM_ERROR = -100 , /* -100 - Error in the UART port number */	 
 UART_RC_NO_DATA_TO_READ , /* -99 - Eror no data to read from the FIFO UART */	 
 UART_RC_ILLEGAL_BAUD_RATE , /* -98 - Error in the UART Bayd Rate */	 
 UART_RC_UART_PARITY_BITS_ERROR , /* -97 - Error in parity bit */	 
 UART_RC_UART_ONE_STOP_BIT_ERROR , /* -96 - Error in one stop bit */	 
 UART_RC_ONE_HALF_OR_TWO_STOP_BIT_ERROR , /* -95 - Error in two stop bit */	 
 UART_RC_BAD_INTERFACE_TYPE , /* -94 - Error in the Interface Type */	 
 UART_RC_UART_NOT_AVAILABLE , /* -93 - Error in try to open UART that is open */	 
 UART_RC_NO_DATA_TO_WRITE , /* -92 - Error No data to writ the len = 0 */	 
 UART_RC_NOT_ALL_BYTE_WRITTEN , /* -91 - Error Not all the Byte write to the UART FIFO */	 
 UART_RC_ISR_ALREADY_BIND , /* -90 - Error try to bind ISR for Basic Interface */	 
 UART_RC_WRONG_ISR_UNBIND , /* -89 - Error in the UnBind ISR for Basic Interface */	 
 UART_RC_FIFO_NOT_EMPTY , /* -88 - Error , the UART FIFO not empty */	 
 UART_RC_UART_OPEN , /* -87 - Error try chance the configurr when the UART open */	 
 UART_RC_GPIO_ERR , /* -86 - Error in the Configure of the GPIO */	 
 UART_RC_IRDA_CONFIG_ERR /* -85 - Illegal IrDA configuration */	 
 } UART_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAM_LOG = 0x00 ,	 
 USB_RAM_LOG = 0x01 ,	 
 MAX_RAM_LOG = 0x02	 
 } Ram_Log_Type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAMLOG_ENABLE = 0x00 ,	 
 USB_RAMLOG_ENABLE = 0x01	 
 } RamLog_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 LOG_DISABLE = 0x0 ,	 
 UART_LOG_ENABLE = 0x1 ,	 
 ACAT_LOG_ENABLE = 0x2	 
 } Log_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MSG_DISABLE = 0x0 ,	 
 ACAT_MSG_ENABLE = 0x1	 
 } Msg_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RTI_LOG_DISABLE = 0x0 ,	 
 RTI_LOG_ENABLE = 0x1	 
 } RTI_ConfigE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Log_ConfigE log_cfg ;	 
 Msg_ConfigE msg_cfg ;	 
 RTI_ConfigE rti_cfg ;	 
 RamLog_ConfigE ramlog ;	 
 } Log_ConfigS;

typedef void ( *UARTNotifyInterrupt ) ( UART_Port ) ;
typedef void ( *UsbLogPrint_t ) ( const char * , ... ) ;
typedef unsigned int time_t ;
DIAG_FILTER ( SW_PLAT , MEP , MEP_UpdateToMRD_NOMEM , DIAG_ERROR)  
 diagPrintf ( " MEP_UpdateToMRD Error: no memory " );

DIAG_FILTER ( SW_PLAT , MEP , MEP_UpdateToMRD_RDISKERROR , DIAG_ERROR)  
 diagPrintf ( " MEP_UpdateToMRD Error: open rdisk file:%s failed " , name );

DIAG_FILTER ( SW_PLAT , MEP , MEP_UpdateToMRD_FileAddError , DIAG_INFORMATION)  
 diagPrintf ( " MEP_UpdateToMRD: Add File:%s to MRD failed! " , name );

//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_mep_data.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/mep_data.c
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
typedef UINT8 MEP_BCD ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 number [ 32 ] ;	 
 UINT8 length ;	 
 } MEP_PASSWORD;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT16 year ;	 
 UINT8 month ;	 
 UINT8 day ;	 
 } MEP_DATE;

typedef UINT16 MEP_MNC ;
typedef UINT16 MEP_MCC ;
typedef UINT16 MEP_AccessTechnologyId ;
typedef UINT8 MEP_Boolean ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 serviceproviderId [ 4 ] ;	 
 } MEP_CODE_SP;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 corporateId [ 4 ] ;	 
 } MEP_CODE_CP;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_SIMPLMN networkIds [ 100 ] ;	 
 MEP_CODE_NS lockNS [ 100 ] ;	 
 MEP_CODE_SP lockSP [ 100 ] ;	 
 MEP_CODE_CP lockCP [ 100 ] ;	 
 MEP_CODE_SIMUSIM SimUsim ;	 
 } MEP_CODE;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_CODE code ;	 
 UINT8 DefaultSiActivated ;	 
 UINT8 DefaultSiEnabled ;	 
 } MEP_CAT_DATA;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_PASSWORD CatPsw [ MEP_MAX_CAT ] ;	 
 MEP_PASSWORD UnblockPsw ;	 
 // #if defined ( SS_IPC_SUPPORT ) && defined ( EXTENED_TRIAL_LIMIT_MEP )	 
	 
	 
 UINT8 TrialLimit [ MEP_MAX_CAT ] ;	 
	 
	 
	 
 } MEP_BLOCK_DATA;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 /*modified for #499867 2014.031800 by yunhail begin*/	 
 UINT32 VersionSize ;	 
 /*modified for #499867 2014.031800 by yunhail end*/	 
 MEP_CAT_DATA data ; // !!! Run F8 And F9 on this field	 
 MEP_BLOCK_DATA blocking ; // !!! Run F8 And F9 on this field	 
 UINT8 signature [ 20 ] ; // This is the output of the F9 function	 
 UINT8 DataIsEncrypted ; // Should be set to FALSE or TRUE -- do not run F8 | F9	 
 } MEP_FIX;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 SiActivated ;	 
 UINT8 SiEnabled ;	 
 UINT8 TrialCounter [ MEP_MAX_CAT ] ;	 
 UINT8 padding [ 15 ] ;	 
 UINT8 signature [ 20 ] ;	 
 } MEP_CHANGEABLE;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 iccid [ 10 ] ;	 
 MEP_DATE date ;	 
 } UDP_ASL_DATA;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UDP_ASL_DATA data [ 10 ] ;	 
 UINT8 NumAslEntries ;	 
 } UDP_ASL;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_PASSWORD psw ;	 
 UDP_ASL asl ;	 
 UINT8 si ;	 
 UINT8 padding [ 15 ] ;	 
 UINT8 signature [ 20 ] ;	 
 } UDP_CHANGEABLE;

typedef void ( *MEP_Callback ) ( MEP_UDP_RC* ) ;
typedef union
 {
 void ( *MEP_PutTC_Callback ) ( MEP_UDP_RC* ) ;
 void ( *MEP_PutSI_Callback ) ( MEP_UDP_RC* ) ;
 void ( *UDP_PutSI_Callback ) ( MEP_UDP_RC* ) ;
 void ( *UDP_PutASL_Callback ) ( MEP_UDP_RC* ) ;
 void ( *UDP_PutPassword_Callback ) ( MEP_UDP_RC* ) ;
 } MEP_UDP_CallBackFuncS ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 ulDebugInfoTag ;	 
 UINT32 ulMrdInfo ;	 
 UINT32 ulMepInfo ;	 
 } MRD_DEBUG_INFO;

//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_ReliableData.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/ReliableData.c
typedef unsigned int size_t ;
typedef va_list __gnuc_va_list ;
typedef unsigned int size_t ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0 = 0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 , GPIO_PIN_33 , GPIO_PIN_34 , GPIO_PIN_35 , GPIO_PIN_36 , GPIO_PIN_37 , GPIO_PIN_38 , GPIO_PIN_39 ,	 
	 
 GPIO_PIN_40 , GPIO_PIN_41 , GPIO_PIN_42 , GPIO_PIN_43 , GPIO_PIN_44 , GPIO_PIN_45 , GPIO_PIN_46 , GPIO_PIN_47 ,	 
 GPIO_PIN_48 , GPIO_PIN_49 , GPIO_PIN_50 , GPIO_PIN_51 , GPIO_PIN_52 , GPIO_PIN_53 , GPIO_PIN_54 , GPIO_PIN_55 ,	 
 GPIO_PIN_56 , GPIO_PIN_57 , GPIO_PIN_58 , GPIO_PIN_59 , GPIO_PIN_60 , GPIO_PIN_61 , GPIO_PIN_62 , GPIO_PIN_63 ,	 
	 
 GPIO_PIN_64 , GPIO_PIN_65 , GPIO_PIN_66 , GPIO_PIN_67 , GPIO_PIN_68 , GPIO_PIN_69 , GPIO_PIN_70 , GPIO_PIN_71 ,	 
 GPIO_PIN_72 , GPIO_PIN_73 , GPIO_PIN_74 , GPIO_PIN_75 , GPIO_PIN_76 , GPIO_PIN_77 , GPIO_PIN_78 , GPIO_PIN_79 ,	 
 GPIO_PIN_80 , GPIO_PIN_81 , GPIO_PIN_82 , GPIO_PIN_83 , GPIO_PIN_84 , GPIO_PIN_85 , GPIO_PIN_86 , GPIO_PIN_87 ,	 
 GPIO_PIN_88 , GPIO_PIN_89 , GPIO_PIN_90 , GPIO_PIN_91 , GPIO_PIN_92 , GPIO_PIN_93 , GPIO_PIN_94 , GPIO_PIN_95 ,	 
	 
 GPIO_PIN_96 , GPIO_PIN_97 , GPIO_PIN_98 , GPIO_PIN_99 , GPIO_PIN_100 , GPIO_PIN_101 , GPIO_PIN_102 , GPIO_PIN_103 ,	 
 GPIO_PIN_104 , GPIO_PIN_105 , GPIO_PIN_106 , GPIO_PIN_107 , GPIO_PIN_108 , GPIO_PIN_109 , GPIO_PIN_110 , GPIO_PIN_111 ,	 
 GPIO_PIN_112 , GPIO_PIN_113 , GPIO_PIN_114 , GPIO_PIN_115 , GPIO_PIN_116 , GPIO_PIN_117 , GPIO_PIN_118 , GPIO_PIN_119 ,	 
 GPIO_PIN_120 , GPIO_PIN_121 , GPIO_PIN_122 , GPIO_PIN_123 , GPIO_PIN_124 , GPIO_PIN_125 , GPIO_PIN_126 , GPIO_PIN_127 ,	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
typedef UINT32 Attribs_Mmu ;
typedef void ( * mmuNotifyEventFunc ) ( MMU_ERROR_STATUS mmuErrorHandle ) ;
typedef union {
 rw_region_item compress_rw_region_list [ 64 ] ;

 UINT8 filer [ 2048 ] ;
 } LOADTABLE_AREA_RW_CPZ_INFO ;
typedef unsigned char BYTE ;
typedef unsigned short int WORD ;
typedef unsigned long DWORD ;
typedef WORD * WORD_PTR ;
typedef DWORD * DWORD_PTR ;
typedef BYTE BYTE_BITMASK ;
typedef VOID_PTR * VOID_PTR_PTR ;
typedef WORD FLASH_DATA_WIDTH ;
typedef char FDI_TCHAR ;
typedef OSASemaRef SEM_ID ;
typedef int SEM_STATUS ;
typedef SEM_MTX * SEM_MTX_ID ;
typedef WORD IDTYPE ;
typedef DATA_LOOKUP * DATA_LOOKUP_PTR ;
typedef DATA_LOCATION * DATA_LOC_PTR ;
typedef OPEN_PARAM * OPEN_STREAM_PTR ;
typedef COMMAND * COMMAND_PTR ;
typedef UNIT_HEADER * UNIT_HDR_PTR ;
typedef MULTI_INSTANCE * MULTI_INST_PTR ;
typedef BLOCK_INFO * BLK_INFO_PTR ;
typedef COMMAND_CONTROL * CMD_CNTRL_PTR ;
typedef WORD FILE_ID ;
typedef signed char int8_t ;
typedef signed short int int16_t ;
typedef signed int int32_t ;
typedef signed __int64 int64_t ;
typedef unsigned char uint8_t ;
typedef unsigned short int uint16_t ;
typedef unsigned int uint32_t ;
typedef unsigned __int64 uint64_t ;
typedef signed char int_least8_t ;
typedef signed short int int_least16_t ;
typedef signed int int_least32_t ;
typedef signed __int64 int_least64_t ;
typedef unsigned char uint_least8_t ;
typedef unsigned short int uint_least16_t ;
typedef unsigned int uint_least32_t ;
typedef unsigned __int64 uint_least64_t ;
typedef signed int int_fast8_t ;
typedef signed int int_fast16_t ;
typedef signed int int_fast32_t ;
typedef signed __int64 int_fast64_t ;
typedef unsigned int uint_fast8_t ;
typedef unsigned int uint_fast16_t ;
typedef unsigned int uint_fast32_t ;
typedef unsigned __int64 uint_fast64_t ;
typedef signed int intptr_t ;
typedef unsigned int uintptr_t ;
typedef signed long long intmax_t ;
typedef unsigned long long uintmax_t ;
typedef float float_t ;
typedef double double_t ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_INT_IFC ,	 
 DIAG_EXT_IFC	 
 } DIAG_IF_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_UART_SPEED_921600 ,	 
 DIAG_UART_SPEED_1842000 ,	 
 DIAG_UART_SPEED_3000000 ,	 
 DIAG_UART_DISABLE ,	 
 } DIAG_UART_SPEED_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_MEM_ENABLE ,	 
 DIAG_MEM_DISABLE	 
 } DIAG_MEM_SWITCH;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_DEV_USB ,	 
 DIAG_DEV_SD ,	 
 DIAG_DEV_FS ,	 
 DIAG_DEV_UART ,	 
 DIAG_DEV_SPI ,	 
 DIAG_DEV_NONE	 
 } DIAG_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 SULOG_DEV_USB ,	 
 SULOG_DEV_SD	 
 } SULOG_DEV_TYPE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 DIAG_IF_TYPE useIntIf ; /* use internal interface or external interface */	 
 DIAG_UART_SPEED_TYPE useHighSpeedUART ; /* high speed UART ( 460800 ) or low speed UART ( 115200 ) */	 
 DIAG_MEM_SWITCH diagMemSwitch ;	 
 DIAG_DEV_TYPE diagDevType ;	 
 SULOG_DEV_TYPE sulogDevType ;	 
 BOOL sdlAutoDelete ;	 
 } diagCfgDataS;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 dbID ;	 
 UINT32 filterBitLength ;	 
 UINT32 reserved [ 6 ] ;	 
 } DIAG_Nvm_Filter_File_Header_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MASS_STORAGE_DISABLE = 0x0 ,	 
 MASS_STORAGE_ENABLE = 0x1	 
 } MassStorage_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* MIFI driver*/	 
 USB_GENERIC_MIFI_DRIVER = 0x0 ,	 
 USB_MARVELL_MIFI_DRIVER = 0x1 ,	 
 USB_ASR_MIFI_DRIVER = 0x2 ,	 
 USB_GENERIC_MOD_DRIVER = 0x10 ,	 
 USB_GENERIC_MOD_ECM_DRIVER = 0x12 ,	 
 USB_DIAG_UAC_DRIVER = 0x14 ,	 
 USB_RNDIS_ONLY_DRIVER = 0x15 ,	 
	 
 /* MBIM driver*/	 
 USB_MBIM_ONLY_DRIVER = 0x40 ,	 
 USB_MBIM_GENERIC_DRIVER = 0x41 ,	 
 USB_MBIM_MAX_DRIVER = 0x4F ,	 
	 
 /* Other driver*/	 
 USB_CDROM_ONLY_DRIVER = 0x91 ,	 
 USB_CDROM_DIAG_DRIVER = 0x92 ,	 
 USB_DIAG_ONLY_DRIVER = 0x93 ,	 
 USB_MODEM_ONLY_DRIVER = 0x94 ,	 
 USB_MODEM_DIAG_DRIVER = 0x95 ,	 
	 
 USB_MAX_DRIVER = 0xFF	 
 } Usb_driver_typeE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_AUTO_INSTALL_DISABLE = 0x0 ,	 
 USB_AUTO_INSTALL_ENABLE = 0x1	 
 } Usb_auto_install_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_OS_DETECT_DISABLE = 0x0 ,	 
 USB_OS_DETECT_ENABLE = 0x1	 
 } Usb_OS_detect_type;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Usb_driver_typeE usb_driver ;	 
 MassStorage_ConfigE mass_storage ;	 
 Usb_auto_install_type auto_install ;	 
 Usb_OS_detect_type os_detect ;	 
 } Usb_DriverS;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 YMODEM_DISABLE = 0x0 ,	 
 YMODEM_ENABLE = 0x1	 
 } Ymodem_Dump_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Ymodem_Dump_config config ;	 
 } Ymodem_Dump_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MBIM_ONLY_DESP = 0x0 ,	 
 MBIM_ATDIAG_DESP = 0x1	 
 } MBIM_DESP_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 MBIM_DESP_config config ;	 
 } MBIM_CFG_type;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 EEH_DUMP_DEV_USB_SD ,	 
 EEH_DUMP_DEV_SPI	 
 } EEH_DUMP_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PMU_POR = 1 ,	 
 PMU_EMR ,	 
 PMU_WDTR = ( PMU_EMR+2 )	 
 } PMU_LastResetStatus;

typedef UINT8 UART_Activity ;
//ICAT EXPORTED STRUCT 
 typedef struct /* This is structure of the UART Configuration */ 
 {	 
 UART_OpMode opMode ; /* fifo mode , non fifo mode or DMA for basic interface*/	 
 UART_TriggerLevel triggerLevel ; /* the trigger level interrupt on 1 , 8 , 16 , 32 */	 
 UART_BaudRates baudRate ; /* the rate of the transmit and the receive up to 111520 ( default - 9600 ) .*/	 
 UART_WordLen numDataBits ; /* 5 , 6 , 7 , or 8 number of data bits in the UART data frame ( default - 8 ) . */	 
 UART_StopBits stopBits ; /* 1 , 1.500000 or 2 stop bits in the UART data frame ( default - 1 ) . */	 
 UART_ParityTBits parityBitType ; /* Even , Odd or no-parity bit type in the UART data frame ( default - Non ) . */	 
 UART_InterfaceType interfaceType ; /* number of interface that the UART driver supplies ( default - UART_IF_TYPE_L2 ) */	 
 BOOL modemSignal ; /* enable operate modem - TRUE , disable modem - FALSE */	 
 BOOL flowControl ; /* enable Auto flow Control - TRUE , disable Auto flow Control - FALSE */	 
 UINT8 sleepMode ; /* enable sleep mode - TRUE , more fine control - see UARTSleepMode enum */	 
 BOOL auto_baud ; /* enable auto_baud , auto-baud-rate detection within the UART ( default - FALSE ) */	 
 UART_SIRConfigure sirIrDA ;	 
 } UARTConfiguration;

//ICAT EXPORTED ENUM 
 typedef enum // change the order -1 to + 
 {	 
 UART_RC_OK = 1 , /* 1 - no errors */	 
	 
 UART_RC_PORT_NUM_ERROR = -100 , /* -100 - Error in the UART port number */	 
 UART_RC_NO_DATA_TO_READ , /* -99 - Eror no data to read from the FIFO UART */	 
 UART_RC_ILLEGAL_BAUD_RATE , /* -98 - Error in the UART Bayd Rate */	 
 UART_RC_UART_PARITY_BITS_ERROR , /* -97 - Error in parity bit */	 
 UART_RC_UART_ONE_STOP_BIT_ERROR , /* -96 - Error in one stop bit */	 
 UART_RC_ONE_HALF_OR_TWO_STOP_BIT_ERROR , /* -95 - Error in two stop bit */	 
 UART_RC_BAD_INTERFACE_TYPE , /* -94 - Error in the Interface Type */	 
 UART_RC_UART_NOT_AVAILABLE , /* -93 - Error in try to open UART that is open */	 
 UART_RC_NO_DATA_TO_WRITE , /* -92 - Error No data to writ the len = 0 */	 
 UART_RC_NOT_ALL_BYTE_WRITTEN , /* -91 - Error Not all the Byte write to the UART FIFO */	 
 UART_RC_ISR_ALREADY_BIND , /* -90 - Error try to bind ISR for Basic Interface */	 
 UART_RC_WRONG_ISR_UNBIND , /* -89 - Error in the UnBind ISR for Basic Interface */	 
 UART_RC_FIFO_NOT_EMPTY , /* -88 - Error , the UART FIFO not empty */	 
 UART_RC_UART_OPEN , /* -87 - Error try chance the configurr when the UART open */	 
 UART_RC_GPIO_ERR , /* -86 - Error in the Configure of the GPIO */	 
 UART_RC_IRDA_CONFIG_ERR /* -85 - Illegal IrDA configuration */	 
 } UART_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAM_LOG = 0x00 ,	 
 USB_RAM_LOG = 0x01 ,	 
 MAX_RAM_LOG = 0x02	 
 } Ram_Log_Type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAMLOG_ENABLE = 0x00 ,	 
 USB_RAMLOG_ENABLE = 0x01	 
 } RamLog_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 LOG_DISABLE = 0x0 ,	 
 UART_LOG_ENABLE = 0x1 ,	 
 ACAT_LOG_ENABLE = 0x2	 
 } Log_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MSG_DISABLE = 0x0 ,	 
 ACAT_MSG_ENABLE = 0x1	 
 } Msg_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RTI_LOG_DISABLE = 0x0 ,	 
 RTI_LOG_ENABLE = 0x1	 
 } RTI_ConfigE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Log_ConfigE log_cfg ;	 
 Msg_ConfigE msg_cfg ;	 
 RTI_ConfigE rti_cfg ;	 
 RamLog_ConfigE ramlog ;	 
 } Log_ConfigS;

typedef void ( *UARTNotifyInterrupt ) ( UART_Port ) ;
typedef void ( *UsbLogPrint_t ) ( const char * , ... ) ;
typedef signed char osa_bool_t ;
typedef signed long osa_base_t ;
typedef unsigned long osa_ubase_t ;
typedef signed char osa_int8_t ;
typedef signed short osa_int16_t ;
typedef signed int osa_int32_t ;
typedef unsigned char osa_uint8_t ;
typedef unsigned short osa_uint16_t ;
typedef unsigned int osa_uint32_t ;
typedef signed long long osa_int64_t ;
typedef unsigned long long osa_uint64_t ;
typedef osa_ubase_t osa_size_t ;
typedef osa_base_t osa_ssize_t ;
typedef osa_int32_t osa_ptr ;
typedef osa_int64_t osa_time_t ;
typedef unsigned int bit ;
typedef void* osa_msgq_t ;
typedef void* osa_mutex_t ;
typedef void* osa_sem_t ;
typedef void* osa_task_t ;
typedef void* osa_timer_t ;
typedef void* osa_event_t ;
//ICAT EXPORTED STRUCT 
 typedef struct NVM_Header 
 {	 
 unsigned long StructSize ; // the size of the user structure below	 
 unsigned long NumofStructs ; // >1 in case of array of structs ( default is 1 ) .	 
 char StructName [ 64 ] ; // the name of the user structure below	 
 char Date [ 32 ] ; // date updated by the ICAT when the file is saved. Filled by calibration SW.	 
 char time [ 32 ] ; // time updated by the ICAT when the file is saved. Filled by calibration SW.	 
 char Version [ 64 ] ; // user version - this field is updated by the SW eng. Every time they update the UserStruct.	 
 char HW_ID [ 32 ] ; // signifies the board number. Filled by calibration SW.	 
 char CalibVersion [ 32 ] ; // signifies the calibration SW version. Filled by calibration SW.	 
 } NVM_Header_ts;

typedef unsigned int size_t ;
typedef unsigned int clock_t ;
typedef unsigned int time_t ;
typedef unsigned int time_t ;
typedef unsigned char BOOL ;
typedef signed char BYTE8 ;
typedef unsigned char UBYTE8 ;
typedef short HWD16 ;
typedef long WORD32 ;
typedef unsigned short UHWD16 ;
typedef unsigned long UWORD32 ;
typedef unsigned long long ULLONG64 ;
typedef char S8 ;
typedef HWD16 S16 ;
typedef WORD32 S32 ;
typedef UBYTE8 U8 ;
typedef UHWD16 U16 ;
typedef UWORD32 U32 ;
typedef void ( *apTYPE_rCallback ) ( UWORD32 ) ;
typedef WORD32 apError ;
typedef UWORD32 fatTYPE_tSector ;
typedef UWORD32 fatTYPE_tCluster ;
typedef UWORD32 fatTYPE_tEntry ;
typedef UWORD32 fatTYPE_tSize ;
typedef UINT8 MEP_BCD ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 number [ 32 ] ;	 
 UINT8 length ;	 
 } MEP_PASSWORD;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT16 year ;	 
 UINT8 month ;	 
 UINT8 day ;	 
 } MEP_DATE;

typedef UINT16 MEP_MNC ;
typedef UINT16 MEP_MCC ;
typedef UINT16 MEP_AccessTechnologyId ;
typedef UINT8 MEP_Boolean ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 serviceproviderId [ 4 ] ;	 
 } MEP_CODE_SP;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 corporateId [ 4 ] ;	 
 } MEP_CODE_CP;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_SIMPLMN networkIds [ 100 ] ;	 
 MEP_CODE_NS lockNS [ 100 ] ;	 
 MEP_CODE_SP lockSP [ 100 ] ;	 
 MEP_CODE_CP lockCP [ 100 ] ;	 
 MEP_CODE_SIMUSIM SimUsim ;	 
 } MEP_CODE;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_CODE code ;	 
 UINT8 DefaultSiActivated ;	 
 UINT8 DefaultSiEnabled ;	 
 } MEP_CAT_DATA;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_PASSWORD CatPsw [ MEP_MAX_CAT ] ;	 
 MEP_PASSWORD UnblockPsw ;	 
 // #if defined ( SS_IPC_SUPPORT ) && defined ( EXTENED_TRIAL_LIMIT_MEP )	 
	 
	 
 UINT8 TrialLimit [ MEP_MAX_CAT ] ;	 
	 
	 
	 
 } MEP_BLOCK_DATA;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 /*modified for #499867 2014.031800 by yunhail begin*/	 
 UINT32 VersionSize ;	 
 /*modified for #499867 2014.031800 by yunhail end*/	 
 MEP_CAT_DATA data ; // !!! Run F8 And F9 on this field	 
 MEP_BLOCK_DATA blocking ; // !!! Run F8 And F9 on this field	 
 UINT8 signature [ 20 ] ; // This is the output of the F9 function	 
 UINT8 DataIsEncrypted ; // Should be set to FALSE or TRUE -- do not run F8 | F9	 
 } MEP_FIX;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 SiActivated ;	 
 UINT8 SiEnabled ;	 
 UINT8 TrialCounter [ MEP_MAX_CAT ] ;	 
 UINT8 padding [ 15 ] ;	 
 UINT8 signature [ 20 ] ;	 
 } MEP_CHANGEABLE;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 iccid [ 10 ] ;	 
 MEP_DATE date ;	 
 } UDP_ASL_DATA;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UDP_ASL_DATA data [ 10 ] ;	 
 UINT8 NumAslEntries ;	 
 } UDP_ASL;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 MEP_PASSWORD psw ;	 
 UDP_ASL asl ;	 
 UINT8 si ;	 
 UINT8 padding [ 15 ] ;	 
 UINT8 signature [ 20 ] ;	 
 } UDP_CHANGEABLE;

typedef void ( *MEP_Callback ) ( MEP_UDP_RC* ) ;
typedef union
 {
 void ( *MEP_PutTC_Callback ) ( MEP_UDP_RC* ) ;
 void ( *MEP_PutSI_Callback ) ( MEP_UDP_RC* ) ;
 void ( *UDP_PutSI_Callback ) ( MEP_UDP_RC* ) ;
 void ( *UDP_PutASL_Callback ) ( MEP_UDP_RC* ) ;
 void ( *UDP_PutPassword_Callback ) ( MEP_UDP_RC* ) ;
 } MEP_UDP_CallBackFuncS ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 ulDebugInfoTag ;	 
 UINT32 ulMrdInfo ;	 
 UINT32 ulMepInfo ;	 
 } MRD_DEBUG_INFO;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Configures Product Debug Level */	 
 PRODUCT_MODE = 0 , /* @ENUM_VAL_DESC@ Product Mode Debug level*/	 
 DEBUG_NORMAL = 1 , /* @ENUM_VAL_DESC@ Normal Debug level*/	 
 DEBUG_EXTEND = 2 /* @ENUM_VAL_DESC@ Extended Debug level*/	 
 } ProductDebugLevel_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Configures UART route */	 
 UART_DEFAULT = 0 , /* @ENUM_VAL_DESC@ Maintain code default*/	 
 UART_DATA_IO = 1 , /* @ENUM_VAL_DESC@ Route UART to DATA IO */	 
 UART_DIAG_ACAT = 2 , /* @ENUM_VAL_DESC@ Route UART to DIAG ACAT */	 
 UART_NULL = 0xFF /* @ENUM_VAL_DESC@ Disable*/	 
 } platformUART_route_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Configures DIAG route */	 
 DIAG_DEFAULT = 0 , /* @ENUM_VAL_DESC@ Maintain code default*/	 
 USB = 1 , /* @ENUM_VAL_DESC@ Route DIAG through USB*/	 
 UART = 2 /* @ENUM_VAL_DESC@ Route DIAG through UART*/	 
 } platformDIAG_route_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Configures USB detection*/	 
 USB_DETECT_DEFAULT = 0 , /* @ENUM_VAL_DESC@ Maintain code default*/	 
 USB_DETECT_ENABLE = 1 , /* @ENUM_VAL_DESC@ Enable USB detection*/	 
 USB_DETECT_DISABLE = 2 , /* @ENUM_VAL_DESC@ Disable USB detection*/	 
 USB_ALWAYS_CONNECT = 3 , /* @ENUM_VAL_DESC@ Always connect to USB*/	 
 USB_ALWAYS_DISCONNECT = 4 /* @ENUM_VAL_DESC@ Never connect to USB*/	 
 } platformUSBdetection_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Configures HeartBeat mode*/	 
 HB_DISABLE= 0 , /* @ENUM_VAL_DESC@ Disable heart beat*/	 
 TO_UART = 1 , /* @ENUM_VAL_DESC@ Send heart beat to UART*/	 
 TO_ACAT = 2 , /* @ENUM_VAL_DESC@ Send heart beat to ACAT*/	 
 ANOTHER = 3 /* @ENUM_VAL_DESC@ Reserved*/	 
 } platformHeartBeat_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Configures RTC Client*/	 
 RTC_CLIENT_DEFAULT = 0x0 , /* @ENUM_VAL_DESC@ Maintain code default*/	 
 RTC_CLIENT_ENABLE = 1 , /* @ENUM_VAL_DESC@ Enable RTC client*/	 
 RTC_CLIENT_DISABLE = 2 /* @ENUM_VAL_DESC@ Disable RTC client*/	 
 } platformRtcClient_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Configures Relaiable Data load source*/	 
 RD_DEFAULT = 0x0 , /* @ENUM_VAL_DESC@ Maintain code default*/	 
 RD_LOAD_CALIB_FILES_FROM_FLASH = 1 , /* @ENUM_VAL_DESC@ Load Callibration files from flash*/	 
 RD_DONT_LOAD_CALIB_FILES_FROM_FALSH = 2 /* @ENUM_VAL_DESC@ Don ' t load Callibration files from flash*/	 
 } platformRelaibeData_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Configure Comm-Apps sync*/	 
 PM_COMM_APPS_SYNC_DISABLED = 0 , /* @ENUM_VAL_DESC@ Disable Comm-Apps sync*/	 
 PM_COMM_APPS_SYNC_ENABLED = 1 /* @ENUM_VAL_DESC@ Enable Comm-Apps sync*/	 
 } nvmPM_CommAppsSync_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Configure Power Manager operation level*/	 
 PM_DEFAULT = 0 , /* @ENUM_VAL_DESC@ Maintain code default*/	 
 PM_FULL_ENABLE = 1 , /* @ENUM_VAL_DESC@ Enable Power Manager full functionality*/	 
 PM_DISABLE = 2 , /* @ENUM_VAL_DESC@ Disable Power Manager */	 
 PM_LEVEL_C1 = 3 , /* @ENUM_VAL_DESC@ Enable Power Manager C1 */	 
 PM_LEVEL_D2 = 4 /* @ENUM_VAL_DESC@ Enable Power Manager D2 */	 
 } nvmPM_level_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Set product point*/	 
 PP_DEFAULT = 0 , /* @ENUM_VAL_DESC@ Maintain code default*/	 
 PP_1 = 1 , /* @ENUM_VAL_DESC@ Set product point to 1 */	 
 PP_2 = 2 , /* @ENUM_VAL_DESC@ Set product point to 2 */	 
 PP_3 = 3 , /* @ENUM_VAL_DESC@ Set product point to 3 */	 
 PP_4 = 4 /* @ENUM_VAL_DESC@ Set product point to 4 */	 
 } nvmPM_ProductPoint_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PNVM_DEFAULT=0 ,	 
 PNVM_ENABLE ,	 
 PNVM_DISABLE	 
 } nvm_EN_DIS_DEF_ts;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 /* @STRUCT_DESC@ Configures Power Manager*/	 
 nvmPM_CommAppsSync_ts PM_AppsCommSyncEnabled ; /* @ITEM_DESC@ Configure Comm-Apps sync , @ITEM_MODE@ Debug , @ITEM_UNIT@ see enum*/	 
 nvmPM_ProductPoint_ts PM_ProductPoint ; /* @ITEM_DESC@ Set product point , @ITEM_MODE@ Debug , @ITEM_UNIT@ see enum*/	 
 nvmPM_level_ts PM_dbgLevel ; /* @ITEM_DESC@ Configure Power Manager operation level , @ITEM_MODE@ Debug , @ITEM_UNIT@ see enum*/	 
 nvm_EN_DIS_DEF_ts PM_newDDRHandshakeEnabled ;	 
 } platformPowerMngr_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Configures USIM error handling*/	 
 USIM_ERR_SILENT = 0 , /* @ENUM_VAL_DESC@ Silent error handling*/	 
 USIM_ERR_ASSERT = 1 , /* @ENUM_VAL_DESC@ Assert on error*/	 
 USIM_ERR_RECOVERY = 2 /* @ENUM_VAL_DESC@ error handling recovery*/	 
 } nvmUsimErrorHandler_ts;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 /* @STRUCT_DESC@ Configures USIM driver settings*/	 
 ProductDebugLevel_ts DebugLevel ; /* @ITEM_DESC@ Configures USIM Debug Level , @ITEM_MODE@ Debug , @ITEM_UNIT@ see enum*/	 
 UINT8 TxDmaDisable ; /* @ITEM_DESC@ Configure Tx Dma Disable , @ITEM_MODE@ Debug , @ITEM_UNIT@ 0 -0xFF*/	 
 UINT8 DetectGpioPinNo ; /* @ITEM_DESC@ Set USIM detection GPIO , @ITEM_MODE@ ReadWrite , @ITEM_UNIT@ Entries 1 -0xFF according to available GPIOs , 0 to diasble detection*/ // disable=0 , Detect_Pin=0xFF , others are GPIO / CGPIO pins	 
 nvmUsimErrorHandler_ts ErrorHandler ; /* @ITEM_DESC@ Configures USIM error handling , @ITEM_MODE@ Debug , @ITEM_UNIT@ see enum*/	 
 } platformUsim_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Configures AppCommMode*/	 
 APPCOMM_DEFAULT = 0 , /* @ENUM_VAL_DESC@ Maintain code default*/	 
 APPCOMM_COMM_IN_RESET = 1 , /* @ENUM_VAL_DESC@ Set Comm in reset*/	 
 APPCOMM_LONG_TYPE = 0x0FFFFFFF /* @ENUM_VAL_DESC@ force AppsComm enum long*/	 
 } platformAppsCommMode_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Configures OSA Error behavior*/	 
 OSA_ERROR_DEFAULT = 0 , /* @ENUM_VAL_DESC@ Maintain code default*/	 
 OSA_ERROR_ASSERT = 1 , /* @ENUM_VAL_DESC@ Assert on OSA errors*/	 
 OSA_ERROR_IGNORE = 2 , /* @ENUM_VAL_DESC@ Ignore OSA errors*/	 
 OSA_ERROR_RESTORE_OK = 3 , /* @ENUM_VAL_DESC@ OSA error restore*/	 
 OSA_ERROR_LONG_TYPE = 0x0FFFFFFF /* @ENUM_VAL_DESC@ force OSA error enum long*/	 
 } platformOsaErrBehavior_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Configures I2C FAST-SLOW mode*/	 
 NVM_I2C_CLK_DEFAULT = 0 , /* @ENUM_VAL_DESC@ Maintain code default*/	 
 NVM_I2C_SLOW_ClK_MODE = 0x00007fff , /* @ENUM_VAL_DESC@ Configure for slow mode align to I2c.h*/	 
 NVM_I2C_FAST_CLK_MODE = 0x00008000 , /* @ENUM_VAL_DESC@ Configure for fast mode align to I2c.h*/	 
 NVM_I2C_CLK_LONG_TYPE = 0x0FFFFFFF /* @ENUM_VAL_DESC@ force I2C error enum long*/	 
 } platfromI2cModeCfg_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* @ENUM_DESC@ Configures I2C repeat start configuration */	 
 NVM_I2C_REPEAT_DEFAULT = 0 , /* @ENUM_VAL_DESC@ Maintain code default*/	 
 NVM_I2C_NOT_REPEAT_START = 1 , /* @ENUM_VAL_DESC@ Configure for slow mode align to I2c.h*/	 
 NVM_I2C_REPEAT_START = 2 , /* @ENUM_VAL_DESC@ Configure for fast mode align to I2c.h*/	 
 NVM_I2C_REPEAT_LONG_TYPE = 0x0FFFFFFF /* @ENUM_VAL_DESC@ force I2C error enum long*/	 
 } platfromI2cRepeatCfg_ts;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 /* @STRUCT_DESC@ I2C Configures */	 
 platfromI2cModeCfg_ts I2cClkMode ; /* @ITEM_DESC@ Configures I2C CLK mode FAST / SLOW , @ITEM_MODE@ Configuration , @ITEM_UNIT@ see enum*/	 
 platfromI2cRepeatCfg_ts I2cRptStrt ; /* @ITEM_DESC@ Configures I2C REPEAT_START mode yes / no , @ITEM_MODE@ Configuration , @ITEM_UNIT@ see enum*/	 
 } platfromI2cCfg_ts;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 /* @STRUCT_DESC@ Platfrom configuration */	 
 /* @STRUCT_NVM_FILE_NAME@ platfrom.nvm */	 
 ProductDebugLevel_ts ProductDebugLevel ; /* @ITEM_DESC@ Configures Product Debug Level , @ITEM_MODE@ Debug , @ITEM_UNIT@ see enum*/	 
	 
 UINT32 DelayMSEC_Startup ; /* @ITEM_DESC@ Startup Delay , @ITEM_MODE@ Debug , @ITEM_UNIT@ milisec*/	 
 UINT32 DelayMSEC_MSL ; /* @ITEM_DESC@ MSL Delay , @ITEM_MODE@ Debug , @ITEM_UNIT@ milisec*/	 
 UINT32 DelayMSEC_Audio ; /* @ITEM_DESC@ Audio Delay , @ITEM_MODE@ Debug , @ITEM_UNIT@ milisec*/	 
 UINT32 DelayMSEC_Power ; /* @ITEM_DESC@ Power Delay , @ITEM_MODE@ Debug , @ITEM_UNIT@ milisec*/	 
 UINT32 DelayMSEC_L1start ; /* @ITEM_DESC@ L1 start Delay , @ITEM_MODE@ Debug , @ITEM_UNIT@ milisec*/	 
 UINT32 DelayMSEC_reserv1 ; /* @ITEM_DESC@ Reserved , @ITEM_MODE@ ReadOnly , @ITEM_UNIT@ Not Relevant*/	 
	 
 platformUART_route_ts UART_route ; /* @ITEM_DESC@ Configures UART route , @ITEM_MODE@ Debug , @ITEM_UNIT@ see enum*/	 
 platformDIAG_route_ts DIAG_route ; /* @ITEM_DESC@ Configures DIAG route , @ITEM_MODE@ Debug , @ITEM_UNIT@ see enum*/	 
 platformUSBdetection_ts USBdetection ; /* @ITEM_DESC@ Configures USB detection , @ITEM_MODE@ Debug , @ITEM_UNIT@ see enum*/	 
 platformHeartBeat_ts HeartBeat ; /* @ITEM_DESC@ Configures HeartBeat modes , @ITEM_MODE@ Debug , @ITEM_UNIT@ see enum*/	 
 platformPowerMngr_ts PowerManagement ; /* @ITEM_DESC@ Configures Power Manager , @ITEM_MODE@ Debug , @ITEM_UNIT@ see structure*/	 
 platformRtcClient_ts RTC_Client ; /* @ITEM_DESC@ Configures RTC Client , @ITEM_MODE@ Debug , @ITEM_UNIT@ see enum*/	 
 platformRelaibeData_ts RD_LoadCalibFiles ; /* @ITEM_DESC@ Configures Relaiable Data load source , @ITEM_MODE@ Debug , @ITEM_UNIT@ see enum*/	 
	 
	 
 platformUsim_ts USIM ; /* @ITEM_DESC@ Configures USIM driver settings , @ITEM_MODE@ Debug , @ITEM_UNIT@ see structure*/	 
 platformAppsCommMode_ts AppCommMode ; /* @ITEM_DESC@ Configures AppCommMode , @ITEM_MODE@ Debug , @ITEM_UNIT@ see enum*/	 
	 
 platformOsaErrBehavior_ts OsaErrBehavior ; /* @ITEM_DESC@ Configures OSA Error behavior , @ITEM_MODE@ Debug , @ITEM_UNIT@ see enum*/	 
 platfromI2cCfg_ts NvmCfgI2C ; /* @ITEM_DESC@ Configures I2c CLK and REPEAT_START , @ITEM_MODE@ Configuration , @ITEM_UNIT@ see enum*/	 
 /* the reserved [ X ] must be update for backward	 
 * compatible if new fields are added to PlatformNvm_ts.	 
 * for example if you add UNIT32 you need to reduce X = X-4*/	 
 UINT8 reserved [ 44 ] ; /* @ITEM_DESC@ the amount of the reserved must be reduced if fields are added , @ITEM_MODE@ debugging , @ITEM_UNIT@ none*/	 
	 
 } PlatformNvm_ts;

typedef unsigned long UINT32 ;
typedef volatile unsigned long VUINT32_T ;
typedef unsigned long UINT32_T ;
typedef volatile unsigned int VUINT_T ;
typedef unsigned int UINT_T ;
typedef int INT_T ;
typedef unsigned short UINT16_T , USHORT ;
typedef volatile unsigned short VUINT16_T ;
typedef unsigned char UINT8_T ;
typedef char INT8_T ;
typedef unsigned int UINT , *PUINT ;
typedef unsigned long long UINT64 , *PUINT64 ;
typedef unsigned char UCHAR , BYTE , *PUCHAR ;
typedef int INT , *PINT ;
typedef long long INT64 , *PINT64 ;
typedef char CHAR , *PCHAR ;
typedef unsigned int size_t ;
typedef volatile UINT VUINT , *PVUINT ;
typedef volatile UINT64 VUINT64 , *PVUINT64 ;
typedef volatile unsigned int VUINT32 , *PVUINT32 ;
typedef volatile UINT16 VUINT16 , *PVUINT16 ;
typedef volatile UINT8 VUINT8 , *PVUINT8 ;
typedef volatile UCHAR VUCHAR , *PVUCHAR ;
typedef volatile INT VINT , *PVINT ;
typedef volatile INT64 VINT64 , *PVINT64 ;
typedef volatile int VINT32 , *PVINT32 ;
typedef volatile INT16 VINT16 , *PVINT16 ;
typedef volatile char VINT8 , *PVINT8 ;
typedef volatile CHAR VCHAR , *PVCHAR ;
typedef UINT16 WORD ;
typedef signed char int8_t ;
typedef unsigned char uint8_t ;
typedef signed short int16_t ;
typedef unsigned short uint16_t ;
typedef signed int int32_t ;
typedef unsigned int uint32_t ;
typedef signed long long int int64_t ;
typedef unsigned long long int uint64_t ;
typedef UINT32 rti_uint32_t ;
typedef UINT64 rti_uint64_t ;
typedef int utlReturnCode_T , *utlReturnCode_P ;
typedef const utlReturnCode_T *utlReturnCode_P2c ;
typedef unsigned int utlDataId_T , *utlDataId_P ;
typedef unsigned int size_t ;
typedef int ssize_t ;
typedef const utlDataId_T *utlDataId_P2c ;
typedef const utlLinkedListNode_T *utlLinkedListNode_P2c ;
typedef unsigned int utlNodeCount_T ;
typedef const utlLinkedList_T *utlLinkedList_P2c ;
typedef int utlSecond_T ;
typedef int utlNanosecond_T ;
typedef const utlRelativeTime_T *utlRelativeTime_P2c ;
typedef const utlAbsoluteTime_T *utlAbsoluteTime_P2c ;
typedef const utlVString_T *utlVString_P2c ;
typedef signed long utlTimerId_T ;
typedef unsigned long utlTimeOutCount_T , *utlTimeOutCount_P ;
typedef utlReturnCode_T ( *utlTimerFunction_P ) ( const utlTimerId_T id ,
 const utlTimeOutCount_T time_out_count ,
 void *arg_p ,
 const utlAbsoluteTime_P2c curr_time_p ) ;
typedef unsigned int utlMutexAttributes_T , *utlMutexAttributes_P ;
typedef unsigned int utlSemaphoreAttributes_T , *utlSemaphoreAttributes_P ;
typedef int utlStateMachineStateId_T , *utlStateMachineStateId_P ;
typedef int utlStateMachineEventId_T , *utlStateMachineEventId_P ;
typedef utlReturnCode_T ( *utlStateMachineStateFunction_P ) ( const utlStateMachine_P state_machine_p ,
 const utlStateMachineStateId_T state ,
 const utlAbsoluteTime_P2c curr_time_p ,
 void *arg_p ) ;
typedef utlReturnCode_T ( *utlStateMachineEventFunction_P ) ( const utlStateMachine_P state_machine_p ,
 const utlStateMachineStateId_T state ,
 const utlStateMachineEventId_T event ,
 const utlAbsoluteTime_P2c curr_time_p ,
 void *arg_p ,
 va_list va_arg_p ) ;
typedef const utlStateMachineEvent_T *utlStateMachineEvent_P2c ;
typedef const utlStateMachineState_T *utlStateMachineState_P2c ;
typedef unsigned int utlStateMachineFlags_T ;
typedef const utlStateMachine_T *utlStateMachine_P2c ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MAT_MSCLASS ,	 
 MAT_AOPS ,	 
 MAT_AOPSCFG ,	 
 MAT_CLAC ,	 
 MAT_E ,	 
 MAT_I ,	 
 MAT_L ,	 
 MAT_M ,	 
 MAT_O ,	 
 MAT_P ,	 
 MAT_Q ,	 
 MAT_T ,	 
 MAT_V ,	 
 MAT_X ,	 
 MAT_Z ,	 
 MAT_ampC ,	 
 MAT_ampD ,	 
 MAT_ampF ,	 
 MAT_ampS ,	 
 MAT_ampZ ,	 
 MAT_ampM ,	 
 MAT_ampV ,	 
 MAT_ampW ,	 
 MAT_CGMI ,	 
 MAT_CGMM ,	 
 MAT_CGMR ,	 
 MAT_CGSN ,	 
 MAT_CSCS ,	 
 MAT_CIMI ,	 
 MAT_ASTO ,	 
 MAT_GMI ,	 
 MAT_GMM ,	 
 MAT_GMR ,	 
 MAT_GSN ,	 
 MAT_GOI ,	 
 MAT_GCAP ,	 
 MAT_GCI ,	 
 MAT_IPR ,	 
 MAT_ICF ,	 
 MAT_IFC ,	 
 MAT_IDSR ,	 
 MAT_EXAMPLE ,	 
 MAT_RawAT ,	 
 MAT_A ,	 
 MAT_D ,	 
 MAT_H ,	 
 MAT_CSTA ,	 
 MAT_CMOD ,	 
 MAT_CVMOD ,	 
 MAT_CHUP ,	 
 MAT_CBST ,	 
 MAT_CVHU ,	 
 MAT_CRLP ,	 
 MAT_CEER ,	 
 MAT_CMUT ,	 
 MAT_ECHUPVT ,	 
 MAT_CREG ,	 
 MAT_CIND ,	 
 MAT_COPS ,	 
 MAT_CPOL ,	 
 MAT_CLCK ,	 
 MAT_CPWD ,	 
 MAT_CLIP ,	 
 MAT_CLIR ,	 
 MAT_COLP ,	 
 MAT_COLR ,	 
 MAT_CNAP ,	 
 MAT_CCFC ,	 
 MAT_CCWA ,	 
 MAT_FDNCHECK ,	 
 MAT_CHLD ,	 
 MAT_CAOC ,	 
 MAT_VTS ,	 
 MAT_VTD ,	 
 MAT_CSUEPOLICY ,	 
 MAT_CRUEPOLICY ,	 
 MAT_C5GREG ,	 
 MAT_C5GQOS ,	 
 MAT_C5GNSSAI ,	 
 MAT_C5GPNSSAI ,	 
 MAT_C5GNSSAIRDP ,	 
 MAT_APPSTART ,	 
 MAT_SETUEOSID ,	 
 MAT_UTTEST ,	 
 MAT_C5GCAPA ,	 
 MAT_CWUS ,	 
 MAT_CLADN ,	 
 MAT_CMICO ,	 
 MAT_OVERHEAT ,	 
 MAT_VOLTAGEFREQ ,	 
 MAT_CDNID ,	 
 MAT_CAG ,	 
 MAT_C5GUSMS ,	 
 MAT_LOCALURSP ,	 
 MAT_TSNCTRL ,	 
 MAT_CSSN ,	 
 MAT_CLCC ,	 
 MAT_FCLASS ,	 
 MAT_CDU ,	 
 MAT_dollarVTS ,	 
 MAT_starDIALE ,	 
 MAT_CSCB ,	 
 MAT_starCISCC ,	 
 MAT_starCIIND ,	 
 MAT_starIMSSRV ,	 
 MAT_MORESMS ,	 
 MAT_POCCMD ,	 
 MAT_startECCLIST ,	 
 MAT_starCCIREG ,	 
 MAT_CUSD ,	 
 MAT_PEER ,	 
 MAT_CSQ ,	 
 MAT_starREJCUSE ,	 
 MAT_CMUX ,	 
 MAT_NETDMSG ,	 
 MAT_CSQEX ,	 
 MAT_CPAS ,	 
 MAT_CFUN ,	 
 MAT_starCFUN ,	 
 MAT_CPIN ,	 
 MAT_CPIN2 ,	 
 MAT_EPIN ,	 
 MAT_CPINR ,	 
 MAT_starSIMDETEC ,	 
 MAT_CTZR ,	 
 MAT_CTZU ,	 
 MAT_starCTZR ,	 
 MAT_CPBS ,	 
 MAT_CPBR ,	 
 MAT_CPBW ,	 
 MAT_CPBF ,	 
 MAT_CSIM ,	 
 MAT_CRSM ,	 
 MAT_CGLA ,	 
 MAT_CRLA ,	 
 MAT_CCHO ,	 
 MAT_CCHC ,	 
 MAT_MSTK ,	 
 MAT_starEUICC ,	 
 MAT_CACM ,	 
 MAT_CAMM ,	 
 MAT_CCWE ,	 
 MAT_ADMINDATA ,	 
 MAT_CGREG ,	 
 MAT_starREGOPT ,	 
 MAT_CGATT ,	 
 MAT_CGACT ,	 
 MAT_CGDATA ,	 
 MAT_CGDCONT ,	 
 MAT_CGDSCONT ,	 
 MAT_CGQMIN ,	 
 MAT_CGQREQ ,	 
 MAT_CGEQREQ ,	 
 MAT_CGEQMIN ,	 
 MAT_GETIP ,	 
 MAT_starTGSINK ,	 
 MAT_CGSEND ,	 
 MAT_starICSSINK ,	 
 MAT_starAUTHReq ,	 
 MAT_starCHAPAUTH ,	 
 MAT_CMGF ,	 
 MAT_starCMGF ,	 
 MAT_LKSMSSTA ,	 
 MAT_CMSS ,	 
 MAT_CMGS ,	 
 MAT_CMGR ,	 
 MAT_CMGW ,	 
 MAT_CSCA ,	 
 MAT_CNMI ,	 
 MAT_CGSMS ,	 
 MAT_CMMS ,	 
 MAT_CMGD ,	 
 MAT_CMGC ,	 
 MAT_CMGL ,	 
 MAT_CSMS ,	 
 MAT_CPMS ,	 
 MAT_CNMA ,	 
 MAT_CSMP ,	 
 MAT_CSDH ,	 
 MAT_CSAS ,	 
 MAT_CRES ,	 
 MAT_CPNER ,	 
 MAT_CGCI ,	 
 MAT_CGOI ,	 
 MAT_VDUMP ,	 
 MAT_VPDUS ,	 
 MAT_VHDL ,	 
 MAT_VECHO ,	 
 MAT_ATDB ,	 
 MAT_CPUC ,	 
 MAT_CRC ,	 
 MAT_CMEE ,	 
 MAT_CDIP ,	 
 MAT_CPLS ,	 
 MAT_CGCMOD ,	 
 MAT_CNUM ,	 
 MAT_DS ,	 
 MAT_CGTFT ,	 
 MAT_starBAND ,	 
 MAT_starBANDIND ,	 
 MAT_starBANDRD ,	 
 MAT_starCLCK ,	 
 MAT_starMEPCG ,	 
 MAT_starENVSIM ,	 
 MAT_starCNMA ,	 
 MAT_starRSTMEMFULL ,	 
 MAT_starPOWERIND ,	 
 MAT_starFASTDORM ,	 
 MAT_starCellLock ,	 
 MAT_EEMOPT ,	 
 MAT_EEMGINFO ,	 
 MAT_ERGA ,	 
 MAT_ERTCA ,	 
 MAT_starCam_I2C ,	 
 MAT_starISP_REG ,	 
 MAT_starCam_rawdump ,	 
 MAT_starFILETEST ,	 
 MAT_starMRD_CDF ,	 
 MAT_starMRD_IMEI ,	 
 MAT_starMRD_MEP ,	 
 MAT_starMRD_CalInfo ,	 
 MAT_starMRD_MEPPLMN ,	 
 MAT_starMRD_SN ,	 
 MAT_starMRD_ITEM ,	 
 MAT_starMRD_ADC ,	 
 MAT_starMRD_RTPADC ,	 
 // #ifdef AT_PRODUCTION_CMNDS	 
 MAT_starGSMTR ,	 
 // #endif	 
 MAT_starCGSN ,	 
 MAT_starHTCCTO ,	 
 MAT_CMEMFULL ,	 
 MAT_starEHSDPA ,	 
 MAT_TPCN ,	 
 MAT_FWDB ,	 
 MAT_starFDY ,	 
 MAT_xorSYSINFO ,	 
 MAT_starCPBC ,	 
 MAT_starFDNBYPASS ,	 
 MAT_starCSCB ,	 
 MAT_starCBMCS ,	 
 MAT_starNASCHK ,	 
 MAT_CGEQOS ,	 
 MAT_CEREG ,	 
 MAT_CGCONTRDP ,	 
 MAT_CGSCONTRDP ,	 
 MAT_CGTFTRDP ,	 
 MAT_CGEQOSRDP ,	 
 MAT_CGEREP ,	 
 MAT_CEMODE ,	 
 MAT_CGPADDR ,	 
 MAT_xorCACAP ,	 
 MAT_CGCLASS ,	 
 MAT_CESQ ,	 
 MAT_BGLTEPLMN ,	 
 MAT_STARCGDFAUTH ,	 
 MAT_dollarMYMINISYS ,	 
 MAT_dollarMYFOTA ,	 
 MAT_VZWRSRP ,	 
 MAT_VZWRSRQ ,	 
 MAT_starCGDFLT ,	 
 MAT_STARNETACT ,	 
 MAT_STARNETREF ,	 
 MAT_STARNETDNS ,	 
 MAT_STARNETIF ,	 
 MAT_STARNETIFCM ,	 
 MAT_STARMPSAPN ,	 
 MAT_starMODEMRESET ,	 
 MAT_starVZWTESTAPP ,	 
 MAT_VZWAPNE ,	 
 MAT_COPN ,	 
 MAT_starGATR ,	 
 MAT_starGRIP ,	 
 MAT_playMP3 ,	 
 MAT_starCGMR ,	 
 MAT_starCOMCFG ,	 
 MAT_starRFTEMP ,	 
 MAT_startRFTEMPEX ,	 
 MAT_starSOCTEMP ,	 
 MAT_TEMPTEST ,	 
 MAT_BANSELCT ,	 
 MAT_SYSSLEEP ,	 
 MAT_starCGATT ,	 
 MAT_CGPIAF ,	 
 MAT_CIREG ,	 
 MAT_starUSBT ,	 
 MAT_starLTECOEX ,	 
 MAT_LTEPOWER ,	 
 MAT_LTETR ,	 
 MAT_COMMTR ,	 
 MAT_starCSQ ,	 
 MAT_MAXPOWER ,	 
 MAT_SIMDETEC ,	 
 MAT_CPLMNS ,	 
 MAT_WS46 ,	 
 MAT_starCELL ,	 
 MAT_CISRVCC ,	 
 MAT_CEVDP ,	 
 MAT_CEUS ,	 
 /* add for BT SAP */	 
 MAT_BTSTATR ,	 
 MAT_starWBAMR ,	 
 MAT_CNMPSD ,	 
 MAT_starREGMODE ,	 
 MAT_starIMLCONFIG ,	 
 MAT_CCLK ,	 
 MAT_starURSLCT ,	 
 MAT_starCBRAT ,	 
 MAT_starSECCAP ,	 
 MAT_starLTEBAND ,	 
 MAT_starPSTHRESHOLD ,	 
 MAT_LPNWUL ,	 
 MAT_LPLOCVR ,	 
 MAT_LPECID ,	 
 MAT_LPOTDOAABORT ,	 
 MAT_LPOTDOAREQ ,	 
 MAT_AGPSSET ,	 
 MAT_POSFUN ,	 
 MAT_L1DEBUG ,	 
 MAT_DSPINFO ,	 
 MAT_BLACKCELL ,	 
 MAT_CSCO ,	 
 MAT_CHIPSET ,	 
 MAT_FWOPT ,	 
 MAT_CIREP ,	 
 MAT_OPERCFG ,	 
 MAT_starPSDC ,	 
 MAT_CEN ,	 
 MAT_CNEM ,	 
 MAT_CAVIMS ,	 
 MAT_CASIMS ,	 
 MAT_CMMIVT ,	 
 MAT_CPSMS ,	 
 MAT_CEDRXS ,	 
 MAT_CEDRXRDP ,	 
 MAT_CCIOTOPT ,	 
 MAT_CRCES ,	 
 MAT_CSCON ,	 
 MAT_CIPCA ,	 
 MAT_CABTSR ,	 
 MAT_CABTRDP ,	 
 MAT_CGAPNRC ,	 
 MAT_MPBK ,	 
 MAT_CSODCP ,	 
 MAT_CRTDCP ,	 
 MAT_LOG ,	 
 MAT_starDIALMODE ,	 
 MAT_starAPNMODE ,	 
 MAT_starAGDCONT ,	 
 MAT_starAGACT ,	 
 MAT_starLWIPCTRL ,	 
 MAT_starNTP ,	 
 MAT_starCGDCONT ,	 
 MAT_starMTU ,	 
 MAT_plusSWITCHSIM ,	 
 MAT_plusDUALSIM ,	 
 MAT_plusBINDSIM ,	 
 MAT_plusCWRITESIM ,	 
 // #ifdef BT_TEST_SUPPORT 20201214 @xiaokeweng force enable as BT / WIFI could be enable / disable in SDK build	 
 MAT_BTTEST ,	 
 // #endif	 
 MAT_GPSINIT ,	 
 MAT_GPSSLEEP ,	 
 MAT_GPSPF ,	 
 MAT_AGNSSGET ,	 
 MAT_AGNSSSET ,	 
 MAT_GPSST ,	 
 MAT_GPSSET ,	 
 MAT_TRUSTNUM ,	 
 MAT_CHKTRUSTNUM ,	 
 MAT_starISIMAID ,	 
 MAT_CSSAC ,	 
 MAT_MEDCR ,	 
 MAT_AGNSSCFG ,	 
 MAT_UNIKEYINFO ,	 
 MAT_UNIKEYINFOM ,	 
 MAT_UNICERTINFO ,	 
 MAT_UNISHCERTINFO ,	 
 MAT_UNIDELCERTINFO ,	 
 MAT_UNIMQTTCONN ,	 
 MAT_UNIMQTTDISCON ,	 
 MAT_UNIMQTTSTATE ,	 
 MAT_UNIMQTTSUB ,	 
 MAT_UNIMQTTPUB ,	 
 MAT_UNIPSMSET ,	 
 MAT_UNIDMPAPNSET ,	 
 MAT_UNIDMPNETLOG ,	 
 MAT_MIPLMD ,	 
 MAT_STARREADVER ,	 
 MAT_STARREADCPUUID ,	 
 MAT_RESET ,	 
 MAT_RSTSET ,	 
 MAT_DNS ,	 
 MAT_dollarMYPOWEROFF ,	 
 MAT_dollarMYSOCKETLED ,	 
 MAT_dollarMYGMR ,	 
 MAT_dollarMYCCID ,	 
 MAT_dollarMYNETURC ,	 
 MAT_dollarMYTYPE ,	 
 MAT_dollarMYNETCON ,	 
 MAT_dollarMYNETACT ,	 
 MAT_dollarMYIPFILTER ,	 
 MAT_dollarMYNETSRV ,	 
 MAT_dollarMYNETOPEN ,	 
 MAT_dollarMYNETREAD ,	 
 MAT_dollarMYNETWRITE ,	 
 MAT_dollarMYNETCLOSE ,	 
 MAT_dollarMYNETACK ,	 
 MAT_dollarMYNETACCEPT ,	 
 MAT_dollarMYNETCREATE ,	 
 MAT_dollarMYFTPOPEN ,	 
 MAT_dollarMYFTPCLOSE ,	 
 MAT_dollarMYFTPSIZE ,	 
 MAT_dollarMYFTPGET ,	 
 MAT_dollarMYFTPPUT ,	 
 MAT_dollarMYBCCH ,	 
 MAT_dollarMYBAND ,	 
 MAT_dollarMYTIMEUPDATE ,	 
 MAT_dollarMYLACID ,	 
 MAT_dollarMYGPSPOS ,	 
 MAT_dollarMYGETKEY ,	 
 MAT_dollarMYSETINFO ,	 
 MAT_dollarMYSYSINFO ,	 
 MAT_dollarMYSYSINFOURC ,	 
 MAT_dollarMYDOWNLOAD ,	 
 MAT_QICSGP ,	 
 MAT_QIACT ,	 
 MAT_QIDEACT ,	 
 MAT_QIOPEN ,	 
 MAT_QICLOSE ,	 
 MAT_QISTATE ,	 
 MAT_QISEND ,	 
 MAT_QIRD ,	 
 MAT_QISENDEX ,	 
 MAT_QISWTMD ,	 
 MAT_QIGETERROR ,	 
 MAT_QISDE ,	 
 MAT_QICFG ,	 
 MAT_QFTPCFG ,	 
 MAT_QFTPOPEN ,	 
 MAT_QFTPCLOSE ,	 
 MAT_QFTPCWD ,	 
 MAT_QFTPPWD ,	 
 MAT_QFTPPUT ,	 
 MAT_QFTPGET ,	 
 MAT_QFTPSIZE ,	 
 MAT_QFTPDEL ,	 
 MAT_QFTPMKDIR ,	 
 MAT_QFTPRMDIR ,	 
 MAT_QFTPMDTM ,	 
 MAT_QFTPRENAME ,	 
 MAT_QFTPLIST ,	 
 MAT_QFTPNLST ,	 
 MAT_QFTPMLSD ,	 
 MAT_QFTPLEN ,	 
 MAT_QFTPSTAT ,	 
 MAT_TCPKEEPALIVE ,	 
 MAT_QSSLCFG ,	 
 MAT_QSSLOPEN ,	 
 MAT_QSSLSEND ,	 
 MAT_QSSLRECV ,	 
 MAT_QSSLCLOSE ,	 
 MAT_QSSLSTATE ,	 
	 
 MAT_NSOCR ,	 
 MAT_NSOST ,	 
 MAT_NSORF ,	 
 MAT_NSOCL ,	 
 MAT_NCDP ,	 
 MAT_NMGS ,	 
 MAT_NMGR ,	 
 MAT_NNMI ,	 
 MAT_NSMI ,	 
 MAT_NQMGR ,	 
 MAT_NQMGS ,	 
 MAT_NRB ,	 
 MAT_NUESTATS ,	 
 MAT_NEARFCN ,	 
 MAT_NPING ,	 
 MAT_NBAND ,	 
 MAT_NLOGLEVEL ,	 
 MAT_NCONFIG ,	 
 MAT_NTSETID ,	 
 MAT_xorHVER ,	 
 MAT_starPROD ,	 
 MAT_NVMFLUSH ,	 
 MAT_starSLT ,	 
 MAT_starPMICREG ,	 
 MAT_starREGRW ,	 
 MAT_starSSGLPC ,	 
 MAT_ZDON ,	 
 MAT_starASRCOPS ,	 
 MAT_starICCID ,	 
 MAT_CEN1 ,	 
 MAT_CEN2 ,	 
 MAT_starSULOGCFG ,	 
 MAT_starWIFICTRL ,	 
 MAT_starSIMPOLL ,	 
 MAT_cellinfo ,	 
 MAT_starEPIN ,	 
 MAT_starVER ,	 
 // #ifdef WIFI_FUNCTION_SUPPOR 20201214 @xiaokeweng force enable as BT / WIFI could be enable / disable in SDK build	 
 MAT_WIFI_CMD ,	 
 // #endif	 
 MAT_Audio_CMD ,	 
 MAT_starMRDBACKUP ,	 
 MAT_starSELECTVSIM ,	 
 MAT_starAVSIM ,	 
 MAT_starMRDWIFIMAC ,	 
 MAT_starMRDBTID ,	 
 MAT_starAUDNVM ,	 
 MAT_starSPN ,	 
 MAT_CMRSS ,	 
 MAT_CMGSS ,	 
 MAT_CMSMS ,	 
 MAT_CMGMS ,	 
 MAT_PACSP ,	 
 MAT_ENVCFG ,	 
 MAT_CUAD ,	 
 MAT_CECALLINSIDE ,	 
 MAT_CECALL ,	 
 MAT_ECALLDATA ,	 
 MAT_ECALLVOICE ,	 
 MAT_ECALLCFG ,	 
 MAT_ECALLONLY ,	 
 MAT_ECALLREG ,	 
 MAT_ECALLONLYSIM ,	 
 MAT_ECALLMSDGEN ,	 
 MAT_ECALLMSD ,	 
 MAT_ECALLPUSH ,	 
 MAT_ECALLMSDCFG ,	 
 MAT_ECALLMEDIAMSD ,	 
 MAT_IMSECALLSUPT ,	 
 MAT_ECALLONLYREG ,	 
 MAT_ECALLOVERIMS ,	 
 MAT_ECALLSMSNUM ,	 
 MAT_ECALLSMS ,	 
 MAT_ECALLMODE ,	 
 MAT_ECALLTIMER ,	 
 MAT_ECALLMUTESPK ,	 
 MAT_AUDGAIN ,	 
 MAT_AUDREC ,	 
 MAT_AUDRECSTOP ,	 
 MAT_AUDPLAY ,	 
 MAT_AUDPLAYSTOP ,	 
 MAT_STARGETIP ,	 
 MAT_ROAMINGDATA ,	 
 MAT_starDNSCFG ,	 
 MAT_DELFBPLMN ,	 
 MAT_COMFEATURE ,	 
 MAT_RPM ,	 
 MAT_CFGRPMSWITCH ,	 
 MAT_CFGRPMPARA ,	 
 MAT_CFGRPMCOUNTER ,	 
 MAT_CFGRPMCLR ,	 
 MAT_CMER ,	 
 MAT_RESENDPARA ,	 
 MAT_CTRSPSTRT ,	 
 MAT_CTRSPGETINFO ,	 
 MAT_CTRSPPROFACT ,	 
 MAT_CTRSPNTFYLEAB ,	 
 MAT_CTRSPSVN ,	 
 MAT_CR ,	 
 MAT_plusSINGLESIM ,	 
 MAT_SIMSLOT ,	 
 MAT_SVWIFI ,	 
	 
 MAT_AICWIFI ,	 
 MAT_SDIOPHASE ,	 
	 
 MAT_QUEC_I ,	 
 MAT_QUEC_CVERSION ,	 
 MAT_QUEC_CSUB ,	 
 MAT_QUEC_EGMR ,	 
 MAT_QUEC_QGMR ,	 
 MAT_QUEC_QWSETMAC ,	 
 MAT_QUEC_AUTODL ,	 
 MAT_QUEC_ADC ,	 
 MAT_QUEC_QSVN ,	 
 MAT_QUEC_QGSN ,	 
 MAT_QUEC_QINF ,	 
	 
	 
 MAT_starBLACKCELL ,	 
 MAT_starAUTOTZ ,	 
 MAT_UNKNOWN ,	 
 MAT_CMD_UNKNOWN = MAT_UNKNOWN ,	 
	 
 /* response type for MAT */	 
 MAT_RSP_UNKNOWN = 1000 ,	 
 MAT_RSP_OK ,	 
 MAT_RSP_ERROR ,	 
 MAT_RSP_CME_ERROR ,	 
 MAT_RSP_CMS_ERROR ,	 
 MAT_RSP_BUSY ,	 
	 
 /* indication type for MAT */	 
 MAT_IND_CONNECT ,	 
 MAT_IND_NO_CARRIER ,	 
 MAT_IND_RING ,	 
 MAT_IND_NO_ANSWER ,	 
 MAT_IND_NO_DIALTONE ,	 
	 
 /* CC Indication */	 
 MAT_IND_CRING ,	 
 MAT_IND_CCCM ,	 
 MAT_IND_CSSU ,	 
 MAT_IND_CSSI ,	 
 MAT_IND_CR ,	 
 MAT_IND_CEI ,	 
	 
 /* DEV Indication */	 
 MAT_IND_SYSCONFIG ,	 
 MAT_IND_EEMGINFOBASIC ,	 
 MAT_IND_EEMGINFOSVC ,	 
 MAT_IND_EEMGINFOPS ,	 
 MAT_IND_EEMGINFONC ,	 
 MAT_IND_EEMGINBFTM ,	 
 MAT_IND_EEMUMTSSVC ,	 
 MAT_IND_EEMUMTSINTRA ,	 
 MAT_IND_EEMUMTSINTER ,	 
 MAT_IND_EEMUMTSINTERRAT ,	 
 MAT_IND_EEMLTESVC ,	 
 MAT_IND_EEMLTEINTRA ,	 
 MAT_IND_EEMLTEINTER ,	 
 MAT_IND_EEMLTEINTERRAT ,	 
 MAT_IND_SNETIND ,	 
 MAT_IND_LPNWDL ,	 
 MAT_IND_LPSTATE ,	 
 MAT_IND_LPMEAST ,	 
 MAT_IND_LPRESET ,	 
 MAT_IND_DIP ,	 
 MAT_IND_LPOTDOAMEAS ,	 
 /* MM Indication */	 
 MAT_IND_CACAP ,	 
 MAT_IND_MODE ,	 
 MAT_IND_COPN ,	 
 MAT_IND_NITZ ,	 
 MAT_IND_MSRI ,	 
 MAT_IND_HOME_ZONE ,	 
	 
 /* MSG Indication */	 
 MAT_IND_MMSG ,	 
 MAT_IND_CMTI ,	 
 MAT_IND_CBM ,	 
 MAT_IND_CDS ,	 
 MAT_IND_CMT ,	 
	 
 /* PB Indication */	 
 MAT_IND_SCPBR ,	 
 MAT_IND_MPBK ,	 
	 
 /* PS Indication */	 
 MAT_IND_CGEQNEG ,	 
 MAT_IND_CGEV ,	 
	 
 /* SIM Indication */	 
 MAT_IND_COTA ,	 
 MAT_IND_REFRESH ,	 
 MAT_IND_SIM_RESET ,	 
 MAT_IND_CARDMODE ,	 
 MAT_IND_SPN ,	 
	 
 /* SS Indication */	 
 MAT_IND_LPLOC ,	 
 MAT_IND_SSRC ,	 
	 
 /* DAT Indication */	 
 MAT_IND_PSSDC ,	 
	 
 /* the change of sim / usim availability status report */	 
 MAT_IND_BTSSTAT ,	 
	 
 MAT_IND_DSAC ,	 
 MAT_IND_ADMINDATA ,	 
 MAT_IND_CIMI ,	 
 MAT_IND_PSLOAD ,	 
 MAT_IND_RBLOOP ,	 
 MAT_IND_CELL ,	 
 MAT_IND_CIREPI ,	 
 MAT_IND_CIREPH ,	 
 MAT_IND_DATASTATUS ,	 
 MAT_IND_CEDRXP ,	 
 MAT_IND_CCIOTOPTI ,	 
 MAT_IND_CABTSRI ,	 
 MAT_IND_CIREGU ,	 
 MAT_IND_AMRCODEC ,	 
 MAT_IND_CNEC_ESM ,	 
 MAT_IND_ATREADY ,	 
 MAT_IND_PLMNLIST ,	 
 MAT_IND_WIFICELLINFO ,	 
 MAT_C5GURSPQRY ,	 
 MAT_Z5GTD ,	 
 MAT_ASRESC ,	 
 MAT_QNWPREFCFG ,	 
	 
 NUM_OF_MAT_CMD	 
 } MATCmdType , MATRspType;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MAT_SVC_0 ,	 
 MAT_SVC_1 ,	 
 MAT_SVC_2 ,	 
 MAT_SVC_3 ,	 
 MAT_SVC_4 ,	 
 MAT_SVC_5 ,	 
 MAT_SVC_6 ,	 
 MAT_SVC_7 ,	 
 MAT_SVC_8 ,	 
 MAT_SVC_9 ,	 
 MAT_SVC_10 ,	 
 MAT_SVC_11 ,	 
 MAT_SVC_12 ,	 
 MAT_SVC_13 ,	 
 MAT_SVC_14 ,	 
 MAT_SVC_15 ,	 
 MAT_SVC_16 ,	 
 MAT_SVC_17 ,	 
 MAT_SVC_18 ,	 
 MAT_SVC_19 ,	 
 MAT_SVC_20 ,	 
 MAT_SVC_21 ,	 
 MAT_SVC_22 ,	 
 MAT_SVC_23 ,	 
 MAT_SVC_24 ,	 
 MAT_SVC_25 ,	 
 MAT_SVC_26 ,	 
 MAT_SVC_27 ,	 
 MAT_SVC_28 ,	 
 MAT_SVC_29 ,	 
 MAT_SVC_30 ,	 
 MAT_SVC_31 ,	 
 MAT_SVC_32 ,	 
 MAT_SVC_33 ,	 
 MAT_SVC_34 ,	 
 MAT_SVC_35 ,	 
 MAT_SVC_36 ,	 
 MAT_SVC_37 ,	 
 MAT_SVC_38 ,	 
 MAT_SVC_39 ,	 
 MAT_SVC_40 ,	 
 MAT_SVC_41 ,	 
 MAT_SVC_42 ,	 
 MAT_SVC_43 ,	 
 MAT_SVC_44 ,	 
 MAT_SVC_45 ,	 
 MAT_SVC_46 ,	 
 MAT_SVC_47 ,	 
 MAT_SVC_48 ,	 
 MAT_SVC_49 ,	 
 MAT_SVC_50 ,	 
 MAT_SVC_51 ,	 
 MAT_SVC_52 ,	 
 MAT_SVC_53 ,	 
 MAT_SVC_54 ,	 
 MAT_SVC_55 ,	 
 MAT_SVC_56 ,	 
 MAT_SVC_57 ,	 
 MAT_SVC_58 ,	 
 MAT_SVC_59 ,	 
 MAT_SVC_60 ,	 
 MAT_SVC_61 ,	 
 MAT_SVC_62 ,	 
 MAT_SVC_63 ,	 
 NUM_OF_MAT_SVC	 
 } MATSvcId;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MAT_SIM_0 ,	 
 MAT_SIM_1 ,	 
 NUM_OF_MAT_SIM	 
 } MATSimId;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 MAT_OP_UNKNOWN ,	 
 MAT_OP_GET , // ? e.g AT+CREG?	 
 MAT_OP_SET , // = e.g AT+CREG= " "	 
 MAT_OP_ACTION , // e.g AT+CPAS	 
 MAT_OP_SYNTAX , // =? e.g AT+CREG=?	 
 MAT_OP_RESERVWED // reserved for future use if needed	 
 } MATOpCode;

typedef int ( *MATConfIndCB ) ( MATSimId sim_id , MATReturnPara *resp , sipc_cmd_info_t*client_tag ) ;
typedef union utlAtDataValue_U {
 unsigned int decimal ;
 unsigned int hexadecimal ;
 unsigned int binary ;
 char *string_p ;
 char *qstring_p ;
 char *dial_string_p ;
 } utlAtDataValue_T , *utlAtDataValue_P ;
typedef const utlAtParameterValue_T *utlAtParameterValue_P2c ;
typedef const utlAtParameter_T *utlAtParameter_P2c ;
typedef const utlAtDceIoConfig_T *utlAtDceIoConfig_P2c ;
typedef const utlAtSoundConfig_T *utlAtSoundConfig_P2c ;
typedef utlReturnCode_T ( *utlAtGetParameterFunction_P ) ( const utlAtParameterOp_T op , const char *command_name_p , const utlAtParameterValue_P2c parameter_values_p , const size_t num_parameters , const char *info_text_p , unsigned int *xid_p , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtSetParameterFunction_P ) ( const utlAtParameterOp_T op , const char *command_name_p , const utlAtParameterValue_P2c parameter_values_p , const size_t num_parameters , const char *info_text_p , unsigned int *xid_p , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtDceIoConfigFunction_P ) ( const utlAtDceIoConfig_P2c dce_io_config_p , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtSoundConfigFunction_P ) ( const utlAtSoundConfig_P2c sound_config_p , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtSParameterFunction_P ) ( const unsigned int parameter_num , const unsigned int parameter_value , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtSaveDialStringFunction_P ) ( const char *location_name_p , const char *dial_string_p , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtRetrieveDialStringFunction_P ) ( const char **location_name_pp , const char **dial_string_pp , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtReplyFunction_P ) ( const char *string_p , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtTxLineDataFunction_P ) ( const unsigned char *octets_p , const size_t n , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtDriverRequestFunction_P ) ( const utlAtParser_P parser_p , const utlAtDriverRequest_T request , void *arg_p , ... ) ;
typedef utlReturnCode_T ( *utlAtCommandSyntaxFunction_P ) ( const utlAtParameterOp_T op , const char *command_name_p , const utlAtParameterValue_P2c parameter_values_p , const size_t num_parameters , const char *info_text_p , unsigned int *xid_p , void *arg_p ) ;
typedef unsigned int ( *utlAtGetAtcmdTimeoutValueFunction_P ) ( const utlAtCommand_P2c cmd_p , const utlAtAsyncOp_T op ) ;
typedef int ( *utlAtcmdTimeoutErrorFunction_P ) ( unsigned int atHandle ) ;
typedef void ( *utlAtcmdContinuousTimeoutFunction_P ) ( void ) ;
typedef int ( *utlAtParserTriggerFunction_P ) ( const utlAtParser_P parser_p ) ;
typedef void ( *utlSetAutoAnswerDelay_P ) ( void *arg_p , unsigned int delay_seconds ) ;
typedef void ( *utlGetAutoAnswerDelay_P ) ( void *arg_p , unsigned short *delay_seconds ) ;
typedef utlReturnCode_T ( *utlSendToProxy_P ) ( const char *command_name_p , const utlAtParameterOp_T op , const char *parameters_string_p , unsigned int *xid_p , void *arg_p ) ;
typedef unsigned int ( *utlIsProxyReq_P ) ( const char *cmdName , utlAtParameterOp_T cmdOp , unsigned int parserId ) ;
typedef void ( *utlIncProxyTOCounter_P ) ( unsigned int incValue ) ;
typedef const utlAtCommand_T *utlAtCommand_P2c ;
typedef const utlAtDialStringOptions_T *utlAtDialStringOptions_P2c ;
typedef const utlAtAsyncResponse_T *utlAtAsyncResponse_P2c ;
typedef const utlAtAsyncResponses_T *utlAtAsyncResponses_P2c ;
typedef const utlAtParser_T *utlAtParser_P2c ;
typedef TX_THREAD* rti_thread_t ;
//ICAT EXPORTED STRUCT 
 typedef struct {	 
 UINT32 product_id ;	 
 UINT8 data [ 20 ] ;	 
 } InfoForBoardTracking_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 rti_mode_none = 0x00 ,	 
 rti_check_mode = 0x01 ,	 
 rti_timer_mode = 0x02 ,	 
 rti_log2acat_mode = 0x03 ,	 
 rti_psoff_mode = 0x04 ,	 
 rti_uarttrace_mode = 0x05 ,	 
 rti_rfuarttest_mode = 0xFF ,	 
	 
 rti_urtlog_mode = 0x100 ,	 
 rti_usbtrace_mode = 0x101 ,	 
 rti_muxtrace_mode = 0x102 ,	 
 rti_fsyslog_mode = 0x103 ,	 
 rti_mode_max = 0xFFFF	 
 } rti_mode;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_DISABLE=0 ,	 
 RTI_EN_VER1=1 ,	 
 RTI_EN_VER2=2	 
 } RTI_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_QUE_DISABLE=0 ,	 
 RTI_QUE_ENABLE=1	 
 } RTI_QUE_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_EVT_DISABLE=0 ,	 
 RTI_EVT_ENABLE=1	 
 } RTI_EVT_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 TIME_OUT_3MS=0x62 ,	 
 TIME_OUT_4MS=0x83 ,	 
 TIME_OUT_5MS=0xA4 ,	 
 TIME_OUT_6MS=0xC4 ,	 
 TIME_OUT_MAX=0xFF	 
 } Timeout_Threshold;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 RTI_TYPE rtiType ;	 
 RTI_QUE_TYPE rtiQueType ;	 
 RTI_EVT_TYPE rtiEvtType ;	 
	 
 int rtiChange ;	 
 int rtiHT ;	 
 int rtiLT ;	 
	 
 int modeChange ;	 
 int modeHT ;	 
 int modeLT ;	 
	 
 Timeout_Threshold Timeout ;	 
 rti_mode rtiMode ;	 
 } RTICfg_t;

typedef UINT_T ( *InitFlash_F ) ( UINT8_T FlashNum , FlashBootType_T FlashBootType , UINT8_T *P_DefaultPartitionNum ) ;
typedef UINT_T ( *FinalizeFlash_F ) ( FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *ReadFlash_F ) ( UINT_T FlashOffset , UINT_T LocalBuffer , UINT_T Size , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *WriteFlash_F ) ( UINT_T FlashOffset , UINT_T LocalBuffer , UINT_T Size , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *EraseFlash_F ) ( UINT_T FlashOffset , UINT_T Size , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *ResetFlash_F ) ( FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *WipeFlash_F ) ( FlashBootType_T FlashBootType ) ;
typedef void ( *ChangePartition_F ) ( UINT_T PartitionNum , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *WriteOTP_F ) ( UINT_T FlashOffset , UINT_T LocalBuffer , UINT_T Size ) ;
typedef UINT_T ( *ReadOTP_F ) ( UINT_T FlashOffset , UINT_T LocalBuffer , UINT_T Size ) ;
typedef UINT_T ( *LockOTP_F ) ( void ) ;
typedef UINT_T ( *ConvertToLogicalAddr_F ) ( UINT_T FlashLocation , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *DataSearchFunc_F ) ( UINT_T *pResult , void *pResvHeader , UINT_T pkgID ) ;
typedef UINT_T ( *DataSearchFindNext_F ) ( UINT_T *pResult ) ;
typedef UINT_T ( *FlashInitAfterTimDownload_F ) ( void *pResvHeader , DataSearchFunc_F , DataSearchFunc_F , DataSearchFindNext_F , UINT_T *myPkgIDs ) ;
typedef unsigned int time_t ;
typedef unsigned int lzo_uint32 ;
typedef unsigned long lzo_uint ;
typedef signed int lzo_int32 ;
typedef signed long lzo_int ;
typedef unsigned int lzo_uint32_t ;
typedef unsigned short lzo_uint16_t ;
typedef int lzo_bool ;
DIAG_FILTER ( SW_PLAT , RDA , ReliableDataPhase2SUCCESS , DIAG_INFORMATION)  
 diagPrintf ( " *** SUCCESS *** - Reliable Data is valid *** " );

DIAG_FILTER ( SW_PLAT , RDA , FactoryDataPhase2FAIL , DIAG_INFORMATION)  
 diagPrintf ( " *** WARNING ***- Reliable Data area is invalid *** " );

DIAG_FILTER ( SW_PLAT , RDA , FactoryDataPhase2SUCCESS , DIAG_INFORMATION)  
 diagPrintf ( " *** SUCCESS *** - Reliable Data is valid *** " );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPack , DIAG_INFORMATION)  
 diagPrintf ( " RDPhase1 DDR buffer at address= [ %lx ] , upper limit [ %lx ] " , RD_DDR_begin , RD_DDR_end );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackStampValid , DIAG_INFORMATION)  
 diagPrintf ( " RD validBufferStamp OK = [ %lx ] " , header.validBufferStamp );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackCS , DIAG_INFORMATION)  
 diagPrintf ( " RD validBufferStamp checksum is ok = [ %lx ] " , header.validBufferStamp );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackStamp , DIAG_INFORMATION)  
 diagPrintf ( " RD WRONG validBufferStamp = [ %lx ] " , header.validBufferStamp );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackVersionFail , DIAG_INFORMATION)  
 diagPrintf ( " RD header version doesn ' t fit = [ %lx ] vs. [ %lx ] " , header.version , 0x00001019 );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackVersion , DIAG_INFORMATION)  
 diagPrintf ( " RD header.version = [ %lx ] " , header.version );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackCheckSum , DIAG_INFORMATION)  
 diagPrintf ( " RD WRONG header.bufCheckSum [ %lx ] != [ %lx ] " , header.bufCheckSum , checksum );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackDo , DIAG_INFORMATION)  
 diagPrintf ( " RD Convert data indx [ %lx ] " , dbgi++ );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackDo1 , DIAG_INFORMATION)  
 diagPrintf ( " ReliableData:Entry type: [ 0x%lx ] exit on [ 0x%lx ] " , entry.entryType , 0xDADADADA );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackIMEI , DIAG_INFORMATION)  
 diagPrintf ( " skip - RD IMEI_TYPE size [ %lx ] was update at ReliableData Pre Phase1 " , entry_size );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackMEP , DIAG_INFORMATION)  
 diagPrintf ( " update - RD MEP_TYPE size [ %lx ] " , entry_size );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackNVM , DIAG_INFORMATION)  
 diagPrintf ( " update - RD NVM_TYPE size [ %lx ] " , entry_size );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackCalibFromFS , DIAG_INFORMATION)  
 diagPrintf ( " USE ## %s ## from NVM , DON ' T LOAD from FLASH " , entry.file_name );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackSN_1 , DIAG_INFORMATION)  
 diagPrintf ( " update - RD SERIAL_NUM_TYPE size [ %lx ] " , entry_size );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackSN_2 , DIAG_INFORMATION)  
 diagPrintf ( " update - RD SERIAL_NUM_TYPE buffer address [ %lx ] " , pRAMbuf );

DIAG_FILTER ( SW_PLAT , RDA , ReliableDataUnPackDone , DIAG_INFORMATION)  
 diagPrintf ( " RD Convert Raw Data to COM data - Done " );

DIAG_FILTER ( SW_PLAT , RDA , APReliableDataCleanMallocFailed , DIAG_INFORMATION)  
 diagPrintf ( " AP RD CL malloc [ 0x%x ] failed! " , len );

DIAG_FILTER ( SW_PLAT , RDA , APReliableDataCleanStampValid , DIAG_INFORMATION)  
 diagPrintf ( " AP RD CL validBufferStamp OK = [ %lx ] " , header.validBufferStamp );

DIAG_FILTER ( SW_PLAT , RDA , APReliableDataCleanCS , DIAG_INFORMATION)  
 diagPrintf ( " AP RD CL validBufferStamp checksum is ok = [ %lx ] " , header.validBufferStamp );

DIAG_FILTER ( SW_PLAT , RDA , APReliableDataCleanStamp , DIAG_INFORMATION)  
 diagPrintf ( " AP RD CL WRONG validBufferStamp = [ %lx ] " , header.validBufferStamp );

DIAG_FILTER ( SW_PLAT , RDA , APReliableDataCleanCheckSum , DIAG_INFORMATION)  
 diagPrintf ( " AP RD CL WRONG header.bufCheckSum [ %lx ] != [ %lx ] " , header.bufCheckSum , checksum );

DIAG_FILTER ( SW_PLAT , RDA , APReliableDataCleanRemove , DIAG_INFORMATION)  
 diagPrintf ( " AP RD CL file name : %s " , entry.file_name );

DIAG_FILTER ( SW_PLAT , RDA , RDSaveFilesToFSOpen , DIAG_INFORMATION)  
 diagPrintf ( " RD save data to file ( open ) " );

DIAG_FILTER ( SW_PLAT , RDA , RDSaveFilesToFSFail , DIAG_INFORMATION)  
 diagPrintf ( " RD save data to file ( open fail ) " );

DIAG_FILTER ( SW_PLAT , RDA , RDSaveFilesToFSClose , DIAG_INFORMATION)  
 diagPrintf ( " RD save data to file ( close ) " );

//ICAT EXPORTED FUNCTION - ReliableData , TEST , RDTest 
 void RDTest ( void *p ) 
 {	 
 BOOL param = 1 ;	 
 if ( p )	 
 param = * ( UINT8 * ) p ;	 
	 
 ReliableDataUnPack ( param , 0 ) ;	 
 }

//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_RdGenData.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/RdGenData.c
typedef unsigned int size_t ;
typedef va_list __gnuc_va_list ;
typedef unsigned int size_t ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0 = 0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 , GPIO_PIN_33 , GPIO_PIN_34 , GPIO_PIN_35 , GPIO_PIN_36 , GPIO_PIN_37 , GPIO_PIN_38 , GPIO_PIN_39 ,	 
	 
 GPIO_PIN_40 , GPIO_PIN_41 , GPIO_PIN_42 , GPIO_PIN_43 , GPIO_PIN_44 , GPIO_PIN_45 , GPIO_PIN_46 , GPIO_PIN_47 ,	 
 GPIO_PIN_48 , GPIO_PIN_49 , GPIO_PIN_50 , GPIO_PIN_51 , GPIO_PIN_52 , GPIO_PIN_53 , GPIO_PIN_54 , GPIO_PIN_55 ,	 
 GPIO_PIN_56 , GPIO_PIN_57 , GPIO_PIN_58 , GPIO_PIN_59 , GPIO_PIN_60 , GPIO_PIN_61 , GPIO_PIN_62 , GPIO_PIN_63 ,	 
	 
 GPIO_PIN_64 , GPIO_PIN_65 , GPIO_PIN_66 , GPIO_PIN_67 , GPIO_PIN_68 , GPIO_PIN_69 , GPIO_PIN_70 , GPIO_PIN_71 ,	 
 GPIO_PIN_72 , GPIO_PIN_73 , GPIO_PIN_74 , GPIO_PIN_75 , GPIO_PIN_76 , GPIO_PIN_77 , GPIO_PIN_78 , GPIO_PIN_79 ,	 
 GPIO_PIN_80 , GPIO_PIN_81 , GPIO_PIN_82 , GPIO_PIN_83 , GPIO_PIN_84 , GPIO_PIN_85 , GPIO_PIN_86 , GPIO_PIN_87 ,	 
 GPIO_PIN_88 , GPIO_PIN_89 , GPIO_PIN_90 , GPIO_PIN_91 , GPIO_PIN_92 , GPIO_PIN_93 , GPIO_PIN_94 , GPIO_PIN_95 ,	 
	 
 GPIO_PIN_96 , GPIO_PIN_97 , GPIO_PIN_98 , GPIO_PIN_99 , GPIO_PIN_100 , GPIO_PIN_101 , GPIO_PIN_102 , GPIO_PIN_103 ,	 
 GPIO_PIN_104 , GPIO_PIN_105 , GPIO_PIN_106 , GPIO_PIN_107 , GPIO_PIN_108 , GPIO_PIN_109 , GPIO_PIN_110 , GPIO_PIN_111 ,	 
 GPIO_PIN_112 , GPIO_PIN_113 , GPIO_PIN_114 , GPIO_PIN_115 , GPIO_PIN_116 , GPIO_PIN_117 , GPIO_PIN_118 , GPIO_PIN_119 ,	 
 GPIO_PIN_120 , GPIO_PIN_121 , GPIO_PIN_122 , GPIO_PIN_123 , GPIO_PIN_124 , GPIO_PIN_125 , GPIO_PIN_126 , GPIO_PIN_127 ,	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
