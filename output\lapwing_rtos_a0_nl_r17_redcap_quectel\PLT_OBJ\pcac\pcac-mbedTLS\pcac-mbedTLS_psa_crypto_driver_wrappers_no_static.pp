//PPC Version : V2.1.9.30
//PPL Source File Name : pcac-mbedTLS_psa_crypto_driver_wrappers_no_static.ppp
//PPL Source File Name : L:/PLT/pcac/mbedTLS/library/psa_crypto_driver_wrappers_no_static.c
typedef unsigned int size_t ;
typedef unsigned int size_t ;
typedef unsigned int clock_t ;
typedef unsigned int time_t ;
typedef time_t mbedtls_time_t ;
typedef signed char int8_t ;
typedef signed short int int16_t ;
typedef signed int int32_t ;
typedef signed __int64 int64_t ;
typedef unsigned char uint8_t ;
typedef unsigned short int uint16_t ;
typedef unsigned int uint32_t ;
typedef unsigned __int64 uint64_t ;
typedef signed char int_least8_t ;
typedef signed short int int_least16_t ;
typedef signed int int_least32_t ;
typedef signed __int64 int_least64_t ;
typedef unsigned char uint_least8_t ;
typedef unsigned short int uint_least16_t ;
typedef unsigned int uint_least32_t ;
typedef unsigned __int64 uint_least64_t ;
typedef signed int int_fast8_t ;
typedef signed int int_fast16_t ;
typedef signed int int_fast32_t ;
typedef signed __int64 int_fast64_t ;
typedef unsigned int uint_fast8_t ;
typedef unsigned int uint_fast16_t ;
typedef unsigned int uint_fast32_t ;
typedef unsigned __int64 uint_fast64_t ;
typedef signed int intptr_t ;
typedef unsigned int uintptr_t ;
typedef signed long long intmax_t ;
typedef unsigned long long uintmax_t ;
typedef unsigned short wchar_t ;
typedef int64_t mbedtls_ms_time_t ;
typedef int mbedtls_iso_c_forbids_empty_translation_units ;
typedef unsigned int size_t ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
typedef signed int ptrdiff_t ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef long double max_align_t ;
typedef int32_t psa_status_t ;
typedef uint16_t psa_key_type_t ;
typedef uint8_t psa_ecc_family_t ;
typedef uint8_t psa_dh_family_t ;
typedef uint32_t psa_algorithm_t ;
typedef uint32_t psa_key_lifetime_t ;
typedef uint8_t psa_key_persistence_t ;
typedef uint32_t psa_key_location_t ;
typedef uint32_t psa_key_id_t ;
typedef psa_key_id_t mbedtls_svc_key_id_t ;
typedef uint32_t psa_key_usage_t ;
typedef uint16_t psa_key_derivation_step_t ;
typedef union {
 unsigned dummy ;
 mbedtls_psa_hash_operation_t mbedtls_ctx ;



 } psa_driver_hash_context_t ;
typedef union {
 unsigned dummy ;
 mbedtls_psa_cipher_operation_t mbedtls_ctx ;




 } psa_driver_cipher_context_t ;
typedef int32_t mbedtls_mpi_sint ;
typedef uint32_t mbedtls_mpi_uint ;
typedef uint64_t mbedtls_t_udbl ;
typedef void mbedtls_ecp_restart_ctx ;
typedef mbedtls_ecp_keypair mbedtls_ecdsa_context ;
typedef void mbedtls_ecdsa_restart_ctx ;
typedef union {
 unsigned dummy ;
 mbedtls_psa_mac_operation_t mbedtls_ctx ;




 } psa_driver_mac_context_t ;
typedef union {
 unsigned dummy ;
 mbedtls_psa_aead_operation_t mbedtls_ctx ;



 } psa_driver_aead_context_t ;
typedef union {
 unsigned dummy ;
 mbedtls_psa_sign_hash_interruptible_operation_t mbedtls_ctx ;
 } psa_driver_sign_hash_interruptible_context_t ;
typedef union {
 unsigned dummy ;
 mbedtls_psa_verify_hash_interruptible_operation_t mbedtls_ctx ;
 } psa_driver_verify_hash_interruptible_context_t ;
typedef union {
 unsigned dummy ;
 mbedtls_psa_pake_operation_t mbedtls_ctx ;




 } psa_driver_pake_context_t ;
typedef union {
 unsigned dummy ;
 # 49 " L: / PLT / pcac / mbedTLS / include / psa / crypto_driver_contexts_key_derivation.h "
 } psa_driver_key_derivation_context_t ;
typedef uint16_t psa_key_bits_t ;
typedef mbedtls_svc_key_id_t psa_key_handle_t ;
typedef uint64_t psa_drv_slot_number_t ;
typedef uint8_t psa_pake_role_t ;
typedef uint8_t psa_pake_step_t ;
typedef uint8_t psa_pake_primitive_type_t ;
typedef uint8_t psa_pake_family_t ;
typedef uint32_t psa_pake_primitive_t ;
typedef psa_status_t ( *psa_drv_se_init_t ) ( psa_drv_se_context_t *drv_context ,
 void *persistent_data ,
 psa_key_location_t location ) ;
typedef uint64_t psa_key_slot_number_t ;
typedef psa_status_t ( *psa_drv_se_mac_setup_t ) ( psa_drv_se_context_t *drv_context ,
 void *op_context ,
 psa_key_slot_number_t key_slot ,
 psa_algorithm_t algorithm ) ;
typedef psa_status_t ( *psa_drv_se_mac_update_t ) ( void *op_context ,
 const uint8_t *p_input ,
 size_t input_length ) ;
typedef psa_status_t ( *psa_drv_se_mac_finish_t ) ( void *op_context ,
 uint8_t *p_mac ,
 size_t mac_size ,
 size_t *p_mac_length ) ;
typedef psa_status_t ( *psa_drv_se_mac_finish_verify_t ) ( void *op_context ,
 const uint8_t *p_mac ,
 size_t mac_length ) ;
typedef psa_status_t ( *psa_drv_se_mac_abort_t ) ( void *op_context ) ;
typedef psa_status_t ( *psa_drv_se_mac_generate_t ) ( psa_drv_se_context_t *drv_context ,
 const uint8_t *p_input ,
 size_t input_length ,
 psa_key_slot_number_t key_slot ,
 psa_algorithm_t alg ,
 uint8_t *p_mac ,
 size_t mac_size ,
 size_t *p_mac_length ) ;
typedef psa_status_t ( *psa_drv_se_mac_verify_t ) ( psa_drv_se_context_t *drv_context ,
 const uint8_t *p_input ,
 size_t input_length ,
 psa_key_slot_number_t key_slot ,
 psa_algorithm_t alg ,
 const uint8_t *p_mac ,
 size_t mac_length ) ;
typedef psa_status_t ( *psa_drv_se_cipher_setup_t ) ( psa_drv_se_context_t *drv_context ,
 void *op_context ,
 psa_key_slot_number_t key_slot ,
 psa_algorithm_t algorithm ,
 psa_encrypt_or_decrypt_t direction ) ;
typedef psa_status_t ( *psa_drv_se_cipher_set_iv_t ) ( void *op_context ,
 const uint8_t *p_iv ,
 size_t iv_length ) ;
typedef psa_status_t ( *psa_drv_se_cipher_update_t ) ( void *op_context ,
 const uint8_t *p_input ,
 size_t input_size ,
 uint8_t *p_output ,
 size_t output_size ,
 size_t *p_output_length ) ;
typedef psa_status_t ( *psa_drv_se_cipher_finish_t ) ( void *op_context ,
 uint8_t *p_output ,
 size_t output_size ,
 size_t *p_output_length ) ;
typedef psa_status_t ( *psa_drv_se_cipher_abort_t ) ( void *op_context ) ;
typedef psa_status_t ( *psa_drv_se_cipher_ecb_t ) ( psa_drv_se_context_t *drv_context ,
 psa_key_slot_number_t key_slot ,
 psa_algorithm_t algorithm ,
 psa_encrypt_or_decrypt_t direction ,
 const uint8_t *p_input ,
 size_t input_size ,
 uint8_t *p_output ,
 size_t output_size ) ;
typedef psa_status_t ( *psa_drv_se_asymmetric_sign_t ) ( psa_drv_se_context_t *drv_context ,
 psa_key_slot_number_t key_slot ,
 psa_algorithm_t alg ,
 const uint8_t *p_hash ,
 size_t hash_length ,
 uint8_t *p_signature ,
 size_t signature_size ,
 size_t *p_signature_length ) ;
typedef psa_status_t ( *psa_drv_se_asymmetric_verify_t ) ( psa_drv_se_context_t *drv_context ,
 psa_key_slot_number_t key_slot ,
 psa_algorithm_t alg ,
 const uint8_t *p_hash ,
 size_t hash_length ,
 const uint8_t *p_signature ,
 size_t signature_length ) ;
typedef psa_status_t ( *psa_drv_se_asymmetric_encrypt_t ) ( psa_drv_se_context_t *drv_context ,
 psa_key_slot_number_t key_slot ,
 psa_algorithm_t alg ,
 const uint8_t *p_input ,
 size_t input_length ,
 const uint8_t *p_salt ,
 size_t salt_length ,
 uint8_t *p_output ,
 size_t output_size ,
 size_t *p_output_length ) ;
typedef psa_status_t ( *psa_drv_se_asymmetric_decrypt_t ) ( psa_drv_se_context_t *drv_context ,
 psa_key_slot_number_t key_slot ,
 psa_algorithm_t alg ,
 const uint8_t *p_input ,
 size_t input_length ,
 const uint8_t *p_salt ,
 size_t salt_length ,
 uint8_t *p_output ,
 size_t output_size ,
 size_t *p_output_length ) ;
typedef psa_status_t ( *psa_drv_se_aead_encrypt_t ) ( psa_drv_se_context_t *drv_context ,
 psa_key_slot_number_t key_slot ,
 psa_algorithm_t algorithm ,
 const uint8_t *p_nonce ,
 size_t nonce_length ,
 const uint8_t *p_additional_data ,
 size_t additional_data_length ,
 const uint8_t *p_plaintext ,
 size_t plaintext_length ,
 uint8_t *p_ciphertext ,
 size_t ciphertext_size ,
 size_t *p_ciphertext_length ) ;
typedef psa_status_t ( *psa_drv_se_aead_decrypt_t ) ( psa_drv_se_context_t *drv_context ,
 psa_key_slot_number_t key_slot ,
 psa_algorithm_t algorithm ,
 const uint8_t *p_nonce ,
 size_t nonce_length ,
 const uint8_t *p_additional_data ,
 size_t additional_data_length ,
 const uint8_t *p_ciphertext ,
 size_t ciphertext_length ,
 uint8_t *p_plaintext ,
 size_t plaintext_size ,
 size_t *p_plaintext_length ) ;
typedef psa_status_t ( *psa_drv_se_allocate_key_t ) (
 psa_drv_se_context_t *drv_context ,
 void *persistent_data ,
 const psa_key_attributes_t *attributes ,
 psa_key_creation_method_t method ,
 psa_key_slot_number_t *key_slot ) ;
typedef psa_status_t ( *psa_drv_se_validate_slot_number_t ) (
 psa_drv_se_context_t *drv_context ,
 void *persistent_data ,
 const psa_key_attributes_t *attributes ,
 psa_key_creation_method_t method ,
 psa_key_slot_number_t key_slot ) ;
typedef psa_status_t ( *psa_drv_se_import_key_t ) (
 psa_drv_se_context_t *drv_context ,
 psa_key_slot_number_t key_slot ,
 const psa_key_attributes_t *attributes ,
 const uint8_t *data ,
 size_t data_length ,
 size_t *bits ) ;
typedef psa_status_t ( *psa_drv_se_destroy_key_t ) (
 psa_drv_se_context_t *drv_context ,
 void *persistent_data ,
 psa_key_slot_number_t key_slot ) ;
typedef psa_status_t ( *psa_drv_se_export_key_t ) ( psa_drv_se_context_t *drv_context ,
 psa_key_slot_number_t key ,
 uint8_t *p_data ,
 size_t data_size ,
 size_t *p_data_length ) ;
typedef psa_status_t ( *psa_drv_se_generate_key_t ) (
 psa_drv_se_context_t *drv_context ,
 psa_key_slot_number_t key_slot ,
 const psa_key_attributes_t *attributes ,
 uint8_t *pubkey , size_t pubkey_size , size_t *pubkey_length ) ;
typedef psa_status_t ( *psa_drv_se_key_derivation_setup_t ) ( psa_drv_se_context_t *drv_context ,
 void *op_context ,
 psa_algorithm_t kdf_alg ,
 psa_key_slot_number_t source_key ) ;
typedef psa_status_t ( *psa_drv_se_key_derivation_collateral_t ) ( void *op_context ,
 uint32_t collateral_id ,
 const uint8_t *p_collateral ,
 size_t collateral_size ) ;
typedef psa_status_t ( *psa_drv_se_key_derivation_derive_t ) ( void *op_context ,
 psa_key_slot_number_t dest_key ) ;
typedef psa_status_t ( *psa_drv_se_key_derivation_export_t ) ( void *op_context ,
 uint8_t *p_output ,
 size_t output_size ,
 size_t *p_output_length ) ;
typedef unsigned int size_t ;
