# 1 "L:/PLT/pcac/pca_components/src/pcac_gm_api.c"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : pcac_gm_api.c
Description : This file implements PCA Components api
 
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved. 
The source code contained or described herein and all documents related to the source code ("Material") are owned 
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or 
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of 
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and 
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted, 
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or 
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement, 
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by 
Intel in writing.

Unless otherwise agreed by Intel in writing, you may not remove or alter this notice or any other notice embedded
in Materials by Intel or Intel's suppliers or licensors in any way
=========================================================================== */

/************************************************************************
 * Project header files
 ***********************************************************************/
# 1 "L:/PLT/pcac/pca_components/inc/pcac_gm_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : pcac_types.h
Description : Data types file 
Notes       : 

INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved. 
The source code contained or described herein and all documents related to the source code ("Material") are owned 
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or 
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of 
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and 
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted, 
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or 
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement, 
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by 
Intel in writing.

Unless otherwise agreed by Intel in writing, you may not remove or alter this notice or any other notice embedded
in Materials by Intel or Intel's suppliers or licensors in any way
=========================================================================== */








# 1 "L:/PLT/csw/platform/inc/gbl_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ============================================================================
File        : gbl_types.h
Description : Global types file for testing the
              os/kal package.

Notes       : This file is only used to test the compilation and
              archiving for the os/kal package.

Copyright 2001, Intel Corporation, All rights reserved.
============================================================================ */




/* Use the Xscale environment types */
# 1 "L:/PLT/env/win32/inc/xscale_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : xscale_types.h
Description : Global types file for the Xscale environment.

Notes       : This file is designed for use in the arm environment
              and is referenced from the gbl_types.h file. Use of
			  this file requires ENV_XSCALE to be defined in xscale_env.mak.
              
Copyright 2001, Intel Corporation, All rights reserved.
=========================================================================== */




typedef unsigned char	BOOL;
typedef unsigned char   UINT8;
typedef unsigned short  UINT16;
typedef unsigned long   UINT32;

typedef char            CHAR;
typedef signed char     INT8;
typedef signed short    INT16;
typedef signed long     INT32;













/*                         end of xscale_types.h
--------------------------------------------------------------------------- */



# 23 "L:/PLT/csw/platform/inc/gbl_types.h"


/* Use the NordHeim environment types */




/* Use the Arm environment types */




/* Use the Gnu environment types */




/* Use the Microsoft Visual C environment types */




  /* Standard typedefs */
  typedef unsigned char   Bool;         /* Boolean                        */

  /* Standard typedefs - to retain compatibility with TDMA */
  typedef UINT8           		 BYTE;         			/* Unsigned 8-bit quantity        */
  typedef UINT8            		 UBYTE;        			/* Unsigned 8-bit quantity        */
  typedef UINT16          		 UWORD;        			/* Unsigned 16-bit quantity       */
  typedef UINT16          		 WORD;         			/* Unsigned 16-bit quantity       */
  typedef INT16           		 SWORD;        			/* Signed 16-bit quantity         */
  typedef UINT32                 DWORD;        			/* Unsigned 32-bit quantity       */
  typedef unsigned long long     UINT64;                /* Unsigned 64-bit quantity       */
  typedef void* 		         VOID_PTR;























  /* A NULL value is required such that it is not mistaken for a valid */
  /* value which includes values in the range of modulo 64. */


  /* Definition of NULL is required */















/*                      end of gbl_types.h
--------------------------------------------------------------------------- */



# 37 "L:/PLT/pcac/pca_components/inc/pcac_gm_types.h"



typedef enum
{ 
PCAC_UTILS = 0,	
PCAC_BUS,		// 1 
PCAC_LLC,		// 2 
PCAC_PLC,		// 3 
PCAC_DL,	   	// 4 
PCAC_AUDIO_STUB,   	// 5 
PCAC_CCI_STUB,		// 6 
PCAC_GPC_STUB,		// 7 
PCAC_SAC,           // 8
PCAC_MAX
} PCAC_COMPONENT;


# 72 "L:/PLT/pcac/pca_components/inc/pcac_gm_types.h"


 
typedef UINT16 PCAC_STATUS;
typedef UINT16 PCAC_INIT_STATUS;






 





# 32 "L:/PLT/pcac/pca_components/src/pcac_gm_api.c"
# 1 "L:/PLT/pcac/pca_components/inc/pcac_gm_config.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : pcac_config.h
Description : Configuration parameters for the
              pca components 

              The following are pca components common configuration or tuning parameters
              that are used to configure the pca components

Notes       : These values can be overridden in gbl_config.h

INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved. 
The source code contained or described herein and all documents related to the source code ("Material") are owned 
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or 
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of 
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and 
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted, 
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or 
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement, 
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by 
Intel in writing.

Unless otherwise agreed by Intel in writing, you may not remove or alter this notice or any other notice embedded
in Materials by Intel or Intel's suppliers or licensors in any way
=========================================================================== */












# 1 "L:/PLT/tavor/Arbel/inc/gbl_config.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ============================================================================
File        : gbl_config.h
Description : Global configuration file for testing the
              os/kal package.

Notes       : This file is only used to test the compilation and
              archiving for the os/kal package.

Copyright 2001, Intel Corporation, All rights reserved.
============================================================================ */




# 45 "L:/PLT/tavor/Arbel/inc/gbl_config.h"

//#undef  OSA_INTERRUPTS
//#define OSA_INTERRUPTS 0





/* OSA 2.3.4 and above support queue names as an API option to supply a queue name to OSAMsgQCreate
 * (enabled by macro OSA_QUEUE_NAMES, default: off).
 * This presents a non backwards-compatible API change.
 * Any code using OSAMsgQCreate must be able to supply the added parameter.
 * For compatibility with previous OSA version undefine the below switch.
 * A compilation failure would occur if an old OSA version was used along with OSA_QUEUE_NAMES defined.
 * */











/*                      end of gbl_config.h
--------------------------------------------------------------------------- */

# 49 "L:/PLT/pcac/pca_components/inc/pcac_gm_config.h"



# 33 "L:/PLT/pcac/pca_components/src/pcac_gm_api.c"
# 1 "L:/PLT/pcac/pca_components/inc/pcac_gm_api.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : pcac_gm_api.h
Description : This file defines PCA Components api
 
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved. 
The source code contained or described herein and all documents related to the source code ("Material") are owned 
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or 
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of 
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and 
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted, 
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or 
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement, 
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by 
Intel in writing.

Unless otherwise agreed by Intel in writing, you may not remove or alter this notice or any other notice embedded
in Materials by Intel or Intel's suppliers or licensors in any way
=========================================================================== */







# 35 "L:/PLT/pcac/pca_components/inc/pcac_gm_api.h"

/*****************************************************************************
 * Constants
 ****************************************************************************/
/*****************************************************************************
 * Function Prototypes
 ****************************************************************************/

void pcacSetStatus(
	PCAC_COMPONENT component,    // Component being reported 
	PCAC_STATUS status           // Status of component to set
	);
void pcacClrStatus(
	PCAC_COMPONENT component,    // Component being reported 
	PCAC_STATUS status           // Status of component to clear
	);

PCAC_STATUS pcacGetStatus(
	PCAC_COMPONENT component    // Component being reported
	);

PCAC_INIT_STATUS pcacGetInitStatus(void); 

void pcacWaitStatus(PCAC_COMPONENT component, PCAC_STATUS status);

















# 34 "L:/PLT/pcac/pca_components/src/pcac_gm_api.c"
# 1 "L:/PLT/pcac/pca_components/inc/pcac_gm_trace.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : pcac_types.h
Description : Data types file
Notes       :

INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.

Unless otherwise agreed by Intel in writing, you may not remove or alter this notice or any other notice embedded
in Materials by Intel or Intel's suppliers or licensors in any way
=========================================================================== */








# 1 "L:/PLT/pcac/msl_utils/inc/msl_trace.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
**
** INTEL CONFIDENTIAL
** Copyright 2006 Intel Corporation All Rights Reserved.
** The source code contained or described herein and all documents related to the source code ("Material") are owned
** by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
** its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
** Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
** treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
** transmitted, distributed, or disclosed in any way without Intel's prior express written permission.
**
** No license under any patent, copyright, trade secret or other intellectual property right is granted to or
** conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
** estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
** Intel in writing.
**
** Unless otherwise agreed by Intel in writing, you may not remove or alter this notice or any other notice embedded
** in Materials by Intel or Intel's suppliers or licensors in any way.
**
**  FILENAME:       msl_trace.h
**
**  PURPOSE:        This is the header file for the MSL abstracted tracing interface
**					definition.
**
**  LAST MODIFIED:  $Modtime:  $
******************************************************************************/





# 1 "L:/PLT/pcac/msl_utils/inc/msl_trmsg.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
**
** INTEL CONFIDENTIAL
** Copyright 2006 Intel Corporation All Rights Reserved. 
** The source code contained or described herein and all documents related to the source code ("Material") are owned 
** by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or 
** its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of 
** Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and 
** treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted, 
** transmitted, distributed, or disclosed in any way without Intel's prior express written permission.
** 
** No license under any patent, copyright, trade secret or other intellectual property right is granted to or 
** conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement, 
** estoppel or otherwise. Any license under such intellectual property rights must be express and approved by 
** Intel in writing.
** 
** Unless otherwise agreed by Intel in writing, you may not remove or alter this notice or any other notice embedded
** in Materials by Intel or Intel's suppliers or licensors in any way. 
**
**  FILENAME:       msl_trace.h
**
**  PURPOSE:        This is the header file for the MSL tracing message output function.
**
**  LAST MODIFIED:  $Modtime:  $
******************************************************************************/




# 36 "L:/PLT/pcac/msl_utils/inc/msl_trmsg.h"


# 88 "L:/PLT/pcac/msl_utils/inc/msl_trmsg.h"



// for wince debug
# 104 "L:/PLT/pcac/msl_utils/inc/msl_trmsg.h"

# 38 "L:/PLT/pcac/msl_utils/inc/msl_trace.h"

# 1 "L:/PLT/pcac/msl_utils/inc/msl_measures.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
**
**  FILENAME:       msl_measures.h
**
**  PURPOSE:        This is a header file for MSL Measurements & Profiling
**
**  LAST MODIFIED:  $Modtime:  $
******************************************************************************/




/* Enable below flag ON for PROFILING MEASUREMENTS (ISPT) */
//#define MSL_MEASUREMENTS_ON


/* Currently MSL_MEASUREMENTS_ON is not supported under the below flavors */






# 39 "L:/PLT/pcac/msl_utils/inc/msl_measures.h"











typedef enum
{
    dummy_ACI_PROFILE_FuncID    = 100,              /*100*/
    serverCiIndicationCallback_FuncID,                              /*101*/
    MslDlTxDataReqDirectBuffer_FuncID,                              /*102*/
    MslBusTransmitPacket_FuncID,                                    /*103*/
    MslBusStartNextTransfer_FuncID,                                 /*104*/
    MslBusSetupDmaTransfer_FuncID,                                  /*105*/
    XllpDmacCfgChannelNoDescTransfer_FuncID,                        /*106*/
    XllpDmacCfgChannelNoDescTransfer_CacheClean_FuncID,             /*107*/
    XllpDmacCfgChannelNoDescTransfer_DmaSetup_FuncID,               /*108*/
    ciServerDataChanCallbackFunc_CiRequest_FuncID,                  /*109*/
    ciRequest_svc_FuncID,                                           /*110*/
    TxBufferDone_FuncID,                                            /*111*/
    llcUmDirectPacketTxConfCallback_FuncID,                         /*112*/
    llcUmDirectPacketTxConfCallback_TxDoneCallback_FuncID,          /*113*/
    serverCiIndicationCallback_Malloc_FuncID,                       /*114*/
    serverCiIndicationCallback_MemCpy_FuncID,                       /*115*/

    last_ACI_PROFILE_FuncID

} ACI_ProfileFuncIDs;




# 40 "L:/PLT/pcac/msl_utils/inc/msl_trace.h"




//typedef UINT32 MslTraceLevel;
enum _MslTraceLevel
{
    MSL_TRACE_ERROR     = 0x0001,
    MSL_TRACE_API       = 0x0002,
    MSL_TRACE_INFO      = 0x0004,
    MSL_TRACE_DEBUG     = 0x0008,
    MSL_TRACE_MISC      = 0x0010,
    MSL_TRACE_INIT      = 0x0020,
    MSL_TRACE_EXCEPT    = 0x0040,
    MSL_TRACE_CHECKSUM  = 0x0080,
    MSL_TRACE_WARNING   = 0x0100,
    MSL_TRACE_ALL       = 0xFFFF
};

typedef UINT32 MSL_TRACE_LEVEL;


MSL_TRACE_LEVEL MslTraceFilter (void);
void SetAciTraceLevel(UINT32 *level, UINT32 len);


# 74 "L:/PLT/pcac/msl_utils/inc/msl_trace.h"

/************************
 * MSL Trace Macros (without ASSERT which define below)
*************************/
/* Tracing is disabled */

# 86 "L:/PLT/pcac/msl_utils/inc/msl_trace.h"

    //#define MSL_ASSERT(cond)   if (cond == 0) while (1)

# 213 "L:/PLT/pcac/msl_utils/inc/msl_trace.h"

# 220 "L:/PLT/pcac/msl_utils/inc/msl_trace.h"
    //for NUCLEUS/RTOS







/************************
 * MSL ASSERT definition
*************************/
# 1 "L:/PLT/hal/core/inc/utils.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

//Copyright 2005, Intel Corporation, All rights reserved.
/************************************************************************/
/*   Utils.h - 															*/
/* 																		*/
/************************************************************************/




# 1 "L:/PLT/csw/platform/inc/global_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/****************************************************************************
 *
 * Name:          global_types.h
 *
 * Description:   Standard type definitions
 *
 ****************************************************************************
 *
 *
 *
 *
 ****************************************************************************
 *                  Copyright (c) Intel 2000
 ***************************************************************************/




# 25 "L:/PLT/csw/platform/inc/global_types.h"
# 1 "L:/PLT/hal/core/inc/utils.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

//Copyright 2005, Intel Corporation, All rights reserved.
/************************************************************************/
/*   Utils.h - 															*/
/* 																		*/
/************************************************************************/

# 312 "L:/PLT/hal/core/inc/utils.h"

# 26 "L:/PLT/csw/platform/inc/global_types.h"

  /* Standard typedefs */
  typedef volatile UINT8  *V_UINT8_PTR;  /* Ptr to volatile unsigned 8-bit quantity       */
  typedef volatile UINT16 *V_UINT16_PTR; /* Ptr to volatile unsigned 16-bit quantity       */
  typedef volatile UINT32 *V_UINT32_PTR; /* Ptr to volatile unsigned 32-bit quantity       */

  typedef unsigned int    U32Bits;
  typedef BOOL BOOLEAN;


  typedef const char *    SwVersion;



  /* Handy macros */
# 47 "L:/PLT/csw/platform/inc/global_types.h"


  /* Bit fields macros */
  // Yaeli Karni - need to work also when number GT 32 ! (march 06)


//strncat by shashal 



 







# 16 "L:/PLT/hal/core/inc/utils.h"
//should be used in BootLoader & Flasher ONLY
//#define LOW_LEVEL_ASSERTS_ONLY

//#if defined (_TAVOR_HARBELL_) || defined(SILICON_PV2)

// To Be Deleted...




typedef enum
{
	CPU_HERMON_B0 = 0,
	CPU_HERMON_B1,
	CPU_HERMON_B2,
	CPU_HERMON_B3,
	CPU_HERMON_TCB874,
	CPU_HERMONL_A0,
	CPU_HERMONC_A0,
	CPU_HERMON_TCC874,
	CPU_HERMONEL_A0,
	CPU_MANITOBA_OTHER,
	CPU_BVD,
	CPU_TAVOR_A0,
	CPU_TAVOR_B0,
	CPU_TAVOR_B1,
	CPU_TAVOR_B2,
	CPU_TAVOR_PV_A0,
	CPU_TAVOR_PV_B0,
	CPU_TAVOR_PV_C0,
	CPU_TTC,
	CPU_OTHER
}CPU_Version;  //if this enum changed, update also the CPU_Version_str[]

// Returns the CPU version according to the above list
CPU_Version GetCpuVersion(void);

typedef enum CPU_family_tag
{
	CPU_TAVOR_PV2,		//Z0, A0, B0
	CPU_TAVOR_MG1,		// Z0=A0, A1, B0
	CPU_TAVOR_MG2,		// A0
	CPU_ESHEL,		// A0
	CPU_NEVO,		// A0
	CPU_ESHEL_LTE,	// A0
	CPU_FAMILY_UNKN
}CPU_family;

CPU_family GetCpuFamily(void);
// Returns the original CPSR
// exp: old_level = disableInterrupts();
// old_level MUST be local automatic variable !!
unsigned long disableInterrupts(void);

// Returns the original CPSR
// exp: old_level = enableInterrupts();
// old_level MUST be local automatic variable !!
unsigned long enableInterrupts(void);

// Restores the IF bits in the CPSR to that value of the CPSR passed.
// The latter should be obtained from the enable/disable functions
// exp: restoreInterrupts(old_level);
// old_level MUST be local automatic variable !!
void restoreInterrupts(unsigned long ir);

// Count Leading Zeros
// Returns: 0 for 0x8xxxxxxx; 1 for 0x04xxxxxx; 31 for 0x00000001; 32 for 0
int _clz(unsigned long x);

// revert
// Returns: reverted endian value.change the order of bytes in the 32bit parameter from big to little endian and vice versa.
unsigned long _rev(unsigned long x);

// CP14 functions
void _xsFreqChange(void);

// Enter idle mode
void _xsGoIdle(void); //just idle the Xscale core
void setIdle(void);   //same as previous
void setIdleExt(UINT32 newXPCR, UINT32 oldXPCR); //idle the core with shutting down the MEMC and modifying PMU.XPCR

//
// General: soft-restart the image
//
void doRestart(void);

// Function analog of ASSERT
void fatalError(int condition);

void asm_isb(void);
void asm_dsb(void);

// enable performance count
void enable_performance_count(void);

// Get performance count
UINT32 get_performance_count(void);

// Set performance count
void set_performance_count(UINT32 value);


// Assert macros





extern void utilsAssertFail(const char      *cond,
                            const char      *file,
                            signed short    line,
                            unsigned char   allowDiag);




//regular ASSERTs
# 141 "L:/PLT/hal/core/inc/utils.h"

# 150 "L:/PLT/hal/core/inc/utils.h"

# 158 "L:/PLT/hal/core/inc/utils.h"













//
// CP14: Performance monitoring unit access
//

// Read/Set PMNC (Control Register, see Elkart core EAS chapter 8)
// Here are the bit definitions:
# 184 "L:/PLT/hal/core/inc/utils.h"



void   cp14SetPMNC(UINT32 value);
UINT32 cp14ReadPMNC(void);

// Read the Clock Counter register (core clock or same/64 depending on the PMNC_CCNT_DIV64 bit - below)
UINT32 cp_ReadCCNT(void);  // NEW generic name. OLD & NEW are aliased
UINT32 cp14ReadCCNT(void); // OLD non-generic name, to be obsolete.
UINT32 cp14SetCCNT(UINT32 value);
UINT32  cp14ReadEVTSEL(void);
void  cp14SetEVTSEL(UINT32 value);

UINT32 getCpRateKHz(void); // returns CP-counter rate in kHz or 0-unknown/default. Depends upon Core frequency


//
// CP6: WS Primary INTC co-processor bus access
//

UINT32 cp6ReadICPR(void);
UINT32 cp6ReadICIP(void);
UINT32 cp6ReadICFP(void);
UINT32 cp6ReadICHP(void);
UINT32 cp6ReadICMR(void);
void cp6WriteICMR(UINT32 value);







//#if defined (_TAVOR_HARBELL_)|| defined(SILICON_PV2)

//ON-CHIP trace buffer is not supported on TAVOR A0 but on B0 only with JTAG protocol
// Let's put macro-stubs meanwhile



//#define ReadTBREG(x)                0
//#define ReadCHKPT1(x)               0
//#define ReadCHKPT0(x)               0
# 243 "L:/PLT/hal/core/inc/utils.h"

// CPSR mode
# 256 "L:/PLT/hal/core/inc/utils.h"


UINT32 ReadSP(void);
UINT32 Read_SPSR(void);
UINT32 ReadCPSR(void);
UINT32 ReadMode_R13(UINT32 mode);
UINT32 ReadMode_R14(UINT32 mode);

// Set SP for the CPU mode specified by CPSR
void SetMode_R13(UINT32 mode, UINT32 sp);

// Set SP and SL (v7) for the current CPU mode
void SetSystemStack(UINT32 sp, UINT32 limit);

// Reads the r0-r14,pc,cpsr values into the given buffer (see EE_RegInfo_Data_t)
void   ReadRegisterContext(UINT32* pBuffer);

// Restores r0-r13,pc,cpsr values from the given buffer (see EE_RegInfo_Data_t)
// LR is not restored!
// Jumps to pBuffer->PC
void   RestoreRegisterContext(UINT32* pBuffer);

// Restores r0-r12 values from the given buffer (see EE_RegInfo_Data_t)
// r13, LR, CPSR are not restored!
// Returns from exception mode and jumps to pBuffer->PC
void   RestoreRegisterContextEx(UINT32* pBuffer, UINT32 setExcModeSP);

//#if !defined (_TAVOR_HARBELL_) && !defined(SILICON_PV2) /* XSCALE only */
# 306 "L:/PLT/hal/core/inc/utils.h"

void	doTurboFrequencyChange(UINT32 fBit,UINT32 tBit);
UINT32	GetTurboFrequencyChangeCfgBits(void *pRegAddress);
UINT32	RunOperationUnderSpecificStack_ASM(void *pFuncAddress,void *pStackAddress, UINT32 funcParam1);



# 251 "L:/PLT/pcac/msl_utils/inc/msl_trace.h"
# 1 "L:/PLT/csw/BSP/inc/asserts.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/




# 26 "L:/PLT/csw/BSP/inc/asserts.h"
//#define ASSERT(x) while(!(x));
//#define ASSERT_NO_DIAG ASSERT
//#define ASSERT_LOW_LEVEL(cond,level) ASSERT(cond)
# 252 "L:/PLT/pcac/msl_utils/inc/msl_trace.h"
    /* Nucleus - Using ICAT and Intel PrePassing */


    /*#define MSL_ASSERT(cond)             {                                                                                                     if (cond == 0)                                                                                    {                                                                                                     diagPrintf("Assert line %d", __LINE__);                                                           diagPrintf("Assert file %s", __FILE__);                                                           OSATaskSleep(500);                                                                                ASSERT(0);                                                                    








                    while (1);                                                                                    }                                                                                             }*/


//#else
    // ASSERT is NOT defined
    //#error "MSL - ASSERT not defined"





# 37 "L:/PLT/pcac/pca_components/inc/pcac_gm_trace.h"
# 38 "L:/PLT/pcac/pca_components/inc/pcac_gm_trace.h"


enum _PcacTraceLevel
{
  PCAC_TRACE_ERROR = 0x01,
  PCAC_TRACE_API  = 0x02,
  PCAC_TRACE_INFO = 0x04,
  PCAC_TRACE_DEBUG = 0x08,
  PCAC_TRACE_MISC = 0x10
};
typedef UINT32 PCAC_TRACE_LEVEL;

/*************************/
/* Trace Macros          */
/*************************/








  //#define PCAC_ASSERT(cond)

# 156 "L:/PLT/pcac/pca_components/inc/pcac_gm_trace.h"


/* define the PCAC_ASSERT as defined in msl_trace which define the basis for all */












# 35 "L:/PLT/pcac/pca_components/src/pcac_gm_api.c"

/************************************************************************
 * Local Definitions
 ***********************************************************************/


/************************************************************************
 * External Variables
 ***********************************************************************/

/************************************************************************
 * Resource Declarations
 ***********************************************************************/
static PCAC_STATUS pcacStatus[PCAC_MAX] = {0,0,0,0,  0,0,0,0, 0}; // Curent value of PCAC MAC = 9	

extern void  OSATaskSleep( UINT32 ticks );


/***********************************************************************
 *
 * Name:        pcacSetStatus()
 *
 * Description: Indicates state of the component
 *
 * Parameters:  
 *   PCAC_COMPONENT  component
 *	 PCAC_STATE      state 
 *
 * Returns:     nothing
 *
 * Notes:       
 *
 ***********************************************************************/
void pcacSetStatus(
	PCAC_COMPONENT component,    // Component being reported
	PCAC_STATUS status        // State of said component 
	)
{
	{ if (!(component < PCAC_MAX)) { utilsAssertFail("component < PCAC_MAX", "pcac_gm_api.c", 73, 1); } };

	if (component < PCAC_MAX)
		pcacStatus[component] |= status;   
}
/***********************************************************************
 *
 * Name:        pcacSetStatus()
 *
 * Description: Indicates state of the component
 *
 * Parameters:  
 *   PCAC_COMPONENT  component
 *	 PCAC_STATE      state 
 *
 * Returns:     nothing
 *
 * Notes:       
 *
 ***********************************************************************/
void pcacClrStatus(
	PCAC_COMPONENT component,    // Component being reported
	PCAC_STATUS status        // State of said component 
	)
{
	{ if (!(component < PCAC_MAX)) { utilsAssertFail("component < PCAC_MAX", "pcac_gm_api.c", 98, 1); } };

	if (component < PCAC_MAX)
		pcacStatus[component] &= (status ^ 0xFF);   
}

/***********************************************************************
 *
 * Name:        pcacGetStatus()
 *
 * Description: Gets state of the component
 *
 * Parameters:  
 *   PCAC_COMPONENT  component
 *
 * Returns:
 *	 PCAC_STATE      state 
 *
 * Notes:       
 *
 ***********************************************************************/
PCAC_STATUS pcacGetStatus(
	PCAC_COMPONENT component    // Component being reported
	)
{
	PCAC_STATUS status;
	{ if (!(component < PCAC_MAX)) { utilsAssertFail("component < PCAC_MAX", "pcac_gm_api.c", 124, 1); } };

	if (component < PCAC_MAX)
		status = pcacStatus[component];   
	else
		status = 0xFF;

	return status;
}


/***********************************************************************
 *
 * Name:        pcacGetInitStatus()
 *
 * Description: Gets init status of all the components
 *
 * Parameters:  None
 *
 * Returns:     nothing
 *
 * Notes:       
 *
 ***********************************************************************/
PCAC_INIT_STATUS pcacGetInitStatus(void)
{
	UINT8 component;
	PCAC_INIT_STATUS initStatus = 0;

	for(component=0; component < PCAC_MAX; component++)
		 if (0x1 & pcacStatus[component])
		 {
        	   initStatus |= (1 << component);
		 }

	return initStatus;
}


/***********************************************************************
 *
 * Name:        pcacWaitStatus()
 *
 * Description: Waits for a status of a component to be set.  
 *              Times out at regular frames and checks again.
 *              Effectively polling.
 *
 * Parameters:  None
 *
 * Returns:     nothing
 *
 * Notes:       
 *
 ***********************************************************************/
void pcacWaitStatus(
	PCAC_COMPONENT component,    // Component being reported
	PCAC_STATUS status        // State of said component 
	)
{
	while (( status & pcacGetStatus(component)) == 0 )
	{
		OSATaskSleep(50);
	}
}
