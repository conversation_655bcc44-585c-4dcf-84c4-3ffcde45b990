# 1 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.c"
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
/* stdlib.h: ANSI draft (X3J11 May 88) library header, section 4.10 */
/* Copyright (C) Codemist Ltd., 1988-1993.                          */
/* Copyright 1991-1998,2014 ARM Limited. All rights reserved.       */
/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */
 
/*
 * stdlib.h declares four types, several general purpose functions,
 * and defines several macros.
 */






  /* armclang and non-strict armcc allow 'long long' in system headers */














  /*
   * Some of these declarations are new in C99.  To access them in C++
   * you can use -D__USE_C99_STDLIB (or -D__USE_C99ALL).
   */








# 54 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 70 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"






   /* unconditional in non-strict C for consistency of debug info */



    typedef unsigned short wchar_t; /* see <stddef.h> */
# 91 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"

typedef struct div_t { int quot, rem; } div_t;
   /* type of the value returned by the div function. */
typedef struct ldiv_t { long int quot, rem; } ldiv_t;
   /* type of the value returned by the ldiv function. */

typedef struct lldiv_t { long long quot, rem; } lldiv_t;
   /* type of the value returned by the lldiv function. */


# 112 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
   /*
    * an integral expression which may be used as an argument to the exit
    * function to return successful termination status to the host
    * environment.
    */

   /*
    * Defining __USE_ANSI_EXAMPLE_RAND at compile time switches to
    * the example implementation of rand() and srand() provided in
    * the ANSI C standard. This implementation is very poor, but is
    * provided for completeness.
    */
# 131 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
   /*
    * RAND_MAX: an integral constant expression, the value of which
    * is the maximum value returned by the rand function.
    */
extern __declspec(__nothrow) int __aeabi_MB_CUR_MAX(void);

   /*
    * a positive integer expression whose value is the maximum number of bytes
    * in a multibyte character for the extended character set specified by the
    * current locale (category LC_CTYPE), and whose value is never greater
    * than MB_LEN_MAX.
    */

   /*
    * If the compiler supports signalling nans as per N965 then it
    * will define __SUPPORT_SNAN__, in which case a user may define
    * _WANT_SNAN in order to obtain a compliant version of the strtod
    * family of functions.
    */




extern __declspec(__nothrow) double atof(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to double
    * representation.
    * Returns: the converted value.
    */
extern __declspec(__nothrow) int atoi(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to int
    * representation.
    * Returns: the converted value.
    */
extern __declspec(__nothrow) long int atol(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to long int
    * representation.
    * Returns: the converted value.
    */

extern __declspec(__nothrow) long long atoll(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to
    * long long int representation.
    * Returns: the converted value.
    */


extern __declspec(__nothrow) double strtod(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to double
    * representation. First it decomposes the input string into three parts:
    * an initial, possibly empty, sequence of white-space characters (as
    * specified by the isspace function), a subject sequence resembling a
    * floating point constant; and a final string of one or more unrecognised
    * characters, including the terminating null character of the input string.
    * Then it attempts to convert the subject sequence to a floating point
    * number, and returns the result. A pointer to the final string is stored
    * in the object pointed to by endptr, provided that endptr is not a null
    * pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned. If the correct value is outside the range of
    *          representable values, plus or minus HUGE_VAL is returned
    *          (according to the sign of the value), and the value of the macro
    *          ERANGE is stored in errno. If the correct value would cause
    *          underflow, zero is returned and the value of the macro ERANGE is
    *          stored in errno.
    */

extern __declspec(__nothrow) float strtof(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) long double strtold(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
   /*
    * same as strtod, but return float and long double respectively.
    */

extern __declspec(__nothrow) long int strtol(const char * __restrict /*nptr*/,
                        char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to long int
    * representation. First it decomposes the input string into three parts:
    * an initial, possibly empty, sequence of white-space characters (as
    * specified by the isspace function), a subject sequence resembling an
    * integer represented in some radix determined by the value of base, and a
    * final string of one or more unrecognised characters, including the
    * terminating null character of the input string. Then it attempts to
    * convert the subject sequence to an integer, and returns the result.
    * If the value of base is 0, the expected form of the subject sequence is
    * that of an integer constant (described in ANSI Draft, section 3.1.3.2),
    * optionally preceded by a '+' or '-' sign, but not including an integer
    * suffix. If the value of base is between 2 and 36, the expected form of
    * the subject sequence is a sequence of letters and digits representing an
    * integer with the radix specified by base, optionally preceded by a plus
    * or minus sign, but not including an integer suffix. The letters from a
    * (or A) through z (or Z) are ascribed the values 10 to 35; only letters
    * whose ascribed values are less than that of the base are permitted. If
    * the value of base is 16, the characters 0x or 0X may optionally precede
    * the sequence of letters and digits following the sign if present.
    * A pointer to the final string is stored in the object
    * pointed to by endptr, provided that endptr is not a null pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned and nptr is stored in *endptr.
    *          If the correct value is outside the range of
    *          representable values, LONG_MAX or LONG_MIN is returned
    *          (according to the sign of the value), and the value of the
    *          macro ERANGE is stored in errno.
    */
extern __declspec(__nothrow) unsigned long int strtoul(const char * __restrict /*nptr*/,
                                       char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to unsigned
    * long int representation. First it decomposes the input string into three
    * parts: an initial, possibly empty, sequence of white-space characters (as
    * determined by the isspace function), a subject sequence resembling an
    * unsigned integer represented in some radix determined by the value of
    * base, and a final string of one or more unrecognised characters,
    * including the terminating null character of the input string. Then it
    * attempts to convert the subject sequence to an unsigned integer, and
    * returns the result. If the value of base is zero, the expected form of
    * the subject sequence is that of an integer constant (described in ANSI
    * Draft, section 3.1.3.2), optionally preceded by a '+' or '-' sign, but
    * not including an integer suffix. If the value of base is between 2 and
    * 36, the expected form of the subject sequence is a sequence of letters
    * and digits representing an integer with the radix specified by base,
    * optionally preceded by a '+' or '-' sign, but not including an integer
    * suffix. The letters from a (or A) through z (or Z) stand for the values
    * 10 to 35; only letters whose ascribed values are less than that of the
    * base are permitted. If the value of base is 16, the characters 0x or 0X
    * may optionally precede the sequence of letters and digits following the
    * sign, if present. A pointer to the final string is stored in the object
    * pointed to by endptr, provided that endptr is not a null pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned and nptr is stored in *endptr.
    *          If the correct value is outside the range of
    *          representable values, ULONG_MAX is returned, and the value of
    *          the macro ERANGE is stored in errno.
    */

/* C90 reserves all names beginning with 'str' */
extern __declspec(__nothrow) long long strtoll(const char * __restrict /*nptr*/,
                                  char ** __restrict /*endptr*/, int /*base*/)
                          __attribute__((__nonnull__(1)));
   /*
    * as strtol but returns a long long int value.  If the correct value is
    * outside the range of representable values,  LLONG_MAX or LLONG_MIN is
    * returned (according to the sign of the value), and the value of the
    * macro ERANGE is stored in errno.
    */
extern __declspec(__nothrow) unsigned long long strtoull(const char * __restrict /*nptr*/,
                                            char ** __restrict /*endptr*/, int /*base*/)
                                   __attribute__((__nonnull__(1)));
   /*
    * as strtoul but returns an unsigned long long int value.  If the correct
    * value is outside the range of representable values, ULLONG_MAX is returned,
    * and the value of the macro ERANGE is stored in errno.
    */

extern __declspec(__nothrow) int rand(void);
   /*
    * Computes a sequence of pseudo-random integers in the range 0 to RAND_MAX.
    * Uses an additive generator (Mitchell & Moore) of the form:
    *   Xn = (X[n-24] + X[n-55]) MOD 2^31
    * This is described in section 3.2.2 of Knuth, vol 2. It's period is
    * in excess of 2^55 and its randomness properties, though unproven, are
    * conjectured to be good. Empirical testing since 1958 has shown no flaws.
    * Returns: a pseudo-random integer.
    */
extern __declspec(__nothrow) void srand(unsigned int /*seed*/);
   /*
    * uses its argument as a seed for a new sequence of pseudo-random numbers
    * to be returned by subsequent calls to rand. If srand is then called with
    * the same seed value, the sequence of pseudo-random numbers is repeated.
    * If rand is called before any calls to srand have been made, the same
    * sequence is generated as when srand is first called with a seed value
    * of 1.
    */

struct _rand_state { int __x[57]; };
extern __declspec(__nothrow) int _rand_r(struct _rand_state *);
extern __declspec(__nothrow) void _srand_r(struct _rand_state *, unsigned int);
struct _ANSI_rand_state { int __x[1]; };
extern __declspec(__nothrow) int _ANSI_rand_r(struct _ANSI_rand_state *);
extern __declspec(__nothrow) void _ANSI_srand_r(struct _ANSI_rand_state *, unsigned int);
   /*
    * Re-entrant variants of both flavours of rand, which operate on
    * an explicitly supplied state buffer.
    */

extern __declspec(__nothrow) void *calloc(size_t /*nmemb*/, size_t /*size*/);
   /*
    * allocates space for an array of nmemb objects, each of whose size is
    * 'size'. The space is initialised to all bits zero.
    * Returns: either a null pointer or a pointer to the allocated space.
    */
extern __declspec(__nothrow) void free(void * /*ptr*/);
   /*
    * causes the space pointed to by ptr to be deallocated (i.e., made
    * available for further allocation). If ptr is a null pointer, no action
    * occurs. Otherwise, if ptr does not match a pointer earlier returned by
    * calloc, malloc or realloc or if the space has been deallocated by a call
    * to free or realloc, the behaviour is undefined.
    */
extern __declspec(__nothrow) void *malloc(size_t /*size*/);
   /*
    * allocates space for an object whose size is specified by 'size' and whose
    * value is indeterminate.
    * Returns: either a null pointer or a pointer to the allocated space.
    */
extern __declspec(__nothrow) void *realloc(void * /*ptr*/, size_t /*size*/);
   /*
    * changes the size of the object pointed to by ptr to the size specified by
    * size. The contents of the object shall be unchanged up to the lesser of
    * the new and old sizes. If the new size is larger, the value of the newly
    * allocated portion of the object is indeterminate. If ptr is a null
    * pointer, the realloc function behaves like a call to malloc for the
    * specified size. Otherwise, if ptr does not match a pointer earlier
    * returned by calloc, malloc or realloc, or if the space has been
    * deallocated by a call to free or realloc, the behaviour is undefined.
    * If the space cannot be allocated, the object pointed to by ptr is
    * unchanged. If size is zero and ptr is not a null pointer, the object it
    * points to is freed.
    * Returns: either a null pointer or a pointer to the possibly moved
    *          allocated space.
    */

extern __declspec(__nothrow) int posix_memalign(void ** /*ret*/, size_t /*alignment*/, size_t /*size*/);
   /*
    * allocates space for an object of size 'size', aligned to a
    * multiple of 'alignment' (which must be a power of two and at
    * least 4).
    *
    * On success, a pointer to the allocated object is stored in
    * *ret, and zero is returned. On failure, the return value is
    * either ENOMEM (allocation failed because no suitable piece of
    * memory was available) or EINVAL (the 'alignment' parameter was
    * invalid).
    */

typedef int (*__heapprt)(void *, char const *, ...);
extern __declspec(__nothrow) void __heapstats(int (* /*dprint*/)(void * /*param*/,
                                           char const * /*format*/, ...),
                        void * /*param*/) __attribute__((__nonnull__(1)));
   /*
    * reports current heap statistics (eg. number of free blocks in
    * the free-list). Output is as implementation-defined free-form
    * text, provided via the dprint function. `param' gives an
    * extra data word to pass to dprint. You can call
    * __heapstats(fprintf,stdout) by casting fprintf to the above
    * function type; the typedef `__heapprt' is provided for this
    * purpose.
    *
    * `dprint' will not be called while the heap is being examined,
    * so it can allocate memory itself without trouble.
    */
extern __declspec(__nothrow) int __heapvalid(int (* /*dprint*/)(void * /*param*/,
                                           char const * /*format*/, ...),
                       void * /*param*/, int /*verbose*/) __attribute__((__nonnull__(1)));
   /*
    * performs a consistency check on the heap. Errors are reported
    * through dprint, like __heapstats. If `verbose' is nonzero,
    * full diagnostic information on the heap state is printed out.
    *
    * This routine probably won't work if the heap isn't a
    * contiguous chunk (for example, if __user_heap_extend has been
    * overridden).
    *
    * `dprint' may be called while the heap is being examined or
    * even in an invalid state, so it must perform no memory
    * allocation. In particular, if `dprint' calls (or is) a stdio
    * function, the stream it outputs to must already have either
    * been written to or been setvbuf'ed, or else the system will
    * allocate buffer space for it on the first call to dprint.
    */
extern __declspec(__nothrow) __declspec(__noreturn) void abort(void);
   /*
    * causes abnormal program termination to occur, unless the signal SIGABRT
    * is being caught and the signal handler does not return. Whether open
    * output streams are flushed or open streams are closed or temporary
    * files removed is implementation-defined.
    * An implementation-defined form of the status 'unsuccessful termination'
    * is returned to the host environment by means of a call to
    * raise(SIGABRT).
    */

extern __declspec(__nothrow) int atexit(void (* /*func*/)(void)) __attribute__((__nonnull__(1)));
   /*
    * registers the function pointed to by func, to be called without its
    * arguments at normal program termination. It is possible to register at
    * least 32 functions.
    * Returns: zero if the registration succeeds, nonzero if it fails.
    */
# 436 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


extern __declspec(__nothrow) __declspec(__noreturn) void exit(int /*status*/);
   /*
    * causes normal program termination to occur. If more than one call to the
    * exit function is executed by a program, the behaviour is undefined.
    * First, all functions registered by the atexit function are called, in the
    * reverse order of their registration.
    * Next, all open output streams are flushed, all open streams are closed,
    * and all files created by the tmpfile function are removed.
    * Finally, control is returned to the host environment. If the value of
    * status is zero or EXIT_SUCCESS, an implementation-defined form of the
    * status 'successful termination' is returned. If the value of status is
    * EXIT_FAILURE, an implementation-defined form of the status
    * 'unsuccessful termination' is returned. Otherwise the status returned
    * is implementation-defined.
    */

extern __declspec(__nothrow) __declspec(__noreturn) void _Exit(int /*status*/);
   /*
    * causes normal program termination to occur. No functions registered
    * by the atexit function are called.
    * In this implementation, all open output streams are flushed, all
    * open streams are closed, and all files created by the tmpfile function
    * are removed.
    * Control is returned to the host environment. The status returned to
    * the host environment is determined in the same way as for 'exit'.
    */     

extern __declspec(__nothrow) char *getenv(const char * /*name*/) __attribute__((__nonnull__(1)));
   /*
    * searches the environment list, provided by the host environment, for a
    * string that matches the string pointed to by name. The set of environment
    * names and the method for altering the environment list are
    * implementation-defined.
    * Returns: a pointer to a string associated with the matched list member.
    *          The array pointed to shall not be modified by the program, but
    *          may be overwritten by a subsequent call to the getenv function.
    *          If the specified name cannot be found, a null pointer is
    *          returned.
    */

extern __declspec(__nothrow) int  system(const char * /*string*/);
   /*
    * passes the string pointed to by string to the host environment to be
    * executed by a command processor in an implementation-defined manner.
    * A null pointer may be used for string, to inquire whether a command
    * processor exists.
    *
    * Returns: If the argument is a null pointer, the system function returns
    *          non-zero only if a command processor is available. If the
    *          argument is not a null pointer, the system function returns an
    *          implementation-defined value.
    */

extern  void *bsearch(const void * /*key*/, const void * /*base*/,
              size_t /*nmemb*/, size_t /*size*/,
              int (* /*compar*/)(const void *, const void *)) __attribute__((__nonnull__(1,2,5)));
   /*
    * searches an array of nmemb objects, the initial member of which is
    * pointed to by base, for a member that matches the object pointed to by
    * key. The size of each member of the array is specified by size.
    * The contents of the array shall be in ascending sorted order according to
    * a comparison function pointed to by compar, which is called with two
    * arguments that point to the key object and to an array member, in that
    * order. The function shall return an integer less than, equal to, or
    * greater than zero if the key object is considered, respectively, to be
    * less than, to match, or to be greater than the array member.
    * Returns: a pointer to a matching member of the array, or a null pointer
    *          if no match is found. If two members compare as equal, which
    *          member is matched is unspecified.
    */
# 524 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


extern  void qsort(void * /*base*/, size_t /*nmemb*/, size_t /*size*/,
           int (* /*compar*/)(const void *, const void *)) __attribute__((__nonnull__(1,4)));
   /*
    * sorts an array of nmemb objects, the initial member of which is pointed
    * to by base. The size of each object is specified by size.
    * The contents of the array shall be in ascending order according to a
    * comparison function pointed to by compar, which is called with two
    * arguments that point to the objects being compared. The function shall
    * return an integer less than, equal to, or greater than zero if the first
    * argument is considered to be respectively less than, equal to, or greater
    * than the second. If two members compare as equal, their order in the
    * sorted array is unspecified.
    */

# 553 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"

extern __declspec(__nothrow) __attribute__((const)) int abs(int /*j*/);
   /*
    * computes the absolute value of an integer j. If the result cannot be
    * represented, the behaviour is undefined.
    * Returns: the absolute value.
    */

extern __declspec(__nothrow) __attribute__((const)) div_t div(int /*numer*/, int /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the resulting
    * quotient is the integer of lesser magnitude that is the nearest to the
    * algebraic quotient. If the result cannot be represented, the behaviour is
    * undefined; otherwise, quot * denom + rem shall equal numer.
    * Returns: a structure of type div_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          int quot; int rem;
    */
extern __declspec(__nothrow) __attribute__((const)) long int labs(long int /*j*/);
   /*
    * computes the absolute value of an long integer j. If the result cannot be
    * represented, the behaviour is undefined.
    * Returns: the absolute value.
    */




extern __declspec(__nothrow) __attribute__((const)) ldiv_t ldiv(long int /*numer*/, long int /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the sign of
    * the resulting quotient is that of the algebraic quotient, and the
    * magnitude of the resulting quotient is the largest integer less than the
    * magnitude of the algebraic quotient. If the result cannot be represented,
    * the behaviour is undefined; otherwise, quot * denom + rem shall equal
    * numer.
    * Returns: a structure of type ldiv_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          long int quot; long int rem;
    */







extern __declspec(__nothrow) __attribute__((const)) long long llabs(long long /*j*/);
   /*
    * computes the absolute value of a long long integer j. If the
    * result cannot be represented, the behaviour is undefined.
    * Returns: the absolute value.
    */




extern __declspec(__nothrow) __attribute__((const)) lldiv_t lldiv(long long /*numer*/, long long /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the sign of
    * the resulting quotient is that of the algebraic quotient, and the
    * magnitude of the resulting quotient is the largest integer less than the
    * magnitude of the algebraic quotient. If the result cannot be represented,
    * the behaviour is undefined; otherwise, quot * denom + rem shall equal
    * numer.
    * Returns: a structure of type lldiv_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          long long quot; long long rem;
    */
# 634 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


/*
 * ARM real-time divide functions for guaranteed performance
 */
typedef struct __sdiv32by16 { int quot, rem; } __sdiv32by16;
typedef struct __udiv32by16 { unsigned int quot, rem; } __udiv32by16;
   /* used int so that values return in separate regs, although 16-bit */
typedef struct __sdiv64by32 { int rem, quot; } __sdiv64by32;

__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __sdiv32by16 __rt_sdiv32by16(
     int /*numer*/,
     short int /*denom*/);
   /*
    * Signed divide: (16-bit quot), (16-bit rem) = (32-bit) / (16-bit)
    */
__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __udiv32by16 __rt_udiv32by16(
     unsigned int /*numer*/,
     unsigned short /*denom*/);
   /*
    * Unsigned divide: (16-bit quot), (16-bit rem) = (32-bit) / (16-bit)
    */
__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __sdiv64by32 __rt_sdiv64by32(
     int /*numer_h*/, unsigned int /*numer_l*/,
     int /*denom*/);
   /*
    * Signed divide: (32-bit quot), (32-bit rem) = (64-bit) / (32-bit)
    */


/*
 * ARM floating-point mask/status function (for both hardfp and softfp)
 */
extern __declspec(__nothrow) unsigned int __fp_status(unsigned int /*mask*/, unsigned int /*flags*/);
   /*
    * mask and flags are bit-fields which correspond directly to the
    * floating point status register in the FPE/FPA and fplib.  
    * __fp_status returns the current value of the status register,
    * and also sets the writable bits of the word
    * (the exception control and flag bytes) to:
    *
    *     new = (old & ~mask) ^ flags;
    */












/*
 * Multibyte Character Functions.
 * The behaviour of the multibyte character functions is affected by the
 * LC_CTYPE category of the current locale. For a state-dependent encoding,
 * each function is placed into its initial state by a call for which its
 * character pointer argument, s, is a null pointer. Subsequent calls with s
 * as other than a null pointer cause the internal state of the function to be
 * altered as necessary. A call with s as a null pointer causes these functions
 * to return a nonzero value if encodings have state dependency, and a zero
 * otherwise. After the LC_CTYPE category is changed, the shift state of these
 * functions is indeterminate.
 */
extern __declspec(__nothrow) int mblen(const char * /*s*/, size_t /*n*/);
   /*
    * If s is not a null pointer, the mblen function determines the number of
    * bytes compromising the multibyte character pointed to by s. Except that
    * the shift state of the mbtowc function is not affected, it is equivalent
    * to   mbtowc((wchar_t *)0, s, n);
    * Returns: If s is a null pointer, the mblen function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the mblen function either returns a 0 (if s points to a
    *          null character), or returns the number of bytes that compromise
    *          the multibyte character (if the next n of fewer bytes form a
    *          valid multibyte character), or returns -1 (they do not form a
    *          valid multibyte character).
    */
extern __declspec(__nothrow) int mbtowc(wchar_t * __restrict /*pwc*/,
                   const char * __restrict /*s*/, size_t /*n*/);
   /*
    * If s is not a null pointer, the mbtowc function determines the number of
    * bytes that compromise the multibyte character pointed to by s. It then
    * determines the code for value of type wchar_t that corresponds to that
    * multibyte character. (The value of the code corresponding to the null
    * character is zero). If the multibyte character is valid and pwc is not a
    * null pointer, the mbtowc function stores the code in the object pointed
    * to by pwc. At most n bytes of the array pointed to by s will be examined.
    * Returns: If s is a null pointer, the mbtowc function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the mbtowc function either returns a 0 (if s points to
    *          a null character), or returns the number of bytes that
    *          compromise the converted multibyte character (if the next n of
    *          fewer bytes form a valid multibyte character), or returns -1
    *          (they do not form a valid multibyte character).
    */
extern __declspec(__nothrow) int wctomb(char * /*s*/, wchar_t /*wchar*/);
   /*
    * determines the number of bytes need to represent the multibyte character
    * corresponding to the code whose value is wchar (including any change in
    * shift state). It stores the multibyte character representation in the
    * array object pointed to by s (if s is not a null pointer). At most
    * MB_CUR_MAX characters are stored. If the value of wchar is zero, the
    * wctomb function is left in the initial shift state).
    * Returns: If s is a null pointer, the wctomb function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the wctomb function returns a -1 if the value of wchar
    *          does not correspond to a valid multibyte character, or returns
    *          the number of bytes that compromise the multibyte character
    *          corresponding to the value of wchar.
    */

/*
 * Multibyte String Functions.
 * The behaviour of the multibyte string functions is affected by the LC_CTYPE
 * category of the current locale.
 */
extern __declspec(__nothrow) size_t mbstowcs(wchar_t * __restrict /*pwcs*/,
                      const char * __restrict /*s*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * converts a sequence of multibyte character that begins in the initial
    * shift state from the array pointed to by s into a sequence of
    * corresponding codes and stores not more than n codes into the array
    * pointed to by pwcs. No multibyte character that follow a null character
    * (which is converted into a code with value zero) will be examined or
    * converted. Each multibyte character is converted as if by a call to
    * mbtowc function, except that the shift state of the mbtowc function is
    * not affected. No more than n elements will be modified in the array
    * pointed to by pwcs. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: If an invalid multibyte character is encountered, the mbstowcs
    *          function returns (size_t)-1. Otherwise, the mbstowcs function
    *          returns the number of array elements modified, not including
    *          a terminating zero code, if any.
    */
extern __declspec(__nothrow) size_t wcstombs(char * __restrict /*s*/,
                      const wchar_t * __restrict /*pwcs*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * converts a sequence of codes that correspond to multibyte characters
    * from the array pointed to by pwcs into a sequence of multibyte
    * characters that begins in the initial shift state and stores these
    * multibyte characters into the array pointed to by s, stopping if a
    * multibyte character would exceed the limit of n total bytes or if a
    * null character is stored. Each code is converted as if by a call to the
    * wctomb function, except that the shift state of the wctomb function is
    * not affected. No more than n elements will be modified in the array
    * pointed to by s. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: If a code is encountered that does not correspond to a valid
    *          multibyte character, the wcstombs function returns (size_t)-1.
    *          Otherwise, the wcstombs function returns the number of bytes
    *          modified, not including a terminating null character, if any.
    */

extern __declspec(__nothrow) void __use_realtime_heap(void);
extern __declspec(__nothrow) void __use_realtime_division(void);
extern __declspec(__nothrow) void __use_two_region_memory(void);
extern __declspec(__nothrow) void __use_no_heap(void);
extern __declspec(__nothrow) void __use_no_heap_region(void);

extern __declspec(__nothrow) char const *__C_library_version_string(void);
extern __declspec(__nothrow) int __C_library_version_number(void);











# 892 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"





/* end of stdlib.h */
# 2 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.c"
# 1 "L:/PLT/os/osa/inc/osa.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : osa.h
Description : This file defines the OS wrapper API and definitions for external application use.

              The OSA API allows applications to be developed independent of
              the target microkernel/hardware environment. It provides the
              facility to add support for multiple independent operating
              systems from different vendors.

Copyright (c) 2001 Intel CCD. All Rights Reserved
=========================================================================== */



# 1 "L:/PLT/os/threadx/inc/tx_thread.h"
/**************************************************************************/ 
/*                                                                        */ 
/*            Copyright (c) 1996-2007 by Express Logic Inc.               */ 
/*                                                                        */ 
/*  This software is copyrighted by and is the sole property of Express   */ 
/*  Logic, Inc.  All rights, title, ownership, or other interests         */ 
/*  in the software remain the property of Express Logic, Inc.  This      */ 
/*  software may only be used in accordance with the corresponding        */ 
/*  license agreement.  Any unauthorized use, duplication, transmission,  */ 
/*  distribution, or disclosure of this software is expressly forbidden.  */ 
/*                                                                        */
/*  This Copyright notice may not be removed or modified without prior    */ 
/*  written consent of Express Logic, Inc.                                */ 
/*                                                                        */ 
/*  Express Logic, Inc. reserves the right to modify this software        */ 
/*  without notice.                                                       */ 
/*                                                                        */ 
/*  Express Logic, Inc.                     <EMAIL>         */
/*  11423 West Bernardo Court               http://www.expresslogic.com   */
/*  San Diego, CA  92127                                                  */
/*                                                                        */
/**************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */ 
/** ThreadX Component                                                     */
/**                                                                       */
/**   Thread                                                              */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/


/**************************************************************************/ 
/*                                                                        */ 
/*  COMPONENT DEFINITION                                   RELEASE        */ 
/*                                                                        */ 
/*    tx_thread.h                                         PORTABLE C      */ 
/*                                                           5.1          */ 
/*  AUTHOR                                                                */ 
/*                                                                        */ 
/*    William E. Lamie, Express Logic, Inc.                               */ 
/*                                                                        */ 
/*  DESCRIPTION                                                           */ 
/*                                                                        */ 
/*    This file defines the ThreadX thread control component, including   */ 
/*    data types and external references.  It is assumed that tx_api.h    */
/*    and tx_port.h have already been included.                           */
/*                                                                        */ 
/*  RELEASE HISTORY                                                       */ 
/*                                                                        */ 
/*    DATE              NAME                      DESCRIPTION             */ 
/*                                                                        */ 
/*  12-12-2005     William E. Lamie         Initial Version 5.0           */ 
/*  04-02-2007     William E. Lamie         Modified comment(s),          */ 
/*                                            replaced UL constant        */ 
/*                                            modifier with ULONG cast,   */ 
/*                                            added logic to use preset   */ 
/*                                            global C data, resulting    */ 
/*                                            in version 5.1              */ 
/*                                                                        */ 
/**************************************************************************/ 



# 1 "L:/PLT/os/threadx/inc/tx_api.h"
/***************************************************************************
* Portions Copyright (c) 2008-2011 Marvell International, Ltd.
*               All Rights Reserved
*
*               Marvell Confidential
* ==========================================================================
*/


/**************************************************************************/
/*                                                                        */
/*            Copyright (c) 1996-2007 by Express Logic Inc.               */
/*                                                                        */
/*  This software is copyrighted by and is the sole property of Express   */
/*  Logic, Inc.  All rights, title, ownership, or other interests         */
/*  in the software remain the property of Express Logic, Inc.  This      */
/*  software may only be used in accordance with the corresponding        */
/*  license agreement.  Any unauthorized use, duplication, transmission,  */
/*  distribution, or disclosure of this software is expressly forbidden.  */
/*                                                                        */
/*  This Copyright notice may not be removed or modified without prior    */
/*  written consent of Express Logic, Inc.                                */
/*                                                                        */
/*  Express Logic, Inc. reserves the right to modify this software        */
/*  without notice.                                                       */
/*                                                                        */
/*  Express Logic, Inc.                     <EMAIL>         */
/*  11423 West Bernardo Court               http://www.expresslogic.com   */
/*  San Diego, CA  92127                                                  */
/*                                                                        */
/**************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */
/** ThreadX Component                                                     */
/**                                                                       */
/**   Application Interface (API)                                         */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/


/**************************************************************************/
/*                                                                        */
/*  APPLICATION INTERFACE DEFINITION                       RELEASE        */
/*                                                                        */
/*    tx_api.h                                            PORTABLE C      */
/*                                                           5.1          */
/*  AUTHOR                                                                */
/*                                                                        */
/*    William E. Lamie, Express Logic, Inc.                               */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file defines the basic Application Interface (API) to the      */
/*    high-performance ThreadX real-time kernel.  All service prototypes  */
/*    and data structure definitions are defined in this file.            */
/*    Please note that basic data type definitions and other architecture-*/
/*    specific information is contained in the file tx_port.h.            */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*  12-12-2005     William E. Lamie         Initial Version 5.0           */
/*  04-02-2007     William E. Lamie         Modified comment(s), and      */
/*                                            replaced UL constant        */
/*                                            modifier with ULONG cast,   */
/*                                            resulting in version 5.1    */
/*                                                                        */
/**************************************************************************/






/* Determine if a C++ compiler is being used.  If so, ensure that standard
   C is used to process the API information.  */

# 89 "L:/PLT/os/threadx/inc/tx_api.h"


/* Include the port-specific data type file.  */

# 1 "L:/PLT/os/threadx/inc/tx_port.h"
/***********************************************************
* Portions (c) Copyright 2008-2011 Marvell International Ltd. 
*
*               Marvell Confidential
* ==========================================================
*/

/**************************************************************************/ 
/*                                                                        */ 
/*            Copyright (c) 1996-2006 by Express Logic Inc.               */ 
/*                                                                        */ 
/*  This software is copyrighted by and is the sole property of Express   */ 
/*  Logic, Inc.  All rights, title, ownership, or other interests         */ 
/*  in the software remain the property of Express Logic, Inc.  This      */ 
/*  software may only be used in accordance with the corresponding        */ 
/*  license agreement.  Any unauthorized use, duplication, transmission,  */ 
/*  distribution, or disclosure of this software is expressly forbidden.  */ 
/*                                                                        */
/*  This Copyright notice may not be removed or modified without prior    */ 
/*  written consent of Express Logic, Inc.                                */ 
/*                                                                        */ 
/*  Express Logic, Inc. reserves the right to modify this software        */ 
/*  without notice.                                                       */ 
/*                                                                        */ 
/*  Express Logic, Inc.                     <EMAIL>         */
/*  11423 West Bernardo Court               http://www.expresslogic.com   */
/*  San Diego, CA  92127                                                  */
/*                                                                        */
/**************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */ 
/** ThreadX Component                                                     */
/**                                                                       */
/**   Port Specific                                                       */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/


/**************************************************************************/ 
/*                                                                        */ 
/*  PORT SPECIFIC C INFORMATION                            RELEASE        */ 
/*                                                                        */ 
/*    tx_port.h                                            ARM9/GNU       */ 
/*                                                           5.0          */ 
/*                                                                        */
/*  AUTHOR                                                                */ 
/*                                                                        */ 
/*    William E. Lamie, Express Logic, Inc.                               */ 
/*                                                                        */ 
/*  DESCRIPTION                                                           */ 
/*                                                                        */ 
/*    This file contains data type definitions that make the ThreadX      */ 
/*    real-time kernel function identically on a variety of different     */ 
/*    processor architectures.  For example, the size or number of bits   */ 
/*    in an "int" data type vary between microprocessor architectures and */ 
/*    even C compilers for the same microprocessor.  ThreadX does not     */ 
/*    directly use native C data types.  Instead, ThreadX creates its     */ 
/*    own special types that can be mapped to actual data types by this   */ 
/*    file to guarantee consistency in the interface and functionality.   */ 
/*                                                                        */ 
/*  RELEASE HISTORY                                                       */ 
/*                                                                        */ 
/*    DATE              NAME                      DESCRIPTION             */ 
/*                                                                        */ 
/*  12-12-2005     William E. Lamie         Initial ARM9 GNU              */ 
/*                                            Support Version 5.0         */ 
/*                                                                        */ 
/**************************************************************************/ 







/* Determine if the optional ThreadX user define file should be used.  */

# 90 "L:/PLT/os/threadx/inc/tx_port.h"


/* Define compiler library include files.  */

//#include <stdlib.h>
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
/* string.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.11 */
/* Copyright (C) Codemist Ltd., 1988-1993.                        */
/* Copyright 1991-1993 ARM Limited. All rights reserved.          */
/* version 0.04 */

/*
 * RCS $Revision$
 * Checkin $Date$
 */

/*
 * string.h declares one type and several functions, and defines one macro
 * useful for manipulating character arrays and other objects treated as
 * character arrays. Various methods are used for determining the lengths of
 * the arrays, but in all cases a char * or void * argument points to the
 * initial (lowest addresses) character of the array. If an array is written
 * beyond the end of an object, the behaviour is undefined.
 */












# 38 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 54 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"




extern __declspec(__nothrow) void *memcpy(void * __restrict /*s1*/,
                    const void * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) void *memmove(void * /*s1*/,
                    const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. Copying takes place as if the n characters from the
    * object pointed to by s2 are first copied into a temporary array of n
    * characters that does not overlap the objects pointed to by s1 and s2,
    * and then the n characters from the temporary array are copied into the
    * object pointed to by s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strcpy(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string pointed to by s2 (including the terminating nul
    * character) into the array pointed to by s1. If copying takes place
    * between objects that overlap, the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncpy(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies not more than n characters (characters that follow a null
    * character are not copied) from the array pointed to by s2 into the array
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */

extern __declspec(__nothrow) char *strcat(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends a copy of the string pointed to by s2 (including the terminating
    * null character) to the end of the string pointed to by s1. The initial
    * character of s2 overwrites the null character at the end of s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncat(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends not more than n characters (a null character and characters that
    * follow it are not appended) from the array pointed to by s2 to the end of
    * the string pointed to by s1. The initial character of s2 overwrites the
    * null character at the end of s1. A terminating null character is always
    * appended to the result.
    * Returns: the value of s1.
    */

/*
 * The sign of a nonzero value returned by the comparison functions is
 * determined by the sign of the difference between the values of the first
 * pair of characters (both interpreted as unsigned char) that differ in the
 * objects being compared.
 */

extern __declspec(__nothrow) int memcmp(const void * /*s1*/, const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the first n characters of the object pointed to by s1 to the
    * first n characters of the object pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the object pointed to by s1 is greater than, equal to, or
    *          less than the object pointed to by s2.
    */
extern __declspec(__nothrow) int strcmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcasecmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2,
    * case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncasecmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2, case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcoll(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2, both
    * interpreted as appropriate to the LC_COLLATE category of the current
    * locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2 when both are interpreted
    *          as appropriate to the current locale.
    */

extern __declspec(__nothrow) size_t strxfrm(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * transforms the string pointed to by s2 and places the resulting string
    * into the array pointed to by s1. The transformation function is such that
    * if the strcmp function is applied to two transformed strings, it returns
    * a value greater than, equal to or less than zero, corresponding to the
    * result of the strcoll function applied to the same two original strings.
    * No more than n characters are placed into the resulting array pointed to
    * by s1, including the terminating null character. If n is zero, s1 is
    * permitted to be a null pointer. If copying takes place between objects
    * that overlap, the behaviour is undefined.
    * Returns: The length of the transformed string is returned (not including
    *          the terminating null character). If the value returned is n or
    *          more, the contents of the array pointed to by s1 are
    *          indeterminate.
    */


# 193 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) void *memchr(const void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an unsigned char) in the
    * initial n characters (each interpreted as unsigned char) of the object
    * pointed to by s.
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the object.
    */

# 209 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an char) in the string
    * pointed to by s (including the terminating null character).
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the string.
    */

extern __declspec(__nothrow) size_t strcspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters not from the string pointed to by
    * s2. The terminating null character is not considered part of s2.
    * Returns: the length of the segment.
    */

# 232 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strpbrk(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of any
    * character from the string pointed to by s2.
    * Returns: returns a pointer to the character, or a null pointer if no
    *          character form s2 occurs in s1.
    */

# 247 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strrchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the last occurence of c (converted to a char) in the string
    * pointed to by s. The terminating null character is considered part of
    * the string.
    * Returns: returns a pointer to the character, or a null pointer if c does
    *          not occur in the string.
    */

extern __declspec(__nothrow) size_t strspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters from the string pointed to by S2
    * Returns: the length of the segment.
    */

# 270 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strstr(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of the
    * sequence of characters (excluding the terminating null character) in the
    * string pointed to by s2.
    * Returns: a pointer to the located string, or a null pointer if the string
    *          is not found.
    */

extern __declspec(__nothrow) char *strtok(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) char *_strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

extern __declspec(__nothrow) char *strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

   /*
    * A sequence of calls to the strtok function breaks the string pointed to
    * by s1 into a sequence of tokens, each of which is delimited by a
    * character from the string pointed to by s2. The first call in the
    * sequence has s1 as its first argument, and is followed by calls with a
    * null pointer as their first argument. The separator string pointed to by
    * s2 may be different from call to call.
    * The first call in the sequence searches for the first character that is
    * not contained in the current separator string s2. If no such character
    * is found, then there are no tokens in s1 and the strtok function returns
    * a null pointer. If such a character is found, it is the start of the
    * first token.
    * The strtok function then searches from there for a character that is
    * contained in the current separator string. If no such character is found,
    * the current token extends to the end of the string pointed to by s1, and
    * subsequent searches for a token will fail. If such a character is found,
    * it is overwritten by a null character, which terminates the current
    * token. The strtok function saves a pointer to the following character,
    * from which the next search for a token will start.
    * Each subsequent call, with a null pointer as the value for the first
    * argument, starts searching from the saved pointer and behaves as
    * described above.
    * Returns: pointer to the first character of a token, or a null pointer if
    *          there is no token.
    *
    * strtok_r() is a common extension which works exactly like
    * strtok(), but instead of storing its state in a hidden
    * library variable, requires the user to pass in a pointer to a
    * char * variable which will be used instead. Any sequence of
    * calls to strtok_r() passing the same char ** pointer should
    * behave exactly like the corresponding sequence of calls to
    * strtok(). This means that strtok_r() can safely be used in
    * multi-threaded programs, and also that you can tokenise two
    * strings in parallel.
    */

extern __declspec(__nothrow) void *memset(void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));
   /*
    * copies the value of c (converted to an unsigned char) into each of the
    * first n charactes of the object pointed to by s.
    * Returns: the value of s.
    */
extern __declspec(__nothrow) char *strerror(int /*errnum*/);
   /*
    * maps the error number in errnum to an error message string.
    * Returns: a pointer to the string, the contents of which are
    *          implementation-defined. The array pointed to shall not be
    *          modified by the program, but may be overwritten by a
    *          subsequent call to the strerror function.
    */
extern __declspec(__nothrow) size_t strlen(const char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * computes the length of the string pointed to by s.
    * Returns: the number of characters that precede the terminating null
    *          character.
    */

extern __declspec(__nothrow) size_t strlcpy(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string src into the string dst, using no more than
    * len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src. Thus, the operation
    * succeeded without truncation if and only if ret < len;
    * otherwise, the value in ret tells you how big to make dst if
    * you decide to reallocate it. (That value does _not_ include
    * the NUL.)
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) size_t strlcat(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * concatenates the string src to the string dst, using no more
    * than len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src plus the original length
    * of dst. Thus, the operation succeeded without truncation if
    * and only if ret < len; otherwise, the value in ret tells you
    * how big to make dst if you decide to reallocate it. (That
    * value does _not_ include the NUL.)
    * 
    * If no NUL is encountered within the first len bytes of dst,
    * then the length of dst is considered to have been equal to
    * len for the purposes of the return value (as if there were a
    * NUL at dst[len]). Thus, the return value in this case is len
    * + strlen(src).
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) void _membitcpybl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpybb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
    /*
     * Copies or moves a piece of memory from one place to another,
     * with one-bit granularity. So you can start or finish a copy
     * part way through a byte, and you can copy between regions
     * with different alignment within a byte.
     * 
     * All these functions have the same prototype: two void *
     * pointers for destination and source, then two integers
     * giving the bit offset from those pointers, and finally the
     * number of bits to copy.
     * 
     * Just like memcpy and memmove, the "cpy" functions copy as
     * fast as they can in the assumption that the memory regions
     * do not overlap, while the "move" functions cope correctly
     * with overlap.
     *
     * Treating memory as a stream of individual bits requires
     * defining a convention about what order those bits are
     * considered to be arranged in. The above functions support
     * multiple conventions:
     * 
     *  - the "bl" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in little-endian fashion, so that the LSB comes
     *    first. (For example, membitcpybl(a,b,0,7,1) would copy
     *    the MSB of the byte at b to the LSB of the byte at a.)
     * 
     *  - the "bb" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in big-endian fashion, so that the MSB comes
     *    first.
     * 
     *  - the "hl" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in little-endian fashion.
     * 
     *  - the "hb" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in big-endian fashion.
     * 
     *  - the "wl" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in little-endian fashion.
     * 
     *  - the "wb" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in big-endian fashion.
     */







# 502 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"



/* end of string.h */

# 96 "L:/PLT/os/threadx/inc/tx_port.h"


/* Define ThreadX basic types for this port.  */ 



typedef char                        CHAR;


typedef unsigned char               UCHAR;

typedef int                                     INT;

typedef unsigned int                UINT;

typedef long                                    LONG;
typedef unsigned long                           ULONG;
typedef short                                   SHORT;
typedef unsigned short                          USHORT;


/* Define the priority levels for ThreadX.  Legal values range
   from 32 to 1024 and MUST be evenly divisible by 32.  */






/* Define the minimum stack for a ThreadX thread on this processor. If the size supplied during
   thread creation is less than this value, the thread create call will return an error.  */






/* Define the system timer thread's default stack size and priority.  These are only applicable
   if TX_TIMER_PROCESS_IN_ISR is not defined.  */










/* Define various constants for the ThreadX ARM port.  */ 

# 153 "L:/PLT/os/threadx/inc/tx_port.h"


/* Define the clock source for trace event entry time stamp. The following two item are port specific.  
   For example, if the time source is at the address 0x0a800024 and is 16-bits in size, the clock 
   source constants would be:

#define TX_TRACE_TIME_SOURCE                    *((ULONG *) 0x0a800024)
#define TX_TRACE_TIME_MASK                      0x0000FFFFUL

*/

# 170 "L:/PLT/os/threadx/inc/tx_port.h"


/* Define the port specific options for the _tx_build_options variable. This variable indicates
   how the ThreadX library was built.  */






















/* Define the in-line initialization constant so that modules with in-line
   initialization capabilities can prevent their initialization from being
   a function call.  */




/* Determine whether or not stack checking is enabled. By default, ThreadX stack checking is 
   disabled. When the following is defined, ThreadX thread stack checking is enabled.  If stack
   checking is enabled (TX_ENABLE_STACK_CHECKING is defined), the TX_DISABLE_STACK_FILLING
   define is negated, thereby forcing the stack fill which is necessary for the stack checking
   logic.  */






/* Define the TX_THREAD control block extensions for this port. The main reason
   for the multiple macros is so that backward compatibility can be maintained with 
   existing ThreadX kernel awareness modules.  */







/* Define the port extensions of the remaining ThreadX objects.  */

# 233 "L:/PLT/os/threadx/inc/tx_port.h"


/* Define the user extension field of the thread control block.  Nothing 
   additional is needed for this port so it is defined as white space.  */








/* Define the macros for processing extensions in tx_thread_create, tx_thread_delete,
   tx_thread_shell_entry, and tx_thread_terminate.  */








/* Define the ThreadX object creation extensions for the remaining objects.  */

# 264 "L:/PLT/os/threadx/inc/tx_port.h"


/* Define the ThreadX object deletion extensions for the remaining objects.  */

# 275 "L:/PLT/os/threadx/inc/tx_port.h"


/* Define ThreadX interrupt lockout and restore macros for protection on 
   access of critical kernel information.  The restore interrupt macro must 
   restore the interrupt posture of the running thread prior to the value 
   present prior to the disable macro.  In most cases, the save area macro
   is used to define a local function save area for the disable and restore
   macros.  */
   



/*Note: 
	Compiler optimization may change the code sequency.
	That may cause some operation out of the protection.
	need add memory barrier to ensure code protection.
*/



# 302 "L:/PLT/os/threadx/inc/tx_port.h"










# 325 "L:/PLT/os/threadx/inc/tx_port.h"

/* Define the interrupt lockout macros for each ThreadX object.  */

# 334 "L:/PLT/os/threadx/inc/tx_port.h"


/* Define the version ID of ThreadX.  This may be utilized by the application.  */





extern  CHAR                    _tx_version_id[];






# 94 "L:/PLT/os/threadx/inc/tx_api.h"

/* Define basic constants for the ThreadX kernel.  */


/* API input parameters and general constants.  */

# 111 "L:/PLT/os/threadx/inc/tx_api.h"
//sccao add extended support for message queue




# 130 "L:/PLT/os/threadx/inc/tx_api.h"


/* Thread execution state values.  */

# 148 "L:/PLT/os/threadx/inc/tx_api.h"


/* API return values.  */



//#define TX_NO_MEMORY                    0x10
# 189 "L:/PLT/os/threadx/inc/tx_api.h"









/* Define the control block definitions for all system objects.  */


/* Define the basic timer management structures.  These are the structures
   used to manage thread sleep, timeout, and user timer requests.  */

/* Define the common internal timer control block.  */

typedef struct TX_TIMER_INTERNAL_STRUCT
{

    /* Define the remaining ticks and re-initialization tick values.  */
    ULONG               tx_timer_internal_remaining_ticks;
    ULONG               tx_timer_internal_re_initialize_ticks;

    /* Define the timeout function and timeout function parameter.  */
    void                (*tx_timer_internal_timeout_function)(ULONG);
    ULONG               tx_timer_internal_timeout_param;


    /* Define the next and previous internal link pointers for active
       internal timers.  */
    struct TX_TIMER_INTERNAL_STRUCT
                        *tx_timer_internal_active_next,
                        *tx_timer_internal_active_previous;

    /* Keep track of the pointer to the head of this list as well.  */
    struct TX_TIMER_INTERNAL_STRUCT
                        **tx_timer_internal_list_head;
} TX_TIMER_INTERNAL;


/* Define the timer structure utilized by the application.  */

typedef struct TX_TIMER_STRUCT
{

    /* Define the timer ID used for error checking.  */
    ULONG               tx_timer_id;

    /* Define the timer's name.  */
    CHAR                *tx_timer_name;

    /* Define the actual contents of the timer.  This is the block that
       is used in the actual timer expiration processing.  */
    TX_TIMER_INTERNAL   tx_timer_internal;

    /* Define the pointers for the created list.  */
    struct TX_TIMER_STRUCT
                        *tx_timer_created_next,
                        *tx_timer_created_previous;

# 267 "L:/PLT/os/threadx/inc/tx_api.h"

	UINT				tx_app_reserved;

} TX_TIMER;


/* ThreadX thread control block structure follows.  Additional fields
   can be added providing they are added after the information that is
   referenced in the port-specific assembly code.  */

typedef struct TX_THREAD_STRUCT
{
    /* The first section of the control block contains critical
       information that is referenced by the port-specific
       assembly language code.  Any changes in this section could
       necessitate changes in the assembly language.  */

    ULONG               tx_thread_id;                   /* Control block ID         */
    ULONG               tx_thread_run_count;            /* Thread's run counter     */
    void                *tx_thread_stack_ptr;           /* Thread's stack pointer   */
    void                *tx_thread_stack_start;         /* Stack starting address   */
    void                *tx_thread_stack_end;           /* Stack ending address     */
    ULONG               tx_thread_stack_size;           /* Stack size               */
    ULONG               tx_thread_time_slice;           /* Current time-slice       */
    ULONG               tx_thread_new_time_slice;       /* New time-slice           */

    /* Define pointers to the next and previous ready threads.  */
    struct TX_THREAD_STRUCT
                        *tx_thread_ready_next,
                        *tx_thread_ready_previous;

    /***************************************************************/

    /* Define the first port extension in the thread control block. This
       is typically defined to whitespace or a pointer type in tx_port.h.  */
    

    /* Nothing after this point is referenced by the target-specific
       assembly language.  Hence, information after this point can
       be added to the control block providing the complete system
       is recompiled.  */
    CHAR                *tx_thread_name;                /* Pointer to thread's name     */
    UINT                tx_thread_priority;             /* Priority of thread (0-1023)  */
    UINT                tx_thread_state;                /* Thread's execution state     */
    UINT                tx_thread_delayed_suspend;      /* Delayed suspend flag         */
    UINT                tx_thread_suspending;           /* Thread suspending flag       */
    UINT                tx_thread_preempt_threshold;    /* Preemption threshold         */
    void                *tx_thread_stack_highest_ptr;   /* Stack highest usage pointer  */

    /* Define the thread's entry point and input parameter.  */
    void                (*tx_thread_entry)(ULONG);
    ULONG               tx_thread_entry_parameter;

    /* Define the thread's timer block.   This is used for thread
       sleep and timeout requests.  */
    TX_TIMER_INTERNAL   tx_thread_timer;

    /* Define the thread's cleanup function and associated data.  This
       is used to cleanup various data structures when a thread
       suspension is lifted or terminated either by the user or
       a timeout.  */
    void                (*tx_thread_suspend_cleanup)(struct TX_THREAD_STRUCT *, UINT);
    void                *tx_thread_suspend_control_block;
    struct TX_THREAD_STRUCT
                        *tx_thread_suspended_next,
                        *tx_thread_suspended_previous;
    ULONG               tx_thread_suspend_info;
    void                *tx_thread_additional_suspend_info;
    UINT                tx_thread_suspend_option;
    UINT                tx_thread_suspend_status;

    /* Define the second port extension in the thread control block. This
       is typically defined to whitespace or a pointer type in tx_port.h.  */
    

    /* Define pointers to the next and previous threads in the
       created list.  */
    struct TX_THREAD_STRUCT
                        *tx_thread_created_next,
                        *tx_thread_created_previous;

    /* Define the third port extension in the thread control block. This
       is typically defined to whitespace in tx_port.h.  */
    

    /* Define a pointer type for FileX extensions.  */
    void                *tx_thread_filex_ptr;

    /* Define the priority inheritance variables. These will be used
       to manage priority inheritance changes applied to this thread
       as a result of mutex get operations.  */
    UINT                tx_thread_original_priority;
    UINT                tx_thread_original_preempt_threshold;
    ULONG               tx_thread_owned_mutex_count;
    struct TX_MUTEX_STRUCT
                        *tx_thread_owned_mutex_list;

# 400 "L:/PLT/os/threadx/inc/tx_api.h"



    /* Define the application callback routine used to notify the application when
       the thread is entered or exits.  */
    void                (*tx_thread_entry_exit_notify)(struct TX_THREAD_STRUCT *, UINT);


    /* Define the fourth port extension in the thread control block. This
       is typically defined to whitespace in tx_port.h.  */
    

    /* Define the user extension field.  This typically is defined
       to white space, but some ports of ThreadX may need to have
       additional fields in the thread control block.  This is
       defined in the file tx_port.h.  */
    ULONG tx_errno;

	 //TODO:added by PijingLiu, this must be carefully, should add to tail.
    /* Reserved words for the system and a single reserved word for the
       application.  */
    ULONG            tx_system_reserved_1;  /* System reserved word   */
    ULONG            tx_system_reserved_2;  /* System reserved word   */
    ULONG            tx_system_reserved_3;  /* System reserved word   */
    ULONG            tx_app_reserved_1;     /* Application reserved   */

} TX_THREAD;


/* Define the block memory pool structure utilized by the application.  */


typedef struct TX_BLOCK_POOL_STRUCT
{

    /* Define the block pool ID used for error checking.  */
    ULONG               tx_block_pool_id;

    /* Define the block pool's name.  */
    CHAR                *tx_block_pool_name;

    /* Define the number of available memory blocks in the pool.  */
    ULONG               tx_block_pool_available;

    /* Save the initial number of blocks.  */
    ULONG               tx_block_pool_total;

    /* Define the head pointer of the available block pool.  */
    UCHAR               *tx_block_pool_available_list;

    /* Save the start address of the block pool's memory area.  */
    UCHAR               *tx_block_pool_start;

    /* Save the block pool's size in bytes.  */
    ULONG               tx_block_pool_size;

    /* Save the individual memory block size - rounded for alignment.  */
    ULONG               tx_block_pool_block_size;

    /* Define the block pool suspension list head along with a count of
       how many threads are suspended.  */
    struct TX_THREAD_STRUCT
                        *tx_block_pool_suspension_list;
    ULONG               tx_block_pool_suspended_count;

    /* Define the created list next and previous pointers.  */
    struct TX_BLOCK_POOL_STRUCT
                        *tx_block_pool_created_next,
                        *tx_block_pool_created_previous;



    /* Define the number of block allocates.  */
    ULONG               tx_block_pool_performance_allocate_count;

    /* Define the number of block releases.  */
    ULONG               tx_block_pool_performance_release_count;

    /* Define the number of block pool suspensions.  */
    ULONG               tx_block_pool_performance_suspension_count;

    /* Define the number of block pool timeouts.  */
    ULONG               tx_block_pool_performance_timeout_count;


    /* Define the port extension in the block pool control block. This
       is typically defined to whitespace in tx_port.h.  */
    

	ULONG				tx_block_pool_reserved;

} TX_BLOCK_POOL;


/* Define the byte memory pool structure utilized by the application.  */

typedef struct TX_BYTE_POOL_STRUCT
{

    /* Define the byte pool ID used for error checking.  */
    ULONG               tx_byte_pool_id;

    /* Define the byte pool's name.  */
    CHAR                *tx_byte_pool_name;

    /* Define the number of available bytes in the pool.  */
    ULONG               tx_byte_pool_available;

    /* Define the number of fragments in the pool.  */
    ULONG               tx_byte_pool_fragments;

    /* Define the head pointer of byte pool.  */
    UCHAR               *tx_byte_pool_list;

    /* Define the search pointer used for initial searching for memory
       in a byte pool.  */
    UCHAR               *tx_byte_pool_search;

    /* Save the start address of the byte pool's memory area.  */
    UCHAR               *tx_byte_pool_start;

    /* Save the byte pool's size in bytes.  */
    ULONG               tx_byte_pool_size;

    /* This is used to mark the owner of the byte memory pool during
       a search.  If this value changes during the search, the local search
       pointer must be reset.  */
    struct TX_THREAD_STRUCT
                        *tx_byte_pool_owner;

    /* Define the byte pool suspension list head along with a count of
       how many threads are suspended.  */
    struct TX_THREAD_STRUCT
                        *tx_byte_pool_suspension_list;
    ULONG               tx_byte_pool_suspended_count;

    /* Define the created list next and previous pointers.  */
    struct TX_BYTE_POOL_STRUCT
                        *tx_byte_pool_created_next,
                        *tx_byte_pool_created_previous;

# 565 "L:/PLT/os/threadx/inc/tx_api.h"

    /* Define the port extension in the byte pool control block. This
       is typically defined to whitespace in tx_port.h.  */
    

} TX_BYTE_POOL;


/* Define the event flags group structure utilized by the application.  */

typedef struct TX_EVENT_FLAGS_GROUP_STRUCT
{

    /* Define the event flags group ID used for error checking.  */
    ULONG               tx_event_flags_group_id;

    /* Define the event flags group's name.  */
    CHAR                *tx_event_flags_group_name;

    /* Define the actual current event flags in this group. A zero in a
       particular bit indicates the event flag is not set.  */
    ULONG               tx_event_flags_group_current;

    /* Define the reset search flag that is set when an ISR sets flags during
       the search of the suspended threads list.  */
    UINT                tx_event_flags_group_reset_search;

    /* Define the event flags group suspension list head along with a count of
       how many threads are suspended.  */
    struct TX_THREAD_STRUCT
                        *tx_event_flags_group_suspension_list;
    ULONG               tx_event_flags_group_suspended_count;

    /* Define the created list next and previous pointers.  */
    struct TX_EVENT_FLAGS_GROUP_STRUCT
                        *tx_event_flags_group_created_next,
                        *tx_event_flags_group_created_previous;

    /* Define the delayed clearing event flags.  */
    ULONG               tx_event_flags_group_delayed_clear;

# 620 "L:/PLT/os/threadx/inc/tx_api.h"



    /* Define the application callback routine used to notify the application when
       an event flag is set.  */
    void                (*tx_event_flags_group_set_notify)(struct TX_EVENT_FLAGS_GROUP_STRUCT *);


    /* Define the port extension in the event flags group control block. This
       is typically defined to whitespace in tx_port.h.  */
    

	UINT				tx_app_reserved;

} TX_EVENT_FLAGS_GROUP;


/* Define the mutex structure utilized by the application.  */

typedef struct TX_MUTEX_STRUCT
{

    /* Define the mutex ID used for error checking.  */
    ULONG               tx_mutex_id;

    /* Define the mutex's name.  */
    CHAR                *tx_mutex_name;

    /* Define the mutex ownership count.  */
    ULONG               tx_mutex_ownership_count;

    /* Define the mutex ownership pointer.  This pointer points to the
       the thread that owns the mutex.  */
    TX_THREAD           *tx_mutex_owner;

    /* Define the priority inheritance flag.  If this flag is set, priority
       inheritance will be in effect.  */
    UINT                tx_mutex_inherit;

    /* Define the save area for the owning thread's original priority and
       threshold.  */
    UINT                tx_mutex_original_priority;
    UINT                tx_mutex_original_threshold;

    /* Define the mutex suspension list head along with a count of
       how many threads are suspended.  */
    struct TX_THREAD_STRUCT
                        *tx_mutex_suspension_list;
    ULONG               tx_mutex_suspended_count;

    /* Define the created list next and previous pointers.  */
    struct TX_MUTEX_STRUCT
                        *tx_mutex_created_next,
                        *tx_mutex_created_previous;

    /* Define the priority of the highest priority thread waiting for
       this mutex.  */
    ULONG               tx_mutex_highest_priority_waiting;

    /* Define the owned list next and previous pointers.  */
    struct TX_MUTEX_STRUCT
                        *tx_mutex_owned_next,
                        *tx_mutex_owned_previous;

# 704 "L:/PLT/os/threadx/inc/tx_api.h"

    /* Define the port extension in the mutex control block. This
       is typically defined to whitespace in tx_port.h.  */
    

} TX_MUTEX;


/* Define the queue structure utilized by the application.  */

typedef struct TX_QUEUE_STRUCT
{

    /* Define the queue ID used for error checking.  */
    ULONG               tx_queue_id;

    /* Define the queue's name.  */
    CHAR                *tx_queue_name;

    /* Define the message size that was specified in queue creation.  */
    UINT                tx_queue_message_size;

    /* Define the total number of messages in the queue.  */
    ULONG               tx_queue_capacity;

    /* Define the current number of messages enqueue and the available
       queue storage space.  */
    ULONG               tx_queue_enqueued;
    ULONG               tx_queue_available_storage;

    /* Define pointers that represent the start and end for the queue's
       message area.  */
    ULONG               *tx_queue_start;
    ULONG               *tx_queue_end;

    /* Define the queue read and write pointers.  Send requests use the write
       pointer while receive requests use the read pointer.  */
    ULONG               *tx_queue_read;
    ULONG               *tx_queue_write;

    /* Define the queue suspension list head along with a count of
       how many threads are suspended.  */
    struct TX_THREAD_STRUCT
                        *tx_queue_suspension_list;
    ULONG               tx_queue_suspended_count;

    /* Define the created list next and previous pointers.  */
    struct TX_QUEUE_STRUCT
                        *tx_queue_created_next,
                        *tx_queue_created_previous;

# 776 "L:/PLT/os/threadx/inc/tx_api.h"



    /* Define the application callback routine used to notify the application when
       the a message is sent to the queue.  */
    void                (*tx_queue_send_notify)(struct TX_QUEUE_STRUCT *);


    /* Define the port extension in the queue control block. This
       is typically defined to whitespace in tx_port.h.  */
    

} TX_QUEUE;


/* Define the semaphore structure utilized by the application.  */

typedef struct TX_SEMAPHORE_STRUCT
{

    /* Define the semaphore ID used for error checking.  */
    ULONG               tx_semaphore_id;

    /* Define the semaphore's name.  */
    CHAR                *tx_semaphore_name;

    /* Define the actual semaphore count.  A zero means that no semaphore
       instance is available.  */
    ULONG               tx_semaphore_count;

    /* Define the semaphore suspension list head along with a count of
       how many threads are suspended.  */
    struct TX_THREAD_STRUCT
                        *tx_semaphore_suspension_list;
    ULONG               tx_semaphore_suspended_count;

    /* Define the created list next and previous pointers.  */
    struct TX_SEMAPHORE_STRUCT
                        *tx_semaphore_created_next,
                        *tx_semaphore_created_previous;

# 831 "L:/PLT/os/threadx/inc/tx_api.h"



    /* Define the application callback routine used to notify the application when
       the a semaphore is put.  */
    void                (*tx_semaphore_put_notify)(struct TX_SEMAPHORE_STRUCT *);


    /* Define the port extension in the semaphore control block. This
       is typically defined to whitespace in tx_port.h.  */
    

} TX_SEMAPHORE;


/* Define the system API mappings based on the error checking
   selected by the user.  Note: this section is only applicable to
   application source code, hence the conditional that turns off this
   stuff when the include file is processed by the ThreadX source. */




/* Determine if error checking is desired.  If so, map API functions
   to the appropriate error checking front-ends.  Otherwise, map API
   functions to the core functions that actually perform the work.
   Note: error checking is enabled by default.  */

# 981 "L:/PLT/os/threadx/inc/tx_api.h"

/* Services with error checking.  */



# 994 "L:/PLT/os/threadx/inc/tx_api.h"

# 1003 "L:/PLT/os/threadx/inc/tx_api.h"

# 1012 "L:/PLT/os/threadx/inc/tx_api.h"

# 1019 "L:/PLT/os/threadx/inc/tx_api.h"

# 1028 "L:/PLT/os/threadx/inc/tx_api.h"

# 1040 "L:/PLT/os/threadx/inc/tx_api.h"

# 1051 "L:/PLT/os/threadx/inc/tx_api.h"

/* Re-map thread create/delete if running our stack scan code */
# 1060 "L:/PLT/os/threadx/inc/tx_api.h"

# 1077 "L:/PLT/os/threadx/inc/tx_api.h"

# 1088 "L:/PLT/os/threadx/inc/tx_api.h"















/* Define the ThreadX entry function that is typically called from the application's main() function.  */

void        _tx_initialize_kernel_enter(void);


/* Define the function prototypes of the ThreadX API.  */

UINT        _txe_block_allocate(TX_BLOCK_POOL *pool_ptr, void **block_ptr, ULONG wait_option);




UINT        _txe_block_pool_create(TX_BLOCK_POOL *pool_ptr, CHAR *name_ptr, ULONG block_size,
                    void *pool_start, ULONG pool_size, UINT pool_control_block_size);

UINT        _txe_block_pool_delete(TX_BLOCK_POOL *pool_ptr);
UINT        _txe_block_pool_info_get(TX_BLOCK_POOL *pool_ptr, CHAR **name, ULONG *available_blocks,
                    ULONG *total_blocks, TX_THREAD **first_suspended,
                    ULONG *suspended_count, TX_BLOCK_POOL **next_pool);
UINT        _tx_block_pool_performance_info_get(TX_BLOCK_POOL *pool_ptr, ULONG *allocates, ULONG *releases,
                    ULONG *suspensions, ULONG *timeouts);
UINT        _tx_block_pool_performance_system_info_get(ULONG *allocates, ULONG *releases,
                    ULONG *suspensions, ULONG *timeouts);
UINT        _txe_block_pool_prioritize(TX_BLOCK_POOL *pool_ptr);
UINT        _txe_block_release(void *block_ptr);

UINT        _txe_byte_allocate(TX_BYTE_POOL *pool_ptr, void **memory_ptr, ULONG memory_size,
                    ULONG wait_option);




UINT        _txe_byte_pool_create(TX_BYTE_POOL *pool_ptr, CHAR *name_ptr, void *pool_start,
                    ULONG pool_size, UINT pool_control_block_size);

UINT        _txe_byte_pool_delete(TX_BYTE_POOL *pool_ptr);
UINT        _txe_byte_pool_info_get(TX_BYTE_POOL *pool_ptr, CHAR **name, ULONG *available_bytes,
                    ULONG *fragments, TX_THREAD **first_suspended,
                    ULONG *suspended_count, TX_BYTE_POOL **next_pool);
UINT        _tx_byte_pool_performance_info_get(TX_BYTE_POOL *pool_ptr, ULONG *allocates, ULONG *releases,
                    ULONG *fragments_searched, ULONG *merges, ULONG *splits, ULONG *suspensions, ULONG *timeouts);
UINT        _tx_byte_pool_performance_system_info_get(ULONG *allocates, ULONG *releases,
                    ULONG *fragments_searched, ULONG *merges, ULONG *splits, ULONG *suspensions, ULONG *timeouts);
UINT        _txe_byte_pool_prioritize(TX_BYTE_POOL *pool_ptr);
UINT        _txe_byte_release(void *memory_ptr);




UINT        _txe_event_flags_create(TX_EVENT_FLAGS_GROUP *group_ptr, CHAR *name_ptr, UINT event_control_block_size);

UINT        _txe_event_flags_delete(TX_EVENT_FLAGS_GROUP *group_ptr);
UINT        _txe_event_flags_get(TX_EVENT_FLAGS_GROUP *group_ptr, ULONG requested_flags,
                    UINT get_option, ULONG *actual_flags_ptr, ULONG wait_option);
UINT        _txe_event_flags_info_get(TX_EVENT_FLAGS_GROUP *group_ptr, CHAR **name, ULONG *current_flags,
                    TX_THREAD **first_suspended, ULONG *suspended_count,
                    TX_EVENT_FLAGS_GROUP **next_group);
UINT        _tx_event_flags_performance_info_get(TX_EVENT_FLAGS_GROUP *group_ptr, ULONG *sets, ULONG *gets,
                    ULONG *suspensions, ULONG *timeouts);
UINT        _tx_event_flags_performance_system_info_get(ULONG *sets, ULONG *gets,
                    ULONG *suspensions, ULONG *timeouts);
UINT        _txe_event_flags_set(TX_EVENT_FLAGS_GROUP *group_ptr, ULONG flags_to_set,
                    UINT set_option);
UINT        _txe_event_flags_set_notify(TX_EVENT_FLAGS_GROUP *group_ptr, void (*events_set_notify)(TX_EVENT_FLAGS_GROUP *));

UINT        _tx_thread_interrupt_control(UINT new_posture);




UINT        _txe_mutex_create(TX_MUTEX *mutex_ptr, CHAR *name_ptr, UINT inherit, UINT mutex_control_block_size);

UINT        _txe_mutex_delete(TX_MUTEX *mutex_ptr);
UINT        _txe_mutex_get(TX_MUTEX *mutex_ptr, ULONG wait_option);
UINT        _txe_mutex_info_get(TX_MUTEX *mutex_ptr, CHAR **name, ULONG *count, TX_THREAD **owner,
                    TX_THREAD **first_suspended, ULONG *suspended_count,
                    TX_MUTEX **next_mutex);
UINT        _tx_mutex_performance_info_get(TX_MUTEX *mutex_ptr, ULONG *puts, ULONG *gets,
                    ULONG *suspensions, ULONG *timeouts, ULONG *inversions, ULONG *inheritances);
UINT        _tx_mutex_performance_system_info_get(ULONG *puts, ULONG *gets, ULONG *suspensions, ULONG *timeouts,
                    ULONG *inversions, ULONG *inheritances);
UINT        _txe_mutex_prioritize(TX_MUTEX *mutex_ptr);
UINT        _txe_mutex_put(TX_MUTEX *mutex_ptr);





UINT        _txe_queue_create(TX_QUEUE *queue_ptr, CHAR *name_ptr, UINT message_size,
                        void *queue_start, ULONG queue_size, UINT queue_control_block_size);

UINT        _txe_queue_delete(TX_QUEUE *queue_ptr);
UINT        _txe_queue_flush(TX_QUEUE *queue_ptr);
UINT        _txe_queue_info_get(TX_QUEUE *queue_ptr, CHAR **name, ULONG *enqueued, ULONG *available_storage,
                    TX_THREAD **first_suspended, ULONG *suspended_count, TX_QUEUE **next_queue);
UINT        _tx_queue_performance_info_get(TX_QUEUE *queue_ptr, ULONG *messages_sent, ULONG *messages_received,
                    ULONG *empty_suspensions, ULONG *full_suspensions, ULONG *full_errors, ULONG *timeouts);
UINT        _tx_queue_performance_system_info_get(ULONG *messages_sent, ULONG *messages_received,
                    ULONG *empty_suspensions, ULONG *full_suspensions, ULONG *full_errors, ULONG *timeouts);
UINT        _txe_queue_receive(TX_QUEUE *queue_ptr, void *destination_ptr, ULONG wait_option);
UINT        _txe_queue_send(TX_QUEUE *queue_ptr, void *source_ptr, ULONG wait_option);
UINT        _txe_queue_send_notify(TX_QUEUE *queue_ptr, void (*queue_send_notify)(TX_QUEUE *));
UINT        _txe_queue_front_send(TX_QUEUE *queue_ptr, void *source_ptr, ULONG wait_option);
UINT        _txe_queue_prioritize(TX_QUEUE *queue_ptr);

UINT        _txe_semaphore_ceiling_put(TX_SEMAPHORE *semaphore_ptr, ULONG ceiling);



UINT        _txe_semaphore_create(TX_SEMAPHORE *semaphore_ptr, CHAR *name_ptr, ULONG initial_count, UINT semaphore_control_block_size);

UINT        _txe_semaphore_delete(TX_SEMAPHORE *semaphore_ptr);
UINT        _txe_semaphore_get(TX_SEMAPHORE *semaphore_ptr, ULONG wait_option);
UINT        _txe_semaphore_info_get(TX_SEMAPHORE *semaphore_ptr, CHAR **name, ULONG *current_value,
                    TX_THREAD **first_suspended, ULONG *suspended_count,
                    TX_SEMAPHORE **next_semaphore);
UINT        _tx_semaphore_performance_info_get(TX_SEMAPHORE *semaphore_ptr, ULONG *puts, ULONG *gets,
                    ULONG *suspensions, ULONG *timeouts);
UINT        _tx_semaphore_performance_system_info_get(ULONG *puts, ULONG *gets,
                    ULONG *suspensions, ULONG *timeouts);
UINT        _txe_semaphore_prioritize(TX_SEMAPHORE *semaphore_ptr);
UINT        _txe_semaphore_put(TX_SEMAPHORE *semaphore_ptr);
UINT        _txe_semaphore_put_notify(TX_SEMAPHORE *semaphore_ptr, void (*semaphore_put_notify)(TX_SEMAPHORE *));

# 1241 "L:/PLT/os/threadx/inc/tx_api.h"
UINT        _txe_thread_create(TX_THREAD *thread_ptr, CHAR *name_ptr,
                    void (*entry_function)(ULONG), ULONG entry_input,
                    void *stack_start, ULONG stack_size,
                    UINT priority, UINT preempt_threshold,
                    ULONG time_slice, UINT auto_start, UINT thread_control_block_size);


UINT        _txe_thread_delete(TX_THREAD *thread_ptr);
UINT        _txe_thread_entry_exit_notify(TX_THREAD *thread_ptr, void (*thread_entry_exit_notify)(TX_THREAD *, UINT));
TX_THREAD  *_tx_thread_identify(void);
UINT        _txe_thread_info_get(TX_THREAD *thread_ptr, CHAR **name, UINT *state, ULONG *run_count,
                    UINT *priority, UINT *preemption_threshold, ULONG *time_slice,
                    TX_THREAD **next_thread, TX_THREAD **next_suspended_thread);
UINT        _tx_thread_performance_info_get(TX_THREAD *thread_ptr, ULONG *resumptions, ULONG *suspensions,
                    ULONG *solicited_preemptions, ULONG *interrupt_preemptions, ULONG *priority_inversions,
                    ULONG *time_slices, ULONG *relinquishes, ULONG *timeouts, ULONG *wait_aborts, TX_THREAD **last_preempted_by);
UINT        _tx_thread_performance_system_info_get(ULONG *resumptions, ULONG *suspensions,
                    ULONG *solicited_preemptions, ULONG *interrupt_preemptions, ULONG *priority_inversions,
                    ULONG *time_slices, ULONG *relinquishes, ULONG *timeouts, ULONG *wait_aborts,
                    ULONG *non_idle_returns, ULONG *idle_returns);
UINT        _txe_thread_preemption_change(TX_THREAD *thread_ptr, UINT new_threshold,
                    UINT *old_threshold);
UINT        _txe_thread_priority_change(TX_THREAD *thread_ptr, UINT new_priority,
                    UINT *old_priority);
void        _txe_thread_relinquish(void);
UINT        _txe_thread_reset(TX_THREAD *thread_ptr);
UINT        _txe_thread_resume(TX_THREAD *thread_ptr);
UINT        _tx_thread_sleep(ULONG timer_ticks);
UINT        _tx_thread_stack_error_notify(void (*stack_error_handler)(TX_THREAD *));
UINT        _txe_thread_suspend(TX_THREAD *thread_ptr);
UINT        _txe_thread_terminate(TX_THREAD *thread_ptr);
UINT        _txe_thread_time_slice_change(TX_THREAD *thread_ptr, ULONG new_time_slice, ULONG *old_time_slice);
UINT        _txe_thread_wait_abort(TX_THREAD *thread_ptr);

ULONG       _tx_time_get(void);
void        _tx_time_set(ULONG new_time);

UINT        _txe_timer_activate(TX_TIMER *timer_ptr);
UINT        _txe_timer_change(TX_TIMER *timer_ptr, ULONG initial_ticks, ULONG reschedule_ticks);





UINT        _txe_timer_create(TX_TIMER *timer_ptr, CHAR *name_ptr,
                    void (*expiration_function)(ULONG), ULONG expiration_input, ULONG initial_ticks,
                    ULONG reschedule_ticks, UINT auto_activate, UINT timer_control_block_size);

UINT        _txe_timer_deactivate(TX_TIMER *timer_ptr);
UINT        _txe_timer_delete(TX_TIMER *timer_ptr);
UINT        _txe_timer_info_get(TX_TIMER *timer_ptr, CHAR **name, UINT *active, ULONG *remaining_ticks,
                    ULONG *reschedule_ticks, TX_TIMER **next_timer);
UINT        _tx_timer_performance_info_get(TX_TIMER *timer_ptr, ULONG *activates, ULONG *reactivates,
                    ULONG *deactivates, ULONG *expirations, ULONG *expiration_adjusts);
UINT        _tx_timer_performance_system_info_get(ULONG *activates, ULONG *reactivates,
                    ULONG *deactivates, ULONG *expirations, ULONG *expiration_adjusts);

UINT        _tx_trace_enable(void *trace_buffer_start, ULONG trace_buffer_size, ULONG registry_entries);
UINT        _tx_trace_disable(void);
void        _tx_trace_isr_enter_insert(ULONG isr_id);
void        _tx_trace_isr_exit_insert(ULONG isr_id);
UINT        _tx_trace_user_event_insert(ULONG event_id, ULONG info_field_1, ULONG info_field_2, ULONG info_field_3, ULONG info_field_4);




/* Determine if a C++ compiler is being used.  If so, complete the standard
   C conditional started above.  */








# 72 "L:/PLT/os/threadx/inc/tx_thread.h"





/* Define thread control specific data definitions.  */








/* Define priority/preempt maps index constant if only 32 priority 
   levels are supported.  Otherwise, an index variable is needed
   and the priority must be modified.  */

# 97 "L:/PLT/os/threadx/inc/tx_thread.h"


/* Define thread control function prototypes.  */

void        _tx_thread_context_save(void);
void        _tx_thread_context_restore(void);
UINT        _tx_thread_create(TX_THREAD *thread_ptr, CHAR *name_ptr, 
                void (*entry_function)(ULONG), ULONG entry_input,
                void *stack_start, ULONG stack_size, 
                UINT priority, UINT preempt_threshold, 
                ULONG time_slice, UINT auto_start);
UINT        _tx_thread_delete(TX_THREAD *thread_ptr);
UINT        _tx_thread_entry_exit_notify(TX_THREAD *thread_ptr, void (*thread_entry_exit_notify)(TX_THREAD *, UINT));
TX_THREAD  *_tx_thread_identify(void);
UINT        _tx_thread_info_get(TX_THREAD *thread_ptr, CHAR **name, UINT *state, ULONG *run_count, 
                UINT *priority, UINT *preemption_threshold, ULONG *time_slice, 
                TX_THREAD **next_thread, TX_THREAD **next_suspended_thread);
void        _tx_thread_initialize(void);
UINT        _tx_thread_interrupt_control(UINT new_posture);
UINT        _tx_thread_performance_info_get(TX_THREAD *thread_ptr, ULONG *resumptions, ULONG *suspensions, 
                ULONG *solicited_preemptions, ULONG *interrupt_preemptions, ULONG *priority_inversions,
                ULONG *time_slices, ULONG *relinquishes, ULONG *timeouts, ULONG *wait_aborts, TX_THREAD **last_preempted_by);
UINT        _tx_thread_performance_system_info_get(ULONG *resumptions, ULONG *suspensions,
                ULONG *solicited_preemptions, ULONG *interrupt_preemptions, ULONG *priority_inversions,
                ULONG *time_slices, ULONG *relinquishes, ULONG *timeouts, ULONG *wait_aborts,
                ULONG *non_idle_returns, ULONG *idle_returns);
UINT        _tx_thread_preemption_change(TX_THREAD *thread_ptr, UINT new_threshold,
                        UINT *old_threshold);
UINT        _tx_thread_priority_change(TX_THREAD *thread_ptr, UINT new_priority,
                        UINT *old_priority);
void        _tx_thread_relinquish(void);
UINT        _tx_thread_reset(TX_THREAD *thread_ptr);
UINT        _tx_thread_resume(TX_THREAD *thread_ptr);
void        _tx_thread_schedule(void);
void        _tx_thread_shell_entry(void);
UINT        _tx_thread_sleep(ULONG timer_ticks);
void        _tx_thread_stack_analyze(TX_THREAD *thread_ptr);
void        _tx_thread_stack_build(TX_THREAD *thread_ptr, void (*function_ptr)(void));
void        _tx_thread_stack_error(TX_THREAD *thread_ptr);
void        _tx_thread_stack_error_handler(TX_THREAD *thread_ptr);
UINT        _tx_thread_stack_error_notify(void (*stack_error_handler)(TX_THREAD *));
void        _tx_thread_system_preempt_check(void);
void        _tx_thread_system_resume(TX_THREAD *thread_ptr);
void        _tx_thread_system_return(void);
void        _tx_thread_system_suspend(TX_THREAD *thread_ptr);
UINT        _tx_thread_suspend(TX_THREAD *thread_ptr);
UINT        _tx_thread_terminate(TX_THREAD *thread_ptr);
UINT        _tx_thread_time_slice_change(TX_THREAD *thread_ptr, ULONG new_time_slice, ULONG *old_time_slice);
void        _tx_thread_time_slice(void);
void        _tx_thread_timeout(ULONG timeout_input);
UINT        _tx_thread_wait_abort(TX_THREAD *thread_ptr);


/* Define error checking shells for API services.  These are only referenced by the 
   application.  */

UINT        _txe_thread_create(TX_THREAD *thread_ptr, CHAR *name_ptr, 
                void (*entry_function)(ULONG), ULONG entry_input,
                void *stack_start, ULONG stack_size, 
                UINT priority, UINT preempt_threshold, 
                ULONG time_slice, UINT auto_start, UINT thread_control_block_size);
UINT        _txe_thread_delete(TX_THREAD *thread_ptr);
UINT        _txe_thread_entry_exit_notify(TX_THREAD *thread_ptr, void (*thread_entry_exit_notify)(TX_THREAD *, UINT));
TX_THREAD  *_tx_thread_identify(void);
UINT        _txe_thread_info_get(TX_THREAD *thread_ptr, CHAR **name, UINT *state, ULONG *run_count, 
                UINT *priority, UINT *preemption_threshold, ULONG *time_slice, 
                TX_THREAD **next_thread, TX_THREAD **next_suspended_thread);
UINT        _txe_thread_preemption_change(TX_THREAD *thread_ptr, UINT new_threshold,
                        UINT *old_threshold);
UINT        _txe_thread_priority_change(TX_THREAD *thread_ptr, UINT new_priority,
                        UINT *old_priority);
void        _txe_thread_relinquish(void);
UINT        _txe_thread_reset(TX_THREAD *thread_ptr);
UINT        _txe_thread_resume(TX_THREAD *thread_ptr);
UINT        _txe_thread_suspend(TX_THREAD *thread_ptr);
UINT        _txe_thread_terminate(TX_THREAD *thread_ptr);
UINT        _txe_thread_time_slice_change(TX_THREAD *thread_ptr, ULONG new_time_slice, ULONG *old_time_slice);
UINT        _txe_thread_wait_abort(TX_THREAD *thread_ptr);


/* Thread control component data declarations follow.  */

/* Determine if the initialization function of this component is including
   this file.  If so, make the data definitions really happen.  Otherwise,
   make them extern so other functions in the component can access them.  */







//THREAD_DECLARE	VOID *			_tx_irq_processing_return;		

/* Define the pointer that contains the system stack pointer.  This is
   utilized when control returns from a thread to the system to reset the
   current stack.  This is setup in the low-level initialization function. */

extern  void *          _tx_thread_system_stack_ptr;


/* Define the current thread pointer.  This variable points to the currently
   executing thread.  If this variable is NULL, no thread is executing.  */

extern  TX_THREAD *     _tx_thread_current_ptr;


/* Define the variable that holds the next thread to execute.  It is important
   to remember that this is not necessarily equal to the current thread 
   pointer.  */

extern  TX_THREAD *     _tx_thread_execute_ptr;


/* Define the head pointer of the created thread list.  */

extern  TX_THREAD *     _tx_thread_created_ptr;


/* Define the variable that holds the number of created threads. */

extern  ULONG           _tx_thread_created_count;


/* Define the current state variable.  When this value is 0, a thread
   is executing or the system is idle.  Other values indicate that 
   interrupt or initialization processing is active.  This variable is
   initialized to TX_INITIALIZE_IN_PROGRESS to indicate initialization is
   active.  */




extern  volatile ULONG  _tx_thread_system_state;



/* Define the 32-bit priority bit-maps. There is one priority bit map for each
   32 priority levels supported. If only 32 priorities are supported there is 
   only one bit map. Each bit within a priority bit map represents that one 
   or more threads at the associated thread priority are ready.  */ 

extern  ULONG           _tx_thread_priority_maps[256/32];


/* Define the priority map active bit map that specifies which of the previously 
   defined priority maps have something set. This is only necessary if more than 
   32 priorities are supported.  */


extern  ULONG           _tx_thread_priority_map_active;





/* Define the 32-bit preempt priority bit maps.  There is one preempt bit map 
   for each 32 priority levels supported. If only 32 priorities are supported 
   there is only one bit map. Each set set bit corresponds to a preempted priority 
   level that had preemption-threshold active to protect against preemption of a 
   range of relatively higher priority threads.  */

extern  ULONG           _tx_thread_preempted_maps[256/32];


/* Define the preempt map active bit map that specifies which of the previously 
   defined preempt maps have something set. This is only necessary if more than 
   32 priorities are supported.  */


extern  ULONG           _tx_thread_preempted_map_active;



/* Define the variable that holds the highest priority group ready for 
   execution.  It is important to note that this is not necessarily the same
   as the priority of the thread pointed to by _tx_execute_thread.  */

extern  UINT            _tx_thread_highest_priority;


/* Define the array that contains the lowest set bit of any 8-bit pattern.  
   Portions of the priority map are used as indexes to quickly find
   the next priority group ready for execution.  This table is initialized
   in the thread control initialization processing.  */

# 301 "L:/PLT/os/threadx/inc/tx_thread.h"

extern  UCHAR           _tx_thread_lowest_bit[256];



/* Define the array of thread pointers.  Each entry represents the threads that
   are ready at that priority group.  For example, index 10 in this array
   represents the first thread ready at priority 10.  If this entry is NULL,
   no threads are ready at that priority.  */

extern  TX_THREAD *     _tx_thread_priority_list[256];


/* Define the global preempt disable variable.  If this is non-zero, preemption is
   disabled.  It is used internally by ThreadX to prevent preemption of a thread in 
   the middle of a service that is resuming or suspending another thread.  */

extern  volatile UINT   _tx_thread_preempt_disable;


/* Define the global build options variable.  This contains a bit map representing
   how the ThreadX library was built. The following are the bit field definitions:

                    Bit(s)                   Meaning

                    31-30               Reserved
                    29-24               Priority groups 1  -> 32 priorities
                                                        2  -> 64 priorities
                                                        3  -> 96 priorities

                                                        ...

                                                        32 -> 1024 priorities
                    23                  TX_TIMER_PROCESS_IN_ISR defined
                    22                  TX_REACTIVATE_INLINE defined
                    21                  TX_DISABLE_STACK_FILLING defined
                    20                  TX_ENABLE_STACK_CHECKING defined
                    19                  TX_DISABLE_PREEMPTION_THRESHOLD defined
                    18                  TX_DISABLE_REDUNDANT_CLEARING defined
                    17                  TX_DISABLE_NOTIFY_CALLBACKS defined
                    16                  TX_BLOCK_POOL_ENABLE_PERFORMANCE_INFO defined
                    15                  TX_BYTE_POOL_ENABLE_PERFORMANCE_INFO defined
                    14                  TX_EVENT_FLAGS_ENABLE_PERFORMANCE_INFO defined
                    13                  TX_MUTEX_ENABLE_PERFORMANCE_INFO defined
                    12                  TX_QUEUE_ENABLE_PERFORMANCE_INFO defined
                    11                  TX_SEMAPHORE_ENABLE_PERFORMANCE_INFO defined
                    10                  TX_THREAD_ENABLE_PERFORMANCE_INFO defined
                    9                   TX_TIMER_ENABLE_PERFORMANCE_INFO defined
                    8                   TX_ENABLE_EVENT_TRACE defined
                    7-0                 Port Specific   */

extern  ULONG           _tx_build_options;


# 364 "L:/PLT/os/threadx/inc/tx_thread.h"

# 443 "L:/PLT/os/threadx/inc/tx_thread.h"














# 22 "L:/PLT/os/osa/inc/osa.h"





# 35 "L:/PLT/os/osa/inc/osa.h"

# 1 "L:/PLT/csw/platform/inc/gbl_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ============================================================================
File        : gbl_types.h
Description : Global types file for testing the
              os/kal package.

Notes       : This file is only used to test the compilation and
              archiving for the os/kal package.

Copyright 2001, Intel Corporation, All rights reserved.
============================================================================ */




/* Use the Xscale environment types */
# 1 "L:/PLT/env/win32/inc/xscale_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : xscale_types.h
Description : Global types file for the Xscale environment.

Notes       : This file is designed for use in the arm environment
              and is referenced from the gbl_types.h file. Use of
			  this file requires ENV_XSCALE to be defined in xscale_env.mak.
              
Copyright 2001, Intel Corporation, All rights reserved.
=========================================================================== */




typedef unsigned char	BOOL;
typedef unsigned char   UINT8;
typedef unsigned short  UINT16;
typedef unsigned long   UINT32;

typedef char            CHAR;
typedef signed char     INT8;
typedef signed short    INT16;
typedef signed long     INT32;













/*                         end of xscale_types.h
--------------------------------------------------------------------------- */



# 23 "L:/PLT/csw/platform/inc/gbl_types.h"


/* Use the NordHeim environment types */




/* Use the Arm environment types */




/* Use the Gnu environment types */




/* Use the Microsoft Visual C environment types */




  /* Standard typedefs */
  typedef unsigned char   Bool;         /* Boolean                        */

  /* Standard typedefs - to retain compatibility with TDMA */
  typedef UINT8           		 BYTE;         			/* Unsigned 8-bit quantity        */
  typedef UINT8            		 UBYTE;        			/* Unsigned 8-bit quantity        */
  typedef UINT16          		 UWORD;        			/* Unsigned 16-bit quantity       */
  typedef UINT16          		 WORD;         			/* Unsigned 16-bit quantity       */
  typedef INT16           		 SWORD;        			/* Signed 16-bit quantity         */
  typedef UINT32                 DWORD;        			/* Unsigned 32-bit quantity       */
  typedef unsigned long long     UINT64;                /* Unsigned 64-bit quantity       */
  typedef void* 		         VOID_PTR;























  /* A NULL value is required such that it is not mistaken for a valid */
  /* value which includes values in the range of modulo 64. */


  /* Definition of NULL is required */















/*                      end of gbl_types.h
--------------------------------------------------------------------------- */



# 37 "L:/PLT/os/osa/inc/osa.h"

/*****************************************************************************
 * OSA Constants
 ****************************************************************************/
# 49 "L:/PLT/os/osa/inc/osa.h"

# 69 "L:/PLT/os/osa/inc/osa.h"

# 85 "L:/PLT/os/osa/inc/osa.h"






 /*========================================================================
  *  OSA Return Error Codes
  *========================================================================*/

  enum
  {
    OS_SUCCESS = 0,        /* 0x0 -no errors                                        */
    OS_FAIL,               /* 0x1 -operation failed code                            */
    OS_TIMEOUT,            /* 0x2 -Timed out waiting for a resource                 */
    OS_NO_RESOURCES,       /* 0x3 -Internal OS resources expired                    */
    OS_INVALID_POINTER,    /* 0x4 -0 or out of range pointer value                  */
    OS_INVALID_REF,        /* 0x5 -invalid reference                                */
    OS_INVALID_DELETE,     /* 0x6 -deleting an unterminated task                    */
    OS_INVALID_PTR,        /* 0x7 -invalid memory pointer                           */
    OS_INVALID_MEMORY,     /* 0x8 -invalid memory pointer                           */
    OS_INVALID_SIZE,       /* 0x9 -out of range size argument                       */
    OS_INVALID_MODE,       /* 0xA, 10 -invalid mode                                 */
    OS_INVALID_PRIORITY,   /* 0xB, 11 -out of range task priority                   */
    OS_UNAVAILABLE,        /* 0xC, 12 -Service requested was unavailable or in use  */
    OS_POOL_EMPTY,         /* 0xD, 13 -no resources in resource pool                */
    OS_QUEUE_FULL,         /* 0xE, 14 -attempt to send to full messaging queue      */
    OS_QUEUE_EMPTY,        /* 0xF, 15 -no messages on the queue                     */
    OS_NO_MEMORY,          /* 0x10, 16 -no memory left                              */
    OS_DELETED,            /* 0x11, 17 -service was deleted                         */
    OS_SEM_DELETED,        /* 0x12, 18 -semaphore was deleted                       */
    OS_MUTEX_DELETED,      /* 0x13, 19 -mutex was deleted                           */
    OS_MSGQ_DELETED,       /* 0x14, 20 -msg Q was deleted                           */
    OS_MBOX_DELETED,       /* 0x15, 21 -mailbox Q was deleted                       */
    OS_FLAG_DELETED,       /* 0x16, 22 -flag was deleted                            */
    OS_INVALID_VECTOR,     /* 0x17, 23 -interrupt vector is invalid                 */
    OS_NO_TASKS,           /* 0x18, 24 -exceeded max # of tasks in the system       */
    OS_NO_FLAGS,           /* 0x19, 25 -exceeded max # of flags in the system       */
    OS_NO_SEMAPHORES,      /* 0x1A, 26 -exceeded max # of semaphores in the system  */
    OS_NO_MUTEXES,         /* 0x1B, 27 -exceeded max # of mutexes in the system     */
    OS_NO_QUEUES,          /* 0x1C, 28 -exceeded max # of msg queues in the system  */
    OS_NO_MBOXES,          /* 0x1D, 29 -exceeded max # of mbox queues in the system */
    OS_NO_TIMERS,          /* 0x1E, 30 -exceeded max # of timers in the system      */
    OS_NO_MEM_POOLS,       /* 0x1F, 31 -exceeded max # of mem pools in the system   */
    OS_NO_INTERRUPTS,      /* 0x20, 32 -exceeded max # of isr's in the system       */
    OS_FLAG_NOT_PRESENT,   /* 0x21, 33 -requested flag combination not present      */
    OS_UNSUPPORTED,        /* 0x22, 34 -service is not supported by the OS          */
    OS_NO_MEM_CELLS,       /* 0x23, 35 -no global memory cells                      */
    OS_DUPLICATE_NAME,     /* 0x24, 36 -duplicate global memory cell name           */
    OS_INVALID_PARM        /* 0x25, 37 -invalid parameter                           */
  };


/*****************************************************************************
 * OSA Data Types
 ****************************************************************************/


typedef void    *OsaRefT ;
typedef UINT8   OSA_STATUS;

/*========================================================================
 *  OSA Initialization:
 *
 *  Initializes OSA internal structures, tables, and OS specific services.
 *
 *========================================================================*/
typedef enum
{
    OSA_SISR_HIGH_PRIORITY = 0,
    OSA_SISR_MED_PRIORITY  = 1,
    OSA_SISR_LOW_PRIORITY  = 2,
    OSA_SISR_MAX_PRIORITY           //  Keep Last.
}
OsaSisrPriorityT ;

typedef struct
{
    UINT32      *SisrStackPtr[(int)OSA_SISR_MAX_PRIORITY] ;     //  [OP,IN] SISR Stack Pointers.
    UINT32      SisrStackSize[(int)OSA_SISR_MAX_PRIORITY] ;     //  [IN]    SISR Stack Size.
}
OsaInitParamsT ;


 extern OSA_STATUS   OsaInit(
    OsaInitParamsT          *pParams            //  [OP,IN] Initialization parameters.
    ) ;

 extern void        OsaRun( void *pForFutureUse );

 extern char        *OsaGetVersion( void );

/*========================================================================
 *  OSA Task Management:
 *========================================================================*/
typedef struct
{
    void                (*entry)(void *) ;      //  Task entry function.
    UINT32              stackSize ;             //  Size of stack.
    UINT32              *stackPtr ;             //  [OP]    Memory area for the stack.
    void                *argv ;                 //  [OP]    A pointer that is passed to the task.
    char                *name ;                 //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;         //  [OP]    TRUE - The object can be accessed from all processes.
    UINT8               priority ;              //  Task priority.
}
OsaTaskCreateParamsT ;

typedef struct
{
    void                (*entry)(unsigned, void *) ;      //  Task entry function.
    UINT32              stackSize ;             //  Size of stack.
    UINT32              *stackPtr ;             //  [OP]    Memory area for the stack.
    void                *argv ;                 //  [OP]    A pointer that is passed to the task.
    char                *name ;                 //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;         //  [OP]    TRUE - The object can be accessed from all processes.
    UINT8               priority ;              //  Task priority.
}
OsaTaskCreateParamsExT ;

 extern OSA_STATUS  OsaTaskCreateEx(
    OsaRefT                 *pOsaRef,           //  [OT]    Reference.
    OsaTaskCreateParamsExT    *pParams            //  [IN]    Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaTaskCreate(
    OsaRefT                 *pOsaRef,           //  [OT]    Reference.
    OsaTaskCreateParamsT    *pParams            //  [IN]    Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaTaskDelete(
    OsaRefT                 OsaRef,             //  [IN]    Reference.
    void                    *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaTaskSuspend(
    OsaRefT                 OsaRef,             //  [IN]    Reference.
    void                    *pForFutureUse
    ) ;

 extern OSA_STATUS OsaTaskResume(
    OsaRefT                 OsaRef,             //  [IN]    Reference.
    void                    *pForFutureUse
    ) ;

 extern void        OsaTaskSleep(
    UINT32                  ticks,              //  [IN]    Time to sleep in ticks.
    void                    *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaTaskChangePriority(
    OsaRefT                 OsaRef,             //  [IN]    Reference.
    UINT8                   newPriority,        //  [IN]    New task priority.
    UINT8                   *oldPriority,       //  [OT]    Old task priority.
    void                    *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaTaskGetPriority(
    OsaRefT                 OsaRef,             //  [IN]    Reference.
    UINT8                   *pPriority,         //  [OT]    Task priority.
    void                    *pForFutureUse
    ) ;

 extern OSA_STATUS OsaHISRGetPriority(
	OsaRefT 				OsaRef, 			//  [IN]    Reference.
	UINT8 					*pPriority, 		//  [OT]    Task priority.
	void 					*pForFutureUse
	) ;
 extern UINT32 OSAHISRIsValid(
	OsaRefT OsaRef
	);
 extern UINT32 OSATaskGetStatus(
	OsaRefT OsaRef
	);
 extern UINT32	OSATaskGetSysParam2(
	OsaRefT OsaRef
	);
 extern void* OSATaskGetStackStart(
	OsaRefT OsaRef
	);
 extern void* OSATaskGetStackEnd(
	OsaRefT OsaRef
	);
 extern UINT32 OSATaskCheckStack(
	UINT32 * stack
	);
 extern void        OsaTaskYield(  void *pForFutureUse  ) ;

 extern OSA_STATUS  OsaTaskGetCurrentRef(
    OsaRefT                 *pOsaRef,           //  [OT]    Reference.
    void                    *pForFutureUse
    ) ;

 extern OsaRefT OsaTaskGetCurrent( void );

 extern OsaRefT OsaCurrentThreadRef(void);

 extern BOOL  OsaTaskSetCurrent(
	OsaRefT 				OsaRef 				//  [IN]    Reference.
    ) ;

 extern OSA_STATUS  OsaHISRGetCurrentRef(
    OsaRefT                 *pOsaRef,           //  [OT]    Reference.
    void                    *pForFutureUse
    ) ;

/*========================================================================
 *  OSA Semaphore Management
 *========================================================================*/
typedef struct
{
    UINT32              initialCount ;              //  [OP]    Initial count of the semaphore (0 = Lock), (Default = 1).
    UINT32              maximumCount ;              //  [OP]    Maximum tasks that can "pass" through the semaphore, (Default = max(1,initialCount)).
    UINT8               waitingMode ;               //  [OP]    OS_FIFO, OS_PRIORITY, (Default = OS_PRIORITY).
    char                *name ;                     //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;             //  [OP]    TRUE - The object can be accessed from all processes.
}
OsaSemaphoreCreateParamsT ;

 extern OSA_STATUS  OsaSemaphoreCreate(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaSemaphoreCreateParamsT   *pParams            //  [OP,IN] Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaSemaphoreAcquire(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      timeout,            //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaSemaphorePoll(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      *pCount,            //  [OT]    Current semaphore count.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaSemaphoreRelease(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaSemaphoreDelete(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

/*========================================================================
 *  OSA Mutex Management
 *========================================================================*/
typedef struct
{
    UINT8               waitingMode ;               //  [OP]    OS_FIFO, OS_PRIORITY, (Default = OS_PRIORITY).
    char                *name ;                     //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;             //  [OP]    TRUE - The object can be accessed from all processes.
    BOOL				inherit;
}
OsaMutexCreateParamsT ;

 extern OSA_STATUS  OsaMutexCreate(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaMutexCreateParamsT       *pParams            //  [OP,IN] Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaMutexLock(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      timeout,            //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMutexUnlock(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMutexDelete(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

/*========================================================================
 *  OSA Interrupt Control
 *========================================================================*/
typedef struct
{
    UINT32              intSource ;                 //          Interrupt number (need to set to OSA_NULL_INT_SOURCE if not used).
    void                (*fisrRoutine)(UINT32) ;    //  [OP]    First level ISR to be called.
    void                (*sisrRoutine)(void) ;      //  [OP]    Second level ISR routine to be called.
    char                *name ;                     //  [OP]    Pointer to a NULL terminated string.
    UINT32              stackSize ;                 //  [OP]    Size of stack.
    OsaSisrPriorityT    priority ;                  //          SISR priority.
}
OsaIsrCreateParamsT ;



 extern OSA_STATUS  OsaIsrCreate(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaIsrCreateParamsT         *pParams            //  [IN]    Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaIsrNotify(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaIsrDelete(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS OsaHisrCreate(
	OsaRefT *hisr,
	CHAR *name,
	void (*hisr_entry)(void),
	unsigned char priority
	);

 extern OSA_STATUS OsaHisrActivate(
	OsaRefT *hisr
	);

 extern OSA_STATUS OsaHisrDel(
	OsaRefT *hisr
	);


/*===========================================================================
 *  OSA Real-Time Access:
 *=========================================================================*/

 extern UINT32  OsaGetTicks( void *pForFutureUse ) ;

 extern UINT32  OsaGetClockRate( void *pForFutureUse ) ;

 extern void    OsaTick( void *pForFutureUse ) ;

/*========================================================================
 *  OSA Sys Context info
 *========================================================================*/
typedef struct
{
    char                *name ;                     //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;             //  [OP]    TRUE - The object can be accessed from all processes.
}
OsaCriticalSectionCreateParamsT ;

 extern OsaRefT     OsaCriticalSectionCreate(
    OsaCriticalSectionCreateParamsT     *pParams            //  [OP,IN] Input Parameters (see datatype for details).
    ) ;

 extern OsaRefT     OsaCriticalSectionEnter(
    OsaRefT                             OsaRef,             //  [IN]    Reference from the Create Function.
    void                                *pForFutureUse
    ) ;

 extern void        OsaCriticalSectionExit(
    OsaRefT                             OsaRef,             //  [IN]    Reference from the Enter Function.
    void                                *pForFutureUse
    ) ;

 extern void        OsaCriticalSectionDelete(
    OsaRefT                             OsaRef,             //  [IN]    Reference from the Create Function.
    void                                *pForFutureUse
    ) ;

 extern UINT32      OsaControlInterrupts(
    UINT32                              mask,               //  [IN]    New interrupt mask.
    void                                *pForFutureUse
    ) ;

 extern UINT32      OsaContextLock( void *pForFutureUse ) ;

 extern UINT32      OsaContextRestore( void *pForFutureUse ) ;

 /*========================================================================
  *  OSA Message Passing
  *========================================================================*/
typedef struct
{
    UINT32              maxSize ;                   //  Max message size the queue supports.
    UINT32              maxMsg ;                    //  Max # of messages in the queue.
    char                *name ;                     //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;             //  [OP]    TRUE - The object can be accessed from all processes.
    UINT8               waitingMode ;               //  OS_FIFO, OS_PRIORITY.
}
OsaMsgQCreateParamsT ;

typedef struct
{
    void                *msgPtr ;                   //  [IN]    Start address of the data.
    UINT32              size ;                      //  [IN]    Size of the message.
    UINT32              timeout ;                   //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
}
OsaMsgQSendParamsT ;

typedef struct
{
    void                *msgPtr ;                   //  [IN]    Start address of the data.
    UINT32              size ;                      //  [IN]    Size of the message.
    UINT32              timeout ;                   //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
    UINT32              actualSize ;                //  [OT]    Actual bytes read from queue.
}
OsaMsgQRecvParamsT ;

 extern OSA_STATUS  OsaMsgQCreate(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaMsgQCreateParamsT        *pParams            //  [IN]    Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaMsgQSend(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    OsaMsgQSendParamsT          *pParams            //  [IN]    See datatype.
    ) ;

 extern OSA_STATUS  OsaMsgQRecv(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    OsaMsgQRecvParamsT          *pParams            //  [IN]    See datatype.
    ) ;

 extern OSA_STATUS  OsaMsgQPoll(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      *pCount,            //  [OT]    Number of messages in Q.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMsgQFreeRate(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      *pFreeRate          //  [OT]    free Rate of Q.

    ) ;

 extern OSA_STATUS  OsaMsgQDelete(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

 /*========================================================================
  *  OSA Mailboxes
  *========================================================================*/
typedef struct
{
    UINT32              maxMsg ;                    //  Max # of messages in the queue.
    char                *name ;                     //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;             //  [OP]    TRUE - The object can be accessed from all processes.
    UINT8               waitingMode ;               //  OS_FIFO, OS_PRIORITY.
}
OsaMailboxQCreateParamsT ;

 extern OSA_STATUS  OsaMailboxQCreate(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaMailboxQCreateParamsT    *pParams            //  [IN]    Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaMailboxQSend(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      mboxData,           //  [IN]    Data to put in mailbox.
    UINT32                      timeout,            //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMailboxQRecv(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      *mboxData,          //  [OT]    Data read from mailbox.
    UINT32                      timeout,            //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMailboxQPoll(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      *pCount,            //  [OT]    Number of messages in Q.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMailboxQDelete(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

/*========================================================================
 *  OSA Event Management:
 *========================================================================*/
typedef struct
{
    char                *name ;                     //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;             //  [OP]    TRUE - The object can be accessed from all processes.
}
OsaFlagCreateParamsT ;

typedef struct
{
    UINT32              mask ;                      //  [IN]    Flag mask.
    UINT32              operation ;                 //  [IN]    OSA_FLAG_AND, OSA_FLAG_AND_CLEAR, OSA_FLAG_OR, OSA_FLAG_OR_CLEAR.
    UINT32              timeout ;                   //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
    UINT32              *flags ;                    //  [OT]    Current value of the flag.
}
OsaFlagWaitParamsT ;

 extern OSA_STATUS  OsaFlagCreate(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaFlagCreateParamsT        *pParams            //  [OP,IN] Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaFlagSet(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      mask,               //  [IN]    Flag mask.
    UINT32                      operation,          //  [IN]    OSA_FLAG_AND, OSA_FLAG_OR.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaFlagWait(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    OsaFlagWaitParamsT          *pParams            //  [IN]    Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaFlagDelete(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

/*========================================================================
 *  OSA Timer Management:
 *========================================================================*/
typedef struct
{
    UINT32              initialTime ;                   //  [IN]    Initial expiration time in clock ticks.
    UINT32              rescheduleTime ;                //  [IN]    Periodic expiration time in clock ticks. 0=One shot timer.
    void                (*callBackRoutine)(UINT32) ;    //  [IN]    Routine to call when timer expiers.
    UINT32              timerArgc ;                     //  [IN]    Argument to be passed to the callBackRoutine
    char                *name ;                         //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;                 //  [OP]    TRUE - The object can be accessed from all processes.
}
OsaTimerParamsT ;

typedef struct
{
    UINT32              status  ;                       //  [O]     Timer status OS_ENABLED, OS_DISABLED.
}
OsaTimerStatusParamsT ;

 extern OSA_STATUS  OsaTimerCreate(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaTimerParamsT             *pParams            //  [OP,IN] Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaTimerStart(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    OsaTimerParamsT             *pParams            //  [OP,IN] Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaTimerStop(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaTimerStatus(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    OsaTimerStatusParamsT       *pParams            //  [OT]    Output Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaTimerDelete(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

/*============================================================================
 * Special memory handling that doesn't use the OS memory services.
 *============================================================================*/
typedef struct
{
    void                *poolBase ;                     //          Pointer to start of pool memory.
    UINT32              poolSize ;                      //          size of the pool.
    char                *name ;                         //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;                 //  [OP]    TRUE - The object can be accessed from all processes.
    UINT32              LowWaterMark ;                  //  [OP]    Number of bytes left in pool tp trigger the LowWaterMarkCbFunc.
    void                (*LowWaterMarkCbFunc)(UINT32) ; //  [OP]    Routine to call when the LowWaterMark is triggered.
}
OsaMemCreateParamsT ;

 extern OSA_STATUS  OsaMemAddMemoryToPool(
    OsaRefT                     OsaRef,             //  [OP,IN] Pool Reference, if NULL, the default pool is used.
    void                        *memBase,           //  [IN]    Pointer to the memory to be added.
    UINT32                      memSize,            //  [IN]    Size of the memory to be added.
    void                        *pForFutureUse
    ) ;

 extern void *      OsaMemAlloc(
    OsaRefT                     OsaRef,             //  [OP,IN] Pool Reference, if NULL, the default pool is used.
    UINT32                      Size                //  [IN]    Number of bytes to be allocated.
    ) ;

 extern void *      OsaMemAllocExt(
    OsaRefT                     poolRef,            //  [OP,IN] Pool Reference, if NULL, the default pool is used.
    UINT32                      Size,               //  [IN]    Number of bytes to be allocated.
    UINT32                      Caller              //  [IN]    Caller of this API.
    ) ;

 extern BOOL        OsaMemAllocAgain(
    void                        *pMem               //  [IN]    Pointer to the memory to be allocated again.
    ) ;

 extern OSA_STATUS  OsaMemCreatePool(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaMemCreateParamsT         *pParams            //  [IN] Input Parameters (see datatype for details).
    ) ;

 extern void        OsaMemFree(
    void                        *pMem               //  [IN]    Pointer to the memory to be freed.
    ) ;

 extern UINT32      OsaMemGetAllocSize(
    void                        *pMem               //  [IN]    Pointer to the memory.
    ) ;

 extern OsaRefT     OsaMemGetDefaultPoolRef( void ) ;

 extern OsaRefT     OsaMemGetPoolRef(
    char                        *poolName,          //  [OP,IN] Pool's name - can be NULL.
    void                        *pMem,              //  [OP,IN] A memory address we need the poolRef for it - can be NULL.
    void                        *pForFutureUse
    ) ;

 extern UINT32      OsaMemGetUserParam(
    void                        *pMem               //  [IN]    Pointer to the allocated memory.
    ) ;

 extern BOOL        OsaMemResizeAlloc(
    void                        *pMem,              //  [IN]    Pointer to the memory to be re-sized.
    UINT32                      NewSize             //  [IN]    New Size.
    ) ;

 extern void        OsaMemSetDefaultPool(
    OsaRefT                     OsaRef              //  [IN]    Pool Reference.
     ) ;

 extern OSA_STATUS  OsaMemSetUserParam(
    void                        *pMem,              //  [IN]    Pointer to the allocated memory.
    UINT32                      Param               //  [IN]    User's parameter.
     ) ;

 extern void        OsaMemSetUserParamsFast(
    void                        *pMem,              //  [IN]    Pointer to the allocated memory.
    UINT32                      Param,              //  [IN]    User's parameter.
    UINT32                      CallerAddress       //  [IN]    Caller address.
     ) ;

 extern OSA_STATUS	OsaPartitionAllocate(
	OsaRefT * 					pOsaRef,
	void **						return_pointer,
	UINT32 						suspend
	);

 extern OSA_STATUS	OsaPartitionFree(
	OsaRefT 					OsaRef
	);

 extern OSA_STATUS OsaPartitionPoolCreate(
	OsaRefT *					pOsaRef,
	char *						pool_name,
	void *						start_address,
	UINT32 						pool_size,
	UINT32 						partition_size,
	UINT32 						suspend_type
	);

 extern UINT32 Osa_TimeoutValue(
	UINT32 						timeout
	);
 extern UINT64 Osa_TimeoutValueEx(
	UINT32 						timeout
	);

 extern OSA_STATUS OsaSystemProtectInit(void);

 extern OSA_STATUS OsaSystemProtect(void);

 extern OSA_STATUS OsaSystemUnProtect(void);


 extern UINT32 OsaGetInterruptCount(void);
/*=============================================================================
 * Remap old names to new ones to remain backwards compatibility with old names.
 *=============================================================================*/
# 1 "L:/PLT/os/osa/inc/osa_old_api.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/







/*****************************************************************************
 * OSA Data Types
 ****************************************************************************/

# 1 "L:/PLT/os/threadx/inc/tx_hisr.h"


# 1 "L:/PLT/os/threadx/inc/tx_api.h"
/***************************************************************************
* Portions Copyright (c) 2008-2011 Marvell International, Ltd.
*               All Rights Reserved
*
*               Marvell Confidential
* ==========================================================================
*/


/**************************************************************************/
/*                                                                        */
/*            Copyright (c) 1996-2007 by Express Logic Inc.               */
/*                                                                        */
/*  This software is copyrighted by and is the sole property of Express   */
/*  Logic, Inc.  All rights, title, ownership, or other interests         */
/*  in the software remain the property of Express Logic, Inc.  This      */
/*  software may only be used in accordance with the corresponding        */
/*  license agreement.  Any unauthorized use, duplication, transmission,  */
/*  distribution, or disclosure of this software is expressly forbidden.  */
/*                                                                        */
/*  This Copyright notice may not be removed or modified without prior    */
/*  written consent of Express Logic, Inc.                                */
/*                                                                        */
/*  Express Logic, Inc. reserves the right to modify this software        */
/*  without notice.                                                       */
/*                                                                        */
/*  Express Logic, Inc.                     <EMAIL>         */
/*  11423 West Bernardo Court               http://www.expresslogic.com   */
/*  San Diego, CA  92127                                                  */
/*                                                                        */
/**************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */
/** ThreadX Component                                                     */
/**                                                                       */
/**   Application Interface (API)                                         */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/


/**************************************************************************/
/*                                                                        */
/*  APPLICATION INTERFACE DEFINITION                       RELEASE        */
/*                                                                        */
/*    tx_api.h                                            PORTABLE C      */
/*                                                           5.1          */
/*  AUTHOR                                                                */
/*                                                                        */
/*    William E. Lamie, Express Logic, Inc.                               */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file defines the basic Application Interface (API) to the      */
/*    high-performance ThreadX real-time kernel.  All service prototypes  */
/*    and data structure definitions are defined in this file.            */
/*    Please note that basic data type definitions and other architecture-*/
/*    specific information is contained in the file tx_port.h.            */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*  12-12-2005     William E. Lamie         Initial Version 5.0           */
/*  04-02-2007     William E. Lamie         Modified comment(s), and      */
/*                                            replaced UL constant        */
/*                                            modifier with ULONG cast,   */
/*                                            resulting in version 5.1    */
/*                                                                        */
/**************************************************************************/



# 1314 "L:/PLT/os/threadx/inc/tx_api.h"



# 6 "L:/PLT/os/threadx/inc/tx_hisr.h"

typedef struct TX_HISR_STRUCT{

	TX_THREAD  			tx_hisr_thread;					//note: should not be a pointer
	TX_SEMAPHORE		tx_hisr_semaphore;		////note: should not be a pointer
	void                (*tx_hisr_entry)(ULONG);
	ULONG               tx_hisr_entry_parameter;
	char				*tx_hisr_stack_start; 
	UINT				reserved;

}TX_HISR;


UINT _tx_hisr_create(TX_HISR *hisr_ptr, CHAR * name,void(* entry_function)(ULONG),
				ULONG entry_input,UINT priority);
UINT _tx_hisr_delete(TX_HISR *hisr_ptr);
UINT _tx_hisr_activate(TX_HISR *hisr_ptr);
void hisr_thread(ULONG entry_input);




# 18 "L:/PLT/os/osa/inc/osa_old_api.h"
# 1 "L:/PLT/os/threadx/inc/tx_api.h"
/***************************************************************************
* Portions Copyright (c) 2008-2011 Marvell International, Ltd.
*               All Rights Reserved
*
*               Marvell Confidential
* ==========================================================================
*/


/**************************************************************************/
/*                                                                        */
/*            Copyright (c) 1996-2007 by Express Logic Inc.               */
/*                                                                        */
/*  This software is copyrighted by and is the sole property of Express   */
/*  Logic, Inc.  All rights, title, ownership, or other interests         */
/*  in the software remain the property of Express Logic, Inc.  This      */
/*  software may only be used in accordance with the corresponding        */
/*  license agreement.  Any unauthorized use, duplication, transmission,  */
/*  distribution, or disclosure of this software is expressly forbidden.  */
/*                                                                        */
/*  This Copyright notice may not be removed or modified without prior    */
/*  written consent of Express Logic, Inc.                                */
/*                                                                        */
/*  Express Logic, Inc. reserves the right to modify this software        */
/*  without notice.                                                       */
/*                                                                        */
/*  Express Logic, Inc.                     <EMAIL>         */
/*  11423 West Bernardo Court               http://www.expresslogic.com   */
/*  San Diego, CA  92127                                                  */
/*                                                                        */
/**************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */
/** ThreadX Component                                                     */
/**                                                                       */
/**   Application Interface (API)                                         */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/


/**************************************************************************/
/*                                                                        */
/*  APPLICATION INTERFACE DEFINITION                       RELEASE        */
/*                                                                        */
/*    tx_api.h                                            PORTABLE C      */
/*                                                           5.1          */
/*  AUTHOR                                                                */
/*                                                                        */
/*    William E. Lamie, Express Logic, Inc.                               */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file defines the basic Application Interface (API) to the      */
/*    high-performance ThreadX real-time kernel.  All service prototypes  */
/*    and data structure definitions are defined in this file.            */
/*    Please note that basic data type definitions and other architecture-*/
/*    specific information is contained in the file tx_port.h.            */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*  12-12-2005     William E. Lamie         Initial Version 5.0           */
/*  04-02-2007     William E. Lamie         Modified comment(s), and      */
/*                                            replaced UL constant        */
/*                                            modifier with ULONG cast,   */
/*                                            resulting in version 5.1    */
/*                                                                        */
/**************************************************************************/



# 1314 "L:/PLT/os/threadx/inc/tx_api.h"



# 19 "L:/PLT/os/osa/inc/osa_old_api.h"

# 1 "L:/PLT/os/osa/inc/osa_tx.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : osa_tx.h
Description : Definition of OSA Software Layer data types specific to the
              ThreadX OS.

Notes       :

=========================================================================== */





# 1 "L:/PLT/os/osa/inc/osa.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : osa.h
Description : This file defines the OS wrapper API and definitions for external application use.

              The OSA API allows applications to be developed independent of
              the target microkernel/hardware environment. It provides the
              facility to add support for multiple independent operating
              systems from different vendors.

Copyright (c) 2001 Intel CCD. All Rights Reserved
=========================================================================== */
# 820 "L:/PLT/os/osa/inc/osa.h"

# 20 "L:/PLT/os/osa/inc/osa_tx.h"
# 1 "L:/PLT/os/threadx/inc/tx_thread.h"
/**************************************************************************/ 
/*                                                                        */ 
/*            Copyright (c) 1996-2007 by Express Logic Inc.               */ 
/*                                                                        */ 
/*  This software is copyrighted by and is the sole property of Express   */ 
/*  Logic, Inc.  All rights, title, ownership, or other interests         */ 
/*  in the software remain the property of Express Logic, Inc.  This      */ 
/*  software may only be used in accordance with the corresponding        */ 
/*  license agreement.  Any unauthorized use, duplication, transmission,  */ 
/*  distribution, or disclosure of this software is expressly forbidden.  */ 
/*                                                                        */
/*  This Copyright notice may not be removed or modified without prior    */ 
/*  written consent of Express Logic, Inc.                                */ 
/*                                                                        */ 
/*  Express Logic, Inc. reserves the right to modify this software        */ 
/*  without notice.                                                       */ 
/*                                                                        */ 
/*  Express Logic, Inc.                     <EMAIL>         */
/*  11423 West Bernardo Court               http://www.expresslogic.com   */
/*  San Diego, CA  92127                                                  */
/*                                                                        */
/**************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */ 
/** ThreadX Component                                                     */
/**                                                                       */
/**   Thread                                                              */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/


/**************************************************************************/ 
/*                                                                        */ 
/*  COMPONENT DEFINITION                                   RELEASE        */ 
/*                                                                        */ 
/*    tx_thread.h                                         PORTABLE C      */ 
/*                                                           5.1          */ 
/*  AUTHOR                                                                */ 
/*                                                                        */ 
/*    William E. Lamie, Express Logic, Inc.                               */ 
/*                                                                        */ 
/*  DESCRIPTION                                                           */ 
/*                                                                        */ 
/*    This file defines the ThreadX thread control component, including   */ 
/*    data types and external references.  It is assumed that tx_api.h    */
/*    and tx_port.h have already been included.                           */
/*                                                                        */ 
/*  RELEASE HISTORY                                                       */ 
/*                                                                        */ 
/*    DATE              NAME                      DESCRIPTION             */ 
/*                                                                        */ 
/*  12-12-2005     William E. Lamie         Initial Version 5.0           */ 
/*  04-02-2007     William E. Lamie         Modified comment(s),          */ 
/*                                            replaced UL constant        */ 
/*                                            modifier with ULONG cast,   */ 
/*                                            added logic to use preset   */ 
/*                                            global C data, resulting    */ 
/*                                            in version 5.1              */ 
/*                                                                        */ 
/**************************************************************************/ 



# 1 "L:/PLT/os/threadx/inc/tx_api.h"
/***************************************************************************
* Portions Copyright (c) 2008-2011 Marvell International, Ltd.
*               All Rights Reserved
*
*               Marvell Confidential
* ==========================================================================
*/


/**************************************************************************/
/*                                                                        */
/*            Copyright (c) 1996-2007 by Express Logic Inc.               */
/*                                                                        */
/*  This software is copyrighted by and is the sole property of Express   */
/*  Logic, Inc.  All rights, title, ownership, or other interests         */
/*  in the software remain the property of Express Logic, Inc.  This      */
/*  software may only be used in accordance with the corresponding        */
/*  license agreement.  Any unauthorized use, duplication, transmission,  */
/*  distribution, or disclosure of this software is expressly forbidden.  */
/*                                                                        */
/*  This Copyright notice may not be removed or modified without prior    */
/*  written consent of Express Logic, Inc.                                */
/*                                                                        */
/*  Express Logic, Inc. reserves the right to modify this software        */
/*  without notice.                                                       */
/*                                                                        */
/*  Express Logic, Inc.                     <EMAIL>         */
/*  11423 West Bernardo Court               http://www.expresslogic.com   */
/*  San Diego, CA  92127                                                  */
/*                                                                        */
/**************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */
/** ThreadX Component                                                     */
/**                                                                       */
/**   Application Interface (API)                                         */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/


/**************************************************************************/
/*                                                                        */
/*  APPLICATION INTERFACE DEFINITION                       RELEASE        */
/*                                                                        */
/*    tx_api.h                                            PORTABLE C      */
/*                                                           5.1          */
/*  AUTHOR                                                                */
/*                                                                        */
/*    William E. Lamie, Express Logic, Inc.                               */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file defines the basic Application Interface (API) to the      */
/*    high-performance ThreadX real-time kernel.  All service prototypes  */
/*    and data structure definitions are defined in this file.            */
/*    Please note that basic data type definitions and other architecture-*/
/*    specific information is contained in the file tx_port.h.            */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*  12-12-2005     William E. Lamie         Initial Version 5.0           */
/*  04-02-2007     William E. Lamie         Modified comment(s), and      */
/*                                            replaced UL constant        */
/*                                            modifier with ULONG cast,   */
/*                                            resulting in version 5.1    */
/*                                                                        */
/**************************************************************************/



# 1314 "L:/PLT/os/threadx/inc/tx_api.h"



# 72 "L:/PLT/os/threadx/inc/tx_thread.h"

# 454 "L:/PLT/os/threadx/inc/tx_thread.h"



# 21 "L:/PLT/os/osa/inc/osa_tx.h"
# 1 "L:/PLT/os/threadx/inc/tx_port.h"
/***********************************************************
* Portions (c) Copyright 2008-2011 Marvell International Ltd. 
*
*               Marvell Confidential
* ==========================================================
*/

/**************************************************************************/ 
/*                                                                        */ 
/*            Copyright (c) 1996-2006 by Express Logic Inc.               */ 
/*                                                                        */ 
/*  This software is copyrighted by and is the sole property of Express   */ 
/*  Logic, Inc.  All rights, title, ownership, or other interests         */ 
/*  in the software remain the property of Express Logic, Inc.  This      */ 
/*  software may only be used in accordance with the corresponding        */ 
/*  license agreement.  Any unauthorized use, duplication, transmission,  */ 
/*  distribution, or disclosure of this software is expressly forbidden.  */ 
/*                                                                        */
/*  This Copyright notice may not be removed or modified without prior    */ 
/*  written consent of Express Logic, Inc.                                */ 
/*                                                                        */ 
/*  Express Logic, Inc. reserves the right to modify this software        */ 
/*  without notice.                                                       */ 
/*                                                                        */ 
/*  Express Logic, Inc.                     <EMAIL>         */
/*  11423 West Bernardo Court               http://www.expresslogic.com   */
/*  San Diego, CA  92127                                                  */
/*                                                                        */
/**************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */ 
/** ThreadX Component                                                     */
/**                                                                       */
/**   Port Specific                                                       */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/


/**************************************************************************/ 
/*                                                                        */ 
/*  PORT SPECIFIC C INFORMATION                            RELEASE        */ 
/*                                                                        */ 
/*    tx_port.h                                            ARM9/GNU       */ 
/*                                                           5.0          */ 
/*                                                                        */
/*  AUTHOR                                                                */ 
/*                                                                        */ 
/*    William E. Lamie, Express Logic, Inc.                               */ 
/*                                                                        */ 
/*  DESCRIPTION                                                           */ 
/*                                                                        */ 
/*    This file contains data type definitions that make the ThreadX      */ 
/*    real-time kernel function identically on a variety of different     */ 
/*    processor architectures.  For example, the size or number of bits   */ 
/*    in an "int" data type vary between microprocessor architectures and */ 
/*    even C compilers for the same microprocessor.  ThreadX does not     */ 
/*    directly use native C data types.  Instead, ThreadX creates its     */ 
/*    own special types that can be mapped to actual data types by this   */ 
/*    file to guarantee consistency in the interface and functionality.   */ 
/*                                                                        */ 
/*  RELEASE HISTORY                                                       */ 
/*                                                                        */ 
/*    DATE              NAME                      DESCRIPTION             */ 
/*                                                                        */ 
/*  12-12-2005     William E. Lamie         Initial ARM9 GNU              */ 
/*                                            Support Version 5.0         */ 
/*                                                                        */ 
/**************************************************************************/ 



# 346 "L:/PLT/os/threadx/inc/tx_port.h"



# 22 "L:/PLT/os/osa/inc/osa_tx.h"
# 1 "L:/PLT/os/threadx/inc/tx_hisr.h"


# 27 "L:/PLT/os/threadx/inc/tx_hisr.h"

# 23 "L:/PLT/os/osa/inc/osa_tx.h"
# 1 "L:/PLT/os/threadx/inc/tx_timer.h"
/**************************************************************************/ 
/*                                                                        */ 
/*            Copyright (c) 1996-2007 by Express Logic Inc.               */ 
/*                                                                        */ 
/*  This software is copyrighted by and is the sole property of Express   */ 
/*  Logic, Inc.  All rights, title, ownership, or other interests         */ 
/*  in the software remain the property of Express Logic, Inc.  This      */ 
/*  software may only be used in accordance with the corresponding        */ 
/*  license agreement.  Any unauthorized use, duplication, transmission,  */ 
/*  distribution, or disclosure of this software is expressly forbidden.  */ 
/*                                                                        */
/*  This Copyright notice may not be removed or modified without prior    */ 
/*  written consent of Express Logic, Inc.                                */ 
/*                                                                        */ 
/*  Express Logic, Inc. reserves the right to modify this software        */ 
/*  without notice.                                                       */ 
/*                                                                        */ 
/*  Express Logic, Inc.                     <EMAIL>         */
/*  11423 West Bernardo Court               http://www.expresslogic.com   */
/*  San Diego, CA  92127                                                  */
/*                                                                        */
/**************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */ 
/** ThreadX Component                                                     */
/**                                                                       */
/**   Timer                                                               */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/


/**************************************************************************/ 
/*                                                                        */ 
/*  COMPONENT DEFINITION                                   RELEASE        */ 
/*                                                                        */ 
/*    tx_timer.h                                          PORTABLE C      */ 
/*                                                           5.1          */ 
/*  AUTHOR                                                                */ 
/*                                                                        */ 
/*    William E. Lamie, Express Logic, Inc.                               */ 
/*                                                                        */ 
/*  DESCRIPTION                                                           */ 
/*                                                                        */ 
/*    This file defines the ThreadX timer management component, including */ 
/*    data types and external references.  It is assumed that tx_api.h    */
/*    and tx_port.h have already been included.                           */
/*                                                                        */ 
/*  RELEASE HISTORY                                                       */ 
/*                                                                        */ 
/*    DATE              NAME                      DESCRIPTION             */ 
/*                                                                        */ 
/*  12-12-2005     William E. Lamie         Initial Version 5.0           */ 
/*  04-02-2007     William E. Lamie         Modified comment(s), and      */ 
/*                                            replaced UL constant        */ 
/*                                            modifier with ULONG cast,   */ 
/*                                            resulting in version 5.1    */ 
/*                                                                        */ 
/**************************************************************************/ 







/* Define timer management specific data definitions.  */







/* Define timer management function prototypes.  */

UINT        _tx_timer_activate(TX_TIMER *timer_ptr);
UINT        _tx_timer_change(TX_TIMER *timer_ptr, ULONG initial_ticks, ULONG reschedule_ticks);
UINT        _tx_timer_create(TX_TIMER *timer_ptr, CHAR *name_ptr, 
                void (*expiration_function)(ULONG), ULONG expiration_input,
                ULONG initial_ticks, ULONG reschedule_ticks, UINT auto_activate);
UINT        _tx_timer_deactivate(TX_TIMER *timer_ptr);
UINT        _tx_timer_delete(TX_TIMER *timer_ptr);
void        _tx_timer_expiration_process(void);
UINT        _tx_timer_info_get(TX_TIMER *timer_ptr, CHAR **name, UINT *active, ULONG *remaining_ticks, 
                ULONG *reschedule_ticks, TX_TIMER **next_timer);
void        _tx_timer_initialize(void);
UINT        _tx_timer_performance_info_get(TX_TIMER *timer_ptr, ULONG *activates, ULONG *reactivates,
                ULONG *deactivates, ULONG *expirations, ULONG *expiration_adjusts);
UINT        _tx_timer_performance_system_info_get(ULONG *activates, ULONG *reactivates,
                ULONG *deactivates, ULONG *expirations, ULONG *expiration_adjusts);
UINT        _tx_timer_system_activate(TX_TIMER_INTERNAL *timer_ptr);
UINT        _tx_timer_system_deactivate(TX_TIMER_INTERNAL *timer_ptr);
void        _tx_timer_thread_entry(ULONG timer_thread_input);

ULONG       _tx_time_get(void);
void        _tx_time_set(ULONG new_time);


/* Define error checking shells for API services.  These are only referenced by the 
   application.  */

UINT        _txe_timer_activate(TX_TIMER *timer_ptr);
UINT        _txe_timer_change(TX_TIMER *timer_ptr, ULONG initial_ticks, ULONG reschedule_ticks);
UINT        _txe_timer_create(TX_TIMER *timer_ptr, CHAR *name_ptr, 
                void (*expiration_function)(ULONG), ULONG expiration_input,
                ULONG initial_ticks, ULONG reschedule_ticks, UINT auto_activate, UINT timer_control_block_size);
UINT        _txe_timer_deactivate(TX_TIMER *timer_ptr);
UINT        _txe_timer_delete(TX_TIMER *timer_ptr);
UINT        _txe_timer_info_get(TX_TIMER *timer_ptr, CHAR **name, UINT *active, ULONG *remaining_ticks, 
                ULONG *reschedule_ticks, TX_TIMER **next_timer);


/* Timer management component data declarations follow.  */

/* Determine if the initialization function of this component is including
   this file.  If so, make the data definitions really happen.  Otherwise,
   make them extern so other functions in the component can access them.  */








/* Define the system clock value that is continually incremented by the 
   periodic timer interrupt processing.  */

extern ULONG     _tx_timer_system_clock;


/* Define the current time slice value.  If non-zero, a time-slice is active.
   Otherwise, the time_slice is not active.  */

extern ULONG     _tx_timer_time_slice;


/* Define the time-slice expiration flag.  This is used to indicate that a time-slice
   has happened.  */

extern UINT      _tx_timer_expired_time_slice;


/* Define the thread and application timer entry list.  This list provides a direct access
   method for insertion of times less than TX_TIMER_ENTRIES.  */

extern TX_TIMER_INTERNAL *_tx_timer_list[32];


/* Define the boundary pointers to the list.  These are setup to easily manage
   wrapping the list.  */

extern TX_TIMER_INTERNAL **_tx_timer_list_start;
extern TX_TIMER_INTERNAL **_tx_timer_list_end;


/* Define the current timer pointer in the list.  This pointer is moved sequentially
   through the timer list by the timer interrupt handler.  */

extern TX_TIMER_INTERNAL **_tx_timer_current_ptr;


/* Define the timer expiration flag.  This is used to indicate that a timer 
   has expired.  */

extern UINT      _tx_timer_expired;


/* Define the created timer list head pointer.  */

extern TX_TIMER *_tx_timer_created_ptr;


/* Define the created timer count.  */

extern ULONG     _tx_timer_created_count;




/* Define the timer thread's control block.  */

extern TX_THREAD _tx_timer_thread;


/* Define the variable that holds the timer thread's starting stack address.  */

extern void      *_tx_timer_stack_start;


/* Define the variable that holds the timer thread's stack size.  */

extern ULONG     _tx_timer_stack_size;


/* Define the variable that holds the timer thread's priority.  */

extern UINT      _tx_timer_priority;

# 210 "L:/PLT/os/threadx/inc/tx_timer.h"

# 219 "L:/PLT/os/threadx/inc/tx_timer.h"

# 251 "L:/PLT/os/threadx/inc/tx_timer.h"



# 24 "L:/PLT/os/osa/inc/osa_tx.h"
# 1 "L:/PLT/os/threadx/inc/tx_api.h"
/***************************************************************************
* Portions Copyright (c) 2008-2011 Marvell International, Ltd.
*               All Rights Reserved
*
*               Marvell Confidential
* ==========================================================================
*/


/**************************************************************************/
/*                                                                        */
/*            Copyright (c) 1996-2007 by Express Logic Inc.               */
/*                                                                        */
/*  This software is copyrighted by and is the sole property of Express   */
/*  Logic, Inc.  All rights, title, ownership, or other interests         */
/*  in the software remain the property of Express Logic, Inc.  This      */
/*  software may only be used in accordance with the corresponding        */
/*  license agreement.  Any unauthorized use, duplication, transmission,  */
/*  distribution, or disclosure of this software is expressly forbidden.  */
/*                                                                        */
/*  This Copyright notice may not be removed or modified without prior    */
/*  written consent of Express Logic, Inc.                                */
/*                                                                        */
/*  Express Logic, Inc. reserves the right to modify this software        */
/*  without notice.                                                       */
/*                                                                        */
/*  Express Logic, Inc.                     <EMAIL>         */
/*  11423 West Bernardo Court               http://www.expresslogic.com   */
/*  San Diego, CA  92127                                                  */
/*                                                                        */
/**************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */
/** ThreadX Component                                                     */
/**                                                                       */
/**   Application Interface (API)                                         */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/


/**************************************************************************/
/*                                                                        */
/*  APPLICATION INTERFACE DEFINITION                       RELEASE        */
/*                                                                        */
/*    tx_api.h                                            PORTABLE C      */
/*                                                           5.1          */
/*  AUTHOR                                                                */
/*                                                                        */
/*    William E. Lamie, Express Logic, Inc.                               */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file defines the basic Application Interface (API) to the      */
/*    high-performance ThreadX real-time kernel.  All service prototypes  */
/*    and data structure definitions are defined in this file.            */
/*    Please note that basic data type definitions and other architecture-*/
/*    specific information is contained in the file tx_port.h.            */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*  12-12-2005     William E. Lamie         Initial Version 5.0           */
/*  04-02-2007     William E. Lamie         Modified comment(s), and      */
/*                                            replaced UL constant        */
/*                                            modifier with ULONG cast,   */
/*                                            resulting in version 5.1    */
/*                                                                        */
/**************************************************************************/



# 1314 "L:/PLT/os/threadx/inc/tx_api.h"



# 25 "L:/PLT/os/osa/inc/osa_tx.h"
# 1 "L:/PLT/os/threadx/inc/tx_initialize.h"
/**************************************************************************/
/*                                                                        */
/*            Copyright (c) 1996-2007 by Express Logic Inc.               */
/*                                                                        */
/*  This software is copyrighted by and is the sole property of Express   */
/*  Logic, Inc.  All rights, title, ownership, or other interests         */
/*  in the software remain the property of Express Logic, Inc.  This      */
/*  software may only be used in accordance with the corresponding        */
/*  license agreement.  Any unauthorized use, duplication, transmission,  */
/*  distribution, or disclosure of this software is expressly forbidden.  */
/*                                                                        */
/*  This Copyright notice may not be removed or modified without prior    */
/*  written consent of Express Logic, Inc.                                */
/*                                                                        */
/*  Express Logic, Inc. reserves the right to modify this software        */
/*  without notice.                                                       */
/*                                                                        */
/*  Express Logic, Inc.                     <EMAIL>         */
/*  11423 West Bernardo Court               http://www.expresslogic.com   */
/*  San Diego, CA  92127                                                  */
/*                                                                        */
/**************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */
/** ThreadX Component                                                     */
/**                                                                       */
/**   Initialize                                                          */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/


/**************************************************************************/
/*                                                                        */
/*  COMPONENT DEFINITION                                   RELEASE        */
/*                                                                        */
/*    tx_initialize.h                                     PORTABLE C      */
/*                                                           5.1          */
/*  AUTHOR                                                                */
/*                                                                        */
/*    William E. Lamie, Express Logic, Inc.                               */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file defines the ThreadX initialization component, including   */
/*    data types and external references.  It is assumed that tx_api.h    */
/*    and tx_port.h have already been included.                           */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*  12-12-2005     William E. Lamie         Initial Version 5.0           */
/*  04-02-2007     William E. Lamie         Modified comment(s), and      */
/*                                            replaced UL constant        */
/*                                            modifier with ULONG cast,   */
/*                                            resulting in version 5.1    */
/*                                                                        */
/**************************************************************************/







/* Define constants that indicate initialization is in progress.  */






/* Define initialization function prototypes.  */

void        _tx_initialize_high_level(void);
void        _tx_initialize_kernel_enter(void);
void        _tx_initialize_kernel_setup(void);
void        _tx_initialize_low_level(void);
void        Application_Initialize(void *first_available_memory);


/* Initialization component data declarations follow.  */

/* Determine if the initialization function of this component is including
   this file.  If so, make the data definitions really happen.  Otherwise,
   make them extern so other functions in the component can access them.  */








/* Define the unused memory pointer.  The value of the first available
   memory address is placed in this variable in the low-level
   initialization function.  The content of this variable is passed
   to the application's system definition function.  */

extern void     *_tx_initialize_unused_memory;



# 26 "L:/PLT/os/osa/inc/osa_tx.h"
# 1 "L:/PLT/csw/BSP/inc/bsp_hisr.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

//
// bsp_hisr.h
//




# 30 "L:/PLT/csw/BSP/inc/bsp_hisr.h"



/*************************************************************
             CREATE  HISR
             ------------
The using of "Create" is very ambigutive:
- with macro OS_Create_HISR
- with procedure Os_Create_HISR
- with procedure Manitoba_Create_HISR
Go to implement Os_Create_HISR() instead of Manitoba_Create_HISR()
"Manitoba_Create_HISR" to be obsolete. Use it as macro only (may be also macroed in the global_types.h)
**/
typedef UINT8   		OS_STATUS ;





typedef	void*	OS_HISR;

void Os_Create_HISR(OS_HISR* hisr, char* name, void (*hisr_entry)(void), unsigned char priority);
INT32 Os_Activate_HISR(OS_HISR* hisr);
INT32 Os_Delete_HISR(OS_HISR* hisr);

# 62 "L:/PLT/csw/BSP/inc/bsp_hisr.h"
int OS_Current_Interrupt_Count(void);





BOOL OS_Current_Task_Status(void* pObject, UINT32* Status);



# 27 "L:/PLT/os/osa/inc/osa_tx.h"


/*
 * Data Types.
 */
typedef struct
{
    TX_SEMAPHORE    TxRef ;
    UINT          maximumCount ;
	UINT 			waitingMode;
	UINT			reserved;
}
OsaSemaphoreT ;

typedef struct
{
    TX_HISR     TxRef ;
    UINT      intSource ;
    void        (*fisrRoutine)(UINT32) ;
    void        (*sisrRoutine)(void) ;
	UINT		reserved;
}
OsaIsrT ;

typedef struct
{
	TX_QUEUE 	TxRef ;
	UINT 		waitingMode;
	UINT		reserved;
}
OsaMsgQT ;

typedef struct
{
    TX_MUTEX    TxRef ;
	UINT 			waitingMode;
	UINT			reserved;
}
OsaMutexT ;
/*
 * Defines.
 */




/*
 * Macros.
 */
# 83 "L:/PLT/os/osa/inc/osa_tx.h"

# 91 "L:/PLT/os/osa/inc/osa_tx.h"

/*
 * Data.
 */

/*
 * Functions.
 */
BOOL Osa_TranslateErrorCode( char *callerFuncName, UINT ErrorCode, OSA_STATUS *pOsaStatus ) ;




# 21 "L:/PLT/os/osa/inc/osa_old_api.h"

typedef void*   OSATaskRef;
typedef void*   OSAHISRRef;
typedef void*   OSASemaRef;
typedef void*   OSAMutexRef;
typedef void*   OSAMsgQRef;
typedef void*   OSAMailboxQRef;
typedef void*   OSAPoolRef;
typedef void*   OSATimerRef;
typedef void*   OSAFlagRef;
typedef void*	OSAPartitionPoolRef;
/* Remain for backwards compatibility */
typedef void*   OSTaskRef;
typedef void*   OSSemaRef;
typedef void*   OSMutexRef;
typedef void*   OSMsgQRef;
typedef void*   OSMailboxQRef;
typedef void*   OSPoolRef;
typedef void*   OSTimerRef;
typedef void*   OSFlagRef;
typedef UINT8   OS_STATUS ;

/*****************************************************************************
 * OSA Function Prototypes
 ****************************************************************************/
# 61 "L:/PLT/os/osa/inc/osa_old_api.h"







/*========================================================================
 *  OSA Initialization:
 *
 *  Initializes OSA internal structures, tables, and OS specific services.
 *
 *========================================================================*/





# 85 "L:/PLT/os/osa/inc/osa_old_api.h"

/*========================================================================
 *  OSA Task Management:
 *========================================================================*/
 extern OSA_STATUS OSATaskCreate(
    OSATaskRef* taskRef,        /* OS task reference                       */
    void*       stackPtr,       /* pointer to start of task stack area     */
    UINT32      stackSize,      /* number of bytes in task stack area      */
    UINT8       priority,       /* task priority 0 - 31                    */
    CHAR        *taskName,      /* task name                               */
    void        (*taskStart)(void*), /* pointer to task entry point        */
    void*       argv            /* task entry argument pointer             */
 );

 extern OSA_STATUS OSATaskCreateEx(
    OSATaskRef* taskRef,        /* OS task reference                       */
    void*       stackPtr,       /* pointer to start of task stack area     */
    UINT32      stackSize,      /* number of bytes in task stack area      */
    UINT8       priority,       /* task priority 0 - 31                    */
    CHAR        *taskName,      /* task name                               */
    void        (*taskStart)(unsigned,void*), /* pointer to task entry point        */
    void*       argv            /* task entry argument pointer             */
 );




# 124 "L:/PLT/os/osa/inc/osa_old_api.h"

# 157 "L:/PLT/os/osa/inc/osa_old_api.h"

/*========================================================================
 *  OSA Semaphore Management
 *========================================================================*/
 extern OSA_STATUS OSASemaphoreCreate (
    OSSemaRef   *semaRef,       /* OS semaphore reference                     */
    UINT32      initialCount,   /* initial count of the semaphore             */
    UINT8       waitingMode     /* mode of tasks waiting OS_FIFO, OS_PRIORITY */
 );







//#define     OSASemaphoreCheck(OsaRef)                   OsaSemaphoreCheck(OsaRef)


# 193 "L:/PLT/os/osa/inc/osa_old_api.h"

/*========================================================================
 *  OSA Mutex Management
 *========================================================================*/
 extern OSA_STATUS OSAMutexCreate (
    OSMutexRef  *mutexRef,       /* OS mutex reference                         */
    UINT8       waitingMode      /* mode of tasks waiting OS_FIFO, OS_PRIORITY */
 );







# 220 "L:/PLT/os/osa/inc/osa_old_api.h"

/*========================================================================
 *  OSA Interrupt Control
 *========================================================================*/
 extern OSA_STATUS OSAIsrCreate(
    UINT32 isrNum,              /* interrupt to attach routine to          */
    void (*fisrRoutine)(UINT32),/* first level ISR routine to be called    */
    void (*sisrRoutine)(void)   /* second level ISR routine to be called   */
 );




extern const OsaRefT *OsaIsrTable ;





# 245 "L:/PLT/os/osa/inc/osa_old_api.h"

/*===========================================================================
 *  OSA Real-Time Access:
 *=========================================================================*/






# 262 "L:/PLT/os/osa/inc/osa_old_api.h"

/*========================================================================
 *  OSA Sys Context info
 *========================================================================*/


# 274 "L:/PLT/os/osa/inc/osa_old_api.h"

# 291 "L:/PLT/os/osa/inc/osa_old_api.h"

 /*========================================================================
  *  OSA Message Passing
  *========================================================================*/
 extern OSA_STATUS OSAMsgQCreate(
    OSMsgQRef   *msgQRef,       /* OS message queue reference              */

    char        *queueName,     /* name of message queue                   */

    UINT32      maxSize,        /* max message size the queue supports     */
    UINT32      maxNumber,      /* max # of messages in the queue          */
    UINT8       waitingMode     /* mode of tasks waiting OS_FIFO, OS_PRIORITY */
 );

 extern OSA_STATUS OSAMsgQSend (
    OSMsgQRef   msgQRef,        /* message queue reference                 */
    UINT32      size,           /* size of the message                     */
    UINT8       *msgPtr,        /* start address of the data to be sent    */
    UINT32      timeout         /* OS_SUSPEND, OS_NO_SUSPEND, or timeout   */
 );

 extern OSA_STATUS OSAMsgQRecv (
    OSMsgQRef   msgQRef,        /* message queue reference                 */
    UINT8       *recvMsg,       /* pointer to the message received         */
    UINT32      size,           /* size of the message                     */
    UINT32      timeout         /* OS_SUSPEND, OS_NO_SUSPEND, or timeout   */
 );






# 334 "L:/PLT/os/osa/inc/osa_old_api.h"

 /*========================================================================
  *  OSA Mailboxes
  *========================================================================*/
 extern OSA_STATUS OSAMailboxQCreate (
    OSMailboxQRef   *mboxQRef,      /* OS mailbox queue reference              */

    char            *queueName,     /* name of mailbox queue                   */

    UINT32          maxNumber,      /* max # of messages in the queue          */
    UINT8           waitingMode     /* mode of tasks waiting OS_FIFO, OS_PRIORITY */
 );








# 376 "L:/PLT/os/osa/inc/osa_old_api.h"

/*========================================================================
 *  OSA Event Management:
 *========================================================================*/

 extern OSA_STATUS OSAFlagWait(
    OSFlagRef   flagRef,        /* OS reference to the flag                */
    UINT32      mask,           /* flag mask to wait for                   */
    UINT32      operation,      /* OSA_FLAG_AND, OSA_FLAG_AND_CLEAR,       */
                                /* OSA_FLAG_OR, OSA_FLAG_OR_CLEAR          */
    UINT32      *flags,         /* Current value of all flags              */
    UINT32      timeout         /* OS_SUSPEND, OS_NO_SUSPEND, or timeout   */
 );







# 411 "L:/PLT/os/osa/inc/osa_old_api.h"

/*========================================================================
 *  OSA Timer Management:
 *========================================================================*/
 extern OSA_STATUS OSATimerStart(
    OSTimerRef  timerRef,       /* OS supplied timer reference             */
    UINT32      initialTime,    /* initial expiration time in ms           */
    UINT32      rescheduleTime, /* timer period after initial expiration   */
    void        (*callBackRoutine)(UINT32), /* timer call-back routine     */
    UINT32      timerArgc       /* argument to be passed to call-back on expiration */
 );



typedef OsaTimerStatusParamsT OSATimerStatus ;






# 450 "L:/PLT/os/osa/inc/osa_old_api.h"

/*========================================================================
 *  OSA Memory Heap Access
 *
 *  Allocating Memory -
 *
 *  Deallocating Memory -
 *
 *========================================================================*/
 extern OSA_STATUS OSAMemPoolCreate (
    OSPoolRef *poolRef,         /* OS assigned reference to the pool      */
    UINT32    poolType,         /* OSA_FIXED or OS_VARIABLE                */
    UINT8*    poolBase,         /* pointer to start of pool memory        */
    UINT32    poolSize,         /* number of bytes in the memory pool     */
    UINT32    partitionSize,    /* size of partitions in fixed pools      */
    UINT8     waitingMode       /* mode of tasks waiting OS_FIFO, OS_PRIORITY */
 );

 extern OSA_STATUS OSAMemPoolAlloc (
    OSPoolRef poolRef,          /* OS reference to the pool to be used    */
    UINT32    size,             /* number of bytes to be allocated        */
    void**    mem,              /* pointer to start of allocated memory   */
    UINT32    timeout           /* OS_SUSPEND, OS_NO_SUSPEND, or timeout  */
 );

 extern OSA_STATUS OSAMemPoolFree (
    OSPoolRef poolRef,          /* OS reference to the pool to be used    */
    void*     mem               /* pointer to start of memory to be freed */
 );







# 500 "L:/PLT/os/osa/inc/osa_old_api.h"







# 528 "L:/PLT/os/osa/inc/osa_old_api.h"











/* Remap old names to new ones to remain backwards compatibility with old names.
 */
# 557 "L:/PLT/os/osa/inc/osa_old_api.h"






# 616 "L:/PLT/os/osa/inc/osa_old_api.h"






# 767 "L:/PLT/os/osa/inc/osa.h"
# 1 "L:/PLT/os/osa/inc/osa_utils.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : osa_utils.h
Description : 

Copyright (c) 2001 Intel CCD. All Rights Reserved
=========================================================================== */




 extern UINT32 OsaTaskList(OSATaskRef *ptrList,UINT32 maximum_pointers);
 extern UINT32 OsaHISRList(OSAHISRRef *ptrList,UINT32 maximum_pointers);
 extern UINT32 OSATaskIsValid(OSATaskRef OsaRef);
 extern UINT32 OSATaskIsDead(OSATaskRef OsaRef);
 extern void* 	OSATaskGetStackStart(OSATaskRef taskRef);
 extern UINT32		OSATaskGetStackSize(OSATaskRef taskRef);
 extern void 		(*OSATaskGetEntry(OSATaskRef taskRef))(void *);
 extern void		OSATaskSetEntry(OSATaskRef OsaRef, void (*entry)(void *));
 extern UINT32		OSATaskGetEntryParam(OSATaskRef hisrRef);
 extern char*		OSATaskGetName(OSATaskRef taskRef);
		

 extern UINT32 		OSATaskGetSysParam1(OSATaskRef taskRef);
 extern void 		OSATaskSetSysParam1(OSATaskRef taskRef, UINT32 value);
 extern UINT32 		OSATaskGetSysParam3(OSATaskRef taskRef);
 extern void 		OSATaskSetSysParam3(OSATaskRef taskRef, UINT32 value);
 extern UINT32 		OSATaskGetAppParam1(OSATaskRef taskRef);



 extern void*		OSAHISRGetStackStart(OSATaskRef hisrRef);
 extern UINT32		OSAHISRGetStackSize(OSATaskRef hisrRef);
 extern void 		(*OSAHISRGetEntry(OSATaskRef hisrRef))(void);
 extern char*		OSAHISRGetName(OSATaskRef hisrRef);

 extern UINT32 		OSAHISRGetSysParam1(OSATaskRef hisrRef);
 extern void 		OSAHISRSetSysParam1(OSATaskRef hisrRef, UINT32 value);
 extern UINT32 		OSAHISRGetAppParam1(OSATaskRef hisrRef);
 extern void 		OSAHISRSetAppParam1(OSATaskRef hisrRef, UINT32 value);

 extern UINT32			OSAPartitionPoolGetAllocated(OSAPartitionPoolRef pool_ptr);
 extern char*			OSAPartitionPoolGetName(OSAPartitionPoolRef PartitionPoolRef);
 extern UINT32			OSAPartitionPoolGetAvailble(OSAPartitionPoolRef PartitionPoolRef);
 extern UINT32			OSAPartitionPoolGetPartitionSize(OSAPartitionPoolRef PartitionPoolRef);
 extern BOOL			OSAPartitionInUse( void* BlockPtr, void* PoolPtr );
 extern OSAPartitionPoolRef 	OSAPartitionGetPoolPtr( void* BlockPtr );
 extern OSA_STATUS 		OSAMsgQFrontSend(OSAMsgQRef queue_ptr, void *message,
                                        UINT32 size, UINT32 suspend);


# 768 "L:/PLT/os/osa/inc/osa.h"
# 769 "L:/PLT/os/osa/inc/osa.h"
# 1 "L:/PLT/os/nu_xscale/inc/nucleus.h"
/*************************************************************************/
/*                                                                       */
/*               Copyright Mentor Graphics Corporation 2004              */
/*                         All Rights Reserved.                          */
/*                                                                       */
/* THIS WORK CONTAINS TRADE SECRET AND PROPRIETARY INFORMATION WHICH IS  */
/* THE PROPERTY OF MENTOR GRAPHICS CORPORATION OR ITS LICENSORS AND IS   */
/* SUBJECT TO LICENSE TERMS.                                             */
/*                                                                       */
/*************************************************************************/

/*************************************************************************/
/*                                                                       */
/* FILE NAME                                           VERSION           */
/*                                                                       */
/*      nucleus.h                                   Nucleus PLUS 1.15    */
/*                                                                       */
/* COMPONENT                                                             */
/*                                                                       */
/*      System Constants                                                 */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This file contains system constants common to both the           */
/*      application and the actual Nucleus PLUS components.  This file   */
/*      also contains data structure definitions that hide internal      */
/*      information from the application.                                */
/*                                                                       */
/*                                                                       */
/* DATA STRUCTURES                                                       */
/*                                                                       */
/*      NU_DRIVER                           I/O Driver control block     */
/*      NU_EVENT_GROUP                      Event group control block    */
/*      NU_HISR                             HISR control block           */
/*      NU_MAILBOX                          Mailbox control block        */
/*      NU_MEMORY_POOL                      Memory Pool control block    */
/*      NU_PARTITION_POOL                   Partition Pool control block */
/*      NU_PIPE                             Pipe control block           */
/*      NU_QUEUE                            Queue control block          */
/*      NU_SEMAPHORE                        Semaphore control block      */
/*      NU_TASK                             Task control block           */
/*      NU_TIMER                            Timer control block          */
/*      NU_PROTECT                          Protection structure         */
/*                                                                       */
/* FUNCTIONS                                                             */
/*                                                                       */
/*      None                                                             */
/*                                                                       */
/* DEPENDENCIES                                                          */
/*                                                                       */
/*      None                                                             */
/*                                                                       */
/*************************************************************************/

/* Check to see if this file has been included already.  */







# 65 "L:/PLT/os/nu_xscale/inc/nucleus.h"












/* Define standard data types.  These definitions allow Nucleus PLUS to
   perform in the same manner on different target platforms.  */

typedef unsigned long           UNSIGNED;
typedef long                    SIGNED;
typedef unsigned char           DATA_ELEMENT;
typedef DATA_ELEMENT            OPTION;
typedef DATA_ELEMENT            BOOLEAN;
typedef int                     STATUS;
typedef unsigned char           UNSIGNED_CHAR;
typedef unsigned int            UNSIGNED_INT;
typedef int                     INT;
typedef unsigned long *         UNSIGNED_PTR;
typedef unsigned char *         BYTE_PTR;


# 106 "L:/PLT/os/nu_xscale/inc/nucleus.h"


/* Define register defines.  R1, R2, R3, and R4 are used in the Nucleus PLUS
   source code in front of variables that are referenced often.  In some
   ports, defining them as "register" will improve performance.  */






# 1018 "L:/PLT/os/nu_xscale/inc/nucleus.h"
/* Define Supervisor and User mode functions */


# 1027 "L:/PLT/os/nu_xscale/inc/nucleus.h"







/* Nucleus PLUS Profiling Support */



















# 770 "L:/PLT/os/osa/inc/osa.h"
# 1 "L:/PLT/os/osa/inc/osa_internals.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : osa_internals.h
Description : Definition of OSA Software Layer data types that are internal in OSA
                and are OS independent.

Notes       :

=========================================================================== */



/*
 * Special OS Definitions for common files.
 */
# 28 "L:/PLT/os/osa/inc/osa_internals.h"

# 35 "L:/PLT/os/osa/inc/osa_internals.h"

/*
 * ASSERT Macro.
 */
# 54 "L:/PLT/os/osa/inc/osa_internals.h"

/*
 * Defines.
 */

/*
 * Macros.
 */
# 74 "L:/PLT/os/osa/inc/osa_internals.h"

# 81 "L:/PLT/os/osa/inc/osa_internals.h"

/*
 * Functions.
 */
void        Osa_Init( void ) ;
OSA_STATUS  OsaMem_InitPools( void ) ;

# 771 "L:/PLT/os/osa/inc/osa.h"



# 784 "L:/PLT/os/osa/inc/osa.h"



# 788 "L:/PLT/os/osa/inc/osa.h"
# 1 "L:/PLT/os/threadx/inc/tx_hisr.h"


# 27 "L:/PLT/os/threadx/inc/tx_hisr.h"

# 789 "L:/PLT/os/osa/inc/osa.h"
# 1 "L:/PLT/os/threadx/inc/tx_api.h"
/***************************************************************************
* Portions Copyright (c) 2008-2011 Marvell International, Ltd.
*               All Rights Reserved
*
*               Marvell Confidential
* ==========================================================================
*/


/**************************************************************************/
/*                                                                        */
/*            Copyright (c) 1996-2007 by Express Logic Inc.               */
/*                                                                        */
/*  This software is copyrighted by and is the sole property of Express   */
/*  Logic, Inc.  All rights, title, ownership, or other interests         */
/*  in the software remain the property of Express Logic, Inc.  This      */
/*  software may only be used in accordance with the corresponding        */
/*  license agreement.  Any unauthorized use, duplication, transmission,  */
/*  distribution, or disclosure of this software is expressly forbidden.  */
/*                                                                        */
/*  This Copyright notice may not be removed or modified without prior    */
/*  written consent of Express Logic, Inc.                                */
/*                                                                        */
/*  Express Logic, Inc. reserves the right to modify this software        */
/*  without notice.                                                       */
/*                                                                        */
/*  Express Logic, Inc.                     <EMAIL>         */
/*  11423 West Bernardo Court               http://www.expresslogic.com   */
/*  San Diego, CA  92127                                                  */
/*                                                                        */
/**************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */
/** ThreadX Component                                                     */
/**                                                                       */
/**   Application Interface (API)                                         */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/


/**************************************************************************/
/*                                                                        */
/*  APPLICATION INTERFACE DEFINITION                       RELEASE        */
/*                                                                        */
/*    tx_api.h                                            PORTABLE C      */
/*                                                           5.1          */
/*  AUTHOR                                                                */
/*                                                                        */
/*    William E. Lamie, Express Logic, Inc.                               */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file defines the basic Application Interface (API) to the      */
/*    high-performance ThreadX real-time kernel.  All service prototypes  */
/*    and data structure definitions are defined in this file.            */
/*    Please note that basic data type definitions and other architecture-*/
/*    specific information is contained in the file tx_port.h.            */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*  12-12-2005     William E. Lamie         Initial Version 5.0           */
/*  04-02-2007     William E. Lamie         Modified comment(s), and      */
/*                                            replaced UL constant        */
/*                                            modifier with ULONG cast,   */
/*                                            resulting in version 5.1    */
/*                                                                        */
/**************************************************************************/



# 1314 "L:/PLT/os/threadx/inc/tx_api.h"



# 790 "L:/PLT/os/osa/inc/osa.h"
# 1 "L:/PLT/os/threadx/inc/tx_initialize.h"
/**************************************************************************/
/*                                                                        */
/*            Copyright (c) 1996-2007 by Express Logic Inc.               */
/*                                                                        */
/*  This software is copyrighted by and is the sole property of Express   */
/*  Logic, Inc.  All rights, title, ownership, or other interests         */
/*  in the software remain the property of Express Logic, Inc.  This      */
/*  software may only be used in accordance with the corresponding        */
/*  license agreement.  Any unauthorized use, duplication, transmission,  */
/*  distribution, or disclosure of this software is expressly forbidden.  */
/*                                                                        */
/*  This Copyright notice may not be removed or modified without prior    */
/*  written consent of Express Logic, Inc.                                */
/*                                                                        */
/*  Express Logic, Inc. reserves the right to modify this software        */
/*  without notice.                                                       */
/*                                                                        */
/*  Express Logic, Inc.                     <EMAIL>         */
/*  11423 West Bernardo Court               http://www.expresslogic.com   */
/*  San Diego, CA  92127                                                  */
/*                                                                        */
/**************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */
/** ThreadX Component                                                     */
/**                                                                       */
/**   Initialize                                                          */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/


/**************************************************************************/
/*                                                                        */
/*  COMPONENT DEFINITION                                   RELEASE        */
/*                                                                        */
/*    tx_initialize.h                                     PORTABLE C      */
/*                                                           5.1          */
/*  AUTHOR                                                                */
/*                                                                        */
/*    William E. Lamie, Express Logic, Inc.                               */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file defines the ThreadX initialization component, including   */
/*    data types and external references.  It is assumed that tx_api.h    */
/*    and tx_port.h have already been included.                           */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*  12-12-2005     William E. Lamie         Initial Version 5.0           */
/*  04-02-2007     William E. Lamie         Modified comment(s), and      */
/*                                            replaced UL constant        */
/*                                            modifier with ULONG cast,   */
/*                                            resulting in version 5.1    */
/*                                                                        */
/**************************************************************************/



# 107 "L:/PLT/os/threadx/inc/tx_initialize.h"

# 791 "L:/PLT/os/osa/inc/osa.h"
# 1 "L:/PLT/os/osa/inc/osa_um_extr.h"
/*************************************************************************/
/*                                                                       */
/*               Copyright Mentor Graphics Corporation 2012              */
/*                         All Rights Reserved.                          */
/*                                                                       */
/* THIS WORK CONTAINS TRADE SECRET AND PROPRIETARY INFORMATION WHICH IS  */
/* THE PROPERTY OF MENTOR GRAPHICS CORPORATION OR ITS LICENSORS AND IS   */
/* SUBJECT TO LICENSE TERMS.                                             */
/*                                                                       */
/*************************************************************************/

/*************************************************************************/
/*                                                                       */
/* FILE NAME                                               VERSION       */
/*                                                                       */
/*      dm_extr.h                                      Nucleus PLUS 1.15 */
/*                                                                       */
/* COMPONENT                                                             */
/*                                                                       */
/*      UM - USB Ring Memory Management                                   */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This file contains function prototypes of all functions          */
/*      accessible to other components.                                  */
/*                                                                       */
/* DATA STRUCTURES                                                       */
/*                                                                       */
/*      None                                                             */
/*                                                                       */
/* DEPENDENCIES                                                          */
/*                                                                       */
/*      um_defs.h                          USB Ring Management constants */
/*                                                                       */
/*************************************************************************/
# 1 "L:/PLT/os/osa/inc/osa_um_defs.h"
/*************************************************************************/
/*                                                                       */
/*               Copyright Mentor Graphics Corporation 2012             */
/*                         All Rights Reserved.                          */
/*                                                                       */
/* THIS WORK CONTAINS TRADE SECRET AND PROPRIETARY INFORMATION WHICH IS  */
/* THE PROPERTY OF MENTOR GRAPHICS CORPORATION OR ITS LICENSORS AND IS   */
/* SUBJECT TO LICENSE TERMS.                                             */
/*                                                                       */
/*************************************************************************/

/*************************************************************************/
/*                                                                       */
/* FILE NAME                                               VERSION       */
/*                                                                       */
/*      um_defs.h                                      Nucleus PLUS 1.15 */
/*                                                                       */
/* COMPONENT                                                             */
/*                                                                       */
/*      UM - USB Ring Memory Management                                   */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This file contains data structure definitions and constants for  */
/*      the USB Ring Memory component.                                    */
/*                                                                       */
/* DATA STRUCTURES                                                       */
/*                                                                       */
/*      DM_PCB                              Dynamic Pool control block   */
/*      DM_HEADER                           Header of each memory block  */
/*      DM_SUSPEND                          Memory suspension block      */
/*                                                                       */
/* DEPENDENCIES                                                          */
/*                                                                       */
/*      cs_defs.h                           Common service definitions   */
/*      tc_defs.h                           Thread Control definitions   */
/*                                                                       */
/*************************************************************************/





/* Check to see if the file has been included already.  */






/* Define the Dynamic Pool Control Block data type.  */
typedef struct UM_PCB_STRUCT
{
    volatile struct UM_BLOCK_HDR_STRUCT* um_hdr;
    UINT16 um_block_size;
    UINT16 um_block_num;

    UINT16 um_waiting_lwm;
    UINT16 um_lwm;

    DATA_ELEMENT *um_data_ptr;

    void (*um_tx_notify)(struct UM_PCB_STRUCT* um_buffer);
    void (*um_resume_notify)(struct UM_PCB_STRUCT* um_buffer);
	void (*um_event_handler)(struct UM_PCB_STRUCT* um_buffer, int evt);
} UM_PCB;

/* Define the header structure that is in front of each memory block.  */
typedef struct UM_BLOCK_HDR_STRUCT
{
    volatile UINT16 um_put_index;
    volatile UINT16 um_get_index;

    DATA_ELEMENT um_padding[32 - 4];
}UM_BLOCK_HDR;

/* Define the block structure that describes each memory block.  */
typedef struct UM_BLOCK_STRUCT
{
    volatile UINT16 um_length;
    volatile UINT16 um_ref;
}UM_BLOCK;

/* Define the header structure that is in front of each memory block.  */
typedef struct UMUP_BLOCK_HDR_STRUCT
{
    volatile UINT16 um_block_put_index;
    volatile UINT16 um_block_get_index;
	volatile UINT16 um_buffer_put_index;
	volatile UINT16 um_buffer_get_index;
    DATA_ELEMENT um_padding[32 - 8];
}UMUP_BLOCK_HDR;
/* Define the block structure that describes each memory block.  */
typedef struct UMUP_BLOCK_STRUCT
{
    UINT16 um_length;
    UINT16 um_ref;
	UINT16 umup_ref;

    DATA_ELEMENT um_padding[32 - 6];
}UMUP_BLOCK;

/* Define the Dynamic Pool Control Block data type for UP direction.  */
typedef struct UMUP_PCB_STRUCT
{
    volatile UMUP_BLOCK_HDR* um_hdr;
    UINT16 um_block_size;
    UINT16 um_block_num;
	

    UINT16 um_waiting_lwm;
    UINT16 um_lwm;

	UINT16 um_packet_headersize;
	UINT16 um_block_by_application;
	
    DATA_ELEMENT *um_data_ptr;

    void (*um_rx_notify)(struct UMUP_PCB_STRUCT* um_buffer);
    void (*um_resume_notify)(struct UMUP_PCB_STRUCT* um_buffer);
} UMUP_PCB;




# 38 "L:/PLT/os/osa/inc/osa_um_extr.h"




/* Check to see if the file has been included already.  */




# 56 "L:/PLT/os/osa/inc/osa_um_extr.h"













/*  Initialization functions.  */
STATUS UMC_Data_Buffer_Init(UM_PCB *buf, DATA_ELEMENT *hdr, DATA_ELEMENT *ptr, 
    					  UINT16 block_size, UINT16 block_num);

/* Core processing functions.  */
DATA_ELEMENT* UMC_Data_Buffer_Get(UM_PCB *buf, UINT16 size, OPTION newblock);
void UMC_Data_Buffer_Put(UM_PCB *buf, DATA_ELEMENT *ptr);

UM_BLOCK* UMC_Data_Buffer_Get_Block(UM_PCB *buf);
void UMC_Data_Buffer_Put_Block(UM_PCB *buf, UM_BLOCK *block);

/* Supplemental service routines */

/* Information retrieval functions.  */
UNSIGNED UMC_Data_Buffer_Get_Size(UM_PCB *buf);

STATUS UMUPC_Data_Buffer_Init(UMUP_PCB *buf, DATA_ELEMENT *hdr, DATA_ELEMENT *ptr, 
					UINT16 block_size, UINT16 block_num);

UMUP_BLOCK* UMUPC_Data_Buffer_Get_Block(UMUP_PCB *buf);

STATUS UMUPC_Data_Buffer_Put_Block(UMUP_PCB *buf, UMUP_BLOCK *block, UINT16 size);
DATA_ELEMENT *UMUPC_Data_Buffer_Get(UMUP_PCB *buf, UINT16* size);

void UMUPC_Data_Buffer_Put(UMUP_PCB *buf, DATA_ELEMENT *ptr);

BOOL Umc_Is_Reach_Hwm(UM_PCB *buf);








# 792 "L:/PLT/os/osa/inc/osa.h"

extern volatile unsigned long	_tx_thread_system_state;

//#define OS_INTERRUTPT_COUNT				(_tx_thread_system_state)




# 814 "L:/PLT/os/osa/inc/osa.h"







# 3 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.c"
# 1 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.h"
/**
 *  Copyright (C) 2006-2016, ARM Limited, All Rights Reserved
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  This file is part of mbed TLS (https://tls.mbed.org)
 */





# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"
/**
 * \file mbedtls/build_info.h
 *
 * \brief Build-time configuration info
 *
 *  Include this file if you need to depend on the
 *  configuration options defined in mbedtls_config.h or MBEDTLS_CONFIG_FILE
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */




# 25 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

/*
 * This set of compile-time defines can be used to determine the version number
 * of the Mbed TLS library used. Run-time variables for the same can be found in
 * version.h
 */

/**
 * The version number x.y.z is split into three parts.
 * Major, Minor, Patchlevel
 */




/**
 * The single version number has the following structure:
 *    MMNNPP00
 *    Major version | Minor version | Patch version
 */




/* Macros for build-time platform detection */





























/* This is defined if the architecture is Armv8-A, or higher */
# 94 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

# 102 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"





/* Define `inline` on some non-C99-compliant compilers. */





# 119 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

/* X.509, TLS and non-PSA crypto configuration */
# 1 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"
/**
 * \file asros_mbedtls_config.h
 *
 * \brief Minimal configuration of features that do not require an entropy source
 */
/*
 *  Copyright (C) 2016, ARM Limited, All Rights Reserved
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  This file is part of mbed TLS (https://tls.mbed.org)
 */
/*
 * Minimal configuration of features that do not require an entropy source
 * Distinguishing reatures:
 * - no entropy module
 * - no TLS protocol implementation available due to absence of an entropy
 *   source
 *
 * See README.txt for usage instructions.
 */





typedef unsigned int size_t;

# 50 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"








//#define MBEDTLS_CONFIG_TLS_DEBUG





/*mbedtls macro sync define begin:*/
/*mbedtls macro sync define begin:*/
/*mbedtls macro sync define begin:*/

/* System support */










/* mbed TLS feature support */
# 88 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"
//#define MBEDTLS_ECDSA_DETERMINISTIC // closed for reduce code size



//#define MBEDTLS_SELF_TEST // closed for reduce code size
//#define MBEDTLS_VERSION_FEATURES //closed for reduce code size



/* mbed TLS modules */


//#define MBEDTLS_ASN1_WRITE_C // closed for reduce code size











//#define MBEDTLS_ERROR_C //closed for reduce code size

//#define MBEDTLS_HMAC_DRBG_C // closed for reduce code size
# 127 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"
//#define MBEDTLS_VERSION_C // closed for reduce code size
# 141 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"
//#define MBEDTLS_SSL_SRV_C
//#define MBEDTLS_CERTS_C // need open?
//#define MBEDTLS_SSL_SESSION_TICKETS















/*2022-01-18, add option, for redmine#51878*/
# 166 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"

/**
 * \def MBEDTLS_X509_ALLOW_UNSUPPORTED_CRITICAL_EXTENSION
 *
 * If set, the X509 parser will not break-off when parsing an X509 certificate
 * and encountering an unknown critical extension.
 *
 * \warning Depending on your PKI use, enabling this can be a security risk!
 *
 * Uncomment to prevent an error.
 */




# 204 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"

/*mbedtls macro sync define end:*/
/*mbedtls macro sync define end:*/
/*mbedtls macro sync define end:*/



# 222 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"

/* Miscellaneous options */
//#define MBEDTLS_AES_ROM_TABLES

# 1 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform_alt.h"
/*
  *  platform_alt.h
  *
  *  Copyright (C) 2018, Arm Limited, All Rights Reserved
  *  SPDX-License-Identifier: Apache-2.0
  *
  *  Licensed under the Apache License, Version 2.0 (the "License"); you may
  *  not use this file except in compliance with the License.
  *  You may obtain a copy of the License at
  *
  *  http://www.apache.org/licenses/LICENSE-2.0
  *
  *  Unless required by applicable law or agreed to in writing, software
  *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  *  See the License for the specific language governing permissions and
  *  limitations under the License.
  *
  */




# 1 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.h"
/**
 *  Copyright (C) 2006-2016, ARM Limited, All Rights Reserved
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  This file is part of mbed TLS (https://tls.mbed.org)
 */


# 25 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform_alt.h"







# 74 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform_alt.h"

# 227 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"

# 125 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"








/* Target and application specific configurations
 *
 * Allow user to override any previous default.
 *
 */




/* PSA crypto configuration */
# 153 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

/* Indicate that all configuration files have been read.
 * It is now time to adjust the configuration (follow through on dependencies,
 * make PSA and legacy crypto consistent, etc.).
 */


/* Auto-enable MBEDTLS_CTR_DRBG_USE_128_BIT_KEY if
 * MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH and MBEDTLS_CTR_DRBG_C defined
 * to ensure a 128-bit key size in CTR_DRBG.
 */




/* Auto-enable MBEDTLS_MD_C if needed by a module that didn't require it
 * in a previous release, to ensure backwards compatibility.
 */




/* PSA crypto specific configuration options
 * - If config_psa.h reads a configuration option in preprocessor directive,
 *   this symbol should be set before its inclusion. (e.g. MBEDTLS_MD_C)
 * - If config_psa.h writes a configuration option in conditional directive,
 *   this symbol should be consulted after its inclusion.
 *   (e.g. MBEDTLS_MD_LIGHT)
 */






# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"
/**
 * \file mbedtls/config_adjust_legacy_crypto.h
 * \brief Adjust legacy configuration configuration
 *
 * This is an internal header. Do not include it directly.
 *
 * Automatically enable certain dependencies. Generally, MBEDTLS_xxx
 * configurations need to be explicitly enabled by the user: enabling
 * MBEDTLS_xxx_A but not MBEDTLS_xxx_B when A requires B results in a
 * compilation error. However, we do automatically enable certain options
 * in some circumstances. One case is if MBEDTLS_xxx_B is an internal option
 * used to identify parts of a module that are used by other module, and we
 * don't want to make the symbol MBEDTLS_xxx_B part of the public API.
 * Another case is if A didn't depend on B in earlier versions, and we
 * want to use B in A but we need to preserve backward compatibility with
 * configurations that explicitly activate MBEDTLS_xxx_A but not
 * MBEDTLS_xxx_B.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */




# 34 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Ideally, we'd set those as defaults in mbedtls_config.h, but
 * putting an #ifdef _WIN32 in mbedtls_config.h would confuse config.py.
 *
 * So, adjust it here.
 * Not related to crypto, but this is the bottom of the stack. */
# 50 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Auto-enable CIPHER_C when any of the unauthenticated ciphers is builtin
 * in PSA. */
# 65 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Auto-enable MBEDTLS_MD_LIGHT based on MBEDTLS_MD_C.
 * This allows checking for MD_LIGHT rather than MD_LIGHT || MD_C.
 */




/* Auto-enable MBEDTLS_MD_LIGHT if needed by a module that didn't require it
 * in a previous release, to ensure backwards compatibility.
 */
# 87 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"


/*
 * - MBEDTLS_MD_CAN_xxx is defined if the md module can perform xxx.
 * - MBEDTLS_MD_xxx_VIA_PSA is defined if the md module may perform xxx via PSA
 *   (see below).
 * - MBEDTLS_MD_SOME_PSA is defined if at least one algorithm may be performed
 *   via PSA (see below).
 * - MBEDTLS_MD_SOME_LEGACY is defined if at least one algorithm may be performed
 *   via a direct legacy call (see below).
 *
 * The md module performs an algorithm via PSA if there is a PSA hash
 * accelerator and the PSA driver subsytem is initialized at the time the
 * operation is started, and makes a direct legacy call otherwise.
 */

/* PSA accelerated implementations */
# 162 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Built-in implementations */
# 199 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"



/* BLOCK_CIPHER module can dispatch to PSA when:
 * - PSA is enabled and drivers have been initialized
 * - desired key type is supported on the PSA side
 * If the above conditions are not met, but the legacy support is enabled, then
 * BLOCK_CIPHER will dynamically fallback to it.
 *
 * In case BLOCK_CIPHER is defined (see below) the following symbols/helpers
 * can be used to define its capabilities:
 * - MBEDTLS_BLOCK_CIPHER_SOME_PSA: there is at least 1 key type between AES,
 *   ARIA and Camellia which is supported through a driver;
 * - MBEDTLS_BLOCK_CIPHER_xxx_VIA_PSA: xxx key type is supported through a
 *   driver;
 * - MBEDTLS_BLOCK_CIPHER_xxx_VIA_LEGACY: xxx key type is supported through
 *   a legacy module (i.e. MBEDTLS_xxx_C)
 */
# 231 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

# 241 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Helpers to state that BLOCK_CIPHER module supports AES, ARIA and/or Camellia
 * block ciphers via either PSA or legacy. */
# 256 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* GCM_C and CCM_C can either depend on (in order of preference) BLOCK_CIPHER_C
 * or CIPHER_C. The former is auto-enabled when:
 * - CIPHER_C is not defined, which is also the legacy solution;
 * - BLOCK_CIPHER_SOME_PSA because in this case BLOCK_CIPHER can take advantage
 *   of the driver's acceleration.
 */





/* Helpers for GCM/CCM capabilities */















/* MBEDTLS_ECP_LIGHT is auto-enabled by the following symbols:
 * - MBEDTLS_ECP_C because now it consists of MBEDTLS_ECP_LIGHT plus functions
 *   for curve arithmetic. As a consequence if MBEDTLS_ECP_C is required for
 *   some reason, then MBEDTLS_ECP_LIGHT should be enabled as well.
 * - MBEDTLS_PK_PARSE_EC_EXTENDED and MBEDTLS_PK_PARSE_EC_COMPRESSED because
 *   these features are not supported in PSA so the only way to have them is
 *   to enable the built-in solution.
 *   Both of them are temporary dependencies:
 *   - PK_PARSE_EC_EXTENDED will be removed after #7779 and #7789
 *   - support for compressed points should also be added to PSA, but in this
 *     case there is no associated issue to track it yet.
 * - PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_DERIVE because Weierstrass key derivation
 *   still depends on ECP_LIGHT.
 * - PK_C + USE_PSA + PSA_WANT_ALG_ECDSA is a temporary dependency which will
 *   be fixed by #7453.
 */
# 306 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Backward compatibility: after #8740 the RSA module offers functions to parse
 * and write RSA private/public keys without relying on the PK one. Of course
 * this needs ASN1 support to do so, so we enable it here. */





/* MBEDTLS_PK_PARSE_EC_COMPRESSED is introduced in Mbed TLS version 3.5, while
 * in previous version compressed points were automatically supported as long
 * as PK_PARSE_C and ECP_C were enabled. As a consequence, for backward
 * compatibility, we auto-enable PK_PARSE_EC_COMPRESSED when these conditions
 * are met. */




/* Helper symbol to state that there is support for ECDH, either through
 * library implementation (ECDH_C) or through PSA. */





/* PK module can achieve ECDSA functionalities by means of either software
 * implementations (ECDSA_C) or through a PSA driver. The following defines
 * are meant to list these capabilities in a general way which abstracts how
 * they are implemented under the hood. */
# 350 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"





/* If MBEDTLS_PSA_CRYPTO_C is defined, make sure MBEDTLS_PSA_CRYPTO_CLIENT
 * is defined as well to include all PSA code.
 */




/* Helpers to state that each key is supported either on the builtin or PSA side. */
# 402 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Helper symbol to state that the PK module has support for EC keys. This
 * can either be provided through the legacy ECP solution or through the
 * PSA friendly MBEDTLS_PK_USE_PSA_EC_DATA (see pk.h for its description). */





/* Historically pkparse did not check the CBC padding when decrypting
 * a key. This was a bug, which is now fixed. As a consequence, pkparse
 * now needs PKCS7 padding support, but existing configurations might not
 * enable it, so we enable it here. */




/* Backwards compatibility for some macros which were renamed to reflect that
 * they are related to Armv8, not aarch64. */
# 428 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* psa_util file features some ECDSA conversion functions, to convert between
 * legacy's ASN.1 DER format and PSA's raw one. */





/* Some internal helpers to determine which keys are available. */
# 449 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Some internal helpers to determine which operation modes are available. */

























# 189 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_x509.h"
/**
 * \file mbedtls/config_adjust_x509.h
 * \brief Adjust X.509 configuration
 *
 * This is an internal header. Do not include it directly.
 *
 * Automatically enable certain dependencies. Generally, MBEDTLS_xxx
 * configurations need to be explicitly enabled by the user: enabling
 * MBEDTLS_xxx_A but not MBEDTLS_xxx_B when A requires B results in a
 * compilation error. However, we do automatically enable certain options
 * in some circumstances. One case is if MBEDTLS_xxx_B is an internal option
 * used to identify parts of a module that are used by other module, and we
 * don't want to make the symbol MBEDTLS_xxx_B part of the public API.
 * Another case is if A didn't depend on B in earlier versions, and we
 * want to use B in A but we need to preserve backward compatibility with
 * configurations that explicitly activate MBEDTLS_xxx_A but not
 * MBEDTLS_xxx_B.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */




# 34 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_x509.h"

# 191 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_ssl.h"
/**
 * \file mbedtls/config_adjust_ssl.h
 * \brief Adjust TLS configuration
 *
 * This is an internal header. Do not include it directly.
 *
 * Automatically enable certain dependencies. Generally, MBEDTLS_xxx
 * configurations need to be explicitly enabled by the user: enabling
 * MBEDTLS_xxx_A but not MBEDTLS_xxx_B when A requires B results in a
 * compilation error. However, we do automatically enable certain options
 * in some circumstances. One case is if MBEDTLS_xxx_B is an internal option
 * used to identify parts of a module that are used by other module, and we
 * don't want to make the symbol MBEDTLS_xxx_B part of the public API.
 * Another case is if A didn't depend on B in earlier versions, and we
 * want to use B in A but we need to preserve backward compatibility with
 * configurations that explicitly activate MBEDTLS_xxx_A but not
 * MBEDTLS_xxx_B.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */




# 34 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_ssl.h"

/* The following blocks make it easier to disable all of TLS,
 * or of TLS 1.2 or 1.3 or DTLS, without having to manually disable all
 * key exchanges, options and extensions related to them. */

# 46 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_ssl.h"





# 59 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_ssl.h"

# 76 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_ssl.h"

# 84 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_ssl.h"







# 193 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

/* Indicate that all configuration symbols are set,
 * even the ones that are calculated programmatically.
 * It is now safe to query the configuration (to check it, to size buffers,
 * etc.).
 */


# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"
/**
 * \file check_config.h
 *
 * \brief Consistency checks for configuration options
 *
 * This is an internal header. Do not include it directly.
 *
 * This header is included automatically by all public Mbed TLS headers
 * (via mbedtls/build_info.h). Do not include it directly in a configuration
 * file such as mbedtls/mbedtls_config.h or #MBEDTLS_USER_CONFIG_FILE!
 * It would run at the wrong time due to missing derived symbols.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */




/* *INDENT-OFF* */







/*
 * We assume CHAR_BIT is 8 in many places. In practice, this is true on our
 * target platforms, so not an issue, but let's just be extra sure.
 */
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
/* limits.h: ANSI 'C' (X3J11 Oct 88) library header, section 2.2.4.2 */
/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991-1997 ARM Limited. All rights reserved         */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */






    /* max number of bits for smallest object that is not a bit-field (byte) */

    /* mimimum value for an object of type signed char */

    /* maximum value for an object of type signed char */

    /* maximum value for an object of type unsigned char */
# 30 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
      /* minimum value for an object of type char */

      /* maximum value for an object of type char */






# 45 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
    /* maximum number of bytes in a multibyte character, */
    /* for any supported locale */


    /* minimum value for an object of type short int */

    /* maximum value for an object of type short int */

    /* maximum value for an object of type unsigned short int */

    /* minimum value for an object of type int */

    /* maximum value for an object of type int */

    /* maximum value for an object of type unsigned int */





    /* minimum value for an object of type long int */





    /* maximum value for an object of type long int */





    /* maximum value for an object of type unsigned long int */


      /* minimum value for an object of type long long int */

      /* maximum value for an object of type long long int */

      /* maximum value for an object of type unsigned long int */




/* end of limits.h */

# 34 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"




# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
/* Copyright (C) ARM Ltd., 1999,2014 */
/* All rights reserved */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */









    /* armcc has builtin '__int64' which can be used in --strict mode */
# 27 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
    /* armclang and non-strict armcc allow 'long long' in system headers */











# 46 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"


/*
 * 'signed' is redundant below, except for 'signed char' and if
 * the typedef is used to declare a bitfield.
 */

    /* 7.18.1.1 */

    /* exact-width signed integer types */
typedef   signed          char int8_t;
typedef   signed short     int int16_t;
typedef   signed           int int32_t;
typedef   signed       __int64 int64_t;

    /* exact-width unsigned integer types */
typedef unsigned          char uint8_t;
typedef unsigned short     int uint16_t;
typedef unsigned           int uint32_t;
typedef unsigned       __int64 uint64_t;

    /* 7.18.1.2 */

    /* smallest type of at least n bits */
    /* minimum-width signed integer types */
typedef   signed          char int_least8_t;
typedef   signed short     int int_least16_t;
typedef   signed           int int_least32_t;
typedef   signed       __int64 int_least64_t;

    /* minimum-width unsigned integer types */
typedef unsigned          char uint_least8_t;
typedef unsigned short     int uint_least16_t;
typedef unsigned           int uint_least32_t;
typedef unsigned       __int64 uint_least64_t;

    /* 7.18.1.3 */

    /* fastest minimum-width signed integer types */
typedef   signed           int int_fast8_t;
typedef   signed           int int_fast16_t;
typedef   signed           int int_fast32_t;
typedef   signed       __int64 int_fast64_t;

    /* fastest minimum-width unsigned integer types */
typedef unsigned           int uint_fast8_t;
typedef unsigned           int uint_fast16_t;
typedef unsigned           int uint_fast32_t;
typedef unsigned       __int64 uint_fast64_t;

    /* 7.18.1.4 integer types capable of holding object pointers */




typedef   signed           int intptr_t;
typedef unsigned           int uintptr_t;


    /* 7.18.1.5 greatest-width integer types */
typedef   signed     long long intmax_t;
typedef unsigned     long long uintmax_t;




    /* 7.18.2.1 */

    /* minimum values of exact-width signed integer types */





    /* maximum values of exact-width signed integer types */





    /* maximum values of exact-width unsigned integer types */





    /* 7.18.2.2 */

    /* minimum values of minimum-width signed integer types */





    /* maximum values of minimum-width signed integer types */





    /* maximum values of minimum-width unsigned integer types */





    /* 7.18.2.3 */

    /* minimum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width unsigned integer types */





    /* 7.18.2.4 */

    /* minimum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding unsigned integer type */






    /* 7.18.2.5 */

    /* minimum value of greatest-width signed integer type */


    /* maximum value of greatest-width signed integer type */


    /* maximum value of greatest-width unsigned integer type */


    /* 7.18.3 */

    /* limits of ptrdiff_t */
# 216 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of sig_atomic_t */



    /* limit of size_t */






    /* limits of wchar_t */
    /* NB we have to undef and redef because they're defined in both
     * stdint.h and wchar.h */



# 241 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of wint_t */







    /* 7.18.4.1 macros for minimum-width integer constants */










    /* 7.18.4.2 macros for greatest-width integer constants */











# 305 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"






/* end of stdint.h */
# 39 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 47 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"














/* Limitations on ECC key types acceleration: if we have any of `PUBLIC_KEY`,
 * `KEY_PAIR_BASIC`, `KEY_PAIR_IMPORT`, `KEY_PAIR_EXPORT` then we must have
 * all 4 of them.
 */
# 76 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

/* Limitations on ECC curves acceleration: partial curve acceleration is only
 * supported with crypto excluding PK, X.509 or TLS.
 * Note: no need to check X.509 as it depends on PK. */
# 100 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"





















# 135 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 150 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"





# 172 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 181 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"






# 197 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"










# 223 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 241 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 251 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

































































# 322 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"




























# 356 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"













# 383 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

/* Use of EC J-PAKE in TLS requires SHA-256. */





# 397 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 412 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"





































# 455 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 462 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"




































































































































































































































# 696 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"
















# 719 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"















































# 776 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"










# 796 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"





/* TLS 1.3 requires separate HKDF parts from PSA,
 * and at least one ciphersuite, so at least SHA-256 or SHA-384
 * from PSA to use with HKDF.
 *
 * Note: for dependencies common with TLS 1.2 (running handshake hash),
 * see MBEDTLS_SSL_TLS_C. */
# 814 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 822 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"







/*
 * The current implementation of TLS 1.3 requires MBEDTLS_SSL_KEEP_PEER_CERTIFICATE.
 */




# 851 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 858 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 865 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"



















/* TLS 1.2 and 1.3 require SHA-256 or SHA-384 (running handshake hash) */
# 897 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"














































# 950 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"





































# 1003 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"









# 1018 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 1025 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"





















































/* Reject attempts to enable options that have been removed and that could
 * cause a build to succeed but with features removed. */













































# 1132 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

/*
 * Avoid warning from -pedantic. This is a convenient place for this
 * workaround since this is included by every single file before the
 * #if defined(MBEDTLS_xxx_C) that results in empty translation units.
 */
typedef int mbedtls_iso_c_forbids_empty_translation_units;

/* *INDENT-ON* */
# 202 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

# 25 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.h"
# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_time.h"
/**
 * \file platform_time.h
 *
 * \brief Mbed TLS Platform time abstraction
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */



# 14 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_time.h"





/*
 * The time_t datatype
 */



/* For time_t */
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"
/* time.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.12 */
/* Copyright (C) Codemist Ltd., 1988-1993.                      */
/* Copyright 1991-1993 ARM Limited. All rights reserved.        */
/* version 0.03 */

/*
 * time.h declares two macros, four types and several functions for
 * manipulating time. Many functions deal with a calendar time that
 * represents the current date (according to the Gregorian
 * calendar) and time. Some functions deal with local time, which
 * is the calendar time expressed for some specific time zone, and
 * with Daylight Savings Time, which is a temporary change in the
 * algorithm for determining local time.
 */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */













# 41 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 57 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"




    /* CLOCKS_PER_SEC: the number per second of the value returned by the
     * clock function. */
# 73 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"





typedef unsigned int clock_t;    /* cpu time type */
typedef unsigned int time_t;     /* date/time in unix secs past 1-Jan-70 */

#pragma push
#pragma anon_unions

struct tm {
    int tm_sec;   /* seconds after the minute, 0 to 60
                     (0 - 60 allows for the occasional leap second) */
    int tm_min;   /* minutes after the hour, 0 to 59 */
    int tm_hour;  /* hours since midnight, 0 to 23 */
    int tm_mday;  /* day of the month, 1 to 31 */
    int tm_mon;   /* months since January, 0 to 11 */
    int tm_year;  /* years since 1900 */
    int tm_wday;  /* days since Sunday, 0 to 6 */
    int tm_yday;  /* days since January 1, 0 to 365 */
    int tm_isdst; /* Daylight Savings Time flag */
    union {       /* ABI-required extra fields, in a variety of types */
        struct {
            int __extra_1, __extra_2;
        };
        struct {
            long __extra_1_long, __extra_2_long;
        };
        struct {
            char *__extra_1_cptr, *__extra_2_cptr;
        };
        struct {
            void *__extra_1_vptr, *__extra_2_vptr;
        };
    };
};

#pragma pop

   /* struct tm holds the components of a calendar time, called the broken-down
    * time. The value of tm_isdst is positive if Daylight Savings Time is in
    * effect, zero if Daylight Savings Time is not in effect, and negative if
    * the information is not available.
    */

extern __declspec(__nothrow) clock_t clock(void);
   /* determines the processor time used.
    * Returns: the implementation's best approximation to the processor time
    *          used by the program since program invocation. The time in
    *          seconds is the value returned divided by the value of the macro
    *          CLK_TCK. The value (clock_t)-1 is returned if the processor time
    *          used is not available.
    */
extern __declspec(__nothrow) double difftime(time_t /*time1*/, time_t /*time0*/);
   /*
    * computes the difference between two calendar times: time1 - time0.
    * Returns: the difference expressed in seconds as a double.
    */
extern __declspec(__nothrow) time_t mktime(struct tm * /*timeptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the broken-down time, expressed as local time, in the structure
    * pointed to by timeptr into a calendar time value with the same encoding
    * as that of the values returned by the time function. The original values
    * of the tm_wday and tm_yday components of the structure are ignored, and
    * the original values of the other components are not restricted to the
    * ranges indicated above. On successful completion, the values of the
    * tm_wday and tm_yday structure components are set appropriately, and the
    * other components are set to represent the specified calendar time, but
    * with their values forced to the ranges indicated above; the final value
    * of tm_mday is not set until tm_mon and tm_year are determined.
    * Returns: the specified calendar time encoded as a value of type time_t.
    *          If the calendar time cannot be represented, the function returns
    *          the value (time_t)-1.
    */
extern __declspec(__nothrow) time_t time(time_t * /*timer*/);
   /*
    * determines the current calendar time. The encoding of the value is
    * unspecified.
    * Returns: the implementations best approximation to the current calendar
    *          time. The value (time_t)-1 is returned if the calendar time is
    *          not available. If timer is not a null pointer, the return value
    *          is also assigned to the object it points to.
    */

extern __declspec(__nothrow) char *asctime(const struct tm * /*timeptr*/) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) char *_asctime_r(const struct tm * /*timeptr*/,
                                char * __restrict /*buf*/) __attribute__((__nonnull__(1,2)));

extern __declspec(__nothrow) char *asctime_r(const struct tm * /*timeptr*/,
                               char * __restrict /*buf*/) __attribute__((__nonnull__(1,2)));

   /*
    * converts the broken-down time in the structure pointed to by timeptr into
    * a string in the form "Sun Sep 16 01:03:52 1973\n\0".
    * Returns: a pointer to the string containing the date and time.
    */
extern __declspec(__nothrow) char *ctime(const time_t * /*timer*/) __attribute__((__nonnull__(1)));
   /*
    * converts the calendar time pointed to by timer to local time in the form
    * of a string. It is equivalent to asctime(localtime(timer));
    * Returns: the pointer returned by the asctime function with that
    *          broken-down time as argument.
    */
extern __declspec(__nothrow) struct tm *gmtime(const time_t * /*timer*/) __attribute__((__nonnull__(1)));
   /*
    * converts the calendar time pointed to by timer into a broken-down time,
    * expressed as Greenwich Mean Time (GMT).
    * Returns: a pointer to that object or a null pointer if GMT not available.
    */
extern __declspec(__nothrow) struct tm *localtime(const time_t * /*timer*/) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) struct tm *_localtime_r(const time_t * __restrict /*timer*/,
                                       struct tm * __restrict /*result*/) __attribute__((__nonnull__(1,2)));

extern __declspec(__nothrow) struct tm *localtime_r(const time_t * __restrict /*timer*/,
                                      struct tm * __restrict /*result*/) __attribute__((__nonnull__(1,2)));

   /*
    * converts the calendar time pointed to by timer into a broken-down time,
    * expressed a local time.
    * Returns: a pointer to that object.
    */
extern __declspec(__nothrow) size_t strftime(char * __restrict /*s*/, size_t /*maxsize*/,
                       const char * __restrict /*format*/,
                       const struct tm * __restrict /*timeptr*/) __attribute__((__nonnull__(1,3,4)));
   /*
    * places characters into the array pointed to by s as controlled by the
    * string pointed to by format. The format string consists of zero or more
    * directives and ordinary characters. A directive consists of a % character
    * followed by a character that determines the directive's behaviour. All
    * ordinary characters (including the terminating null character) are copied
    * unchanged into the array. No more than maxsize characters are placed into
    * the array. Each directive is replaced by appropriate characters  as
    * described in the following list. The appropriate characters are
    * determined by the LC_TIME category of the current locale and by the
    * values contained in the structure pointed to by timeptr.
    * %a is replaced by the locale's abbreviated weekday name.
    * %A is replaced by the locale's full weekday name.
    * %b is replaced by the locale's abbreviated month name.
    * %B is replaced by the locale's full month name.
    * %c is replaced by the locale's appropriate date and time representation.
    * %d is replaced by the day of the month as a decimal number (01-31).
    * %H is replaced by the hour (24-hour clock) as a decimal number (00-23).
    * %I is replaced by the hour (12-hour clock) as a decimal number (01-12).
    * %j is replaced by the day of the year as a decimal number (001-366).
    * %m is replaced by the month as a decimal number (01-12).
    * %M is replaced by the minute as a decimal number (00-59).
    * %p is replaced by the locale's equivalent of either AM or PM designations
    *       associated with a 12-hour clock.
    * %S is replaced by the second as a decimal number (00-61).
    * %U is replaced by the week number of the year (Sunday as the first day of
    *       week 1) as a decimal number (00-53).
    * %w is replaced by the weekday as a decimal number (0(Sunday) - 6).
    * %W is replaced by the week number of the year (Monday as the first day of
    *       week 1) as a decimal number (00-53).
    * %x is replaced by the locale's appropriate date representation.
    * %X is replaced by the locale's appropriate time representation.
    * %y is replaced by the year without century as a decimal number (00-99).
    * %Y is replaced by the year with century as a decimal number.
    * %Z is replaced by the timezone name or abbreviation, or by no characters
    *       if no time zone is determinable.
    * %% is replaced by %.
    * If a directive is not one of the above, the behaviour is undefined.
    * Returns: If the total number of resulting characters including the
    *          terminating null character is not more than maxsize, the
    *          strftime function returns the number of characters placed into
    *          the array pointed to by s not including the terminating null
    *          character. otherwise, zero is returned and the contents of the
    *          array are indeterminate.
    */











# 280 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"



/* end of time.h */

# 27 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_time.h"
typedef time_t mbedtls_time_t;


# 34 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_time.h"
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"
/* Copyright (C) ARM Ltd., 1999,2014 */
/* All rights reserved */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */

/* Based on WG14/N843 (C9X) Committee Draft August 3, 1998 */








# 20 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"



# 33 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"


    /* 7.8.1 */

# 51 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 66 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 81 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 96 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 111 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 126 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 141 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 156 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 171 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 186 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 201 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"





   /* unconditional in non-strict C for consistency of debug info */



      typedef unsigned short wchar_t; /* see <stddef.h> */
# 221 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

typedef struct imaxdiv_t { intmax_t quot, rem; } imaxdiv_t;
   /* type of the value returned by the imaxdiv function. */





__declspec(__nothrow) intmax_t strtoimax(const char * __restrict /*nptr*/,
                   char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
    /* as for strtol */
__declspec(__nothrow) uintmax_t strtoumax(const char * __restrict /*nptr*/,
                    char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
    /* as for strtoul */

__declspec(__nothrow) intmax_t wcstoimax(const wchar_t * __restrict /*nptr*/,
                   wchar_t ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
__declspec(__nothrow) uintmax_t wcstoumax(const wchar_t * __restrict /*nptr*/,
                    wchar_t ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));

extern __declspec(__nothrow) __attribute__((const)) intmax_t imaxabs(intmax_t /*j*/);
   /*
    * computes the absolute value of an intmax_t j. If the result cannot be
    * represented, the behaviour is undefined.
    * Returns: the absolute value.
    */
extern __declspec(__nothrow) __attribute__((const)) imaxdiv_t imaxdiv(intmax_t /*numer*/, intmax_t /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the sign of
    * the resulting quotient is that of the algebraic quotient, and the
    * magnitude of the resulting quotient is the largest integer less than the
    * magnitude of the algebraic quotient. If the result cannot be represented,
    * the behaviour is undefined; otherwise, quot * denom + rem shall equal
    * numer.
    * Returns: a structure of type imaxdiv_t, comprising both the quotient and
    *          the remainder. the structure shall contain the following
    *          members, in either order.
    *          intmax_t quot; intmax_t rem;
    */







/* end of inttypes.h */

# 35 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_time.h"
typedef int64_t mbedtls_ms_time_t;


/**
 * \brief   Get time in milliseconds.
 *
 * \return Monotonically-increasing current time in milliseconds.
 *
 * \note Define MBEDTLS_PLATFORM_MS_TIME_ALT to be able to provide an
 *       alternative implementation
 *
 * \warning This function returns a monotonically-increasing time value from a
 *          start time that will differ from platform to platform, and possibly
 *          from run to run of the process.
 *
 */
mbedtls_ms_time_t mbedtls_ms_time(void);

/*
 * The function pointers for time
 */
# 74 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_time.h"





# 27 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.h"


# 46 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.h"









# 63 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.h"

/*
 * MBEDTLS_ERR_PLATFORM_HW_FAILED is deprecated and should not be used.
 */




typedef struct mbedtls_timing_delay_context
{
    unsigned int timer; // ticks
    unsigned int int_ms;
    unsigned int fin_ms;
} mbedtls_timing_delay_context;

int mbedtls_timing_get_delay(void *data);
void mbedtls_timing_set_delay(void *data, unsigned int int_ms, unsigned int fin_ms);


# 92 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.h"


mbedtls_ms_time_t mbedtls_ms_time(void);


# 4 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.c"

unsigned long rti_get_current_tick(void);

int mbedtls_hardware_poll(void *data, unsigned char *output, 
                                   unsigned int len, unsigned int *olen)
{
    unsigned int seed = (unsigned int)(rti_get_current_tick() & 0xFFFFFFFFUL);
    unsigned int r;
    int i = 0;

    srand(seed);

    for(i = 0; i < len; i++)
    {
        output[i] = (unsigned char)rand();
    }
    *olen = len;

    return 0;
}


static unsigned int mbedtls_timing_get_timer(mbedtls_timing_delay_context *ctx, int reset)
{
    if(reset)
    {
        ctx->timer = OsaGetTicks(0);
        return 0;
    }
    else
    {
        unsigned int now = OsaGetTicks(0);
        now = now - ctx->timer;
        return now * 5; // 5ms per tick
    }
}

int mbedtls_timing_get_delay(void *data)
{
    mbedtls_timing_delay_context *ctx = (mbedtls_timing_delay_context *) data;
    unsigned int elapsed_ms;

    if(ctx->fin_ms == 0)
    {
        return -1;
    }

    elapsed_ms = mbedtls_timing_get_timer(ctx, 0);

    if(elapsed_ms >= ctx->fin_ms)
    {
        return 2;
    }

    if(elapsed_ms >= ctx->int_ms)
    {
        return 1;
    }

    return 0;
}

void mbedtls_timing_set_delay(void *data, unsigned int int_ms, 
                                                    unsigned int fin_ms)
{
    mbedtls_timing_delay_context *ctx = (mbedtls_timing_delay_context *) data;

    ctx->int_ms = int_ms;
    ctx->fin_ms = fin_ms;

    if(fin_ms != 0)
    {
        mbedtls_timing_get_timer(ctx, 1);
    }
}



struct timeval
{
	int tv_sec;        /* seconds */
	int tv_usec;       /* and microseconds */
};

int gettimeofday(struct timeval *tv, void* dummy);
mbedtls_ms_time_t mbedtls_ms_time(void)
{
    int ret;
    struct timeval now;
    mbedtls_ms_time_t current_ms;

    gettimeofday(&now, 0);

    current_ms = now.tv_sec;
    return(current_ms * 1000 + now.tv_usec / 1000);
}
