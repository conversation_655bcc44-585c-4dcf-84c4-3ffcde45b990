//PPC Version : V2.1.9.30
//PPL Source File Name : pcac-duster_firewall_management.ppp
//PPL Source File Name : L:/PLT/pcac/duster/src/firewall_management.c
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
typedef unsigned int size_t ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_INT_IFC ,	 
 DIAG_EXT_IFC	 
 } DIAG_IF_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_UART_SPEED_921600 ,	 
 DIAG_UART_SPEED_1842000 ,	 
 DIAG_UART_SPEED_3000000 ,	 
 DIAG_UART_DISABLE ,	 
 } DIAG_UART_SPEED_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_MEM_ENABLE ,	 
 DIAG_MEM_DISABLE	 
 } DIAG_MEM_SWITCH;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_DEV_USB ,	 
 DIAG_DEV_SD ,	 
 DIAG_DEV_FS ,	 
 DIAG_DEV_UART ,	 
 DIAG_DEV_SPI ,	 
 DIAG_DEV_NONE	 
 } DIAG_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 SULOG_DEV_USB ,	 
 SULOG_DEV_SD	 
 } SULOG_DEV_TYPE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 DIAG_IF_TYPE useIntIf ; /* use internal interface or external interface */	 
 DIAG_UART_SPEED_TYPE useHighSpeedUART ; /* high speed UART ( 460800 ) or low speed UART ( 115200 ) */	 
 DIAG_MEM_SWITCH diagMemSwitch ;	 
 DIAG_DEV_TYPE diagDevType ;	 
 SULOG_DEV_TYPE sulogDevType ;	 
 BOOL sdlAutoDelete ;	 
 } diagCfgDataS;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 dbID ;	 
 UINT32 filterBitLength ;	 
 UINT32 reserved [ 6 ] ;	 
 } DIAG_Nvm_Filter_File_Header_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MASS_STORAGE_DISABLE = 0x0 ,	 
 MASS_STORAGE_ENABLE = 0x1	 
 } MassStorage_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* MIFI driver*/	 
 USB_GENERIC_MIFI_DRIVER = 0x0 ,	 
 USB_MARVELL_MIFI_DRIVER = 0x1 ,	 
 USB_ASR_MIFI_DRIVER = 0x2 ,	 
 USB_GENERIC_MOD_DRIVER = 0x10 ,	 
 USB_GENERIC_MOD_ECM_DRIVER = 0x12 ,	 
 USB_DIAG_UAC_DRIVER = 0x14 ,	 
 USB_RNDIS_ONLY_DRIVER = 0x15 ,	 
	 
 /* MBIM driver*/	 
 USB_MBIM_ONLY_DRIVER = 0x40 ,	 
 USB_MBIM_GENERIC_DRIVER = 0x41 ,	 
 USB_MBIM_MAX_DRIVER = 0x4F ,	 
	 
 /* Other driver*/	 
 USB_CDROM_ONLY_DRIVER = 0x91 ,	 
 USB_CDROM_DIAG_DRIVER = 0x92 ,	 
 USB_DIAG_ONLY_DRIVER = 0x93 ,	 
 USB_MODEM_ONLY_DRIVER = 0x94 ,	 
 USB_MODEM_DIAG_DRIVER = 0x95 ,	 
	 
 USB_MAX_DRIVER = 0xFF	 
 } Usb_driver_typeE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_AUTO_INSTALL_DISABLE = 0x0 ,	 
 USB_AUTO_INSTALL_ENABLE = 0x1	 
 } Usb_auto_install_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_OS_DETECT_DISABLE = 0x0 ,	 
 USB_OS_DETECT_ENABLE = 0x1	 
 } Usb_OS_detect_type;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Usb_driver_typeE usb_driver ;	 
 MassStorage_ConfigE mass_storage ;	 
 Usb_auto_install_type auto_install ;	 
 Usb_OS_detect_type os_detect ;	 
 } Usb_DriverS;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 YMODEM_DISABLE = 0x0 ,	 
 YMODEM_ENABLE = 0x1	 
 } Ymodem_Dump_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Ymodem_Dump_config config ;	 
 } Ymodem_Dump_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MBIM_ONLY_DESP = 0x0 ,	 
 MBIM_ATDIAG_DESP = 0x1	 
 } MBIM_DESP_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 MBIM_DESP_config config ;	 
 } MBIM_CFG_type;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 EEH_DUMP_DEV_USB_SD ,	 
 EEH_DUMP_DEV_SPI	 
 } EEH_DUMP_DEV_TYPE;

typedef unsigned int size_t ;
typedef va_list __gnuc_va_list ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0 = 0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 , GPIO_PIN_33 , GPIO_PIN_34 , GPIO_PIN_35 , GPIO_PIN_36 , GPIO_PIN_37 , GPIO_PIN_38 , GPIO_PIN_39 ,	 
	 
 GPIO_PIN_40 , GPIO_PIN_41 , GPIO_PIN_42 , GPIO_PIN_43 , GPIO_PIN_44 , GPIO_PIN_45 , GPIO_PIN_46 , GPIO_PIN_47 ,	 
 GPIO_PIN_48 , GPIO_PIN_49 , GPIO_PIN_50 , GPIO_PIN_51 , GPIO_PIN_52 , GPIO_PIN_53 , GPIO_PIN_54 , GPIO_PIN_55 ,	 
 GPIO_PIN_56 , GPIO_PIN_57 , GPIO_PIN_58 , GPIO_PIN_59 , GPIO_PIN_60 , GPIO_PIN_61 , GPIO_PIN_62 , GPIO_PIN_63 ,	 
	 
 GPIO_PIN_64 , GPIO_PIN_65 , GPIO_PIN_66 , GPIO_PIN_67 , GPIO_PIN_68 , GPIO_PIN_69 , GPIO_PIN_70 , GPIO_PIN_71 ,	 
 GPIO_PIN_72 , GPIO_PIN_73 , GPIO_PIN_74 , GPIO_PIN_75 , GPIO_PIN_76 , GPIO_PIN_77 , GPIO_PIN_78 , GPIO_PIN_79 ,	 
 GPIO_PIN_80 , GPIO_PIN_81 , GPIO_PIN_82 , GPIO_PIN_83 , GPIO_PIN_84 , GPIO_PIN_85 , GPIO_PIN_86 , GPIO_PIN_87 ,	 
 GPIO_PIN_88 , GPIO_PIN_89 , GPIO_PIN_90 , GPIO_PIN_91 , GPIO_PIN_92 , GPIO_PIN_93 , GPIO_PIN_94 , GPIO_PIN_95 ,	 
	 
 GPIO_PIN_96 , GPIO_PIN_97 , GPIO_PIN_98 , GPIO_PIN_99 , GPIO_PIN_100 , GPIO_PIN_101 , GPIO_PIN_102 , GPIO_PIN_103 ,	 
 GPIO_PIN_104 , GPIO_PIN_105 , GPIO_PIN_106 , GPIO_PIN_107 , GPIO_PIN_108 , GPIO_PIN_109 , GPIO_PIN_110 , GPIO_PIN_111 ,	 
 GPIO_PIN_112 , GPIO_PIN_113 , GPIO_PIN_114 , GPIO_PIN_115 , GPIO_PIN_116 , GPIO_PIN_117 , GPIO_PIN_118 , GPIO_PIN_119 ,	 
 GPIO_PIN_120 , GPIO_PIN_121 , GPIO_PIN_122 , GPIO_PIN_123 , GPIO_PIN_124 , GPIO_PIN_125 , GPIO_PIN_126 , GPIO_PIN_127 ,	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PMU_POR = 1 ,	 
 PMU_EMR ,	 
 PMU_WDTR = ( PMU_EMR+2 )	 
 } PMU_LastResetStatus;

typedef UINT8 UART_Activity ;
//ICAT EXPORTED STRUCT 
 typedef struct /* This is structure of the UART Configuration */ 
 {	 
 UART_OpMode opMode ; /* fifo mode , non fifo mode or DMA for basic interface*/	 
 UART_TriggerLevel triggerLevel ; /* the trigger level interrupt on 1 , 8 , 16 , 32 */	 
 UART_BaudRates baudRate ; /* the rate of the transmit and the receive up to 111520 ( default - 9600 ) .*/	 
 UART_WordLen numDataBits ; /* 5 , 6 , 7 , or 8 number of data bits in the UART data frame ( default - 8 ) . */	 
 UART_StopBits stopBits ; /* 1 , 1.500000 or 2 stop bits in the UART data frame ( default - 1 ) . */	 
 UART_ParityTBits parityBitType ; /* Even , Odd or no-parity bit type in the UART data frame ( default - Non ) . */	 
 UART_InterfaceType interfaceType ; /* number of interface that the UART driver supplies ( default - UART_IF_TYPE_L2 ) */	 
 BOOL modemSignal ; /* enable operate modem - TRUE , disable modem - FALSE */	 
 BOOL flowControl ; /* enable Auto flow Control - TRUE , disable Auto flow Control - FALSE */	 
 UINT8 sleepMode ; /* enable sleep mode - TRUE , more fine control - see UARTSleepMode enum */	 
 BOOL auto_baud ; /* enable auto_baud , auto-baud-rate detection within the UART ( default - FALSE ) */	 
 UART_SIRConfigure sirIrDA ;	 
 } UARTConfiguration;

//ICAT EXPORTED ENUM 
 typedef enum // change the order -1 to + 
 {	 
 UART_RC_OK = 1 , /* 1 - no errors */	 
	 
 UART_RC_PORT_NUM_ERROR = -100 , /* -100 - Error in the UART port number */	 
 UART_RC_NO_DATA_TO_READ , /* -99 - Eror no data to read from the FIFO UART */	 
 UART_RC_ILLEGAL_BAUD_RATE , /* -98 - Error in the UART Bayd Rate */	 
 UART_RC_UART_PARITY_BITS_ERROR , /* -97 - Error in parity bit */	 
 UART_RC_UART_ONE_STOP_BIT_ERROR , /* -96 - Error in one stop bit */	 
 UART_RC_ONE_HALF_OR_TWO_STOP_BIT_ERROR , /* -95 - Error in two stop bit */	 
 UART_RC_BAD_INTERFACE_TYPE , /* -94 - Error in the Interface Type */	 
 UART_RC_UART_NOT_AVAILABLE , /* -93 - Error in try to open UART that is open */	 
 UART_RC_NO_DATA_TO_WRITE , /* -92 - Error No data to writ the len = 0 */	 
 UART_RC_NOT_ALL_BYTE_WRITTEN , /* -91 - Error Not all the Byte write to the UART FIFO */	 
 UART_RC_ISR_ALREADY_BIND , /* -90 - Error try to bind ISR for Basic Interface */	 
 UART_RC_WRONG_ISR_UNBIND , /* -89 - Error in the UnBind ISR for Basic Interface */	 
 UART_RC_FIFO_NOT_EMPTY , /* -88 - Error , the UART FIFO not empty */	 
 UART_RC_UART_OPEN , /* -87 - Error try chance the configurr when the UART open */	 
 UART_RC_GPIO_ERR , /* -86 - Error in the Configure of the GPIO */	 
 UART_RC_IRDA_CONFIG_ERR /* -85 - Illegal IrDA configuration */	 
 } UART_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAM_LOG = 0x00 ,	 
 USB_RAM_LOG = 0x01 ,	 
 MAX_RAM_LOG = 0x02	 
 } Ram_Log_Type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAMLOG_ENABLE = 0x00 ,	 
 USB_RAMLOG_ENABLE = 0x01	 
 } RamLog_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 LOG_DISABLE = 0x0 ,	 
 UART_LOG_ENABLE = 0x1 ,	 
 ACAT_LOG_ENABLE = 0x2	 
 } Log_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MSG_DISABLE = 0x0 ,	 
 ACAT_MSG_ENABLE = 0x1	 
 } Msg_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RTI_LOG_DISABLE = 0x0 ,	 
 RTI_LOG_ENABLE = 0x1	 
 } RTI_ConfigE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Log_ConfigE log_cfg ;	 
 Msg_ConfigE msg_cfg ;	 
 RTI_ConfigE rti_cfg ;	 
 RamLog_ConfigE ramlog ;	 
 } Log_ConfigS;

typedef void ( *UARTNotifyInterrupt ) ( UART_Port ) ;
typedef void ( *UsbLogPrint_t ) ( const char * , ... ) ;
typedef unsigned int size_t ;
typedef unsigned int clock_t ;
typedef unsigned int time_t ;
typedef int ( *rpc_fun_cb ) ( void * userdata , const char * tag , int type , const char * value , int data_type ) ;
typedef void ( *tr069_delay_cb ) ( void * context ) ;
typedef unsigned char BOOL ;
typedef union {

 falcon_asr_ota_head_info ota_diffhead ;

 unsigned char reserved [ 3072 ] ;
 } FALCON_ASR_OTA_HEADER ;
typedef int ( *customer_ota_ras_func ) ( unsigned int flash_addr , unsigned int len ) ;
typedef void ( *customer_flash_enable_4byte_func ) ( void ) ;
typedef void ( *customer_flash_disable_4byte_func ) ( void ) ;
typedef __fota_param s_sd_firmware_flag ;
typedef int AT_RESP_STATUS ;
typedef unsigned long UINT32 ;
typedef UINT32 Attribs_Mmu ;
typedef void ( * mmuNotifyEventFunc ) ( MMU_ERROR_STATUS mmuErrorHandle ) ;
typedef union {
 rw_region_item compress_rw_region_list [ 64 ] ;

 UINT8 filer [ 2048 ] ;
 } LOADTABLE_AREA_RW_CPZ_INFO ;
typedef unsigned char BYTE ;
typedef unsigned short int WORD ;
typedef unsigned long DWORD ;
typedef WORD * WORD_PTR ;
typedef DWORD * DWORD_PTR ;
typedef BYTE BYTE_BITMASK ;
typedef VOID_PTR * VOID_PTR_PTR ;
typedef WORD FLASH_DATA_WIDTH ;
typedef char FDI_TCHAR ;
typedef OSASemaRef SEM_ID ;
typedef int SEM_STATUS ;
typedef SEM_MTX * SEM_MTX_ID ;
typedef WORD FILE_ID ;
typedef volatile unsigned long VUINT32_T ;
typedef unsigned long UINT32_T ;
typedef volatile unsigned int VUINT_T ;
typedef unsigned int UINT_T ;
typedef int INT_T ;
typedef unsigned short UINT16_T , USHORT ;
typedef volatile unsigned short VUINT16_T ;
typedef unsigned char UINT8_T ;
typedef char INT8_T ;
typedef unsigned int UINT , *PUINT ;
typedef unsigned long long UINT64 , *PUINT64 ;
typedef unsigned char UCHAR , BYTE , *PUCHAR ;
typedef int INT , *PINT ;
typedef long long INT64 , *PINT64 ;
typedef char CHAR , *PCHAR ;
typedef unsigned int size_t ;
typedef volatile UINT VUINT , *PVUINT ;
typedef volatile UINT64 VUINT64 , *PVUINT64 ;
typedef volatile unsigned int VUINT32 , *PVUINT32 ;
typedef volatile UINT16 VUINT16 , *PVUINT16 ;
typedef volatile UINT8 VUINT8 , *PVUINT8 ;
typedef volatile UCHAR VUCHAR , *PVUCHAR ;
typedef volatile INT VINT , *PVINT ;
typedef volatile INT64 VINT64 , *PVINT64 ;
typedef volatile int VINT32 , *PVINT32 ;
typedef volatile INT16 VINT16 , *PVINT16 ;
typedef volatile char VINT8 , *PVINT8 ;
typedef volatile CHAR VCHAR , *PVCHAR ;
typedef UINT16 WORD ;
typedef signed char int8_t ;
typedef unsigned char uint8_t ;
typedef signed short int16_t ;
typedef unsigned short uint16_t ;
typedef signed int int32_t ;
typedef unsigned int uint32_t ;
typedef signed long long int int64_t ;
typedef unsigned long long int uint64_t ;
typedef UINT32 rti_uint32_t ;
typedef UINT64 rti_uint64_t ;
//ICAT EXPORTED ENUM 
 typedef enum CISGID 
 {	 
 CI_SG_ID_FIRST = 1 ,	 
 CI_SG_ID_CC = CI_SG_ID_FIRST ,	 
 CI_SG_ID_SS ,	 
 CI_SG_ID_MM ,	 
 CI_SG_ID_PB ,	 
 CI_SG_ID_SIM ,	 
 CI_SG_ID_MSG ,	 
 CI_SG_ID_PS ,	 
 CI_SG_ID_DAT ,	 
 CI_SG_ID_DEV ,	 
 CI_SG_ID_HSCSD ,	 
 CI_SG_ID_DEB ,	 
 CI_SG_ID_ATPI ,	 
 CI_SG_ID_PL ,	 
 CI_SG_ID_OAM ,	 
 CI_SG_ID_NEXTAVAIL /* This must always be the last entry in the list */	 
 } _CiServiceGroupId;

//ICAT EXPORTED ENUM 
 typedef enum CIRC 
 {	 
 CIRC_SUCCESS = 0 ,	 
 CIRC_FAIL ,	 
 CIRC_INTERLINK_FAIL , /* the link between application subsystem and communication subsystem is broken */	 
 CIRC_SH_NULL_CONFIRM_CB ,	 
 CIRC_SH_NULL_FREEREQMEM_CB ,	 
 CIRC_SH_INVALID_HANDLE ,	 
 CIRC_SH_INVALID_OPER ,	 
 CIRC_SH_NULL_REQPARAS ,	 
 CIRC_SG_INVALID_HANDLE ,	 
 CIRC_SG_RESERVED_PRIMID ,	 
 CIRC_SG_NULL_REQPARAS ,	 
 CIRC_SG_NULL_RSPPARAS ,	 
 CIRC_NUM_RESCODES	 
 } _CiReturnCode;

typedef UINT16 CiReturnCode ;
//ICAT EXPORTED ENUM 
 typedef enum CISHOPER 
 {	 
 CI_SH_OPER_REGISTERSH = 0 ,	 
 CI_SH_OPER_DEREGISTERSH ,	 
 CI_SH_OPER_GETVERSION ,	 
 CI_SH_OPER_REGISTERSG ,	 
 CI_SH_OPER_DEREGISTERSG ,	 
 CI_SH_OPER_REGISTERDEFSG ,	 
 CI_SH_OPER_DEREGISTERDEFSG ,	 
 CI_SH_QUERYEXTENDED ,	 
 CI_SH_QUERYEXTENDEDSERVICEPRIM ,	 
 CI_SH_QUERYEXTENDEDSERVICEGROUP ,	 
 CI_SH_OPER_CIVERSION_NEGO_REQ ,	 
 CI_SH_OPER_CIVERSION_NEGO_CNF ,	 
 CI_SH_NUMOPERS	 
 } CiShOper;

typedef UINT8 CiBoolean ;
typedef unsigned char Boolean ;
typedef UINT8 CiServiceGroupID ;
typedef UINT16 CiPrimitiveID ;
typedef UINT32 CiServiceHandle ;
typedef UINT32 CiRequestHandle ;
typedef UINT32 CiIndicationHandle ;
typedef UINT32 CiShHandle ;
typedef UINT32 CiShOpaqueHandle ;
typedef UINT32 CiShRequestHandle ;
typedef UINT32 CiSgOpaqueHandle ;
typedef void ( *CiShConfirm ) ( CiShOpaqueHandle opShHandle , CiShOper oper , void *cnfParas , CiShRequestHandle opHandle ) ;
typedef void ( *CiConfirm ) ( CiSgOpaqueHandle opSgOpaqueHandle , CiServiceGroupID id , CiPrimitiveID primId , CiRequestHandle reqHandle , void *paras ) ;
typedef void ( *CiIndicate ) ( CiSgOpaqueHandle opSgOpaqueHandle , CiServiceGroupID id , CiPrimitiveID primId , CiIndicationHandle indHandle , void *paras ) ;
typedef void ( *CiShFreeReqMem ) ( CiShOpaqueHandle opShFreeHandle , CiShOper oper , void *reqParas ) ;
typedef void ( *CiShFreeCnfMem ) ( CiShOpaqueHandle opShFreeHandle , CiShOper oper , void *cnfParas ) ;
typedef void ( *CiSgFreeReqMem ) ( CiSgOpaqueHandle opSgFreeHandle , CiServiceGroupID id , CiPrimitiveID primId , void *reqParas ) ;
typedef void ( *CiSgFreeRspMem ) ( CiSgOpaqueHandle opSgFreeHandle , CiServiceGroupID id , CiPrimitiveID primId , void *rspParas ) ;
typedef void ( *CiSgFreeCnfMem ) ( CiSgOpaqueHandle opSgFreeHandle , CiServiceGroupID id , CiPrimitiveID primId , void *cnfParas ) ;
typedef void ( *CiSgFreeIndMem ) ( CiSgOpaqueHandle opSgFreeHandle , CiServiceGroupID id , CiPrimitiveID primId , void *indParas ) ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPrimList_struct 
 {	 
 UINT16 size ; /* actual number of primitive IDs that are held in the pList */	 
 CiPrimitiveID primId [ 156 ] ;	 
 } CiPrimitiveList;

//ICAT EXPORTED STRUCT 
 typedef struct CiShOperRegisterShCnf_struct 
 {	 
 CiReturnCode rc ;	 
 CiShHandle handle ;	 
 CiShOpaqueHandle opShFreeHandle ;	 
 CiShFreeCnfMem ciShFreeCnfMem ;	 
 } CiShOperRegisterShCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiShOperDeregisterShCnf_struct 
 {	 
 CiReturnCode rc ;	 
 } CiShOperDeregisterShCnf;

//ICAT EXPORTED STRUCTURE 
 typedef struct CiShOperGetVersionReq_struct 
 {	 
 CiServiceGroupID id ;	 
 } CiShOperGetVersionReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiShOperGetVersionCnf_struct 
 {	 
 CiReturnCode rc ;	 
 CiServiceGroupID id ;	 
 UINT8 major ;	 
 UINT8 minor ;	 
 } CiShOperGetVersionCnf;

//ICAT EXPORTED STRUCTURE 
 typedef struct CiShOperRegisterDefaultSgReq_struct 
 {	 
 CiSgOpaqueHandle opSgDefCnfHandle ;	 
 CiConfirm ciCnfDef ;	 
 CiIndicate ciIndDef ;	 
 CiSgOpaqueHandle opSgDefIndHandle ;	 
 } CiShOperRegisterDefaultSgReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiShOperRegisterDefaultSgCnf_struct 
 {	 
 CiReturnCode rc ;	 
 CiServiceHandle defHandle ;	 
 CiSgOpaqueHandle opSgFreeDefCnfHandle ;	 
 CiSgFreeCnfMem ciSgFreeDefCnfMem ;	 
 CiSgOpaqueHandle opSgFreeDefIndHandle ;	 
 CiSgFreeIndMem ciSgFreeDefIndMem ;	 
 } CiShOperRegisterDefaultSgCnf;

//ICAT EXPORTED STRUCTURE 
 typedef struct CiShOperDeregisterDefaultSgReq_struct 
 {	 
 CiServiceHandle defHandle ;	 
 } CiShOperDeregisterDefaultSgReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiShOperDeregisterDefaultSgCnf_struct 
 {	 
 CiReturnCode rc ;	 
 } CiShOperDeregisterDefaultSgCnf;

//ICAT EXPORTED STRUCTURE 
 typedef struct CiShOperRegisterSgReq_struct 
 {	 
 CiServiceGroupID id ;	 
 CiSgOpaqueHandle opSgCnfHandle ;	 
 CiConfirm ciConfirm ;	 
 CiSgOpaqueHandle opSgIndHandle ;	 
 CiIndicate ciIndicate ;	 
 CiSgOpaqueHandle opSgFreeReqHandle ;	 
 CiSgFreeReqMem ciSgFreeReqMem ;	 
 CiSgOpaqueHandle opSgFreeRspHandle ;	 
 CiSgFreeRspMem ciSgFreeRspMem ;	 
 } CiShOperRegisterSgReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiShOperRegisterSgCnf_struct 
 {	 
 CiReturnCode rc ;	 
 CiServiceGroupID id ;	 
 CiServiceHandle handle ;	 
 CiSgOpaqueHandle opSgFreeCnfHandle ;	 
 CiSgFreeCnfMem ciSgFreeCnfMem ;	 
 CiSgOpaqueHandle opSgFreeIndHandle ;	 
 CiSgFreeIndMem ciSgFreeIndMem ;	 
 } CiShOperRegisterSgCnf;

//ICAT EXPORTED STRUCTURE 
 typedef struct CiShOperDeregisterSgReq_struct 
 {	 
 CiServiceHandle handle ;	 
 } CiShOperDeregisterSgReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiShOperDeregisterSgCnf_struct 
 {	 
 CiReturnCode rc ;	 
 CiServiceGroupID id ;	 
 } CiShOperDeregisterSgCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiShQueryExtendedReq_struct 
 {	 
 CiServiceGroupID id ;	 
 } CiShQueryExtendedReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiShQueryExtendedCnf_struct 
 {	 
 CiReturnCode rc ;	 
 CiServiceGroupID id ;	 
 UINT16 mSize ;	 
 CiPrimitiveList ciPrimList ;	 
 } CiShQueryExtendedCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiShQueryExtendedServicePrimReq_struct 
 {	 
 CiServiceGroupID id ;	 
 CiPrimitiveID primId ;	 
 } CiShQueryExtendedServicePrimReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiShQueryExtendedServicePrimCnf_struct 
 {	 
 CiReturnCode rc ;	 
 CiServiceGroupID id ;	 
 CiPrimitiveID primId ;	 
 CiBoolean supported ;	 
 } CiShQueryExtendedServicePrimCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiShQueryExtendedServiceGroupReq_struct 
 {	 
 CiServiceGroupID id ;	 
 } CiShQueryExtendedServiceGroupReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiShQueryExtendedServiceGroupCnf_struct 
 {	 
 CiReturnCode rc ;	 
 CiServiceGroupID id ;	 
 CiBoolean supported ;	 
 } CiShQueryExtendedServiceGroupCnf;

typedef UINT16 CiVersion ;
//ICAT EXPORTED STRUCT 
 typedef struct CiShOperCIVersionNegoReq_struct 
 {	 
 UINT8 ccSuppVerNum ;	 
 UINT8 ssSuppVerNum ;	 
 UINT8 mmSuppVerNum ;	 
 UINT8 pbSuppVerNum ;	 
 UINT8 simSuppVerNum ;	 
 UINT8 msgSuppVerNum ;	 
 UINT8 psSuppVerNum ;	 
 UINT8 datSuppVerNum ;	 
 UINT8 devSuppVerNum ;	 
 UINT8 hscsdSuppVerNum ;	 
 UINT8 debSuppVerNum ;	 
 UINT8 atpiSuppVerNum ;	 
 UINT8 plSuppVerNum ;	 
 UINT8 oamSuppVerNum ;	 
 CiVersion ccSuppVer [ 10 ] ;	 
 CiVersion ssSuppVer [ 10 ] ;	 
 CiVersion mmSuppVer [ 10 ] ;	 
 CiVersion pbSuppVer [ 10 ] ;	 
 CiVersion simSuppVer [ 10 ] ;	 
 CiVersion msgSuppVer [ 10 ] ;	 
 CiVersion psSuppVer [ 10 ] ;	 
 CiVersion datSuppVer [ 10 ] ;	 
 CiVersion devSuppVer [ 10 ] ;	 
 CiVersion hscsdSuppVer [ 10 ] ;	 
 CiVersion debSuppVer [ 10 ] ;	 
 CiVersion atpiSuppVer [ 10 ] ;	 
 CiVersion plSuppVer [ 10 ] ;	 
 CiVersion oamSuppVer [ 10 ] ;	 
 } CiShOperCIVersionNegoReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiShOperCIVersionNegoCnf_struct 
 {	 
 CiReturnCode rc ;	 
 CiVersion ccVer ;	 
 CiVersion ssVer ;	 
 CiVersion mmVer ;	 
 CiVersion pbVer ;	 
 CiVersion simVer ;	 
 CiVersion msgVer ;	 
 CiVersion psVer ;	 
 CiVersion datVer ;	 
 CiVersion devVer ;	 
 CiVersion hscsdVer ;	 
 CiVersion debVer ;	 
 CiVersion atpiVer ;	 
 CiVersion plVer ;	 
 CiVersion oamVer ;	 
 } CiShOperCIVersionNegoCnf;

//ICAT EXPORTED ENUM 
 typedef enum CI_ADDR_NUMTYPE 
 {	 
 CI_NUMTYPE_UNKNOWN = 0x00 , /* Unknown */	 
 CI_NUMTYPE_INTERNATIONAL = 0x01 , /* International Number */	 
 CI_NUMTYPE_NATIONAL = 0x02 , /* National Number */	 
 CI_NUMTYPE_NETWORK = 0x03 , /* Network Specific ( e.g. Operator Access ) */	 
 CI_NUMTYPE_DEDICATED = 0x04 , /* Dedicated Access Number , Short Code */	 
 CI_NUMTYPE_EXTENSION = 0x07 /* Reserved for Extension */	 
 /* << All other codes are reserved >> */	 
 } _CiAddrNumType;

typedef UINT8 CiAddrNumType ;
//ICAT EXPORTED ENUM 
 typedef enum CI_ADDR_NUMPLAN 
 {	 
 CI_NUMPLAN_UNKNOWN = 0x00 , /* Unknown */	 
 CI_NUMPLAN_E164_E163 = 0x01 , /* ISDN / Telephony ( E.164 / E.163 ) */	 
 CI_NUMPLAN_DATA_X121 = 0x03 , /* Data Numbering Plan ( X.121 ) */	 
 CI_NUMPLAN_TELEX_F69 = 0x04 , /* Telex Numbering Plan ( F.69 ) */	 
 CI_NUMPLAN_NATIONAL = 0x08 , /* National Numbering Plan */	 
 CI_NUMPLAN_PRIVATE = 0x09 , /* Private Numbering Plan */	 
 CI_NUMPLAN_RSVD_CTS = 0x0b , /* Reserved for CTS */	 
 CI_NUMPLAN_EXTENSION = 0x0f /* Reserved for Extension */	 
 /* << All other codes are reserved >> */	 
 } _CiAddrNumPlan;

typedef UINT8 CiAddrNumPlan ;
//ICAT EXPORTED STRUCT 
 typedef struct CiAddressType_struct 
 {	 
 CiAddrNumType NumType ; /* TON: Type of Number */	 
 CiAddrNumPlan NumPlan ; /* NPI: Numbering Plan Identification */	 
 } CiAddressType;

//ICAT EXPORTED STRUCT 
 typedef struct CiAddressInfo_struct 
 {	 
 CiAddressType AddrType ; /* Address Type Information */	 
 UINT8 Length ; /* Address Length in digits */	 
 UINT8 Digits [ 40 ] ; /* Address Digits */	 
	 
 } CiAddressInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiOptAddressInfo_struct 
 {	 
 CiBoolean Present ; /* Address Info present? */	 
 CiAddressInfo AddressInfo ; /* Address Info */	 
 } CiOptAddressInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiSubaddrInfo_struct 
 {	 
 CiBoolean Present ; /* Subaddress Info present? */	 
 UINT8 Type ; /* Subaddress Type */	 
 UINT8 Length ; /* Subaddress Length in characters */	 
 UINT8 Digits [ 40 ] ; /* Subaddress Digits */	 
 } CiSubaddrInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiNameInfo_struct 
 {	 
 /*Added by cherryli@02.02.2015 for CQ84004 begin.*/	 
	 
	 
	 
 /*Added by cherryli@02.02.2015 for CQ84004 end.*/	 
 UINT8 Length ; /* Name Length in characters */	 
 UINT8 Name [ 40 ] ; /* Alphanumeric Name */	 
 } CiNameInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiOptNameInfo_struct 
 {	 
 CiBoolean Present ; /* Name Info present? */	 
 CiNameInfo NameInfo ; /* Name Info */	 
 } CiOptNameInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiCallerInfo_struct 
 {	 
 CiOptAddressInfo OptCallerNumber ; /* Optional Caller Phone Number */	 
 CiSubaddrInfo OptCallerSubaddr ; /* Optional Caller Subaddress */	 
 CiOptNameInfo OptCallerName ; /* Optional Caller Name ( Alpha Tag ) */	 
 } CiCallerInfo;

//ICAT EXPORTED ENUM 
 typedef enum SSI_CALL_STATUS 
 {	 
 SSI_CS_CFU_ACTIVE = 0 , /* Unconditional Call Forwarding active */	 
 SSI_CS_CCF_ACTIVE , /* Conditional CF trigger ( s ) active */	 
 SSI_CS_FORWARDED , /* Call has been forwarded */	 
 SSI_CS_WAITING , /* Call is waiting */	 
 SSI_CS_CUG_CALL , /* Outgoing CUG call ( Index also present ) */	 
 SSI_CS_MO_BARRED , /* Outgoing ( MO ) calls are barred */	 
 SSI_CS_MT_BARRED , /* Incoming ( MT ) calls are barred */	 
 SSI_CS_CLIR_REJECTED , /* CLIR suppression rejected */	 
 SSI_CS_DEFLECTED , /* Call has been deflected */	 
 /* This one must always be last in the list! */	 
 SSI_NUM_CS /* Number of SSI Call Status values */	 
 } _CiSsiCallStatus;

typedef UINT8 CiSsiCallStatus ;
//ICAT EXPORTED STRUCT 
 typedef struct CiSsiNotifyInfo_struct 
 {	 
 CiSsiCallStatus Status ; /* Call Status indicator */	 
 UINT8 CugIndex ; /* CUG Index ( if applicable ) */	 
 } CiSsiNotifyInfo;

//ICAT EXPORTED ENUM 
 typedef enum SSU_CALL_STATUS 
 {	 
 SSU_CS_FORWARDED = 0 , /* Call has been forwarded */	 
 SSU_CS_CUG_CALL , /* Incoming CUG call ( Index also present ) */	 
 SSU_CS_HELD , /* Call held ( Voice Call ) */	 
 SSU_CS_UNHELD , /* Call retrieved ( Voice Call ) */	 
 SSU_CS_ENTERED_MPTY , /* Multiparty call entered ( Voice call ) */	 
 SSU_CS_RELEASED_HELD , /* Held call released ( Voice call ) */	 
 SSU_CS_FORWARD_CHECK , /* Forward Check SS message received */	 
 SSU_CS_ECT_ALERTING , /* Connecting with remote party in ECT */	 
 SSU_CS_ECT_CONNECTED , /* Connected with remote party in ECT */	 
 /* ( Caller Information may also be present ) */	 
 SSU_CS_DEFLECTED , /* Call has been deflected */	 
 SSU_CS_INCOMING_FORWARDED , /* Additional incoming call forwarded */	 
 /* This one must always be last in the list! */	 
 SSU_NUM_CS /* Number of SSU Call Status values */	 
 } _CiSsuCallStatus;

typedef UINT8 CiSsuCallStatus ;
//ICAT EXPORTED STRUCT 
 typedef struct CiSsuNotifyInfo_struct 
 {	 
 CiSsuCallStatus Status ; /* Call Status indicator */	 
 UINT8 CugIndex ; /* CUG Index ( if applicable ) */	 
 CiCallerInfo CallerInfo ; /* Optional Caller Information */	 
 /* ( Caller Name info is never present ) */	 
 } CiSsuNotifyInfo;

//ICAT EXPORTED ENUM 
 typedef enum CI_BSTYPE_SPEED 
 {	 
 CI_BSTYPE_SPEED_AUTOBAUD = 0 , /* Autobauding for 3.100000 kHz Non-Transparent */	 
	 
 /* 3.100000 kHz Audio definitions */	 
 CI_BSTYPE_SPEED_300_V21 , /* 300 bps ( V.21 ) */	 
 CI_BSTYPE_SPEED_1200_V22 , /* 1200 bps ( V.22 ) */	 
 CI_BSTYPE_SPEED_1200_75_V23 , /* 1200 / 75 bps ( V.23 ) */	 
 CI_BSTYPE_SPEED_2400_V22BIS , /* 2400 bps ( V.22bis ) */	 
 CI_BSTYPE_SPEED_2400_V26TER , /* 2400 bps ( V.26ter ) */	 
 CI_BSTYPE_SPEED_4800_V32 , /* 4800 bps ( V.32 ) */	 
 CI_BSTYPE_SPEED_9600_V32 , /* 9600 bps ( V.32 ) */	 
	 
 /* V.34 definitions */	 
 CI_BSTYPE_SPEED_9600_V34 , /* 9600 bps */	 
 CI_BSTYPE_SPEED_14400_V34 , /* 14400 bps */	 
 CI_BSTYPE_SPEED_19200_V34 , /* 19200 bps */	 
 CI_BSTYPE_SPEED_28800_V34 , /* 28800 bps */	 
 CI_BSTYPE_SPEED_33600_V34 , /* 33600 bps */	 
	 
 /* V.34 definitions */	 
 CI_BSTYPE_SPEED_1200_V120 , /* 1200 bps */	 
 CI_BSTYPE_SPEED_2400_V120 , /* 2400 bps */	 
 CI_BSTYPE_SPEED_4800_V120 , /* 4800 bps */	 
 CI_BSTYPE_SPEED_9600_V120 , /* 9600 bps */	 
 CI_BSTYPE_SPEED_14400_V120 , /* 14.400000 kbps */	 
 CI_BSTYPE_SPEED_19200_V120 , /* 19.200000 kbps */	 
 CI_BSTYPE_SPEED_28800_V120 , /* 28.800000 kbps */	 
 CI_BSTYPE_SPEED_38400_V120 , /* 38.400000 kbps */	 
 CI_BSTYPE_SPEED_48000_V120 , /* 48.000000 kbps */	 
 CI_BSTYPE_SPEED_56000_V120 , /* 56.000000 kbps */	 
	 
 /* V.110 or X.31 Flag Stuffing UDI definitions */	 
 CI_BSTYPE_SPEED_300_V110 , /* 300 bps */	 
 CI_BSTYPE_SPEED_1200_V110 , /* 1200 bps */	 
 CI_BSTYPE_SPEED_2400_V110_X31 , /* 2400 bps */	 
 CI_BSTYPE_SPEED_4800_V110_X31 , /* 4800 bps */	 
 CI_BSTYPE_SPEED_9600_V110_X31 , /* 9600 bps */	 
 CI_BSTYPE_SPEED_14400_V110_X31 , /* 14.400000 kbps */	 
 CI_BSTYPE_SPEED_19200_V110_X31 , /* 19.200000 kbps */	 
 CI_BSTYPE_SPEED_28800_V110_X31 , /* 28.800000 kbps */	 
 CI_BSTYPE_SPEED_38400_V110_X31 , /* 38.400000 kbps */	 
 CI_BSTYPE_SPEED_48000_V110_X31 , /* 48.000000 kbps */	 
 CI_BSTYPE_SPEED_56000_V110_X31 , /* 56.000000 kbps ( can also get FTM ) */	 
 CI_BSTYPE_SPEED_64000_X31 , /* 64.000000 kbps ( can also get FTM ) */	 
	 
 /* Bit Transparent Mode definitions */	 
 CI_BSTYPE_SPEED_56000_BTM , /* 56 kbps */	 
 CI_BSTYPE_SPEED_64000_BTM , /* 64 kbps */	 
	 
 /* PIAFS ( Personal Internet Access Forum Standard ) definitions */	 
 CI_BSTYPE_SPEED_32000_PIAFS , /* 56.000000 kbps ( PIAFS32k ) */	 
 CI_BSTYPE_SPEED_64000_PIAFS , /* 64.000000 kbps ( PIAFS64k ) */	 
	 
 /* Multimedia Call definitions */	 
 CI_BSTYPE_SPEED_28800_MM , /* 28.800000 kbps */	 
 CI_BSTYPE_SPEED_32000_MM , /* 32.000000 kbps */	 
 CI_BSTYPE_SPEED_33600_MM , /* 33.600000 kbps */	 
 CI_BSTYPE_SPEED_56000_MM , /* 56 kbps */	 
 CI_BSTYPE_SPEED_64000_MM , /* 64 kbps */	 
	 
 /* This one must always be last in the list! */	 
 CI_NUM_BSTYPE_SPEEDS /* Number of Bearer Service Speeds defined */	 
 } _CiBsTypeSpeed;

typedef UINT8 CiBsTypeSpeed ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPassword_struct 
 {	 
 UINT8 len ; /* length of the password , [ CI_MIN_PASSWORD_LENGTH - CI_MAX_PASSWORD_LENGTH ] */	 
 UINT8 data [ 16 ] ;	 
 } CiPassword;

//ICAT EXPORTED STRUCT 
 typedef struct CiString_struct 
 {	 
 UINT16 len ;	 
 CHAR valStr [ 100 ] ;	 
 } CiString;

//ICAT EXPORTED STRUCT 
 typedef struct CiStringExt_struct 
 {	 
 UINT16 len ;	 
 CHAR valStr [ 200 ] ;	 
 } CiStringExt;

//ICAT EXPORTED STRUCT 
 typedef struct CiNumericRange_struct 
 {	 
 UINT32 min ; /* lower limit */	 
 UINT32 max ; /* upper limit */	 
 } CiNumericRange;

typedef UINT32 CiBitRange ;
//ICAT EXPORTED STRUCT 
 typedef struct CiNumericList_struct 
 {	 
 CiBoolean hasRange ; /* range of numbers are included */	 
 CiBoolean hasIndvNums ; /* individual numbers are included */	 
 UINT16 rangeLstSize ; /* actual number of CiNumericRange structures held in the range list ,	 
 ignored if hasRange is FALSE */	 
 CiNumericRange rangeLst [ 20 ] ;	 
 UINT16 indvLstSize ; /* actual number of numbers held in the individual number list ,	 
 ignored if hasIndNums is FALSE */	 
 UINT32 indvList [ 50 ] ;	 
 } CiNumericList;

//ICAT EXPORTED ENUM 
 typedef enum CIERRACCTYPE 
 {	 
 CIERR_ACC_TYPE_CHV1 , /* CHV1 password */	 
 CIERR_ACC_TYPE_CHV2 , /* CHV2 password */	 
 CIERR_ACC_TYPE_UNBLOCK_CHV1 , /* Unblock CHV1 password */	 
 CIERR_ACC_TYPE_UNBLOCK_CHV2 , /* Unblock CHV2 password */	 
 CIERR_ACC_TYPE_ADMIN , /* Administrate access right */	 
 CIERR_ACC_TYPE_CB , /* call barring password */	 
 CIERR_ACC_TYPE_INTERNAL /* internal access type */	 
 } _CiErrAccType;

typedef UINT8 CiErrAccType ;
//ICAT EXPORTED ENUM 
 typedef enum CIERRACCCAUSE 
 {	 
 CIERR_ACC_PRIV_REQUIRED , /* access privilege ( e.g. password ) is required to gain access right */	 
 CIERR_ACC_PW_NOT_INIT , /* password is not initialized or registered */	 
 CIERR_ACC_PW_BLOCKED , /* password was blocked because of a series of unsuccessful verification attempts */	 
 CIERR_ACC_RES_UNAVAIL , /* required resource is unavailable */	 
 CIERR_ACC_UNKNOWN /* unknown error access cause */	 
 } _CiErrAccCause;

typedef UINT8 CiErrAccCause ;
//ICAT EXPORTED ENUM 
 typedef enum CIERRINPUTCODE 
 {	 
 /* wrong inputs */	 
 CIRC_ERR_WRONG_PASSWORD = 0xF000 , /* wrong password */	 
 CIRC_ERR_BAD_PARAMETER , /* parameter invalid or out of range */	 
 CIRC_ERR_WRONG_INDEX , /* invalid index */	 
 CIRC_ERR_LONG_STR , /* string too long */	 
 CIRC_ERR_WRONG_CHAR , /* invalid characters in text string */	 
 CIRC_ERR_LONG_DIALSTR /* dial string too long */	 
 } _CiErrInputCode;

typedef UINT16 CiErrInputCode ;
//ICAT EXPORTED ENUM 
 typedef enum CIERRINTERLINKCAUSE 
 {	 
 CIERR_INTERLINK_DOWN , /* interlink broken , receiver ' s action TBD */	 
 CIERR_INTERLINK_MEM_OVERFLOW /* memory overflow in the link , receiver ' s action TBD */	 
 } _CiErrInterLinkCause;

typedef UINT8 CiErrInterLinkCause ;
typedef UINT8 CiEmptyPrim ;
//ICAT EXPORTED STRUCT 
 typedef struct CiLongAdrInfo_tag 
 {	 
 UINT8 Length ; /* Name Length in characters */	 
 UINT8 Name [ 50 ] ; /* Alphanumeric Name */	 
 } CiLongAdrInfo;

//ICAT EXPORTED ENUM 
 typedef enum CI_EDIT_CMD_ACTION 
 {	 
 CIED_ADD = 0 ,	 
 CIED_DELETE = 1 ,	 
 CIED_REPLACE = 2 ,	 
 } _CiEditCmdType;

typedef UINT8 CiEditCmdType ;
//ICAT EXPORTED ENUM 
 typedef enum CICC_EMLPP_CALL_PRIORITY 
 {	 
 CICC_EMLPP_CALL_PRIORITY_NONE = 0x00 ,	 
 CICC_EMLPP_CALL_PRIORITY_4 = 0x01 ,	 
 CICC_EMLPP_CALL_PRIORITY_3 = 0x02 ,	 
 CICC_EMLPP_CALL_PRIORITY_2 = 0x03 ,	 
 CICC_EMLPP_CALL_PRIORITY_1 = 0x04 ,	 
 CICC_EMLPP_CALL_PRIORITY_0 = 0x05 ,	 
 CICC_EMLPP_CALL_PRIORITY_B = 0x06 ,	 
 CICC_EMLPP_CALL_PRIORITY_A = 0x07 ,	 
 CICC_EMLPP_NUM_OF_PRIORITIES = 0x07	 
 } _CiCcEmlppCallPriority;

typedef UINT8 CiCcEmlppCallPriority ;
//ICAT EXPORTED ENUM 
 typedef enum CI_ERR_PRIM 
 {	 
 CI_ERR_PRIM_HASNOSUPPORT_CNF = 0xF000 ,	 
 CI_ERR_PRIM_HASINVALIDPARAS_CNF ,	 
 CI_ERR_PRIM_ISINVALIDREQUEST_CNF ,	 
 CI_ERR_PRIM_SIMNOTREADY_CNF ,	 
 CI_ERR_PRIM_ACCESSDENIED_CNF ,	 
 CI_ERR_PRIM_INTERLINKDOWN_IND ,	 
 CI_ERR_PRIM_INTERLINKDOWN_RSP ,	 
 CI_ERR_PRIM_INTERLINKUP_IND ,	 
	 
 /* This should always be the last enum entry */	 
 CI_ERR_PRIM_NEXTAVAIL	 
 } _CiErrPrim;

typedef UINT16 CiErrPrim ;
typedef CiEmptyPrim CiErrPrimHasNoSupportCnf ;
//ICAT EXPORTED STRUCT 
 typedef struct CiErrPrimHasInvalidParasCnf_struct 
 {	 
 CiErrInputCode err ;	 
 } CiErrPrimHasInvalidParasCnf;

typedef CiEmptyPrim CiErrPrimIsInvalidRequestCnf ;
typedef CiEmptyPrim CiErrPrimSimNotReadyCnf ;
//ICAT EXPORTED STRUCT 
 typedef struct CiErrPrimAccessDeniedCnf_struct 
 {	 
 CiErrAccType type ;	 
 CiErrAccCause cause ;	 
 } CiErrPrimAccessDeniedCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiErrPrimInterlinkDownInd_struct 
 {	 
 CiErrInterLinkCause cause ;	 
 } CiErrPrimInterlinkDownInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiErrPrimInterlinkDownRsp_struct 
 {	 
 CiErrInterLinkCause cause ;	 
 } CiErrPrimInterlinkDownRsp;

typedef CiEmptyPrim CiErrPrimInterlinkUpInd ;
//ICAT EXPORTED ENUM 
 typedef enum CI_PS_PRIM 
 {	 
 CI_PS_PRIM_SET_ATTACH_STATE_REQ = 1 , /**< \brief Requests to attach ME to , or detach ME from , the packet domain service \details */	 
 CI_PS_PRIM_SET_ATTACH_STATE_CNF , /**< \brief Confirms a request and attaches ME to , or detaches ME from , the packet domain service \details */	 
 CI_PS_PRIM_GET_ATTACH_STATE_REQ , /**< \brief Requests to get the current packet domain service state \details */	 
 CI_PS_PRIM_GET_ATTACH_STATE_CNF , /**< \brief Confirms a request and returns the current packet domain service state \details */	 
 CI_PS_PRIM_DEFINE_PDP_CTX_REQ , /**< \brief Requests to define a PDP context for a specified CID \details */	 
 CI_PS_PRIM_DEFINE_PDP_CTX_CNF , /**< \brief Confirms a request to define a PDP context for a specified CID	 
 * \details If the PDP context address field is NULL , a dynamic address is requested. */	 
 CI_PS_PRIM_DELETE_PDP_CTX_REQ , /**< \brief Requests to delete a PDP context \details */	 
 CI_PS_PRIM_DELETE_PDP_CTX_CNF , /**< \brief Confirms a request to delete a PDP context \details */	 
 CI_PS_PRIM_GET_PDP_CTX_REQ , /**< \brief Requests to get a PDP context definition \details */	 
 CI_PS_PRIM_GET_PDP_CTX_CNF = 10 , /**< \brief Confirms a request and returns the PDP context setting \details */	 
 CI_PS_PRIM_GET_PDP_CTX_CAPS_REQ , /**< \brief Requests the PDP context capabilities supported by the cellular subsystem \details */	 
 CI_PS_PRIM_GET_PDP_CTX_CAPS_CNF , /**< \brief Confirms a request and returns the PDP context capabilities supported by the cellular subsystem \details */	 
 CI_PS_PRIM_SET_PDP_CTX_ACT_STATE_REQ , /**< \brief Requests to activate ( or deactivate ) one or all PDP contexts \details */	 
 CI_PS_PRIM_SET_PDP_CTX_ACT_STATE_CNF , /**< \brief Confirms a request and activates ( or deactivates ) one or all PDP contexts \details */	 
 CI_PS_PRIM_GET_PDP_CTXS_ACT_STATE_REQ , /**< \brief Requests to get the current activation state of all defined PDP contexts \details */	 
 CI_PS_PRIM_GET_PDP_CTXS_ACT_STATE_CNF , /**< \brief Confirms a request and returns the current activation state of all defined PDP contexts \details */	 
 CI_PS_PRIM_ENTER_DATA_STATE_REQ , /**< \brief Requests to notify the cellular subsystem that the application subsystem is entering a data state , which means it is now going to send or receive packet data	 
 * \details This request triggers a PDP attach procedure and / or a PDP context activation procedure if they have not already been generated.	 
 * The parameter optimizedData enables the optimized ACI data plane.	 
 * This parameter must be set to TRUE to use the optimized DATA service group primitives.	 
 * Note that the option not to use the ACI optimized data plane is supported for backward compatibility. */	 
 CI_PS_PRIM_ENTER_DATA_STATE_CNF , /**< \brief Confirms a request and notifies the cellular subsystem that the application subsystem has entered a data state	 
 * \details Now , the cellular subsystem can start using the DATA service group	 
 * primitives to send and receive data over the packet service domain. */	 
 CI_PS_PRIM_MT_PDP_CTX_ACT_MODIFY_IND , /**< \brief Indicates that a network initiated the activation or modification of a PDP context \details */	 
 CI_PS_PRIM_MT_PDP_CTX_ACT_MODIFY_RSP = 20 , /**< \brief Responds to a mobile terminated PDP context indication \details */	 
 CI_PS_PRIM_MT_PDP_CTX_ACTED_IND , /**< \brief Indicates the mobile terminated PDP context is activated after manual or auto answer	 
 * \details The cellular subsystem assigns the CID ( context ID ) for the MT PDP context. */	 
 CI_PS_PRIM_SET_GSMGPRS_CLASS_REQ , /**< \brief Requests to set the mobile class for GSM / GPRS \details This primitive only applies to GSM / GPRS networks. */	 
 CI_PS_PRIM_SET_GSMGPRS_CLASS_CNF , /**< \brief Confirms the request and sets the mobile class for GSM / GPRS \details This primitive only applies to GSM / GPRS networks. */	 
 CI_PS_PRIM_GET_GSMGPRS_CLASS_REQ , /**< \brief Requests the current setting of the GSM / GPRS mobile class \details This primitive only applies to GSM / GPRS networks. */	 
 CI_PS_PRIM_GET_GSMGPRS_CLASS_CNF , /**< \brief Confirms a request and gets the current GSM / GPRS mobile class \details This only applies to GSM / GPRS networks. */	 
 CI_PS_PRIM_GET_GSMGPRS_CLASSES_REQ , /**< \brief Requests the supported GSM / GPRS mobile classes \details */	 
 CI_PS_PRIM_GET_GSMGPRS_CLASSES_CNF , /**< \brief Confirms a request and returns the supported GSM / GPRS mobile classes \details This only applies to GSM / GPRS networks. */	 
 CI_PS_PRIM_ENABLE_NW_REG_IND_REQ , /**< \brief Requests to enable or disable GPRS network registration status reports \details */	 
 CI_PS_PRIM_ENABLE_NW_REG_IND_CNF , /**< \brief Confirms a request and enables or disables GPRS network registration status reports \details */	 
 CI_PS_PRIM_NW_REG_IND = 30 , /**< \brief Indicates the GPRS network registration status \details GPRS network indications may be enabled or disabled by CI_PS_PRIM_ENABLE_NW_REG_IND_REQ.	 
 * This indication is disabled by default. No explicit response is required. */	 
 CI_PS_PRIM_SET_QOS_REQ , /**< \brief Requests to set the QoS profile for a PDP context	 
 * \details The ME checks the minimum acceptable profile against the negotiated profile returned in the Activate PDP Context Accept message.	 
 * The required quality of service profile is used when the ME sends an Activate PDP Context Request message to the network.	 
 * This is only used for 2.500000 G ( R97 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_GET_3G_QOS_REQ for 3 G ( R99 ) QoS profile parameters.*/	 
 CI_PS_PRIM_SET_QOS_CNF , /**< \brief Confirms a request and sets the QoS profile	 
 * \details This is only used for 2.500000 G ( R97 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_GET_3G_QOS_CNF for 3 G ( R99 ) QoS profile parameters. */	 
 CI_PS_PRIM_DEL_QOS_REQ , /**< \brief Requests to delete the QoS profile for a PDP context	 
 * \details If a PDP context does not have a minimum or required	 
 * QoS profile , the QoS is determined by the network on PDP context activation.	 
 * This is only used for 2.500000 G ( R97 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_DEL_3G_QOS_REQ for 3 G ( R99 ) QoS profile parameters.*/	 
 CI_PS_PRIM_DEL_QOS_CNF , /**< \brief Confirms a request and deletes the QoS profile setting	 
 * \details This is only used for 2.500000 G ( R97 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_DEL_3G_QOS_CNF for 3 G ( R99 ) QoS profile parameters.*/	 
 CI_PS_PRIM_GET_QOS_REQ , /**< \brief Requests the QoS profile for a PDP context	 
 * \details This is only used for 2.500000 G ( R97 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_GET_3G_QOS_REQ for 3 G ( R99 ) QoS profile parameters.*/	 
 CI_PS_PRIM_GET_QOS_CNF , /**< \brief Confirms a request and gets the QoS profile for a PDP context	 
 * \details This is only used for 2.500000 G ( R97 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_GET_3G_QOS_CNF for 3 G ( R99 ) QoS profile parameters.*/	 
 /*Michal Bukai - AutoAttach Configuration - Samsung - START*/	 
 CI_PS_PRIM_ENABLE_POWERON_AUTO_ATTACH_REQ , /**< \brief Configure auto attach to PS domain on power up	 
 * \details The configuration will be saved in NVM and will be effective in the next power up. */	 
 CI_PS_PRIM_ENABLE_POWERON_AUTO_ATTACH_CNF , /**< \brief Confirms the request and updates NVM auto attach configuration \details */	 
 /*Michal Bukai - AutoAttach Configuration - Samsung - End*/	 
 CI_PS_PRIM_MT_PDP_CTX_REJECTED_IND , /**< \brief \details NOT SUPPORTED REMOVE FROM API */	 
 CI_PS_PRIM_PDP_CTX_DEACTED_IND = 40 , /**< \brief Indicates that the PDP context has been deactivated	 
 * \details This indication is sent if PS event reports are enabled ; see CI_PS_PRIM_ENABLE_EVENTS_REPORTING_REQ.*/	 
 CI_PS_PRIM_PDP_CTX_REACTED_IND , /**< \brief \details NOT SUPPORTED REMOVE FROM API */	 
 CI_PS_PRIM_DETACHED_IND , /**< \brief Indicates that the ME has detached from the packet service domain	 
 * \details The indication is sent if PS event reports are enabled ; see CI_PS_PRIM_ENABLE_EVENTS_REPORTING_REQ. */	 
 CI_PS_PRIM_GPRS_CLASS_CHANGED_IND , /**< \brief Indicates that the GSM / GPRS mobile class has changed \details */	 
 CI_PS_PRIM_GET_DEFINED_CID_LIST_REQ , /**< \brief Requests the defined PDP context identifiers list \details */	 
 CI_PS_PRIM_GET_DEFINED_CID_LIST_CNF , /**< \brief Confirms a request and returns the defined PDP context identifiers list \details */	 
 CI_PS_PRIM_GET_NW_REG_STATUS_REQ , /**< \brief Requests the GPRS network registration status \details */	 
 CI_PS_PRIM_GET_NW_REG_STATUS_CNF , /**< \brief Confirms a request and returns the GPRS network registration status \details */	 
 CI_PS_PRIM_GET_QOS_CAPS_REQ , /**< \brief Requests the QoS capabilities	 
 * \details This is only used for 2.500000 G ( R97 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_GET_3G_QOS_CAPS_REQ for 3 G ( R99 ) QoS profile parameters.*/	 
 CI_PS_PRIM_GET_QOS_CAPS_CNF , /**< \brief Confirms a request and returns the QoS capabilities	 
 * \details This is only used for 2.500000 G ( R97 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_GET_3G_QOS_CAPS_CNF for 3 G ( R99 ) QoS profile parameters.*/	 
 CI_PS_PRIM_ENABLE_EVENTS_REPORTING_REQ = 50 , /**< \brief Requests to enable or disable PS event reports	 
 * \details By default , event reporting indications are enabled.	 
 * Event indications include the following: \n	 
 * CI_PS_PRIM_PDP_CTX_DEACTED_IND \n	 
 * CI_PS_PRIM_DETACHED_IND */	 
 CI_PS_PRIM_ENABLE_EVENTS_REPORTING_CNF , /**< \brief Confirms a request and enables or disables PS event reports \details */	 
	 
 /* SCR #1401348: 3 G Quality of Service ( QoS ) primitives */	 
 CI_PS_PRIM_GET_3G_QOS_REQ , /**< \brief Requests the 3 G QoS profile for a PDP context	 
 * \details This is only used for 3 G ( R99 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_GET_QOS_REQ for 2.500000 G ( R97 ) QoS profile parameters.*/	 
 CI_PS_PRIM_GET_3G_QOS_CNF , /**< \brief Confirms a request and returns the 3 G QoS profile for a PDP context	 
 * \details This is only used for 3 G ( R99 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_GET_QOS_CNF for 2.500000 G ( R97 ) QoS profile parameters.*/	 
 CI_PS_PRIM_SET_3G_QOS_REQ , /**< \brief Requests to set a 3 G QoS profile for PDP context activation	 
 * \details The negotiated QoS profile cannot be written by this request.	 
 * If the qosType parameter is set to CI_PS_3G_QOSTYPE_NEG , CCI returns an error indication.	 
 * The required and minimum quality of service profiles are used when the MT sends an Activate PDP Context Request for a primary or	 
 * secondary PDP context or a Modify PDP Context Request to the network.	 
 * This primitive is used to set 3 G ( R99 ) QoS profile parameters	 
 * Use CI_PS_PRIM_SET_QOS_REQ to set the 2.500000 G ( R97 ) QoS profile parameters.*/	 
 CI_PS_PRIM_SET_3G_QOS_CNF , /**< \brief Confirms a request and sets a 3 G QoS profile for a PDP context	 
 * \details This is only used for 3 G ( R99 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_SET_QOS_CNF for 2.500000 G ( R97 ) QoS profile parameters.*/	 
 CI_PS_PRIM_DEL_3G_QOS_REQ , /**< \brief Requests to delete the 3 G QoS profile for a PDP context	 
 * \details The negotiated QoS profile cannot be deleted by this request.	 
 * If the qosType parameter is set to CI_PS_3G_QOSTYPE_NEG , CCI returns an error indication.	 
 * If a PDP context does not have a minimum or required QoS profile ,	 
 * the QoS is determined by the network on PDP context activation.	 
 * This is only used for 3 G ( R99 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_DEL_QOS_REQ for 2.500000 G ( R97 ) QoS profile parameters.*/	 
 CI_PS_PRIM_DEL_3G_QOS_CNF , /**< \brief Confirms a request and deletes the 3 G QoS profile	 
 * \details This is only used for 3 G ( R99 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_DEL_QOS_CNF for 2.500000 G ( R97 ) QoS profile parameters. */	 
	 
 CI_PS_PRIM_GET_3G_QOS_CAPS_REQ , /**< \brief Requests the 3 G QoS capabilities	 
 * \details This is only used for the 3 G ( R99 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_GET_QOS_CAPS_REQ for 2.500000 G ( R97 ) QoS profile parameters.*/	 
 CI_PS_PRIM_GET_3G_QOS_CAPS_CNF , /**< \brief Confirms a request and gets the 3 G QoS capabilities	 
 * \details This is only used for 3 G ( R99 ) QoS profile parameters.	 
 * Use CI_PS_PRIM_GET_QOS_CAPS_CNF for 2.500000 G ( R97 ) QoS profile parameters. */	 
	 
 /* SCR #1438547: Secondary PDP Context primitives */	 
 CI_PS_PRIM_DEFINE_SEC_PDP_CTX_REQ = 60 , /**< \brief Requests to define a secondary PDP context \details */	 
 CI_PS_PRIM_DEFINE_SEC_PDP_CTX_CNF , /**< \brief Confirms a request to define a secondary PDP context \details */	 
 CI_PS_PRIM_DELETE_SEC_PDP_CTX_REQ , /**< \brief Requests to delete a secondary PDP context	 
 * \details An error is returned if the secondary PDP context does not exist. */	 
 CI_PS_PRIM_DELETE_SEC_PDP_CTX_CNF , /**< \brief Confirms a request to delete a secondary PDP context \details */	 
 CI_PS_PRIM_GET_SEC_PDP_CTX_REQ , /**< \brief Requests a secondary PDP context definition	 
 * \details An error is returned if the secondary PDP context does not exist. */	 
 CI_PS_PRIM_GET_SEC_PDP_CTX_CNF , /**< \brief Confirms a request and returns the secondary PDP context definition \details An error is returned if the secondary PDP context does not exist. */	 
	 
 /* SCR #1438547: traffic flow template ( TFT ) primitives */	 
 CI_PS_PRIM_DEFINE_TFT_FILTER_REQ , /**< \brief Requests to define a traffic flow template ( TFT ) packet filter	 
 * \details Traffic flow templates are described in 3 GPP TS 23.060000 section 15.300000 .	 
 * Each PDP context connected to a particular PDP address and APN may be associated with a traffic flow template ( TFT ) . ( TFTs enable	 
 * filtering of downlink IP packets. )	 
 * A TFT contains one to eight packet filters.	 
 * The use of traffic flow templates allows multiple PDP contexts	 
 * ( each with a different quality of service ) to share the same PDP address.	 
 * The TFT packet filters are used to route incoming IP packets to their appropriate PDP contexts.	 
 * Only one PDP context may exist without an associated TFT , and this PDP context	 
 * is considered the default	 
 * PDP context. The network routes downlink packets	 
 * to this PDP context if none of the TFT packet filters apply.	 
 * A TFT , if one exists , is always associated with a PDP context during secondary PDP context activation.	 
 * A TFT may be added to an activated PDP context ( either a primary or a secondary context )	 
 * using the MS-initiated PDP context modify procedure , which is initiated by a CI_PS_PRIM_MODIFY_PDP_CTX_REQ request.	 
 * The packet filter contents field encoding is specified in 3 GPP TS 24.008000 Table 10.500000 .162 ( Section 10.500000 .6.12 ) .	 
 * An error is returned if no more TFT packet filters are allowed. */	 
 CI_PS_PRIM_DEFINE_TFT_FILTER_CNF , /**< \brief Confirms a request and defines a TFT packet filter \details */	 
 CI_PS_PRIM_DELETE_TFT_REQ , /**< \brief Requests to delete the traffic flow template ( TFT ) associated with a PDP context \details All packet filters that comprise the indicated TFT are deleted.	 
 * An error is returned when: \n	 
 * No TFT exists for the indicated PDP context. \n	 
 * The PDP context itself ( either primary or secondary ) is not defined. \n	 
 * More than one PDP context is using a single PDP address , and deleting this TFT would violate the rule that only one PDP	 
 * context using a particular PDP address may exist without a TFT associated with it. \n	 
 * See 3 GPP TS 23.060000 section 15.300000 .1 ( Rules for Operations on TFTs ) . */	 
 CI_PS_PRIM_DELETE_TFT_CNF , /**< \brief Confirms a request and deletes the traffic flow template \details */	 
 CI_PS_PRIM_GET_TFT_REQ = 70 , /**< \brief Requests to get the traffic flow template ( TFT ) associated with a PDP context \details Requests a list of all packet filters that comprise the TFT for the specified PDP context.	 
 * An error is returned if a TFT does not exist for the indicated PDP context , or if the PDP context ( either primary or secondary ) is not	 
 * defined. */	 
 CI_PS_PRIM_GET_TFT_CNF , /**< \brief Confirms a request and gets the traffic flow template ( TFT ) associated with a PDP context \details */	 
	 
 /* SCR TBD: PDP context modify primitives */	 
 CI_PS_PRIM_MODIFY_PDP_CTX_REQ , /**< \brief Requests to modify one PDP context or all active PDP contexts \details Allows the quality of service ( QoS ) and / or the traffic flow template ( TFT ) to be modified for a PDP context that has already been	 
 * activated. This request can be used for either primary or secondary PDP contexts.	 
 * Before issuing this request , set up or modify the QoS and / or TFT , using the appropriate CI requests. */	 
 CI_PS_PRIM_MODIFY_PDP_CTX_CNF , /**< \brief Confirms a request and modifies one PDP context or all active PDP contexts \details */	 
 CI_PS_PRIM_GET_ACTIVE_CID_LIST_REQ , /**< \brief Requests to get a list of context identifiers for all active PDP contexts \details This request is similar to CI_PS_PRIM_GET_DEFINED_CID_LIST_REQ except that it only returns information for active PDP contexts.	 
 * It is provided to support the same functionality as the " AT+CGCMOD=? " command. See 3 GPP TS 27.007000 section 10.100000 .11. */	 
 CI_PS_PRIM_GET_ACTIVE_CID_LIST_CNF , /**< \brief Confirms a request and returns a list of context identifiers for all active PDP contexts \details */	 
 CI_PS_PRIM_REPORT_COUNTER_REQ , /**< \brief Requests to configure the PDP Context Data Counter report	 
 * \details Data counters are maintained by the protocol stack for all active PDP contexts.	 
 * Data counter values are reported to the application subsystem using CI_PS_PRIM_COUNTER_IND.	 
 * This request is rejected if the control plane has not been attached to packet domain services or there are no active PDP contexts. */	 
 CI_PS_PRIM_REPORT_COUNTER_CNF , /**< \brief Confirms a request and configures the PDP Context Data Counter report \details */	 
 CI_PS_PRIM_RESET_COUNTER_REQ , /**< \brief Requests to reset PDP context data counters	 
 * \details Data counters are maintained by the protocol stack for all active PDP contexts.	 
 * Depending on the parameter settings , this request resets the data counters to zero for one or all active PDP contexts.	 
 * This request is rejected if: \n	 
 * The Control Plane is not attached to Packet Domain services. \n	 
 * There are no active PDP contexts. \n	 
 * The doAll parameter is FALSE and the CID parameter is invalid or does not specify an active PDP context. */	 
 CI_PS_PRIM_RESET_COUNTER_CNF , /**< \brief Confirms a request and resets PDP context data counters \details */	 
 CI_PS_PRIM_COUNTER_IND = 80 , /**< \brief Indicates a PDP context data counter report \details CCI sends this indication on request or periodically , as configured by CI_PS_PRIM_REPORT_COUNTER_REQ. If a periodic report cycle	 
 * is stopped , this indication is disabled.	 
 * The totals indicate the number of bytes ( octets ) sent and received since the data counters were last reset. \n	 
 * The totalULBytes counter is the total number of uplink data octets before compression ( if any ) . \n	 
 * The totalDLBytes counter is the total number of downlink data octets after decompression ( if any ) . \n	 
 * See also CI_PS_PRIM_RESET_COUNTER_REQ. */	 
	 
 CI_PS_PRIM_SEND_DATA_REQ , /**< \brief \details NOT SUPPORTED REMOVE FROM API */	 
	 
 CI_PS_PRIM_SEND_DATA_CNF , /**< \brief \details NOT SUPPORTED REMOVE FROM API */	 
	 
 /* Michal Bukai & Boris Tsatkin AT&T Smart Card support - Start*/	 
 /*** AT&T- Smart Card CI_PS_PRIM_ ACL SERVICE: LIST , SET , EDIT -BT6 */	 
 CI_PS_PRIM_SET_ACL_SERVICE_REQ , /**< \brief Requests to enable or disable APN control list ( ACL ) service	 
 * \details PIN2 must be verified ( using CI_SIM_PRIM_OPERCHV_REQ ) before using this request */	 
 CI_PS_PRIM_SET_ACL_SERVICE_CNF , /**< \brief Confirms the request to enable or disable APN control list ( ACL ) service	 
 * \details */	 
 CI_PS_PRIM_GET_ACL_SIZE_REQ , /**< \brief Requests the size of the ACL list	 
 * \details */	 
 CI_PS_PRIM_GET_ACL_SIZE_CNF , /**< \brief Confirms the request and returns the size of the ACL list	 
 * \details */	 
 CI_PS_PRIM_READ_ACL_ENTRY_REQ , /**< \brief Requests to read an entry from the ACL list	 
 * \details */	 
 CI_PS_PRIM_READ_ACL_ENTRY_CNF , /**< \brief Confirms the request and returns the requested ACL entry	 
 * \details */	 
 CI_PS_PRIM_EDIT_ACL_ENTRY_REQ , /**< \brief Requests to edit an entry in the ACL list	 
 * \details PIN2 must be verified ( using CI_SIM_PRIM_OPERCHV_REQ ) before using this request. */	 
 CI_PS_PRIM_EDIT_ACL_ENTRY_CNF = 90 , /**< \brief Confirms the request to edit an entry in the ACL list */	 
 /* ADD NEW COMMON PRIMITIVES HERE , BEFORE ' CI_PS_PRIM_LAST_COMMON_PRIM ' */	 
 /* Michal Bukai & Boris Tsatkin AT&T Smart Card support - End*/	 
	 
 /* Michal Bukai �C PDP authentication - Start*/	 
 CI_PS_PRIM_AUTHENTICATE_REQ , /**< \brief Requests to add authentication parameters to a defined PDP context.	 
 * \details The command must be sent after the PDP context was defined and before the PDP context is activated.	 
 * The authentication parameters will be sent to the GGSN in a protocol configuration information entry , when PDP context is activated.	 
 * In case authentication parameters are already defined for this PDP context the new authentication parameters will replace the existing parameters.	 
 * AuthenticationType = NONE , will delete authentication parameters defined for this PDP context. */	 
 CI_PS_PRIM_AUTHENTICATE_CNF , /**< \brief Confirms the authentication request. \details */	 
 /* Michal Bukai �C PDP authentication - End*/	 
 /* Michal Bukai �C Fast Dormancy - Start*/	 
 CI_PS_PRIM_FAST_DORMANT_REQ , /**< \brief Requests to release data radio bearers , in order to speed entry to DRX mode.	 
 * \details The application should use this request for offline applications such as push mail ,	 
 * in cases that it knows that data transition is complete and there is no anticipated data transmission in the next minute.	 
 * The PS will send " Signalling connection release indication " to the NW requesting to release data radio bearers. */	 
 CI_PS_PRIM_FAST_DORMANT_CNF , /**< \brief Confirms the fast dormancy request.	 
 * \details The following result codes can be received:	 
 * CIRC_PS_SUCCESS - Indicates " Signalling connection release indication " was sent to the NW	 
 * CIRC_PS_FAILURE Indicates " Signalling connection release indication " was not sent to the NW due to one of the following reasons:	 
 * Active RAT is not UMTS	 
 * There no active PDP contexts	 
 * There is an active CS connection	 
 * RRC state is not CELL DCH or CELL FACH	 
 * CIRC_PS SRVOPT_NOT_SUPPORTED -Indicates fast dormancy is not supported. */	 
 /* Michal Bukai �C Fast Dormancy - End*/	 
	 
 CI_PS_PRIM_GET_CURRENT_JOB_REQ , /**< \brief Requests current ongoing request for PS service group. */	 
 CI_PS_PRIM_GET_CURRENT_JOB_CNF , /**< \brief Confirms the current job request. */	 
	 
 CI_PS_PRIM_SET_FAST_DORMANCY_CONFIG_REQ , /**< \brief Requests to set configuration of fast dormancy. */	 
 CI_PS_PRIM_SET_FAST_DORMANCY_CONFIG_CNF , /**< \brief Confirms the configuration of fast dormancy. */	 
	 
 CI_PS_PRIM_PDP_ACTIVATION_REJECT_CAUSE_IND , /**< \brief Indicates SM cause code for PDP activation reject. */	 
	 
 CI_PS_PRIM_SET_PS_PAGING_CONFIG_REQ = 100 , /**< \brief Requests to set activation / deactvation of DSDS PS+Paging. */	 
 CI_PS_PRIM_SET_PS_PAGING_CONFIG_CNF , /**< \brief Confirms the configuration of DSDS PS+Paging */	 
	 
 /*Michal Bukai - AutoAttach Configuration - Samsung - START*/	 
 CI_PS_PRIM_GET_POWERON_AUTO_ATTACH_STATUS_REQ , /**< \brief Requests to read the configuration status of auto attach to PS domain on power up \details */	 
 CI_PS_PRIM_GET_POWERON_AUTO_ATTACH_STATUS_CNF , /**< \brief Confirms the request and returns auto attach configuration status \details */	 
 /*Michal Bukai - AutoAttach Configuration - Samsung - END*/	 
 CI_PS_PRIM_READ_4G_PDP_CTX_DYN_PARA_REQ , /**< \brief Gets a PDP context definition. \details */	 
 CI_PS_PRIM_READ_4G_PDP_CTX_DYN_PARA_CNF , /**< \brief Gets a PDP context definition. \details */	 
 CI_PS_PRIM_READ_4G_PDP_CTXS_ACT_DYN_PARA_REQ , /**< \brief Gets all defined PDP contexts current activation state. \details */	 
 CI_PS_PRIM_READ_4G_PDP_CTXS_ACT_DYN_PARA_CNF , /**< \brief Reports the current activation state of all defined PDP contexts. \details */	 
 CI_PS_PRIM_ENABLE_4G_NW_REG_IND_REQ , /**< \brief Enables / disables EPS network registration status reports. \details */	 
 CI_PS_PRIM_ENABLE_4G_NW_REG_IND_CNF , /**< \brief Confirms request to enable / disable EPS network registration status reports. \details */	 
 CI_PS_PRIM_4G_NW_REG_IND = 110 ,	 
 CI_PS_PRIM_GET_4G_NW_REG_STATUS_REQ , /**< \brief Requests the EPS network registration status. \details */	 
 CI_PS_PRIM_GET_4G_NW_REG_STATUS_CNF , /**< \brief Reports the EPS network registration status. \details */	 
 CI_PS_PRIM_GET_4G_QOS_REQ ,	 
 CI_PS_PRIM_GET_4G_QOS_CNF ,	 
 CI_PS_PRIM_SET_4G_QOS_REQ ,	 
 CI_PS_PRIM_SET_4G_QOS_CNF ,	 
 CI_PS_PRIM_DEL_4G_QOS_REQ ,	 
 CI_PS_PRIM_DEL_4G_QOS_CNF ,	 
 CI_PS_PRIM_GET_4G_QOS_CAPS_REQ ,	 
 CI_PS_PRIM_GET_4G_QOS_CAPS_CNF = 120 ,	 
	 
 CI_PS_PRIM_GET_4G_MODE_REQ ,	 
 CI_PS_PRIM_GET_4G_MODE_CNF ,	 
 CI_PS_PRIM_SET_4G_MODE_REQ ,	 
 CI_PS_PRIM_SET_4G_MODE_CNF ,	 
 CI_PS_PRIM_GET_4G_MODE_CAPS_REQ ,	 
 CI_PS_PRIM_GET_4G_MODE_CAPS_CNF ,	 
 CI_PS_PRIM_GET_PDP_ADDR_REQ ,	 
 CI_PS_PRIM_GET_PDP_ADDR_CNF ,	 
 CI_PS_PRIM_GET_PDP_ADDR_LIST_REQ ,	 
 CI_PS_PRIM_GET_PDP_ADDR_LIST_CNF = 130 ,	 
 CI_PS_PRIM_READ_4G_SEC_PDP_CTX_DYN_PARA_REQ , /**< \brief Requests a Secondary PDP Context Read Dynamic Parameters .	 
 * \details Returns an error if the Secondary PDP Context does not exist. */	 
 CI_PS_PRIM_READ_4G_SEC_PDP_CTX_DYN_PARA_CNF , /**< \brief Reports a Secondary PDP Context Dynamic Parameters . Indicates an error if the Secondary PDP Context does not exist. \details */	 
 CI_PS_PRIM_READ_4G_SEC_PDP_CTXS_ACT_DYN_PARA_REQ ,	 
 CI_PS_PRIM_READ_4G_SEC_PDP_CTXS_ACT_DYN_PARA_CNF ,	 
 CI_PS_PRIM_READ_4G_QOS_DYN_PARA_REQ ,	 
 CI_PS_PRIM_READ_4G_QOS_DYN_PARA_CNF ,	 
 CI_PS_PRIM_READ_4G_QOS_DYN_PARA_CAPS_REQ ,	 
 CI_PS_PRIM_READ_4G_QOS_DYN_PARA_CAPS_CNF ,	 
 CI_PS_PRIM_GET_4G_EVET_REP_REQ ,	 
 CI_PS_PRIM_GET_4G_EVET_REP_CNF = 140 ,	 
 CI_PS_PRIM_SET_4G_EVET_REP_REQ ,	 
 CI_PS_PRIM_SET_4G_EVET_REP_CNF ,	 
 CI_PS_PRIM_GET_4G_EVET_REP_CAPS_REQ ,	 
 CI_PS_PRIM_GET_4G_EVET_REP_CAPS_CNF ,	 
	 
	 
 CI_PS_PRIM_GET_4G_VOICE_CALL_MODE_REQ ,	 
 CI_PS_PRIM_GET_4G_VOICE_CALL_MODE_CNF ,	 
 CI_PS_PRIM_SET_4G_VOICE_CALL_MODE_REQ ,	 
 CI_PS_PRIM_SET_4G_VOICE_CALL_MODE_CNF ,	 
 CI_PS_PRIM_GET_4G_VOICE_CALL_MODE_CAPS_REQ ,	 
 CI_PS_PRIM_GET_4G_VOICE_CALL_MODE_CAPS_CNF = 150 ,	 
	 
 CI_PS_PRIM_READ_TRAFFIC_FLOW_TEMP_DYN_PARA_REQ , // Traffic Flow Template Read Dynamic Parameters +CGTFTRDP	 
 CI_PS_PRIM_READ_TRAFFIC_FLOW_TEMP_DYN_PARA_CNF , // Traffic Flow Template Read Dynamic Parameters +CGTFTRDP	 
 CI_PS_PRIM_READ_TRAFFIC_FLOW_TEMP_DYN_PARA_CAPS_REQ , // Traffic Flow Template Read Dynamic Parameters +CGTFTRDP	 
 CI_PS_PRIM_READ_TRAFFIC_FLOW_TEMP_DYN_PARA_CAPS_CNF , // Traffic Flow Template Read Dynamic Parameters +CGTFTRDP	 
	 
 CI_PS_PRIM_DATACOMP_REPORTING_REQ , /**< \brief Sets data compression reporting to on or off. Also used to read current setting of data	 
 compression reporting. Data compression	 
 reporting is used when AT+DR AT command is	 
 executed. If enabled , than sending of	 
 CI_PS_PRIM_DATACOMP_IND is enabled */	 
 CI_PS_PRIM_DATACOMP_REPORTING_CNF , /**< \brief Confirmation to the sets data compression	 
 reporting request. Returns the current setting	 
 of the data compression reporting. */	 
 CI_PS_PRIM_DATACOMP_IND , /**< \brief Indicates the status of the data compression. */	 
	 
 CI_PS_PRIM_SET_IMS_VOICE_CALL_AVAILABILITY_REQ , /**< \brief Set command informs the MT whether the UE is currently available for voice calls with the IMS ( see 3 GPP TS 24.229000 )	 
 * \details The information can be used by the MT to determine " IMS voice not available " as defined in 3 GPP TS 24.301000 ,	 
 * and for mobility management for IMS voice termination , see 3 GPP TS 24.008000 */	 
 CI_PS_PRIM_SET_IMS_VOICE_CALL_AVAILABILITY_CNF , /**< \brief Confirmation to the setting of whether the UE is currently available for voice calls with the IMS */	 
 CI_PS_PRIM_GET_IMS_VOICE_CALL_AVAILABILITY_REQ = 160 , /**< \brief Gets the stored setting of the IMS voice call availability */	 
 CI_PS_PRIM_GET_IMS_VOICE_CALL_AVAILABILITY_CNF , /**< \brief Confirmation to the request to get the stored setting of the IMS voice call availability */	 
 CI_PS_PRIM_SET_IMS_SMS_AVAILABILITY_REQ , /**< \brief Set command informs the MT whether the UE is currently available for SMS using IMS ( see 3 GPP TS 24.229000 )	 
 * \details The information can be used by the MT to determine the need to remain attached for non-EPS services ,	 
 * as defined in 3 GPP TS 24.301000 */	 
 CI_PS_PRIM_SET_IMS_SMS_AVAILABILITY_CNF , /**< \brief Confirmation to the setting of whether the UE is currently available for SMS with the IMS */	 
 CI_PS_PRIM_GET_IMS_SMS_AVAILABILITY_REQ , /**< \brief Gets the stored setting of the IMS SMS availability */	 
 CI_PS_PRIM_GET_IMS_SMS_AVAILABILITY_CNF , /**< \brief Confirmation to the request to get the stored setting of the IMS SMS availability */	 
 CI_PS_PRIM_SET_MM_IMS_VOICE_TERMINATION_REQ , /**< \brief Sets the Mobility Management for IMS Voice Termination to support terminating access domain selection by the network */	 
 CI_PS_PRIM_SET_MM_IMS_VOICE_TERMINATION_CNF , /**< \brief Confirmation to the request to set the MM for IMS Voice Termination */	 
 CI_PS_PRIM_GET_MM_IMS_VOICE_TERMINATION_REQ , /**< \brief Gets the setting of the Mobility Management for IMS Voice Termination */	 
 CI_PS_PRIM_GET_MM_IMS_VOICE_TERMINATION_CNF , /**< \brief Confirmation to the request to get the stored setting of the MM for IMS Voice Termination */	 
	 
 CI_PS_PRIM_DEFINE_DEFAULT_PDP_CTX_REQ = 170 , /** AT*CGDFLT , set the default PDP info */	 
 CI_PS_PRIM_DEFINE_DEFAULT_PDP_CTX_CNF ,	 
 CI_PS_PRIM_GET_DEFAULT_PDP_CTX_REQ , /** AT*CGDFLT? , get the default PDP info */	 
 CI_PS_PRIM_GET_DEFAULT_PDP_CTX_CNF ,	 
	 
 CI_PS_PRIM_SET_APN_REQ , /** AT+VZWAPNE= , used to set APN info */	 
 CI_PS_PRIM_SET_APN_CNF ,	 
 CI_PS_PRIM_GET_APN_REQ , /** AT+VZWAPNE? , used to get APN info */	 
 CI_PS_PRIM_GET_APN_CNF ,	 
	 
 CI_PS_PRIM_SET_IMS_REG_STATE_REQ , /** used to notify CP the IMS register state , as IMS on AP side now , when IMS register state changes , should notify CP */	 
 CI_PS_PRIM_SET_IMS_REG_STATE_CNF ,	 
 CI_PS_PRIM_UE_EVENT_TO_IMS_IND = 180 , /** used by CP to notify the IMS module some UE event , such as: UICC removed , APN changed , etc */	 
	 
 CI_PS_PRIM_SET_IMS_REG_INFO_IND_REQ , /** AT+CIREG= [ <n> ] , set whether need to report IMS register state. As IMS on AP side now , this CI do not need to be processed in CP side by now*/	 
 CI_PS_PRIM_SET_IMS_REG_INFO_IND_CNF ,	 
 CI_PS_PRIM_IMS_REG_INFO_IND , /** +CIREGU: <reg_info> [ , <ext_info> ] . IMS module report the IMS state*/	 
 CI_PS_PRIM_GET_IMS_REG_INFO_REQ , /** AT+CIREG? , read command*/	 
 CI_PS_PRIM_GET_IMS_REG_INFO_CNF ,	 
	 
 CI_PS_PRIM_SET_DEFAULT_PDP_AUTHENTICATE_REQ , /** AT*CGDFAUTH=<mode> , <type> [ , <UserName> [ , <Password> ] ] */	 
 CI_PS_PRIM_SET_DEFAULT_PDP_AUTHENTICATE_CNF ,	 
 CI_PS_PRIM_GET_DEFAULT_PDP_AUTHENTICATE_REQ ,	 
 CI_PS_PRIM_GET_DEFAULT_PDP_AUTHENTICATE_CNF , /** AT*CGDFAUTH=<mode> */	 
	 
 CI_PS_PRIM_SET_VOICE_DOMAIN_PREFERENCE_REQ = 190 , /** AT+CVDP= [ <setting> ] / AT+CEVDP= [ <setting> ] , UE ' s Voice Domain Preference */	 
 CI_PS_PRIM_SET_VOICE_DOMAIN_PREFERENCE_CNF ,	 
 CI_PS_PRIM_GET_VOICE_DOMAIN_PREFERENCE_REQ , /** AT+CVDP? AT+CEVDP? UE ' s Voice Domain Preference UTRAN */	 
 CI_PS_PRIM_GET_VOICE_DOMAIN_PREFERENCE_CNF ,	 
	 
 CI_PS_PRIM_SET_EPS_USAGE_SETTING_REQ , /** AT+CEUS= [ <setting> ] , UE ' s usage setting for EPS*/	 
 CI_PS_PRIM_SET_EPS_USAGE_SETTING_CNF ,	 
 CI_PS_PRIM_GET_EPS_USAGE_SETTING_REQ , /** AT+CEUS? , UE ' s usage setting for EPS*/	 
 CI_PS_PRIM_GET_EPS_USAGE_SETTING_CNF ,	 
	 
 CI_PS_PRIM_SET_AP_UNIVERSAL_SETTING_REQ ,	 
 CI_PS_PRIM_SET_AP_UNIVERSAL_SETTING_CNF ,	 
	 
 CI_PS_PRIM_SET_PS_SERVICE_DOMAIN_REQ = 200 ,	 
 CI_PS_PRIM_SET_PS_SERVICE_DOMAIN_CNF ,	 
 CI_PS_PRIM_GET_PS_SERVICE_DOMAIN_REQ ,	 
 CI_PS_PRIM_GET_PS_SERVICE_DOMAIN_CNF ,	 
	 
 CI_PS_PRIM_SET_IMS_SERVICE_STATUS_REQ ,	 
 CI_PS_PRIM_SET_IMS_SERVICE_STATUS_CNF ,	 
	 
 CI_PS_PRIM_SUSPEND_RESUME_IND ,	 
	 
 CI_PS_PRIM_CHAP_AUTHENTICATE_REQ , /** AT*CHAPAUTH=cid [ , <challenge> [ , <response> ] ] , for PPP CHAP authentication */	 
 CI_PS_PRIM_CHAP_AUTHENTICATE_CNF ,	 
	 
 CI_PS_PRIM_ACTIVATE_RECONF_PDP_CTX_REQ , /** used to activate reconfigured PDP ( an already activated PDP is re-defined ) */	 
 CI_PS_PRIM_ACTIVATE_RECONF_PDP_CTX_CNF = 210 ,	 
	 
 /* ============== Added for REL13 ====================================================*/	 
 CI_PS_PRIM_SET_PSM_CONFIG_REQ ,	 
 CI_PS_PRIM_SET_PSM_CONFIG_CNF ,	 
 CI_PS_PRIM_GET_PSM_CONFIG_REQ ,	 
 CI_PS_PRIM_GET_PSM_CONFIG_CNF ,	 
	 
 CI_PS_PRIM_SET_EDRX_CONFIG_REQ ,	 
 CI_PS_PRIM_SET_EDRX_CONFIG_CNF ,	 
 CI_PS_PRIM_GET_EDRX_CONFIG_REQ ,	 
 CI_PS_PRIM_GET_EDRX_CONFIG_CNF ,	 
 CI_PS_PRIM_EDRX_INFO_IND ,	 
 CI_PS_PRIM_READ_EDRX_DYN_PARA_REQ = 220 ,	 
 CI_PS_PRIM_READ_EDRX_DYN_PARA_CNF ,	 
	 
 CI_PS_PRIM_SET_CIOT_CONFIG_REQ ,	 
 CI_PS_PRIM_SET_CIOT_CONFIG_CNF ,	 
 CI_PS_PRIM_GET_CIOT_CONFIG_REQ ,	 
 CI_PS_PRIM_GET_CIOT_CONFIG_CNF ,	 
 CI_PS_PRIM_CIOT_NW_INFO_IND ,	 
	 
 CI_PS_PRIM_CONFIG_SIGNALLING_CONNECTION_REQ ,	 
 CI_PS_PRIM_CONFIG_SIGNALLING_CONNECTION_CNF ,	 
 CI_PS_PRIM_GET_SIGNALLING_CONNECTION_STATUS_REQ ,	 
 CI_PS_PRIM_GET_SIGNALLING_CONNECTION_STATUS_CNF = 230 ,	 
 CI_PS_PRIM_SIGNALLING_CONNECTION_IND ,	 
	 
 CI_PS_PRIM_SET_INITIAL_PDP_ACTIVATION_OPT_REQ ,	 
 CI_PS_PRIM_SET_INITIAL_PDP_ACTIVATION_OPT_CNF ,	 
 CI_PS_PRIM_GET_INITIAL_PDP_ACTIVATION_OPT_REQ ,	 
 CI_PS_PRIM_GET_INITIAL_PDP_ACTIVATION_OPT_CNF ,	 
	 
 CI_PS_PRIM_SET_APN_BACKOFF_TIMER_STATUS_REQ ,	 
 CI_PS_PRIM_SET_APN_BACKOFF_TIMER_STATUS_CNF ,	 
 CI_PS_PRIM_GET_APN_BACKOFF_TIMER_STATUS_REQ ,	 
 CI_PS_PRIM_GET_APN_BACKOFF_TIMER_STATUS_CNF ,	 
 CI_PS_PRIM_APN_BACKOFF_TIMER_STATUS_REPORT_IND = 240 ,	 
 CI_PS_PRIM_READ_APN_BACKOFF_TIMER_DYN_PARA_REQ ,	 
 CI_PS_PRIM_READ_APN_BACKOFF_TIMER_DYN_PARA_CNF ,	 
	 
 CI_PS_PRIM_GET_APN_RATE_CONTROL_REQ ,	 
 CI_PS_PRIM_GET_APN_RATE_CONTROL_CNF ,	 
	 
 /* Added by Daniel for CQ00123144 , begin */	 
 CI_PS_PRIM_SET_5G_QOS_REQ ,	 
 CI_PS_PRIM_SET_5G_QOS_CNF ,	 
 CI_PS_PRIM_SEND_UE_POLICY_REQ ,	 
 CI_PS_PRIM_SEND_UE_POLICY_CNF ,	 
 CI_PS_PRIM_RECV_UE_POLICY_IND ,	 
 CI_PS_PRIM_SET_UE_POLICY_REPORT_REQ = 250 ,	 
 CI_PS_PRIM_SET_UE_POLICY_REPORT_CNF ,	 
 CI_PS_PRIM_GET_UE_POLICY_REPORT_REQ ,	 
 CI_PS_PRIM_GET_UE_POLICY_REPORT_CNF ,	 
 CI_PS_PRIM_SEND_UE_OS_ID_REQ ,	 
 CI_PS_PRIM_SEND_UE_OS_ID_CNF ,	 
 CI_PS_PRIM_SEND_LADN_INDICATION_REQ ,	 
 CI_PS_PRIM_SEND_LADN_INDICATION_CNF ,	 
 CI_PS_PRIM_RECV_LADN_INFORMATION_IND ,	 
 CI_PS_PRIM_SET_LADN_INFORMATION_REQ ,	 
 CI_PS_PRIM_SET_LADN_INFORMATION_CNF = 260 ,	 
 CI_PS_PRIM_GET_LADN_INFORMATION_REQ ,	 
 CI_PS_PRIM_GET_LADN_INFORMATION_CNF ,	 
 /* Added by Daniel for CQ00123144 , end */	 
 /* Added by Hailei for AT+C5GNSSAI begin */	 
 CI_PS_PRIM_SET_DEFAULT_NSSAI_REQ ,	 
 CI_PS_PRIM_SET_DEFAULT_NSSAI_CNF ,	 
 CI_PS_PRIM_GET_DEFAULT_NSSAI_REQ ,	 
 CI_PS_PRIM_GET_DEFAULT_NSSAI_CNF ,	 
 /* Added by Hailei for AT+C5GNSSAI end */	 
 /* Added by Hailei for AT+C5GPNSSAI begin */	 
 CI_PS_PRIM_SET_PREFERRED_NSSAI_REQ ,	 
 CI_PS_PRIM_SET_PREFERRED_NSSAI_CNF ,	 
 CI_PS_PRIM_GET_PREFERRED_NSSAI_REQ ,	 
 CI_PS_PRIM_GET_PREFERRED_NSSAI_CNF = 270 ,	 
 /* Added by Hailei for AT+C5GPNSSAI end */	 
 /* Added by Hailei for AT+C5GNSSAIRDP begin */	 
 CI_PS_PRIM_GET_NSSAI_REQ ,	 
 CI_PS_PRIM_GET_NSSAI_CNF ,	 
 /* Added by Hailei for AT+C5GNSSAIRDP end */	 
 /* Added by Hailei for AT+CMICO begin */	 
 CI_PS_PRIM_SET_MICO_REQ ,	 
 CI_PS_PRIM_SET_MICO_CNF ,	 
 CI_PS_PRIM_GET_MICO_REQ ,	 
 CI_PS_PRIM_GET_MICO_CNF ,	 
 /* Added by Hailei for AT+CMICO end */	 
	 
 CI_PS_PRIM_ENABLE_5G_NW_REG_IND_REQ ,	 
 CI_PS_PRIM_ENABLE_5G_NW_REG_IND_CNF ,	 
 CI_PS_PRIM_5G_NW_REG_IND ,	 
 CI_PS_PRIM_GET_5G_NW_REG_STATUS_REQ = 280 ,	 
 CI_PS_PRIM_GET_5G_NW_REG_STATUS_CNF ,	 
	 
 CI_PS_PRIM_TFT_REPORT_IND ,	 
	 
 CI_PS_PRIM_SET_UE_TEST_REQ ,	 
 CI_PS_PRIM_SET_UE_TEST_CNF ,	 
	 
	 
 CI_PS_PRIM_SET_PS_DATAOFF_REQ ,	 
 CI_PS_PRIM_SET_PS_DATAOFF_CNF ,	 
 CI_PS_PRIM_GET_PS_DATAOFF_REQ ,	 
 CI_PS_PRIM_GET_PS_DATAOFF_CNF ,	 
 CI_PS_PRIM_EXEMPT_SERVICE_LIST_IND ,	 
	 
 CI_PS_PRIM_GET_5G_QOS_REQ = 290 ,	 
 CI_PS_PRIM_GET_5G_QOS_CNF ,	 
	 
 CI_PS_PRIM_SEND_MICO_IND ,	 
 /* Added by xgh for AT+CWUS begin */	 
 CI_PS_PRIM_SET_WUS_REQ ,	 
 CI_PS_PRIM_SET_WUS_CNF ,	 
 CI_PS_PRIM_GET_WUS_REQ ,	 
 CI_PS_PRIM_GET_WUS_CNF ,	 
 /* Added by xgh for AT+CWUS end */	 
	 
 /* Added by xgh for AT+C5GCAPA begin */	 
 CI_PS_PRIM_SET_5GCAPA_REQ ,	 
 CI_PS_PRIM_SET_5GCAPA_CNF ,	 
 CI_PS_PRIM_GET_5GCAPA_REQ ,	 
 CI_PS_PRIM_GET_5GCAPA_CNF ,	 
 /* Added by xgh for AT+C5GCAPA end */	 
	 
 /* Added by Daniel for CQ00135764 20220301 , begin */	 
 CI_PS_PRIM_RECOM_BR_QUERY_REQ ,	 
 CI_PS_PRIM_RECOM_BR_QUERY_CNF ,	 
 CI_PS_PRIM_RECOM_BR_REPORT_IND ,	 
 CI_PS_PRIM_SET_RECOM_BR_REPORT_REQ ,	 
 CI_PS_PRIM_SET_RECOM_BR_REPORT_CNF ,	 
 CI_PS_PRIM_GET_RECOM_BR_REPORT_REQ ,	 
 CI_PS_PRIM_GET_RECOM_BR_REPORT_CNF ,	 
 /* Added by Daniel for CQ00135764 20220301 , end */	 
	 
 /* Modified by Daniel for CQ00138540 , begin */	 
 CI_PS_PRIM_NSSAI_REPORT_IND ,	 
 /* Modified by Daniel for CQ00138540 , end */	 
	 
 /* Added by Daniel for CQ00139757 , begin */	 
 CI_PS_PRIM_SET_PARAS_REQ ,	 
 CI_PS_PRIM_SET_PARAS_CNF ,	 
 /* Added by Daniel for CQ00139757 , end */	 
	 
 /* Added by hailei for CQ00141768 , begin */	 
 CI_PS_PRIM_SET_SMS_OVER_NAS_REQ ,	 
 CI_PS_PRIM_SET_SMS_OVER_NAS_CNF ,	 
 CI_PS_PRIM_GET_SMS_OVER_NAS_REQ ,	 
 CI_PS_PRIM_GET_SMS_OVER_NAS_CNF ,	 
 CI_PS_PRIM_SEND_SMS_OVER_NAS_IND ,	 
 /* Added by hailei for CQ00141768 , end */	 
	 
 /* Added by Daniel for CQ00142357 , begin */	 
 CI_PS_PRIM_GET_PARAS_REQ ,	 
 CI_PS_PRIM_GET_PARAS_CNF ,	 
 CI_PS_PRIM_PARAS_REPORT_IND ,	 
 /* Added by Daniel for CQ00142357 , end */	 
	 
 CI_PS_PRIM_CONFIG_NAS_CONNECTION_RELEASE_REQ ,	 
 CI_PS_PRIM_CONFIG_NAS_CONNECTION_RELEASE_CNF ,	 
 /* END OF COMMON PRIMITIVES LIST */	 
 CI_PS_PRIM_LAST_COMMON_PRIM	 
	 
 /* the customer specific extension primitives are added starting from	 
 * CI_PS_PRIM_firstCustPrim = CI_PS_PRIM_LAST_COMMON_PRIM as the first identifier.	 
 * The actual primitive names and IDs are defined in the associated	 
 * ' ci_ps_cust_xxx.h ' file.	 
 */	 
	 
 /* DO NOT ADD ANY MORE PRIMITIVES HERE */	 
	 
 } _CiPsPrim;

//ICAT EXPORTED ENUM 
 typedef enum CIRC_PS 
 {	 
 CIRC_PS_SUCCESS = 0 , /**< Request completed successfully */	 
 CIRC_PS_FAILURE , /**< Request failed */	 
	 
 /* failure to perform an Attach */	 
 CIRC_PS_ILLEGAL_MS = 0x03 , /**< Illegal MS */	 
 CIRC_PS_ILLEGAL_ME = 0x06 , /**< Illegal ME */	 
 CIRC_PS_GPRS_SERVICES_NOT_ALLOWED = 0x07 , /**< GPRS service not allowed */	 
 CIRC_PS_OPER_DETERMINED_BARRING = 0x08 , /**< Operator Determined Barring */	 
 CIRC_PS_DETACH = 10 , // 0x0A / **< implicitly detached * /	 
 CIRC_PS_PLMN_NOT_ALLOWED = 0x0B , /**< PLMN not allowed */	 
 CIRC_PS_LA_NOT_ALLOWED = 0x0C , /**< Location area not allowed */	 
 CIRC_PS_ROAMING_NOT_ALLOWED = 0x0D , /**< Roaming not allowed in this location area */	 
 CIRC_PS_MSC_NOT_REACH = 16 , // 0x10 / **< MSC temporarily not reachable * /	 
 CIRC_PS_NW_CONGESTION = 22 , // 0x16 / **< Congestion * /	 
 CIRC_PS_RESOURCE_INSUFF = 26 , // 0x1A / **< Insufficient resources * /	 
 CIRC_PS_APN = 27 , // 0x1B / **< Missing or unknown APN * /	 
 CIRC_PS_UNKNOWN_PDP_ADD_TYPE = 28 , // 0x1C / **< unknown PDP address or PDP type * /	 
 CIRC_PS_USER_AUTH_FAIL = 29 , // 0x1D / **< user authentication failed * /	 
 CIRC_PS_ACT_REJECT_GGSN = 30 , // 0x1E / **< Activation rejected by GGSN * /	 
 CIRC_PS_ACT_REJECT = 31 , // 0x1F / **< Activation rejected , unspecified * /	 
 /* failure to Activate a context */	 
 CIRC_PS_SRVOPT_NOT_SUPPORTED = 32 , // 0x20 / **< Service option not supported * /	 
 CIRC_PS_SRVOPT_NOT_SUBSCRIBED = 33 , // 0x21 / **< Requested service option not subscribed * /	 
 CIRC_PS_SRVOPT_TEMP_OUT_OF_ORDER = 34 , // 0x22 / **< Service option temporarily out of order * /	 
 CIRC_PS_NSAPI_ALREADY_USED = 35 , // 0x23 / **< NSAPI already used * /	 
 CIRC_PS_QOS = 37 , // 0x25 / **< QoS not accepted * /	 
 CIRC_PS_NETWORK_FAILURE = 38 , // 0x26 / **< Network failure * /	 
 CIRC_PS_REACTIVATION_REQ = 39 , // 0x27 / **< Reactivation required * /	 
 // Z.S. MT PDP support	 
 /* TFT errors for MT PDP start*/	 
 /* From spec ( 24.301000 / 9.900000 .4.4 ) */	 
 CIRC_PS_ESM_SEMANTIC_ERROR_IN_THE_TFT_OPERATION = 41 , // 0x29 / * SM_CAUSE_SEMANTIC_ERROR_IN_TFT_OPERATION * /	 
 CIRC_PS_ESM_SYNTACTICAL_ERROR_IN_THE_TFT_OPERATION = 42 , // 0x2A / * SM_CAUSE_SYNTACTICAL_ERROR_IN_TFT_OPERATION * /	 
 CIRC_PS_ESM_INVALID_EPS_BEARER_IDENTITY = 43 , // 0x2B / * SM_CAUSE_UNKNOWN_PDP_CONTEXT * /	 
 CIRC_PS_ESM_SEMANTIC_ERRORS_IN_PACKET_FILTER = 44 , // 0x2C / * SM_CAUSE_SEMANTIC_ERRORS_IN_PACKET_FILTER * /	 
 CIRC_PS_ESM_SYNTACTICAL_ERRORS_IN_PACKET_FILTER = 45 , // 0x2D / * SM_CAUSE_SYNTACTICAL_ERRORS_IN_PACKET_FILTER * /	 
 CIRC_PS_ESM_EPS_BEARER_CONTEXT_WITHOUT_TFT_ALREADY_ACTIVATED = 46 , // 0x2E / * SM_CAUSE_PDP_CONTEXT_WITHOUT_TFT_ALREADY_ACTIVATED * /	 
 CIRC_PS_ESM_LAST_PDN_DISCONNECTION_NOT_ALLOWED = 49 , // 0x31	 
 CIRC_PS_ESM_PDN_TYPE_IPV4_ONLY_ALLOWED = 50 , // 0x32	 
 CIRC_PS_ESM_PDN_TYPE_IPV6_ONLY_ALLOWED = 51 , // 0x33	 
 CIRC_PS_ESM_PDN_TYPE_SINGLE_IP_ALLOWED = 52 , // 0x34	 
 CIRC_PS_PROTOCOL_ERROR_MIN = 95 , // 0x5F / **< protocol errors - low range , old value , useless now * /	 
 CIRC_PS_PROTOCOL_ERROR_MAX = 111 , // 0x6F / **< protocol errors - high range , old value , useless now * /	 
 /* TFT errors for MT PDP end*/	 
 CIRC_PS_UNSPECIFIED_ERROR = 148 , // 0x94 / **< Unspecified GPRS error * /	 
 CIRC_PS_PDP_AUTHEN_FAILURE = 149 , // 0x95 / **< PDP authentication failure * /	 
	 
 /* other GPRS errors */	 
 CIRC_PS_INVALID_MS_CLASS = 150 , // 0x96 / **< Invalid mobile class * /	 
	 
 /* Additional return codes , not specified in TS 27.007000 - start from 200 */	 
 CIRC_PS_INFO_UNAVAILABLE = 200 , // 0xC8 / **< Requested information is unavailable * /	 
	 
 CIRC_PS_ALREADY_PROCESSING = 201 , // 0xC9 / **< The requested command is already being processed , I.e. , this REQ is redundant * /	 
 CIRC_PS_BUSY_WITH_OTHER_JOB = 202 , // 0xCA / **< The CP is busy processing another command so this one can ' t be serviced , and CP will not add the REQ into its queue * /	 
	 
 CIRC_PS_INVALID_PARAMETER = 203 , // 0xCB / **< Generic error - the requested service primitive has invalid parameters * /	 
 CIRC_PS_INVALID_REQ = 204 , // 0xCC / **< Generic error - the requested service primitive can not be handled at current state * /	 
 CIRC_PS_SIM_NOT_READY = 205 , // 0xCD / **< Generic error - the requested service primitive fails because SIM is not ready * /	 
 CIRC_PS_ACCESS_DENIED = 206 , // 0xCE / **< Generic error - the requested service primitive fails because access is denied * /	 
 CIRC_PS_INVALID_CID = 207 , // 0xCF / **< Generic error - the requested Cid is invalid * /	 
 CIRC_PS_TFT_PACKET_ERROR_DEFAULT_PDP = 208 , // 0xD0 / **< Generic error - the TFT is invalid for default MT PDP * /	 
 CIRC_PS_TFT_PACKET_ERROR_NON_DEFAULT_PDP = 209 , // 0xD1 / **< Generic error - the TFT is invalid for NON default MT PDP * /	 
 CIRC_PS_PENDING_SUCCESS = 210 , // 0xD2 / **<LTE MO PDP equest completed successfully * /	 
 CIRC_PS_RPM_REJECT = 880 , // 0x370 / **< Generic error - the RPM manager rejected the request. * /	 
	 
 /**** SM reject cause ( 24.008000 ) also contained in CiPsRc vaule , one to one mapped with sml3_typ.h ****/	 
 CIRC_PS_SM_LLC_OR_SNDCP_FAILURE = 0x19 ,	 
 CIRC_PS_SM_INSUFFIC_RESOURCES = 0x1A ,	 
 CIRC_PS_SM_MISSING_OR_UNKNOWN_APN = 0x1B ,	 
 CIRC_PS_SM_UNKNOWN_PDP_ADDR_OR_TYPE = 0x1C ,	 
 CIRC_PS_SM_USER_AUTH_FAILED = 0x1D ,	 
 CIRC_PS_SM_ACTIV_REJ_BY_GGSN = 0x1E ,	 
 CIRC_PS_SM_ACTIV_REJ_UNSPECIFIED = 0x1F ,	 
 CIRC_PS_SM_SERVICE_OPT_NOT_SUPPORTED = 0x20 ,	 
 CIRC_PS_SM_SERVICE_OPT_NOT_SUBSCRIBED = 0x21 ,	 
 CIRC_PS_SM_SERVICE_OPT_TEMP_OUT_OF_ORDER = 0x22 ,	 
 CIRC_PS_SM_NSAPI_ALREADY_USED = 0x23 ,	 
 CIRC_PS_SM_REGULAR_DEACTIVATION = 0x24 ,	 
 CIRC_PS_SM_QOS_NOT_ACCEPTED = 0x25 ,	 
 CIRC_PS_SM_NETWORK_FAILURE = 0x26 ,	 
 CIRC_PS_SM_REACTIVATION_REQUIRED = 0x27 ,	 
 CIRC_PS_SM_FEATURE_NOT_SUPPORTED = 0x28 , /* Added for 111 -13748 */	 
 CIRC_PS_SM_SEMANTIC_ERROR_IN_TFT_OPERATION = 0x29 ,	 
 CIRC_PS_SM_SYNTACTICAL_ERROR_IN_TFT_OPERATION = 0x2A ,	 
 CIRC_PS_SM_UNKNOWN_PDP_CONTEXT = 0x2B ,	 
 CIRC_PS_SM_SEMANTIC_ERRORS_IN_PACKET_FILTER = 0x2C ,	 
 CIRC_PS_SM_SYNTACTICAL_ERRORS_IN_PACKET_FILTER = 0x2D ,	 
 CIRC_PS_SM_PDP_CONTEXT_WITHOUT_TFT_ALREADY_ACTIVATED = 0x2E ,	 
 CIRC_PS_SM_PDP_TYPE_IPV4_ONLY_ALLOWED = 0x32 ,	 
 CIRC_PS_SM_PDP_TYPE_IPV6_ONLY_ALLOWED = 0x33 ,	 
 CIRC_PS_SM_SINGLE_ADDRESS_BEARERS_ONLY_ALLOWED = 0x34 ,	 
 CIRC_PS_SM_INVALID_TI_VALUE = 0x51 ,	 
 CIRC_PS_SM_SEMANTICALLY_INCORRECT_MSG = 0x5F ,	 
 CIRC_PS_SM_INVALID_MAND_INFORMATION = 0x60 ,	 
 CIRC_PS_SM_MSG_TYPE_NONEXIST_OR_NOT_IMP = 0x61 ,	 
 CIRC_PS_SM_MSG_TYPE_INCOMPAT_WITH_STATE = 0x62 ,	 
 CIRC_PS_SM_IE_NONEXIST_OR_NOT_IMP = 0x63 ,	 
 CIRC_PS_SM_CONDITIONAL_IE_ERROR = 0x64 ,	 
 CIRC_PS_SM_MSG_INCOMPAT_WITH_STATE = 0x65 ,	 
 CIRC_PS_SM_PROTOCOL_ERROR_UNSPEC = 0x6F ,	 
 /* Added for rel6: APN restriction value incompatible with active PDP context */	 
 CIRC_PS_SM_APN_RESTRICTION = 0x70 , // last SM cause	 
	 
 /**** ESM reject cause also contained in CiPsRc vaule , one to one mapped with sml3_typ.h ****/	 
 // ESM cause , 24.301000 - 9.900000 .4.4	 
 CIRC_PS_ESM_OPERATOR_DETERMINED_BARRING = 0x08 ,	 
 CIRC_PS_ESM_INSUFFICIENT_RESOURCES = 0x1a ,	 
 CIRC_PS_ESM_UNKNOWN_OR_MISSING_APN = 0x1b ,	 
 CIRC_PS_ESM_UNKNOWN_PDN_TYPE = 0x1c ,	 
 CIRC_PS_ESM_USER_AUTHENTICATION_FAILED = 0x1d ,	 
 CIRC_PS_ESM_REQUEST_REJECTED_BY_SERVING_GW_OR_PDN_GW = 0x1e ,	 
 CIRC_PS_ESM_REQUEST_REJECTED_UNSPECIFIED = 0x1f ,	 
 CIRC_PS_ESM_SERVICE_OPTION_NOT_SUPPORTED = 0x20 ,	 
 CIRC_PS_ESM_REQUESTED_SERVICE_OPTION_NOT_SUBSCRIBED = 0x21 ,	 
 CIRC_PS_ESM_SERVICE_OPTION_TEMPORARILY_OUT_OF_ORDER = 0x22 ,	 
 CIRC_PS_ESM_PTI_ALREADY_IN_USE = 0x23 ,	 
 CIRC_PS_ESM_REGULAR_DEACTIVATION = 0x24 ,	 
 CIRC_PS_ESM_EPS_QOS_NOT_ACCEPTED = 0x25 ,	 
 CIRC_PS_ESM_NETWORK_FAILURE = 0x26 ,	 
 // CIRC_PS_ESM_SEMANTIC_ERROR_IN_THE_TFT_OPERATION = 0x29 ,	 
 // CIRC_PS_ESM_SYNTACTICAL_ERROR_IN_THE_TFT_OPERATION = 0x2a ,	 
 // CIRC_PS_ESM_INVALID_EPS_BEARER_IDENTITY = 0x2b ,	 
 // CIRC_PS_ESM_SEMANTIC_ERRORS_IN_PACKET_FILTER = 0x2c ,	 
 // CIRC_PS_ESM_SYNTACTICAL_ERRORS_IN_PACKET_FILTER = 0x2d ,	 
 // CIRC_PS_ESM_EPS_BEARER_CONTEXT_WITHOUT_TFT_ALREADY_ACTIVATED = 0x2e ,	 
 CIRC_PS_ESM_PTI_MISMATCH = 0x2f ,	 
 // CIRC_PS_ESM_LAST_PDN_DISCONNECTION_NOT_ALLOWED = 0x31 ,	 
 // CIRC_PS_ESM_PDN_TYPE_IPV4_ONLY_ALLOWED = 0x32 ,	 
 // CIRC_PS_ESM_PDN_TYPE_IPV6_ONLY_ALLOWED = 0x33 ,	 
 CIRC_PS_ESM_SINGLE_ADDRESS_BEARERS_ONLY_ALLOWED = 0x34 ,	 
 CIRC_PS_ESM_ESM_INFORMATION_NOT_RECEIVED = 0x35 ,	 
 CIRC_PS_ESM_PDN_CONNECTION_DOES_NOT_EXIST = 0x36 ,	 
 CIRC_PS_ESM_MULTIPLE_PDN_CONNECTIONS_FOR_A_GIVEN_APN_NOT_ALLOWED = 0x37 ,	 
 CIRC_PS_ESM_COLLISION_WITH_NETWORK_INITIATED_REQUEST = 0x38 ,	 
 CIRC_PS_ESM_UNSUPPORTED_QCI_VALUE = 0x3b ,	 
 CIRC_PS_ESM_INVALID_PTI_VALUE = 0x51 ,	 
 CIRC_PS_ESM_SEMANTICALLY_INCORRECT_MESSAGE = 0x5f ,	 
 CIRC_PS_ESM_INVALID_MANDATORY_INFORMATION = 0x60 ,	 
 CIRC_PS_ESM_MESSAGE_TYPE_NONEXISTENT_OR_NOT_IMPLEMENTED = 0x61 ,	 
 CIRC_PS_ESM_MESSAGE_TYPE_NOT_COMPATIBLE_WITH_THE_PROTOCOL_STATE = 0x62 ,	 
 CIRC_PS_ESM_INFORMATION_ELEMENT_NONEXISTENT_OR_NOT_IMPLEMENTED = 0x63 ,	 
 CIRC_PS_ESM_CONDITIONAL_IE_ERROR = 0x64 ,	 
 CIRC_PS_ESM_MESSAGE_NOT_COMPATIBLE_WITH_THE_PROTOCOL_STATE = 0x65 ,	 
 CIRC_PS_ESM_PROTOCOL_ERROR_OR_UNSPECIFIED = 0x6f ,	 
 CIRC_PS_ESM_APN_RESTRICTION_VALUE_INCOMPATIBLE_WITH_ACTIVE_EPS_BEARER_CONTEXT = 0x70 ,	 
	 
 /****internal reject cause also contained in CiPsRc vaule , one to one mapped with sml3_typ.h ****/	 
 // internal reject start from 0x0100 , and the cause before 0x0100 is reserved for 3 GPP ;	 
 /** !!!!!! Local cause !!!!!!!!!!*/	 
 CIRC_PS_INTERNAL_LOCAL_CAUSE_BASE = 0x0100 ,	 
 CIRC_PS_ENMERGNECY_BEARER_SERVICE_ALREADY_RUN = 0x0101 , // for emergency bearer only	 
 CIRC_PS_HANDOVER_FLAG = 0x0102 , // IRAT	 
 CIRC_PS_CAUSE_EPS_SERVICE_NOT_AVAILABLE = 0x0103 , // EPS PS service not available ,	 
	 
 CIRC_PS_NOTIFY_REATTACH = 0x0104 ,	 
 CIRC_PS_NOTIFY_DETACH = 0x0105 ,	 
 CIRC_PS_PDN_REQUEST_NEED_RETRY = 0x0106 ,	 
 CIRC_PS_APN_IS_NOT_AVAILABLE = 0x0107 , // APN is missing	 
 CIRC_PS_EMERGENCY_PDN_REQUEST_CONTAINS_APN = 0x0108 , // emergency bearer should not contain APN	 
 CIRC_PS_ATTACH_FOR_EMERGENCY_BEARER_SERVICE = 0x0109 , // emergency attached , but require additional bearer	 
 // SM_CAUSE_IMS_BLOCK = 0x89 ,	 
	 
 CIRC_PS_PDP_OPERATTION_NOT_ALLOWED = 0x010B , // PDP operation not allowed for some reason	 
 CIRC_PS_PDP_INPUT_PARAM_INVALID = 0x010C ,	 
 CIRC_PS_T3396_RUNNING = 0x010D ,	 
 CIRC_PS_TIMER_OUT_ERROR = 0x010E ,	 
	 
 // Cause for PDP activation request reject in AB side ;	 
 CIRC_PS_NO_FREE_NSAPIS = 0x0150 ,	 
 CIRC_PS_GPRS_SERVICE_NOT_AVAILABLE = 0x0151 ,	 
 CIRC_PS_POWERING_DOWN = 0x0152 ,	 
 CIRC_PS_FDN_FAILURE = 0x0153 ,	 
 CIRC_PS_APN_CHECK_FAILURE = 0x0154 ,	 
	 
	 
 CIRC_PS_OPERATION_REJECT_BY_MM = 0x0200 , // reject by GMM / EMM	 
 CIRC_PS_PDP_REJECT_DSDS = 0x3300 , /**< PDP reject on DSDS */	 
	 
	 
 CIRC_PS_NO_CAUSE_SET = 0x3400 ,	 
	 
 /*!!!! internal cause add here !!!!*/	 
 CIRC_PS_CAUSE_UNKNOWN = 0xFFFF ,	 
 /* This one must always be last in the list! */	 
 CIRC_PS_NUM_RESCODES = 0xFFFF /**< Number of result codes */	 
 } _CiPsRc;

typedef UINT16 CiPsRc ;
//ICAT EXPORTED ENUM 
 typedef enum CIPSPDPTYPE_TAG 
 {	 
 CI_PS_PDP_TYPE_PPP = 0 , /**< PPP */	 
 CI_PS_PDP_TYPE_IP , /**< IPv4 */	 
 CI_PS_PDP_TYPE_IPV6 , /**< IPv6 */	 
 CI_PS_PDP_TYPE_IPV4V6 , /**< IPv4v6 */	 
 CI_PS_PDP_TYPE_X25 , /**< X25 */	 
 CI_PS_PDP_TYPE_OSPIH , /**< OSPIH */	 
 CI_PS_PDP_TYPE_NONIP , /**< Non-IP */	 
 CI_PS_PDP_TYPE_ETHERNET , /**< Ethernet> */	 
 CI_PS_PDP_TYPE_UNSTRUCTURED , /**<Unstructured> */	 
	 
 CI_PS_PDP_NUM_TYPES	 
 } _CiPsPdpType;

typedef UINT8 CiPsPdpType ;
typedef UINT8 SacPsEventReportMode ;
typedef UINT8 SacPsEventReportBufferMode ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPdpAddr_struct 
 {	 
 UINT8 len ; /**< Length of the address field [ CI_PS_PDP_IP_V4_SIZE| CI_PS_PDP_IP_V6_SIZE ] */	 
 UINT8 valData [ 64 +1 ] ; /**< Address field */	 
 } CiPsPdpAddr;

//ICAT EXPORTED ENUM 
 typedef enum CIPSDCOMP_TAG 
 {	 
 CI_PS_DCOMP_OFF = 0 , /**< Off ; this is the default value */	 
 CI_PS_DCOMP_ON , /**< Manufacturer preferred compression */	 
 CI_PS_DCOMP_V42bis , /**< V.42 bis */	 
 CI_PS_DCOMP_V44 , /**< V.44 */	 
	 
 CI_PS_NUM_DCOMPS	 
 } _CiPsDcomp;

typedef UINT8 CiPsDcomp ;
//ICAT EXPORTED ENUM 
 typedef enum CIPSHCOMP_TAG 
 {	 
 CI_PS_HCOMP_OFF = 0 , /**< Off ; this is the default value */	 
 CI_PS_HCOMP_TCPIP , /**< TCPIP header compression - RFC 1144 */	 
 CI_PS_HCOMP_IP , /**< IP header compression - RFC 2507 */	 
	 
 CI_PS_NUM_HCOMPS	 
 } _CiPsHcomp;

typedef UINT8 CiPsHcomp ;
typedef UINT8 CiPsIpv4AllocType ;
typedef UINT8 CiPsEmergencyIndType ;
typedef UINT8 CiPsPcscfDiscoveryType ;
typedef UINT8 CiPsImCnSignallingFlagIndType ;
typedef UINT8 CiPsNslpi ;
typedef UINT8 CiPsSecPco ;
typedef UINT8 CiPsSscMode ;
typedef UINT8 CiPsPrefAccessType ;
typedef UINT8 CiPsRQosInd ;
typedef UINT8 CiPsMh6Pdu ;
typedef UINT8 CiPsAlwaysOnReq ;
typedef UINT8 CiPsOldCid ;
typedef UINT8 CiPsEsmCauseType ;
typedef UINT8 CiPsSmFollowAct ;
typedef UINT8 CiPsIpv4MtuDiscoveryType ;
typedef UINT8 CiPsLocalAddrIndType ;
typedef UINT8 CiPsNonIpMtuDiscoveryType ;
typedef UINT8 CiPsMoPdpActReason ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsID_struct 
 {	 
 UINT8 cid ; /**< PDP Context Identifier , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 UINT8 p_cid ;	 
 UINT8 bearer_id ; /**< PDP Context Identifier , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
	 
 CiBoolean psiPresent ;	 
 UINT8 psi ;	 
	 
 CiBoolean qfiPresent ;	 
 UINT8 qfi ;	 
 } 
 CiPsID;

//ICAT EXPORTED ENUM 
 typedef enum CiPsPdpAddrType_tag 
 {	 
 CI_PS_PDP_INVALID_ADDR = 0 ,	 
 CI_PS_PDP_IPV4 , // 4 bytes length	 
 CI_PS_PDP_FULL_IPV6 , // 16 bytes length	 
 CI_PS_PDP_IPV6_INTERFACE // 8 bytes length	 
 } CiPsPdpAddrType;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPdpIpAddr_struct 
 {	 
 UINT16 addrType ; // CiPsPdpAddrType , invalid - 0 , ipv4 - 1 , Full ipv6 - 2 , Ipv6 interface - 3 ;	 
 UINT16 subnetLength ; // 0 - invalid	 
 UINT8 valData [ 16 ] ; /**< Address field */	 
 } CiPsPdpIpAddr;

//ICAT EXPORTED ENUM 
 typedef enum CiPsPdpBearType_struct 
 {	 
 CI_PS_INVALID_PDP_TYPE = 0 ,	 
 CI_PS_PRIMARY_PDP = 1 ,	 
 CI_PS_DEFAULT_PDP = 1 ,	 
	 
 CI_PS_SECONDARY_PDP = 2 ,	 
 CI_PS_DEDICATED_PDP = 2 ,	 
	 
 CI_PS_MAX_PDP_TYPE	 
 } _CiPsPdpBearType;

typedef UINT8 CiPsPdpBearType ;
//ICAT EXPORTED ENUM 
 typedef enum CIPSREQTYPE_TAG 
 {	 
 CI_PS_REQ_FOR_NEW_OR_HANDOVER_PDP = 0 , /*0 - PDP context is for new PDP context establishment or for handover from a non-3GPP access network */	 
 CI_PS_REQ_FOR_EMERGENCY_BEARER_SERVICES = 1 , /*1 - PDP context is for emergency bearer services */	 
 CI_PS_REQ_FOR_NEW_PDP = 2 , /*2 - PDP context is for new PDP context establishment */	 
 CI_PS_REQ_FOR_HANDOVER = 3 , /*3 - PDP context is for handover from a non-3GPP access network */	 
 CI_PS_REQ_FOR_HANDOVER_EMERGENCY = 4 , /*4 - PDP context is for handover of emergency bearer services from a non-3GPP access network */	 
	 
 CI_PS_REQ_FOR_MMS = 10 , /*10 - PDP context is for MMS ( internal use only ) */	 
	 
 CI_PS_NUM_REQ_TYPE	 
 } _CiPsReqType;

typedef UINT8 CiPsReqType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPdpCtx_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 Boolean voiceCid ; /*identify upper cid if is voice cid*/	 
 CiPsPdpType type ; /**< PDP type \sa CiPsPdpType */	 
 UINT8 bearer_id ; /**< PDP Context Identifier , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 CiPsPdpBearType pdpBearType ; // 0 - invalid , 1 - primary / default PDP , 2 - dedicated / secondary PDP	 
 UINT8 p_cid ;	 
	 
 /* # Start Contiguous Code Section # */	 
 CiBoolean apnPresent ; /**< Flag indicating that the APN is present ( optional field ) \sa CCI API Ref Manual*/	 
 CiString apn ; /**< APN , length range [ CI_PS_APN_MIN_SIZE - CI_PS_APN_MAX_SIZE ] . \sa CCI API Ref Manual */	 
 // CiBoolean addrPresent ; / **< Flag indicating that the address is present ( optional field ) \sa CCI API Ref Manual * /	 
 CiPsPdpIpAddr ipv4Addr ; /**< PDP address \sa CiPsPdpAddr_struct */	 
 CiPsPdpIpAddr ipv6Addr ;	 
 CiBoolean dcompPresent ; /**< Flag indicating that data compression field is present \sa CCI API Ref Manual */	 
 CiPsDcomp dcomp ; /**< PDP data compression , only applicable to SNDCP , ignore it for UMTS \sa CiPsDcomp */	 
 CiBoolean hcompPresent ; /**< Flag indicating that header compression field is present \sa CCI API Ref Manual */	 
 CiPsHcomp hcomp ; /**< PDP header compression \sa CiPsHcomp */	 
 CiBoolean pdParasPresent ; /**< Flag indicating that the pdParas is present ( optional field ) \sa CCI API Ref Manual*/	 
 CiString pdParas ; /**< PDP specific parameters \sa CCI API Ref Manual */	 
	 
 CiBoolean ipAddrAllocPresent ;	 
 CiPsIpv4AllocType ipAddrAlloc ;	 
	 
 CiBoolean reqTypePresent ; /**< Flag indicating that request type field is present \sa CiPsReqType */	 
 CiPsReqType reqType ; /**Type of PDP context activation request , refer to TS27.007 c80*/	 
 CiBoolean pCscfDiscoveryPresent ;	 
 CiPsPcscfDiscoveryType pCscfDiscovery ;	 
 CiBoolean imCnSignallingFlagIndPresent ;	 
 CiPsImCnSignallingFlagIndType imCnSignallingFlagInd ; /**IM_CN_Signalling_Flag_Ind*/	 
 // CiBoolean nslpiPresent ;	 
 // CiPsNslpi nslpi ;	 
 // CiBoolean secPcoPresent ;	 
 // CiPsSecPco secPco ;	 
 CiBoolean nslpiPresent ;	 
 UINT8 nslpi ; /**NSLPI*/	 
 CiBoolean securePcoPresent ;	 
 UINT8 securePco ; /**securePCO*/	 
	 
 CiBoolean ipv4MtudiscoveryPresent ;	 
 CiPsIpv4MtuDiscoveryType ipv4Mtudiscovery ; /**IPv4_MTU_discovery*/	 
 CiBoolean localAddrIndPresent ;	 
 CiPsLocalAddrIndType localAddrInd ; /**Local_Addr_ind*/	 
 CiBoolean nonIpMtuDiscoveryPresent ;	 
 CiPsNonIpMtuDiscoveryType nonIpMtuDiscovery ; /**Non-IP_MTU_discovery*/	 
	 
 CiBoolean sscModePresent ;	 
 CiPsSscMode sscMode ;	 
 CiBoolean sNssaiPresent ;	 
 CiString sNssai ;	 
 CiBoolean prefAccessTypePresent ;	 
 CiPsPrefAccessType prefAccessType ;	 
 CiBoolean rQosIndPresent ;	 
 CiPsRQosInd rQosInd ;	 
 CiBoolean mh6PduPresent ;	 
 CiPsMh6Pdu mh6Pdu ;	 
 CiBoolean alwaysOnReqPresent ;	 
 CiPsAlwaysOnReq alwaysOnReq ;	 
	 
 CiBoolean lifeTimePresent ;	 
 UINT16 lifeTime ;	 
	 
 CiBoolean oldCidPresent ;	 
 CiPsOldCid oldCid ;	 
	 
 CiBoolean esmCausePresent ;	 
 CiPsEsmCauseType esmCause ;	 
 CiPsSmFollowAct smFlwAct ; // SM following action	 
 } CiPsPdpCtx;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPdpCtxInfo_struct 
 {	 
 CiBoolean actState ; /**< Activation state ; TRUE: activate \sa CCI API Ref Manual */	 
 CiPsPdpCtx pdpCtx ; /**< PDP context parameters \sa CiPsPdpCtx_struct */	 
 } CiPsPdpCtxInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPdpCtxCap_struct 
 {	 
 CiNumericRange cids ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] \sa CCI API Ref Manual */	 
 CiPsPdpType type ; /**< PDP type \sa CiPsPdpType */	 
 CiBitRange bitsDcomp ; /**< Data compression capability , represented as bit mask. Each bit represents a value in CiPsDcomp. \sa CiPsDcomp */	 
 /* each bit represents a capability in CiPsDcomp ,	 
 e.g. ( bitsDcomp& ( 1 << CI_PS_HCOMP_OFF ) ) !=0 means	 
 CI_PS_DCOMP_OFF is supported ,	 
 ( bitsDcomp& ( 1 <<CI_PS_DCOMP_ON ) ) !=0 means	 
 CI_PS_DCOMP_ON is supported */	 
 CiBitRange bitsHcomp ; /**< Header compression capability , represented as bit mask. Each bit represents a value in CiPsHcomp. \sa CiPsHcomp*/	 
	 
 /* TBD , not sure <pd1> to <pdn> is going to be presented */	 
 } CiPsPdpCtxCap;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPdpCtxCaps_struct 
 {	 
 UINT8 size ; /**< Number of capability profiles ; currently only one profile is supported */	 
 CiPsPdpCtxCap caps [ 1 ] ; /**< PDP context capabilities \sa CiPsPdpCtxCap_struct*/	 
 } CiPsPdpCtxCaps;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPdpCtxActState_struct 
 {	 
 UINT8 cid ; /**< PDP context identification , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 CiBoolean activated ; /**< TRUE: activated ; FALSE: deactivated \sa CCI API Ref Manual */	 
 } CiPsPdpCtxActState;

typedef CiPsPdpCtxActState *CiPsPdpCtxActStateListPtr ;
//ICAT EXPORTED ENUM 
 typedef enum CIPSL2P_TAG 
 {	 
 CI_PS_L2P_NONE = 0 , /**< Not PPP */	 
 CI_PS_L2P_PPP , /**< PPP */	 
	 
 CI_PS_NUM_L2PS	 
 } _CiPsL2P;

typedef UINT8 CiPsL2P ;
//ICAT EXPORTED ENUM 
 typedef enum CIPSGSMGPRSCLASS_TAG 
 {	 
 CI_PS_GSMGPRS_CLASS_A = 0 , /**< Class A */	 
 CI_PS_GSMGPRS_CLASS_B , /**< Class B */	 
 CI_PS_GSMGPRS_CLASS_CS , /**< Class C , GPRS only */	 
 CI_PS_GSMGPRS_CLASS_CC , /**< Class C , circuit switch only */	 
	 
 CI_PS_GSMGPRS_NUM_CLASSES	 
 } _CiPsGsmGprsClass;

typedef UINT8 CiPsGsmGprsClass ;
//ICAT EXPORTED ENUM 
 /** \brief Network registration configuration flag values */ 
 /** \remarks Common Data Section */ 
 typedef enum CIPSNWREGINDFLAG_TAG 
 {	 
 CI_PS_NW_REG_IND_DISABLE = 0 , /**< Disable network registration status reports , n = 0 */	 
 CI_PS_NW_REG_IND_ENABLE_STA_ONLY , /**< Enable network registration status reports , n = 1 */	 
 CI_PS_NW_REG_IND_ENABLE_DETAIL , /**< Enable detailed network registration status reports , n= 2 */	 
 CI_PS_NW_REG_IND_ENABLE_MORE_DETAIL , /**< Enable more detailed network registration status reports , n = 3 */	 
 CI_PS_NW_REG_IND_ENABLE_PSM , /**< Enable more detailed network registration status reports , n = 4 */	 
 CI_PS_NW_REG_IND_ENABLE_PSM_DETAIL , /**< Enable more detailed network registration status reports , n = 5 */	 
	 
 CI_PS_NW_REG_IND_ENABLE_NUM	 
	 
 } _CiPsNwRegIndFlag;

typedef UINT8 CiPsNwRegIndFlag ;
typedef UINT8 CiPs4GNwRegIndFlag ;
//ICAT EXPORTED ENUM 
 typedef enum CIPSNWREGSTATUS_TAG 
 {	 
 CI_PS_NW_REG_STA_NOT_REGED = 0 , /**< Not registered and not searching */	 
 CI_PS_NW_REG_STA_REG_HPLMN , /**< Registered on home PLMN */	 
 CI_PS_NW_REG_STA_TRYING , /**< Not registered , but cellular subsystem is searching for a PLMN to register to */	 
 CI_PS_NW_REG_STA_REG_DENIED , /**< Registration denied */	 
 CI_PS_NW_REG_STA_UNKNOWN , /**< Unknown */	 
 CI_PS_NW_REG_STA_REG_ROAMING = 5 , /**< Registered on visited PLMN */	 
 CI_PS_NW_REG_STA_SMS_ONLY_HOME , /**< registered for " SMS only " , home network ( applicable only when <AcT> indicates E-UTRAN ) */	 
 CI_PS_NW_REG_STA_SMS_ONLY_ROAMING , /**< registered for " SMS only " , roaming ( applicable only when <AcT> indicates E-UTRAN ) */	 
 CI_PS_NW_REG_STA_EMERGENCY_ONLY_FOR_REGISTERED , /**< attached for emergency bearer services only ( see NOTE 2 ) */	 
 CI_PS_NW_REG_STA_CSFB_NOT_PREFERRED_HOME , /**<registered for " CSFB not preferred " , home network ( applicable only when <AcT> indicates E-UTRAN ) */	 
 CI_PS_NW_REG_STA_CSFB_NOT_PREFERRED_ROAMING = 10 , /**<registered for " CSFB not preferred " , roaming ( applicable only when <AcT> indicates E-UTRAN ) */	 
 CI_PS_NW_REG_STA_ACCESS_TO_RLOS , /**<attached for access to RLOS ( See NOTE 2 a ( not applicable ) */	 
 CI_PS_NW_REG_STA_DISASTER_FOR_ROAMING , /**<register fro " disaster for roaming services " */	 
 CI_PS_NW_REG_STA_DISASTER_CONDITION , /**<disaster condition applied to the current PLMN*/	 
	 
 /*self-defined value*/	 
 /*Modified by zuohuaxu for CQ00153422 , begin*/	 
	 
 CI_PS_NW_REG_STA_REG_EMERGENCY = 64 , /**< Not registered for emergency only*/	 
 CI_PS_NW_REG_STA_ECALL_INACTIVE , /**< eCall only when camp on NR / LTE for eCall over IMS */	 
 /*Modified by zuohuaxu for CQ00153422 , end*/	 
	 
 CI_PS_NUM_REGSTATUS /**< Number of status values defined */	 
 } _CiPsNwRegStatus;

typedef UINT8 CiPsNwRegStatus ;
//ICAT EXPORTED ENUM 
 typedef enum CIPS_ACT_TECH_MODE 
 {	 
 CI_PS_ACT_GSM = 0 , /**< GSM */	 
 CI_PS_ACT_GSM_COMPACT , /**< Not supported */	 
 CI_PS_ACT_UTRAN , /**< UTRAN */	 
 CI_PS_ACT_GSM_EGPRS , /**< GSM w / EGPRS */	 
 CI_PS_ACT_UTRAN_HSDPA , /**< UTRAN w / HSDPA */	 
 CI_PS_ACT_UTRAN_HSUPA , /**< UTRAN w / HSUPA */	 
 CI_PS_ACT_UTRAN_HSPA , /**< UTRAN w / HSDPA and HSUPA */	 
 CI_PS_ACT_EUTRAN , /**< E-UTRAN */	 
 CI_PS_ACT_EC_GSM_IOT , /**< EC-GSM-Iot */	 
 CI_PS_ACT_EUTRAN_NB , /**< EUTRAN ( NB-S1 ) */	 
 CI_PS_ACT_EUTRAN_TO_5GCN , /**<EUTRAN CONNECTED TO 5 GC */	 
 CI_PS_ACT_NR_TO_5GCN , /**<NR */	 
 CI_PS_ACT_NGRAN , /**<NGRAN */	 
 CI_PS_ACT_EUTRAN_NR_DUAL_LINK , /**<ENDC */	 
 CI_PS_ACT_SATELLITE_EUTRAN_NB ,	 
 CI_PS_ACT_SATELLITE_EUTRAN_WB ,	 
 CI_PS_ACT_SATELLITE_NGRAN ,	 
	 
 CI_PS_ACT_UTRAN_HSPA_PLUS = 30 , /**< UTRAN w / HSPA+ */	 
 CI_PS_ACT_EUTRAN_PLUS , /**< E-UTRAN CA*/	 
 /* Added by taow 20190708 CQ00115423 , begin */	 
 CI_PS_ACT_UTRAN_DC_HSPA , /*DC-HSPA*/	 
 /* Added by taow 20190708 CQ00115423 , end */	 
 CI_PS_NUM_ACT	 
 } _CiPsAccTechMode;

typedef UINT8 CiPsAccTechMode ;
//ICAT EXPORTED ENUM 
 typedef enum CIPS_CAUSE_TYPE 
 {	 
 CI_PS_CAUSE_TYPE_PS = 0 , /**< Indicates that <reject_cause> contains a GMM / EMM cause value , see 3 GPP TS 24.008000 [ 8 ] Annex G.*/	 
 CI_PS_CAUSE_TYPE_MANUFACTURER , /**< Indicates that <reject_cause> contains a manufacturer specific cause */	 
 CI_PS_CAUSE_NONE ,	 
 CI_PS_NUM_CAUSE_TYPE	 
 } _CiPsCauseType;

typedef UINT8 CiPsCauseType ;
//ICAT EXPORTED ENUM 
 typedef enum CIPS_NETOP_DIGIT_MNC 
 {	 
 CIPS_NETOP_TWO_DIGIT_MNC = 2 , /*2 digit */	 
 CIPS_NETOP_THREE_DIGIT_MNC , /*3 digit */	 
 /* This one must always be last in the list! */	 
 CIPS_NUM_NETOP_DIGIT_MNC	 
 } _CiPsNetOpDigitMnc;

typedef UINT8 CiPsNetOpDigitMnc ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsNetworkId_struct 
 {	 
 UINT16 CountryCode ; /**< 3 -digit country code */	 
 UINT16 NetworkCode ; /**< 3 -digit network code */	 
	 
 CiPsNetOpDigitMnc MncDigit ; /**< MncDigit \sa CiPsNetOpDigitMnc */	 
 } CiPsNetworkId;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsNwRegInfo_struct 
 {	 
 CiPsNwRegStatus status ; /**< Network registration status \sa CiPsNwRegStatus */	 
	 
 CiBoolean lacPresent ; /**< Indicates if LAC and cell ID are present \sa CCI API Ref Manual */	 
 UINT16 lac ; /**< Location area code */	 
 UINT32 cellId ; /**< Cell ID */	 
 CiPsAccTechMode act ; /**< Network access technology ( GSM , UTRAN , LTE etc. ) \sa CiPsAccTechMode */	 
 UINT8 rac ; /**<one byte routing area code> */	 
	 
 CiBoolean causePresent ; /**< Indicates if causeType and rejectCause are present> **/	 
 CiPsCauseType causeType ; /**<cause_type>: integer type ; indicates the type of <reject_cause>**/	 
 UINT32 rejectCause ; /**<reject_cause>: integer type ; contains the cause of the failed registration **/	 
 /* add by perse 1032018 add +CREG / +CGREG / +CEREG indication content with rplmn CQ00117472 , begin */	 
 // #if defined ( CRANE_Z1 )	 
 CiPsNetworkId rplmnInfo ; /**reprot rplmn information*/	 
 // #endif	 
 /* add by perse 1032018 add +CREG / +CGREG / +CEREG indication content with rplmn CQ00117472 , end */	 
 } CiPsNwRegInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiPs4GNwRegInfo_struct 
 {	 
 CiPsNwRegStatus status ; /**< Network registration status. \sa CiPsNwRegStatus */	 
	 
 CiBoolean tacPresent ; /**< Indicates if LAC and Cell ID are present. \sa CCI API Ref Manual */	 
 UINT16 tac ; /**< String type ; two byte tracking area code in hexadecimal format ( e.g. " 0 C3 " equals 195 in decimal ) */	 
 UINT32 cellId ; /**< Cell ID */	 
 CiPsAccTechMode act ; /**< Network access technology ( GSM , UTRAN , LTE etc. ) \sa CiPsAccTechMode */	 
	 
 CiBoolean causePresent ; /**< Indicates if causeType and rejectCause are present. >**/	 
 CiPsCauseType causeType ; /**<cause_type>: integer type ; indicates the type of <reject_cause>**/	 
 UINT32 rejectCause ; /**<reject_cause>: integer type ; contains the cause of the failed registration.**/	 
 CiBoolean activeTimePresent ; /**< Indicates if Active Time is present. >**/	 
 UINT8 activeTime ; /**< string type , one byte in an 8 bit format , indicates the Active Timer value T3324 **/	 
	 
 CiBoolean periodicTauPresent ; /**< Indicates if Periodic TAU is prenset. >**/	 
 UINT8 periodicTau ; /**< string type , one byte in an 8 bit format , indicates the extended periodic TAU value T3412 **/	 
 /* add by xwzhou for CQ67291 on 8052014 , begin */	 
 CiBoolean volteAvailable ;	 
 CiBoolean imsEmergencyAvailable ;	 
 /* add by xwzhou for CQ67291 on 8052014 , end */	 
 /* add by perse 1032018 add +CREG / +CGREG / +CEREG indication content with rplmn CQ00117472 , begin */	 
 // #if defined ( CRANE_Z1 )	 
 CiPsNetworkId rplmnInfo ; /**reprot rplmn information*/	 
 // #endif	 
 /* add by perse 1032018 add +CREG / +CGREG / +CEREG indication content with rplmn CQ00117472 , end */	 
 } CiPs4GNwRegInfo;

//ICAT EXPORTED ENUM 
 typedef enum CIPSQOSRELIABILITYCLASS 
 {	 
 CI_PS_QOS_RELIABILITY_CLASS_SUBSCRIBED = 0 , /**< Subscribed reliability class */	 
 CI_PS_QOS_RELIABILITY_CLASS_1 , /**< Acknowledged GTP , LLC , and RLC ; protected data */	 
 CI_PS_QOS_RELIABILITY_CLASS_2 , /**< Unacknowledged GTP ; acknowledged LLC and RLC , Protected data */	 
 CI_PS_QOS_RELIABILITY_CLASS_3 , /**< Unacknowledged GTP and LLC ; acknowledged RLC , Protected data */	 
 CI_PS_QOS_RELIABILITY_CLASS_4 , /**< Unacknowledged GTP , LLC , and RLC , protected data */	 
 CI_PS_QOS_RELIABILITY_CLASS_5 , /**< Unacknowledged GTP , LLC , and RLC , unprotected data */	 
	 
 CI_PS_QOS_NUM_RELIABILITY_CLASSES	 
 } _CiPsQosReliabilityClass;

typedef UINT8 CiPsQosReliabilityClass ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsQosProfile_struct 
 {	 
 UINT8 precedence ; /**< Precedence class */	 
 UINT8 delay ; /**< Delay class */	 
 UINT8 reliability ; /**< Reliability class */	 
 UINT8 peak ; /**< Peak throughput */	 
 UINT8 mean ; /**< Mean throughput */	 
 } CiPsQosProfile;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsSecPdpCtx_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier */	 
 UINT8 p_cid ; /**< Primary PDP context identifier */	 
 UINT8 bearer_id ; /**< PDP Context Identifier , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 CiBoolean dcompPresent ; /**< TRUE if present \sa CCI API Ref Manual */	 
 CiBoolean hcompPresent ; /**< TRUE if present \sa CCI API Ref Manual */	 
 CiPsDcomp dcomp ; /**< PDP data compression , only applicable to SNDCP , ignore it for UMTS \sa CiPsDcomp */	 
 CiPsHcomp hcomp ; /**< PDP header compression \sa CiPsHcomp */	 
 CiBoolean imCnSigFlagPresent ;	 
 CiPsImCnSignallingFlagIndType imCnSigFlag ;	 
	 
 CiBoolean psiPresent ;	 
 UINT8 psi ;	 
 CiBoolean qfiPresent ;	 
 UINT8 qfi ;	 
 } CiPsSecPdpCtx;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsSecPdpCtxInfo_struct 
 {	 
 CiBoolean actState ; /**< Activation state \sa CCI API Ref Manual */	 
 CiPsSecPdpCtx secPdpCtx ; /**< Secondary PDP context information \sa CiPsSecPdpCtx_struct */	 
 } CiPsSecPdpCtxInfo;

//ICAT EXPORTED ENUM 
 typedef enum CIPS3GQOSTYPE_TAG 
 {	 
 CI_PS_3G_QOSTYPE_MIN = 0 , /**< Minimum QoS */	 
 CI_PS_3G_QOSTYPE_REQ , /**< Requested QoS */	 
 CI_PS_3G_QOSTYPE_NEG , /**< Negotiated QoS */	 
	 
 CI_PS_3G_QOSTYPE_NUMTYPES	 
 } _CiPs3GQosType;

typedef UINT8 CiPs3GQosType ;
//ICAT EXPORTED ENUM 
 typedef enum CIPS3GTRAFFICCLASS_TAG 
 {	 
 CI_PS_3G_TRAFFIC_CLASS_CONVERSATIONAL = 0 , /**< Conversational */	 
 CI_PS_3G_TRAFFIC_CLASS_STREAMING , /**< Streaming */	 
 CI_PS_3G_TRAFFIC_CLASS_INTERACTIVE , /**< Interactive */	 
 CI_PS_3G_TRAFFIC_CLASS_BACKGROUND , /**< Background */	 
 CI_PS_3G_TRAFFIC_CLASS_SUBSCRIBED , /**< Subscribed value */	 
	 
 CI_PS_3G_TRAFFIC_CLASS_NUMCLASSES	 
 } _CiPs3GTrafficClass;

typedef UINT8 CiPs3GTrafficClass ;
//ICAT EXPORTED ENUM 
 typedef enum CIPS3GDLVORDER_TAG 
 {	 
 CI_PS_3G_DLV_ORDER_NO = 0 , /**< Without delivery order ( no ) */	 
 CI_PS_3G_DLV_ORDER_YES , /**< With delivery order ( yes ) */	 
 CI_PS_3G_DLV_ORDER_SUBSCRIBED , /**< Subscribed value */	 
	 
 CI_PS_3G_NUM_DLV_ORDER	 
 } _CiPs3GDlvOrder;

typedef UINT8 CiPs3GDlvOrder ;
//ICAT EXPORTED ENUM 
 typedef enum CIPS3GDLVERRORSDU_TAG 
 {	 
 CI_PS_3G_DLV_ERROR_SDU_NO = 0 , /**< Erroneous SDUs are not delivered ( no ) */	 
 CI_PS_3G_DLV_ERROR_SDU_YES , /**< Erroneous SDUs are delivered ( yes ) */	 
 CI_PS_3G_DLV_ERROR_SDU_NODETECT , /**< No detect ( ' - ' ) */	 
 CI_PS_3G_DLV_ERROR_SDU_SUBSCRIBED , /**< Subscribed value */	 
	 
 CI_PS_3G_NUM_DLV_ERROR_SDU	 
 } _CiPs3GDlvErrorSdu;

typedef UINT8 CiPs3GDlvErrorSdu ;
//ICAT EXPORTED ENUM 
 typedef enum CIPS3GRESIDUALBER_TAG 
 {	 
 CI_PS_3G_RESIDUAL_BER_SUBSCRIBED = 0 , /**< Subscribed value */	 
 CI_PS_3G_RESIDUAL_BER_5EM2 , /**< 5 * 10 ^-2 */	 
 CI_PS_3G_RESIDUAL_BER_1EM2 , /**< 1 * 10 ^-2 */	 
 CI_PS_3G_RESIDUAL_BER_5EM3 , /**< 5 * 10 ^-3 */	 
 CI_PS_3G_RESIDUAL_BER_4EM3 , /**< 4 * 10 ^-3 */	 
 CI_PS_3G_RESIDUAL_BER_1EM3 , /**< 1 * 10 ^-3 */	 
 CI_PS_3G_RESIDUAL_BER_1EM4 , /**< 1 * 10 ^-4 */	 
 CI_PS_3G_RESIDUAL_BER_1EM5 , /**< 1 * 10 ^-5 */	 
 CI_PS_3G_RESIDUAL_BER_1EM6 , /**< 1 * 10 ^-6 */	 
 CI_PS_3G_RESIDUAL_BER_6EM8 , /**< 6 * 10 ^-8 */	 
	 
 CI_PS_3G_NUM_RESIDUAL_BER	 
 } _CiPs3GResidualBer;

typedef UINT8 CiPs3GResidualBer ;
//ICAT EXPORTED ENUM 
 typedef enum CIPS3GSDUERRORRATIO_TAG 
 {	 
 CI_PS_3G_SDU_ERROR_RATIO_SUBSCRIBED = 0 , /**< Subscribed value */	 
 CI_PS_3G_SDU_ERROR_RATIO_1EM2 , /**< 1 * 10 ^-2 */	 
 CI_PS_3G_SDU_ERROR_RATIO_7EM3 , /**< 7 * 10 ^-3 */	 
 CI_PS_3G_SDU_ERROR_RATIO_1EM3 , /**< 1 * 10 ^-3 */	 
 CI_PS_3G_SDU_ERROR_RATIO_1EM4 , /**< 1 * 10 ^-4 */	 
 CI_PS_3G_SDU_ERROR_RATIO_1EM5 , /**< 1 * 10 ^-5 */	 
 CI_PS_3G_SDU_ERROR_RATIO_1EM6 , /**< 1 * 10 ^-6 */	 
 CI_PS_3G_SDU_ERROR_RATIO_1EM1 , /**< 1 * 10 ^-1 */	 
	 
 CI_PS_3G_NUM_SDU_ERROR_RATIOS	 
 } _CiPs3GSduErrorRatio;

typedef UINT8 CiPs3GSduErrorRatio ;
//ICAT EXPORTED ENUM 
 typedef enum CIPS3GTRAFFICPRIORITY_TAG 
 {	 
 CI_PS_3G_SDU_TRAFFIC_PRIORITY_SUBSCRIBED = 0 , /**< Subscribed value */	 
 CI_PS_3G_SDU_TRAFFIC_PRIORITY_LEVEL_1 , /**< Priority Level 1 */	 
 CI_PS_3G_SDU_TRAFFIC_PRIORITY_LEVEL_2 , /**< Priority Level 2 */	 
 CI_PS_3G_SDU_TRAFFIC_PRIORITY_LEVEL_3 , /**< Priority Level 3 */	 
	 
 CI_PS_3G_NUM_TRAFFIC_PRIORITIES	 
 } _CiPs3GTrafficPriority;

typedef UINT8 CiPs3GTrafficPriority ;
typedef CiPsQosProfile CiPs25GQosProfile ;
//ICAT EXPORTED ENUM 
 typedef enum CIPSISEXTENSION_TAG 
 {	 
 CI_PS_3G_MAX_BIT_RATE_FOR_DL = 0 , /**< Maximum bit rate for DL */	 
 CI_PS_3G_GUARNTEED_BIT_RATE_FOR_DL , /**< Maximum guaranteed bit rate for DL */	 
 CI_PS_3G_MAX_BIT_RATE_FOR_UL , /**< Maximum bit rate for UL */	 
 CI_PS_3G_GUARNTEED_BIT_RATE_FOR_UL , /**< Maximum guaranteed bit rate for UL */	 
 CI_PS_3G_NUM_EXTENTION_IND = 0x7FFFFFFF	 
 } _CiPsIsExtensionType;

typedef CiBitRange CiPsIsExtensionType ;
//ICAT EXPORTED ENUM 
 typedef enum CIPS_SOURCE_STATI_DESC_TAG 
 {	 
 CI_PS_SOURCE_STAT_DESC_UNKNOWN = 0 , /**< Unknown */	 
 CI_PS_SOURCE_STAT_DESC_SPEECH , /**< Speech */	 
 CI_PS_SOURCE_STAT_DESC_NUM = 0x7F	 
 } _CiPsSourceStatisticDescriptorType;

typedef UINT8 CiPsSourceStatisticDescriptorType ;
//ICAT EXPORTED ENUM 
 typedef enum CIPS_SIGNALLING_IND_TAG 
 {	 
 CI_PS_NOT_OPTIMIZED_FOR_SIGNALLING = 0 , /**< Not optimized for signaling traffic */	 
 CI_PS_OPTIMIZED_FOR_SIGNALLING , /**< Optimized for signaling traffic */	 
 CI_PS_SIGNALLING_IND_NUM = 0x7F	 
 } _CiPsSignallingIndicationType;

typedef UINT8 CiPsSignallingIndicationType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPs3GQosProfile_struct 
 {	 
 CiPs3GTrafficClass trafficClass ; /**< Traffic class \sa CiPs3GTrafficClass */	 
 CiPs3GDlvOrder deliveryOrder ; /**< Delivery order \sa CiPs3GDlvOrder */	 
 CiPs3GDlvErrorSdu deliveryOfErrSdu ; /**< Delivery of erroneous SDUs \sa CiPs3GDlvErrorSdu */	 
 CiPs3GResidualBer resBER ; /**< Residual bit error rate \sa CiPs3GResidualBer */	 
 CiPs3GSduErrorRatio sduErrRatio ; /**< SDU error ratio \sa CiPs3GSduErrorRatio */	 
 CiPs3GTrafficPriority thPriority ; /**< Traffic handling priority ( interactive class only ) \sa CiPs3GTrafficPriority */	 
	 
 UINT8 transDelay ; /**< Transfer delay ( conversational / streaming classes only ) */	 
 UINT8 maxSduSize ; /**< Max SDU size */	 
 UINT16 maxULRate ; /**< Max bit rate , uplink */	 
 UINT16 maxDLRate ; /**< Max bit rate , downlink */	 
 UINT16 guaranteedULRate ; /**< Guaranteed bit rate , uplink */	 
 UINT16 guaranteedDLRate ; /**< Guaranteed bit rate , downlink */	 
 CiPsIsExtensionType IsExtension ; /**< Bit mask indicating if the parameters maxDLRate and guaranteedDLRate are encoded as	 
 extension bytes */	 
 CiPsSourceStatisticDescriptorType SourceStatisticDescriptor ; /**< Specifies characteristics of the source of submitted SDUs */	 
 CiPsSignallingIndicationType SignallingIndication ; /**< Indicates the signaling nature of the submitted SDUs. */	 
	 
 } CiPs3GQosProfile;

typedef CiPsPdpAddr CiPsPdpAddrMask ;
//ICAT EXPORTED ENUM 
 typedef enum CIPS_TFT_DIRECTION_IND_TAG 
 {	 
 CI_PS_TFT_DIRECTION_PRE_R7 = 0 ,	 
 CI_PS_TFT_DIRECTION_UPLINK ,	 
 CI_PS_TFT_DIRECTION_DOWNLINK ,	 
 CI_PS_TFT_DIRECTION_BI_DIRECTIONAL ,	 
 CI_PS_NUM_TFT_DIR	 
 } _CiPsTftDirectionIndicationType;

typedef UINT8 CiPsTftDirectionIndicationType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsTftFilter_struct 
 {	 
 UINT8 cid ; /**< PDP Context Identifier , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 UINT8 pfId ; /**< Packet filter identifier */	 
 UINT8 epIndex ; /**< Evaluation precedence index */	 
 /* Added by Daniel for ********** , begin */	 
 CiBoolean matchAll ;	 
 /* Added by Daniel for ********** , end */	 
 UINT8 pIdNextHdr ; /**< Protocol number ( ipv4 ) / next header ( ipv6 ) */	 
 CiBoolean pIdNextHdrPresent ; /**< TRUE: if present \sa CCI API Ref Manual */ // Michal	 
 UINT8 tosTc ; /**< Type of service / traffic class */	 
 CiBoolean tosPresent ; /**< TRUE: if present \sa CCI API Ref Manual */ // Michal	 
 UINT8 tosTcMask ; /**< Type of service / traffic class mask */	 
 CiNumericRange dstPortRange ; /**< Destination port range \sa CCI API Ref Manual */	 
 CiBoolean dstPortRangePresent ; /**< TRUE: if present \sa CCI API Ref Manual */ // Michal	 
 CiNumericRange srcPortRange ; /**< Source port range \sa CCI API Ref Manual */	 
 CiBoolean srcPortRangePresent ; /**< TRUE: if present \sa CCI API Ref Manual */ // Michal	 
	 
 UINT32 ipSecSPI ; /**< IPSec security parameter index */	 
 CiBoolean ipSecSPIPresent ; /**< TRUE: if present \sa CCI API Ref Manual */ // Michal	 
 UINT32 flowLabel ; /**< Flow label */	 
 CiBoolean flowLabelPresent ; /**< TRUE: if present \sa CCI API Ref Manual */ // Michal	 
 CiPsPdpIpAddr remoteAddrAndMask ; /**< remote address and subnet mask */ // the netmask infor store in this struct.	 
 // CiPsPdpAddrMask srcAddrMask ; / **< Source address mask - subnet mask \sa CiPsPdpAddrMask * /	 
 CiPsTftDirectionIndicationType direction ; /**< specifies the transmission direction in which the packet filter shall be applied */	 
 UINT8 nwpfId ; /**< NW Packet filter identifier */	 
	 
 CiPsPdpIpAddr localAddrAndMask ; /**< local address and subnet mask , seems useless */	 
	 
 /* Added by Daniel for CQ00136993 , begin */	 
 CiBoolean ethernetTypePresent ;	 
 UINT16 ethernetType ; // defined as 802.300000	 
	 
 CiBoolean srcMacAddrPresent ;	 
 UINT8 srcMacAddr [ 6 ] ;	 
	 
 CiBoolean destMacAddrPresent ;	 
 UINT8 destMacAddr [ 6 ] ;	 
	 
 CiBoolean srcMacAddrRangePresent ;	 
 UINT8 lowSrcMacAddr [ 6 ] ;	 
 UINT8 highSrcMacAddr [ 6 ] ;	 
	 
 CiBoolean destMacAddrRangePresent ;	 
 UINT8 lowDestMacAddr [ 6 ] ;	 
 UINT8 highDestMacAddr [ 6 ] ;	 
	 
 CiBoolean cTagVidPresent ;	 
 UINT16 cTagVid ; // 802.100000 Q Customer-VLAN tag VID ,	 
 // bit16~bit13: spare ,	 
 // bit12~bit0: VID	 
 CiBoolean sTagVidPresent ;	 
 UINT16 sTagVid ; // 802.100000 Q Service-VLAN tag VID ,	 
 // bit16~bit13: spare ,	 
 // bit12~bit0: VID	 
 CiBoolean cTagPcpDeiPresent ;	 
 UINT8 cTagPcpDei ; // 802.100000 Q Customer-VLAN tag PCP and DEI ,	 
 // bit8~bit5: spare	 
 // bit4~bit2: PCP	 
 // bit1: DEI	 
 CiBoolean sTagPcpDeiPresent ;	 
 UINT8 sTagPcpDei ; // 802.100000 Q Service-VLAN tag PCP and DEI ,	 
 // bit8~bit5: spare	 
 // bit4~bit2: PCP	 
 // bit1: DEI	 
 /* Added by Daniel for CQ00136993 , end */	 
 } CiPsTftFilter;

//ICAT EXPORTED ENUM 
 typedef enum CIPSTFTOPCODE_TAG 
 {	 
 CI_PS_TFT_OPCODE_SPARE = 0 ,	 
 CI_PS_TFT_OPCODE_CREATE_NEW ,	 
 CI_PS_TFT_OPCODE_DELETE_EXISTING ,	 
 CI_PS_TFT_OPCODE_ADD_PACKET_FILTERS ,	 
 CI_PS_TFT_OPCODE_REPLACE_PACKET_FILTERS ,	 
 CI_PS_TFT_OPCODE_DELETE_PACKET_FILTERS ,	 
 CI_PS_TFT_OPCODE_NO_TFT_OPERATION ,	 
 CI_PS_NUMBER_OF_TFT_OP_CODES	 
 } _CiPsTftOpCode;

typedef UINT8 CiPsTftOpCode ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsIndicatedPdpCtx_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 CiPsPdpType type ; /**< PDP type \sa CiPsPdpType */	 
 CiPsPdpBearType pdpBearType ; /**Primary PDP , or secondary PDP */	 
 UINT8 p_cid ; /**only valid , when secondary PDP*/	 
	 
 // CiBoolean addrPresent ; / **< TRUE: if present \sa CCI API Ref Manual * /	 
 // CiPsPdpAddr addr ; / **< PDP address string \sa CiPsPdpAddr_struct * /	 
 CiPsPdpIpAddr ipv4Addr ; /**if not vaiable , addrType = CI_PS_PDP_INVALID_ADDR*/	 
 CiPsPdpIpAddr ipv6Addr ; /**if not vaiable , addrType = CI_PS_PDP_INVALID_ADDR*/	 
 } CiPsIndicatedPdpCtx;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsQosCap_struct 
 {	 
 CiPsPdpType type ; /**< PDP type \sa CiPsPdpType */	 
 CiNumericRange precedenceCap ; /**< Precedence class [ 0 -4 ] \sa CCI API Ref Manual */	 
 CiNumericRange delayCap ; /**< Delay class [ 0 -3 ] \sa CCI API Ref Manual */	 
 CiNumericRange reliabilityCap ; /**< Reliability class [ 0 -5 ] \sa CCI API Ref Manual */	 
 CiNumericRange peakCap ; /**< Peak throughput [ 0 -9 ] \sa CCI API Ref Manual */	 
 CiNumericList meanCap ; /**< Mean throughput [ 0 -18 , 31 ] \sa CCI API Ref Manual */	 
 } CiPsQosCap;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsQosCaps_struct 
 {	 
 UINT8 size ; /**< Number of defined PDP contexts */	 
 CiPsQosCap caps [ 1 ] ; /**< QoS capabilities , optional if return code is not CIRC_PS_SUCCESS \sa CiPsQosCap_struc */	 
 } CiPsQosCaps;

typedef CiPsQosCap CiPs25GQosCap ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPs3GQosCap_struct 
 {	 
 CiPsPdpType type ; /**< PDP type \sa CiPsPdpType */	 
 CiNumericRange trafficClass ; /**< Traffic class [ 0 ..4 ] \sa CCI API Ref Manual */	 
 CiNumericRange deliveryOrder ; /**< Delivery order [ 0 ..2 ] \sa CCI API Ref Manual */	 
 CiNumericRange deliverErrSdu ; /**< Delivery of erroneous SDUs [ 0 ..3 ] \sa CCI API Ref Manual */	 
 CiNumericRange resBER ; /**< Residual BER [ 0 ..9 ] \sa CCI API Ref Manual */	 
 CiNumericRange errRatio ; /**< SDU error ratio [ 0 ..7 ] \sa CCI API Ref Manual */	 
 CiNumericRange thPriority ; /**< Traffic handling priority [ 0 ..3 ] \sa CCI API Ref Manual */	 
	 
 CiNumericRange transDelay ; /**< Transfer delay [ 0x00 ..0x3e ] \sa CCI API Ref Manual */	 
 CiNumericRange maxSduSize ; /**< Maximum SDU size [ 0x00 ..0x99 ] \sa CCI API Ref Manual */	 
 CiNumericRange maxULRate ; /**< Max bit rate , uplink [ 0x00 ..0xff ] \sa CCI API Ref Manual */	 
 CiNumericRange maxDLRate ; /**< Max bit rate , downlink [ 0x00 ..0xff ] \sa CCI API Ref Manual */	 
 CiNumericRange guaranteedULRate ; /**< Guaranteed bit rate , uplink [ 0x00 ..0xff ] \sa CCI API Ref Manual */	 
 CiNumericRange guaranteedDLRate ; /**< Guaranteed bit rate , downlink [ 0x00 ..0xff ] \sa CCI API Ref Manual */	 
 CiNumericRange SourceStatisticDescriptor ; /**< Specifies characteristics of the source of submitted SDUs [ 0x00 ..0xff ] \sa CCI API Ref Manual */	 
 } CiPs3GQosCap;

//ICAT EXPORTED STRUCT 
 typedef struct CiPs3GQosCaps_struct 
 {	 
 UINT8 size ; /**< Size */	 
 CiPs3GQosCap caps [ ( 8 + 7 ) ] ; /** 3 G QoS capability per defined PDP context \sa CiPs3GQosCap_struct */	 
 /*--3 / 5 / 2009 10 :26AM	 
 * Note: need to check max size of array	 
 * ------------*/	 
 } CiPs3GQosCaps;

//ICAT EXPORTED STRUCT 
 typedef struct CiPs4GQosProfile_struct 
 {	 
 UINT8 qci ; /**Qos Class Identifier */	 
 CiBoolean gbrMbrPresent ; /**indicate whether GBR & MBR presnt */	 
 UINT32 maxULRate ; /**<UL_MBR Max Bit Rate , Uplink , in kbps */	 
 UINT32 maxDLRate ; /**<DL_MBR Max Bit Rate , Downlink , in kbps ( MAX 256000 kbps ) */	 
 UINT32 guaranteedULRate ; /**<UL_GBR Guaranteed Bit Rate , Uplink , in kbps */	 
 UINT32 guaranteedDLRate ; /**<DL_GBR Guaranteed Bit Rate , Downlink , in kbps */	 
 CiBoolean ambrPresent ; /**indicate whether AMBR presnt for +CGEQOSRDP */	 
 UINT32 apnULAmbr ; /**<UL_AMBR , UL APN aggregate MBR , in kbps */	 
 UINT32 apnDLAmbr ; /**<DL_AMBR , DL APN aggregate MBR , in kbps */	 
 } CiPs4GQosProfile , 
 CiPs5GQosProfile;

//ICAT EXPORTED STRUCT 
 typedef struct CiPs4GQosCap_struct 
 {	 
 UINT8 qci ; /**Qos Class Identifier */	 
 CiNumericRangeBYTE maxULRate ; /**< Max Bit Rate , Uplink */	 
 CiNumericRangeBYTE maxDLRate ; /**< Max Bit Rate , Downlink */	 
 CiNumericRangeBYTE guaranteedULRate ; /**< Guaranteed Bit Rate , Uplink */	 
 CiNumericRangeBYTE guaranteedDLRate ; /**< Guaranteed Bit Rate , Downlink */	 
 } CiPs4GQosCap;

//ICAT EXPORTED STRUCT 
 typedef struct CiPs4GQosCaps_struct 
 {	 
 UINT8 size ; /**< Size. */	 
 /* # Start Contiguous Code Section # */	 
 CiPs4GQosCap caps [ ( 8 + 7 ) ] ; /** 4 G QoS capability per defined PDP context. \sa CiPs4GQosCap_struct */	 
	 
 /* # End Contiguous Code Section # */	 
 } CiPs4GQosCaps;

//ICAT EXPORTED ENUM 
 typedef enum CIPS_COUNTERREPORTTYPES 
 {	 
 CI_PS_COUNTER_REPORT_ONE_SHOT = 0 , /**< A single CI_PS_PRIM_COUNTER_IND indication is sent when the information is received from the protocol stack. */	 
 CI_PS_COUNTER_REPORT_PERIODIC , /**< Periodic CI_PS_PRIM_COUNTER_IND indications to be sent at intervals specified	 
 * by the interval parameter. The minimum value for the interval parameter is one second ; if it is set to zero , CCI uses a	 
 * one second interval. */	 
 CI_PS_COUNTER_REPORT_STOP , /**< Stop periodic report */	 
	 
 CI_PS_NUM_COUNTER_REPORT_TYPES	 
 } _CiPsCounterReportType;

typedef UINT8 CiPsCounterReportType ;
typedef UINT8 CiPsAttachStateCause ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetAttachStateReq_struct 
 {	 
 CiBoolean state ; /**< State of the PS attachment. TRUE: attach ; FALSE: detach. \sa CCI API Ref Manual */	 
 CiPsAttachStateCause cause ;	 
 } CiPsPrimSetAttachStateReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetAttachStateCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimSetAttachStateCnf;

typedef CiEmptyPrim CiPsPrimGetAttachStateReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetAttachStateCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiBoolean state ; /**< State of the PS attachment. TRUE: attached ; FALSE: detached. \sa CCI API Ref Manual */	 
 } CiPsPrimGetAttachStateCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDefinePdpCtxReq_struct 
 {	 
 CiPsPdpCtx pdpCtx ; /**< PDP context definition \sa CiPsPdpCtx_struct */	 
 } CiPsPrimDefinePdpCtxReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDefinePdpCtxCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimDefinePdpCtxCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDeletePdpCtxReq_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } CiPsPrimDeletePdpCtxReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDeletePdpCtxCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimDeletePdpCtxCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetPdpCtxReq_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier */	 
 } CiPsPrimGetPdpCtxReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetPdpCtxCnf_struct 
 {	 
	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiBoolean ctxPresent ; /**< TRUE: if present \sa CCI API Ref Manual */	 
 CiPsPdpCtxInfo ctx ; /**< PDP context information , optional if rc is not CIRC_PS_SUCCESS \sa CiPsPdpCtxInfo_struct */	 
 UINT8 pcoData [ 250 ] ; /**< Extended PCO to replace ctx.pdpCtx.pdParas.valStr */ /*Lilei , CQ00133813 , 20211104 */	 
 } CiPsPrimGetPdpCtxCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimRead4GPdpCtxDynParaReq_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier. */	 
 } CiPsPrimRead4GPdpCtxDynParaReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPdpCtxDynPara_struct 
 {	 
 UINT8 cid ;	 
 CiBoolean bidPresent ;	 
 UINT8 bid ; // if CID actived , bear ID must configured	 
 CiBoolean apnPresent ;	 
 CiString apn ; // 104 bytes	 
 CiPsPdpIpAddr ipv4Addr ;	 
 CiPsPdpIpAddr ipv6Addr ;	 
 UINT8 gwAddrNum ;	 
 UINT8 dnsAddrNum ;	 
 UINT8 pCscfAddrNum ;	 
 UINT8 reserved0 ;	 
 CiPsPdpIpAddr gwAddr [ 4 ] ;	 
 CiPsPdpIpAddr dnsAddr [ 4 ] ;	 
 CiPsPdpIpAddr pCscfAddr [ 4 ] ;	 
 UINT8 imCnSigFlag ; // 0 , 1	 
 UINT8 lipaInd ; // 0 , 1	 
	 
 CiPsSmFollowAct smFlwAct ;	 
 CiBoolean smCausePresent ; // ESM CAUSE from NW	 
 CiPsEsmCauseType smCause ; // ESM CAUSE from NW	 
	 
 CiBoolean ipv4MtuPresent ;	 
 UINT16 ipv4Mtu ;	 
 CiBoolean wlanOffloadPresent ;	 
 UINT8 wlanOffload ; /**WLAN_Offload : 0 ~3 not supported*/	 
 CiBoolean localAddrIndPresent ;	 
 UINT8 localAddrInd ; /**Local_Addr_ind:0~1*/	 
 CiBoolean nonIpMtuPresent ;	 
 UINT16 nonIpMtu ; /**Non-IP_MTU*/	 
 CiBoolean servingPlmnRateControlPresent ;	 
 UINT16 servingPlmnRateControlValue ; /**Serving_PLMN_rate_control_value*/	 
	 
 CiBoolean psiPresent ;	 
 UINT8 psi ;	 
 CiBoolean qfiPresent ;	 
 UINT8 qfi ;	 
 CiBoolean sscModePresent ;	 
 CiPsSscMode sscMode ;	 
 CiBoolean sNssaiPresent ;	 
 CiString sNssai ;	 
 CiBoolean accessTypePresent ;	 
 CiPsPrefAccessType accessType ;	 
 CiBoolean rqTimerPresent ;	 
 UINT32 rqTimer ;	 
 CiBoolean alwaysOnInd ;	 
 } CiPsPdpCtxDynPara;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimRead4GPdpCtxDynParaCnf_struct 
 {	 
	 
 CiPsRc rc ; // UINT16	 
 CiBoolean ctxPresent ;	 
 CiPsPdpCtxDynPara ctxDynPara ;	 
 } CiPsPrimRead4GPdpCtxDynParaCnf;

typedef CiEmptyPrim CiPsPrimGetPdpCtxCapsReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetPdpCtxCapsCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiPsPdpCtxCaps pdpCtxCaps ; /**< PDP context capabilities supported by the cellular subsystem \sa CiPsPdpCtxCaps_struct */	 
 } CiPsPrimGetPdpCtxCapsCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetPdpCtxActStateReq_struct 
 {	 
 CiBoolean state ; /**< State of the PS attachment. TRUE: activate ; FALSE: deactivate. \sa CCI API Ref Manual */	 
 CiBoolean doAll ; /**< Not supported*/	 
 UINT8 cid ; /**< PDP context identifier */	 
 CiPsL2P l2p ; /**< L2 protocol type \sa CiPsL2P */	 
 } CiPsPrimSetPdpCtxActStateReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetPdpCtxActStateCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
	 
 CiBoolean smCausePresent ; // if activated PDP , SM caused from NW , if deactivate PDP , this flag not valid	 
 CiPsEsmCauseType smCause ; // ESM CAUSE from NW	 
 CiPsSmFollowAct smFlwAct ; // SM following action , if deactivate a PDP , not valid	 
 } CiPsPrimSetPdpCtxActStateCnf;

typedef CiEmptyPrim CiPsPrimGetPdpCtxsActStateReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetPdpCtxsActStateCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 UINT8 num ; /**< Number of defined PDP contexts [ 0 -CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 CiPsPdpCtxActState lst [ ( 8 + 7 ) ] ; /**< Activation state for the defined PDP contexts \sa CiPsPdpCtxActState_struct */	 
 } CiPsPrimGetPdpCtxsActStateCnf;

typedef CiEmptyPrim CiPsPrimRead4GPdpCtxsActDynParaReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimRead4GPdpCtxsActDynParaCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 UINT8 num ; /**< Number of defined PDP contexts [ 0 -CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 UINT8 cid [ ( 8 + 7 ) ] ; /**< PDP context information , optional if rc is not CIRC_PS_SUCCESS. \sa CiPsPdpCtxInfo_struct */	 
 } CiPsPrimRead4GPdpCtxsActDynParaCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEnterDataStateReq_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier */	 
 CiPsL2P l2p ; /**< L2 protocol type \sa CiPsL2P */	 
 CiBoolean optimizedData ; /**< TRUE indicates that optimized ACI data plane is used \sa CCI API Ref Manual */	 
 } CiPsPrimEnterDataStateReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEnterDataStateCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiBoolean smCausePresent ; // ESM CAUSE from NW	 
 CiPsEsmCauseType smCause ; // ESM CAUSE from NW	 
 CiPsSmFollowAct smFlwAct ; // SM following action	 
 } CiPsPrimEnterDataStateCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimMtPdpCtxActModifyInd_struct 
 {	 
 CiPsPdpCtx pdpCtx ; /**< PDP context information \sa CiPsPdpCtx_struct */	 
 /* Modified by Daniel for CQ00141242 , begin */	 
	 
	 
	 
	 
 /*	 
 Bit 1 TFT changed	 
 Bit 2 Qos changed	 
 Bit 3 WLAN Offload changed	 
 Bit 4 Relocation of PDU session anchor requested	 
 Bit 5 PDP address or PDP type changed	 
 Bit 6 ATSSS parameters changed	 
 Bit 7 P-CSCF restoration requested	 
 */	 
 UINT16 change_reason ;	 
 /* Modified by Daniel for CQ00141242 , end */	 
 } CiPsPrimMtPdpCtxActModifyInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimMtPdpCtxActModifyRsp_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 UINT8 cid ; /**< PDP context identifier */	 
 CiBoolean accept ; /**< TRUE: accept ; FALSE: reject \sa CCI API Ref Manual */	 
 CiPsL2P l2p ; /**< L2 protocol type \sa CiPsL2P */	 
 } CiPsPrimMtPdpCtxActModifyRsp;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimMtPdpCtxActedInd_struct 
 {	 
 CiPsPdpCtx pdpCtx ; /**< PDP context information \sa CiPsPdpCtx_struct */	 
 CiBoolean isMEInitiated ; /** MO / MT ; ME / NW */	 
 CiPsMoPdpActReason pdpReason ; /** for CGEV*/	 
 UINT8 cid_other ; // only valided when pdpReason = " CI_PS_PDP_SINGLE_ONLY_ALLOWED_SEC_SUCC "	 
	 
 CiBoolean epsAttach ; /* indicate whether in EPS attach procedure */	 
 CiBoolean isImsDefault ; /* whether IMS default bearer */ /*Lilei , CQ00148256 , 20240122 */	 
 } CiPsPrimMtPdpCtxActedInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetGsmGprsClassReq_struct 
 {	 
 CiPsGsmGprsClass classType ; /**< Mobile class for GSM / GPRS \sa CiPsGsmGprsClass */	 
 } CiPsPrimSetGsmGprsClassReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetGsmGprsClassCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimSetGsmGprsClassCnf;

typedef CiEmptyPrim CiPsPrimGetGsmGprsClassReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetGsmGprsClassCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiPsGsmGprsClass classType ; /**< Mobile class for GSM / GPRS \sa CiPsGsmGprsClass */	 
 } CiPsPrimGetGsmGprsClassCnf;

typedef CiEmptyPrim CiPsPrimGetGsmGprsClassesReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetGsmGprsClassesCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiBitRange classes ; /**< Mobile class for GSM / GPRS \sa CCI API Ref Manual */	 
 } CiPsPrimGetGsmGprsClassesCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEnableNwRegIndReq_struct 
 {	 
 CiPsNwRegIndFlag flag ; /**< Configures network registration status reports \sa CiPsNwRegIndFlag */	 
 } CiPsPrimEnableNwRegIndReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEnableNwRegIndCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimEnableNwRegIndCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimNwRegInd_struct 
 {	 
 CiPsNwRegInfo nwRegInfo ; /**< Network registration information \sa CiPsNwRegInfo_struct */	 
 } CiPsPrimNwRegInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEnable4GNwRegIndReq_struct 
 {	 
 CiPs4GNwRegIndFlag flag ; /**< Configures nework registration status reports.\sa CiPsNwRegIndFlag */	 
 } CiPsPrimEnable4GNwRegIndReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEnable4GNwRegIndCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimEnable4GNwRegIndCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrim4GNwRegInd_struct 
 {	 
 CiPs4GNwRegInfo nwRegInfo ; /**< Network registration information \sa CiPsPrim4GNwRegInd_struct */	 
 } CiPsPrim4GNwRegInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetQosReq_struct 
 {	 
 CiBoolean isMin ; /**< Indicates if the profile requested is minimum or required QoS profile \sa CCI API Ref Manual */	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 CiPsQosProfile qosProf ; /**< QoS profile data \sa CiPsQosProfile_struct */	 
 } CiPsPrimSetQosReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetQosCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimSetQosCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDelQosReq_struct 
 {	 
 CiBoolean isMin ; /**< Indicates if the profile requested is minimum or required QoS profile \sa CCI API Ref Manual */	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } CiPsPrimDelQosReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDelQosCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimDelQosCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetQosReq_struct 
 {	 
 CiBoolean isMin ; /**< Indicates if the profile requested is minimum or required QoS profile \sa CCI API Ref Manual */	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } CiPsPrimGetQosReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetQosCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiBoolean qosProfPresent ; /**< Not in use \sa CCI API Ref Manual */	 
 CiPsQosProfile qosProf ; /**< QoS profile , optional if rc is not CIRC_PS_SUCCESS \sa CiPsQosProfile_struct */	 
 } CiPsPrimGetQosCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEnablePoweronAutoAttachReq_struct 
 {	 
 CiBoolean enableAutoAttach ; /**< AutoAttach configuration. TRUE: Attach to PS domain will be automatically initiated on power up	 
 * FALSE: Attach to PS domain will be initiated by the user. \sa CCI API Ref Manual */	 
 } CiPsPrimEnablePoweronAutoAttachReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEnablePoweronAutoAttachCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc. */	 
 } CiPsPrimEnablePoweronAutoAttachCnf;

typedef CiEmptyPrim CiPsPrimGetPoweronAutoAttachStatusReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetPoweronAutoAttachStatusCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc. */	 
 CiBoolean AutoAttachStatus ; /**< AutoAttach configuration. TRUE: Attach to PS domain will be automatically initiated on power up	 
 * FALSE: Attach to PS domain will be initiated by the user. \sa CCI API Ref Manual */	 
 } CiPsPrimGetPoweronAutoAttachStatusCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimMtPdpCtxRejectedInd_struct 
 {	 
 UINT8 cause ;	 
 CiPsIndicatedPdpCtx indedPdpCtx ;	 
 } CiPsPrimMtPdpCtxRejectedInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimPdpCtxDeactedInd_struct 
 {	 
 UINT8 cause ; /**< Cause for PDP context deactivation. SM cause is defined in 3 GPP TS 24.008000 section 10.500000 .6.6. */	 
 CiBoolean isMEInitiated ; /**< TRUE if ME requested PDP context deactivation or PPP connection failure detected in comm . \sa CCI API Ref Manual */	 
 UINT8 res1U8 [ 2 ] ; /**< ( padding ) */	 
 CiPsIndicatedPdpCtx indedPdpCtx ; /**< Indicated PDP context \sa CiPsPdpCtxInd_struct */	 
 } CiPsPrimPdpCtxDeactedInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimPdpCtxReactedInd_struct 
 {	 
 CiPsIndicatedPdpCtx indedPdpCtx ;	 
 } CiPsPrimPdpCtxReactedInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDetachedInd_struct 
 {	 
 CiBoolean isMeDetach ; /**< Indicates if detach is initiated by ME or network. TRUE indicates by ME ; FALSE indicates by network. \sa CCI API Ref Manual */	 
 } CiPsPrimDetachedInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGprsClassChangedInd_struct 
 {	 
 CiPsGsmGprsClass classType ; /**< Mobile class for GSM / GPRS \sa CiPsGsmGprsClass */	 
 CiBoolean IsMEClassChanged ; /**< TRUE if network mode changed. \sa CCI API Ref Manual */	 
 } CiPsPrimGprsClassChangedInd;

typedef CiEmptyPrim CiPsPrimGetDefinedCidListReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetDefinedCidListCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 UINT8 size ; /**< Size of the CID list [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM ] */	 
 UINT8 cidLst [ ( 8 + 7 ) ] ; /**< CID list */	 
 } CiPsPrimGetDefinedCidListCnf;

typedef CiEmptyPrim CiPsPrimGetNwRegStatusReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetNwRegStatusCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiPsNwRegIndFlag regIndflag ; /** report level , n = 0 , 1 , 2 , 3 **/	 
 CiPsNwRegInfo nwRegInfo ; /**< Network registration information \sa CiPsNwRegInfo_struct */	 
 } CiPsPrimGetNwRegStatusCnf;

typedef CiEmptyPrim CiPsPrimGet4GNwRegStatusReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet4GNwRegStatusCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 CiPs4GNwRegIndFlag regIndflag ; /** report level , n = 0 , 1 , 2 , 3 **/	 
 CiPs4GNwRegInfo nwRegInfo ; /**< Network registration information. \sa CiPs4GNwRegInfo_struct */	 
 } CiPsPrimGet4GNwRegStatusCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetQosCapsReq_struct 
 {	 
 CiBoolean isMin ; /**< Not in use */	 
 } CiPsPrimGetQosCapsReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetQosCapsCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiBoolean qosCapsPresent ; /**< TRUE: if present */	 
 CiPsQosCaps qosCaps ; /**< QoS capabilities , optional if rc is not CIRC_PS_SUCCESS \sa CiPsQosCaps_struct */	 
	 
 } CiPsPrimGetQosCapsCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEnableEventsReportingReq_struct 
 {	 
 CiBoolean enable ; /**< TRUE: enable events reporting ; FALSE: disable events reporting ; default: FALSE \sa CCI API Ref Manual */	 
 } CiPsPrimEnableEventsReportingReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEnableEventsReportingCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimEnableEventsReportingCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet3GQosReq_struct 
 {	 
 CiPs3GQosType qosType ; /**< Specifies 3 G minimum , required or negotiated QoS profile \sa CiPs3GQosType */ // REQ / MIN / NEG	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } CiPsPrimGet3GQosReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet3GQosCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiBoolean qosProfPresent ; /**< If TRUE , qosProf contains the 3 G QoS profile ; if FALSE , qosProf does not contain useful information \sa CCI API Ref Manual */	 
 CiPs3GQosProfile qosProf ; /**< 3 G QoS profile , optional if rc is not CIRC_PS_SUCCESS \sa CiPs3GQosProfile_struct */	 
 } CiPsPrimGet3GQosCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSet3GQosReq_struct 
 {	 
 CiPs3GQosType qosType ; /**< Specifies 3 G minimum or required QoS profile \sa CiPs3GQosType */ // REQ / MIN ( NEG is not valid here )	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 CiPs3GQosProfile qosProf ; /**< 3 G QoS profile \sa CiPs3GQosProfile_struct */	 
 } CiPsPrimSet3GQosReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSet3GQosCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimSet3GQosCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDel3GQosReq_struct 
 {	 
 CiPs3GQosType qosType ; /**< Specifies 3 G minimum or required QoS profile \sa CiPs3GQosType */ // REQ / MIN ( NEG is not valid here )	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } CiPsPrimDel3GQosReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDel3GQosCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimDel3GQosCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet3GQosCapsReq_struct 
 {	 
 CiPs3GQosType qosType ; /**< Not in use */ // REQ / MIN / NEG	 
 } CiPsPrimGet3GQosCapsReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet3GQosCapsCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiBoolean qosCapsPresent ; /**< If TRUE , qosCaps contains the 3 G QoS capabilities ; if FALSE , qosCaps does not contain useful information. \sa CCI API Ref Manual*/	 
 CiPs3GQosCaps qosCaps ; /**< 3 G QoS capabilities ; optional if rc is not CIRC_PS_SUCCESS \sa CiPs3GQosCaps_struct */	 
 } CiPsPrimGet3GQosCapsCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet4GQosReq_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } CiPsPrimGet4GQosReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet4GQosCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 CiBoolean qosProfPresent ; /**< If TRUE , qosProf will have the 3 G QoS profile ; If FALSE , qosProf doesn ' t contain useful information ; \sa CCI API Ref Manual */	 
 CiPs4GQosProfile qosProfile ; /**< 4 G QoS profile , optional if rc is not CIRC_PS_SUCCESS ; \sa CiPs3GQosProfile_struct */	 
 } CiPsPrimGet4GQosCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSet4GQosReq_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 CiPs4GQosProfile qosProfile ; /**< 4 G QoS profile , optional if rc is not CIRC_PS_SUCCESS ; \sa CiPs3GQosProfile_struct */	 
 } CiPsPrimSet4GQosReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSet5GQosReq_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 CiPs5GQosProfile qosProfile ; /**< 5 G QoS profile , optional if rc is not CIRC_PS_SUCCESS ; \sa CiPs3GQosProfile_struct */	 
 } CiPsPrimSet5GQosReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSet4GQosCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } 
 CiPsPrimSet4GQosCnf , CiPsPrimSet5GQosCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet5GQosReq_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } CiPsPrimGet5GQosReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet5GQosCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 CiBoolean qosProfPresent ; /**< If TRUE , qosProf will have the 5 G QoS profile ; If FALSE , qosProf doesn ' t contain useful information ; \sa CCI API Ref Manual */	 
 CiPs5GQosProfile qosProfile ; /**< 5 G QoS profile , optional if rc is not CIRC_PS_SUCCESS ; \sa CiPs3GQosProfile_struct */	 
 } CiPsPrimGet5GQosCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDel4GQosReq_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } CiPsPrimDel4GQosReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDel4GQosCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimDel4GQosCnf;

typedef CiEmptyPrim CiPsPrimGet4GQosCapsReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet4GQosCapsCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 CiBoolean qosCapsPresent ; /**< If TRUE , qosCaps will have the 3 G QoS capabilities ; If FALSE , qosCaps doesn ' t contain useful information ; \sa CCI API Ref Manual*/	 
 CiPs4GQosCaps qosCaps ; /**< 4 G QoS profile , optional if rc is not CIRC_PS_SUCCESS ; \sa CiPs3GQosProfile_struct */	 
 } CiPsPrimGet4GQosCapsCnf;

typedef CiEmptyPrim CiPsPrimGet4GModeReq ;
//ICAT EXPORTED STRUCT 
 
 typedef struct CiPsPrimGet4GModeCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 UINT8 cipslteOperateMode ;	 
 } CiPsPrimGet4GModeCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSet4GModeReq_struct 
 {	 
 UINT8 cipslteOperateMode ;	 
 } CiPsPrimSet4GModeReq;

//ICAT EXPORTED STRUCT 
 
 typedef struct CiPsPrimSet4GModeCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimSet4GModeCnf;

typedef CiEmptyPrim CiPsPrimGet4GModeCapsReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsLteOperateModes_struct 
 {	 
 UINT8 ciPsLteOperateMode [ 4 ] ; /** 4 G QoS capability per defined PDP context. \sa CiPs4GQosCap_struct */	 
 } CiPsLteOperateModes;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet4GModeCapsCnf_struct 
 {	 
 CiPsLteOperateModes ciPsLteOperateModes ;	 
 } CiPsPrimGet4GModeCapsCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetPdpAddrReq_struct 
 {	 
 UINT8 num ;	 
 UINT8 cid [ ( 8 + 7 ) ] ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } CiPsPrimGetPdpAddrReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetPdpAddrCnf_struct 
 {	 
 CiPsRc rc ;	 
 UINT8 num ;	 
 UINT8 cid [ ( 8 + 7 ) ] ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 CiPsPdpAddr pdpAddress [ ( 8 + 7 ) ] ;	 
 } CiPsPrimGetPdpAddrCnf;

typedef CiEmptyPrim CiPsPrimGetPdpAddrListReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetPdpAddrListCnf_struct 
 {	 
 CiPsRc rc ;	 
 UINT8 nums ;	 
 UINT8 cid [ ( 8 + 7 ) ] ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } CiPsPrimGetPdpAddrListCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDefineSecPdpCtxReq_struct 
 {	 
 CiPsSecPdpCtx secPdpCtx ; /**< Secondary PDP context \sa CiPsSecPdpCtx_struct */	 
 } CiPsPrimDefineSecPdpCtxReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDefineSecPdpCtxCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimDefineSecPdpCtxCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDeleteSecPdpCtxReq_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } CiPsPrimDeleteSecPdpCtxReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDeleteSecPdpCtxCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimDeleteSecPdpCtxCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetSecPdpCtxReq_struct 
 {	 
 UINT8 cid ; /**< Secondary PDP context ID [ [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] ] */	 
 } 
 CiPsPrimGetSecPdpCtxReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetSecPdpCtxCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiBoolean ctxPresent ; /**< Set to FALSE , if the result code parameter indicates an error \sa CCI API Ref Manual */	 
 CiPsSecPdpCtxInfo ctx ; /**< Secondary PDP context information \sa CiPsSecPdpCtxInfo_struct */	 
 } 
 CiPsPrimGetSecPdpCtxCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimRead4GSecPdpCtxDynParaReq_struct 
 {	 
 UINT8 cid ; /**< Secondary PDP context ID [ [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] ] */	 
 } 
 CiPsPrimRead4GSecPdpCtxDynParaReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimRead4GSecPdpCtxDynParaCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 UINT8 num ;	 
 CiPsID psId [ ( 8 + 7 ) ] ;	 
 CiPsImCnSignallingFlagIndType imCnSigFlag [ ( 8 + 7 ) ] ;	 
 } 
 CiPsPrimRead4GSecPdpCtxDynParaCnf;

typedef CiEmptyPrim CiPsPrimRead4GSecPdpCtxsActDynParaReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimRead4GSecPdpCtxsActDynParaCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 UINT8 num ;	 
 UINT8 cid [ ( 8 + 7 ) ] ; /**< Secondary PDP Context Information. \sa CiPsSecPdpCtxInfo_struct */	 
 } 
 CiPsPrimRead4GSecPdpCtxsActDynParaCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDefineTftFilterReq_struct 
 {	 
 UINT8 cid ; /**< Secondary PDP context ID [ [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] ] */	 
	 
 CiBoolean qriPresent ;	 
 UINT8 qri ;	 
	 
 UINT8 trafficSegregation ;	 
	 
 CiPsTftFilter filter ; /**< TFT filter parameters \sa CiPsTftFilter_struct */	 
 } 
 CiPsPrimDefineTftFilterReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDefineTftFilterCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } 
 CiPsPrimDefineTftFilterCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDeleteTftReq_struct 
 {	 
 UINT8 cid ; /**< Context ID [ [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] ] */	 
 } 
 CiPsPrimDeleteTftReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDeleteTftCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } 
 CiPsPrimDeleteTftCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetTftReq_struct 
 {	 
 UINT8 cid ; /**< Context ID [ [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] ] */	 
 } 
 CiPsPrimGetTftReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetTftCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } 
 CiPsPrimGetTftCnf;

typedef UINT8 CiPsTftQueryType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimTftReportInd_struct 
 {	 
 /* Added by Daniel for CQ00141618 , begin */	 
 CiPsTftQueryType type ;	 
 /* Added by Daniel for CQ00141618 , end */	 
	 
 UINT8 cid ;	 
	 
 CiBoolean qriPresent ;	 
 UINT8 qri ;	 
	 
 CiBoolean dqr ; // default QoS rule indication	 
	 
 CiPsTftOpCode opCode ; /** Only valid when get TFT of a specified CID */	 
	 
 UINT8 rulePrecedence ;	 
	 
 UINT8 qfi ;	 
 UINT8 sessionId ;	 
	 
 UINT8 trafficSegregation ;	 
	 
 UINT8 numFilters ; /**< Number of packet filters */	 
 CiPsTftFilter filters [ 8 ] ; /**< Not in use */	 
 } 
 CiPsPrimTftReportInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimReadTrafficFlowTempDynParaReq_struct 
 {	 
 UINT8 cid ; /**< PDP Context ID , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } 
 CiPsPrimReadTrafficFlowTempDynParaReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimReadTrafficFlowTempDynParaCnf_struct 
 {	 
 CiPsRc rc ;	 
 } 
 CiPsPrimReadTrafficFlowTempDynParaCnf;

typedef CiEmptyPrim CiPsPrimReadTrafficFlowTempDynParaCapsReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimReadTrafficFlowTempDynParaCapsCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 UINT8 num ;	 
 UINT8 cid [ ( 8 + 7 ) ] ; /**< PDP context ID list , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } 
 CiPsPrimReadTrafficFlowTempDynParaCapsCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimModifyPdpCtxReq_struct 
 {	 
 CiBoolean doAll ; /**< Not supported */	 
 UINT8 cid ; /**< PDP context identifier */	 
 } 
 CiPsPrimModifyPdpCtxReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimModifyPdpCtxCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } 
 CiPsPrimModifyPdpCtxCnf;

typedef CiEmptyPrim CiPsPrimGetActiveCidListReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetActiveCidListCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 UINT8 size ; /**< Size of the CID list [ 0 ..CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM ] */	 
 UINT8 cidLst [ ( 8 + 7 ) ] ; /**< CID list */	 
 } CiPsPrimGetActiveCidListCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimReportCounterReq_struct 
 {	 
 CiPsCounterReportType type ; /**< Report type \sa CiPsCounterReportType */	 
 UINT16 interval ; /**< Report interval ( seconds ) , minimum report interval is 1 second. Required for periodic report configuration. */	 
 } CiPsPrimReportCounterReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimReportCounterCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimReportCounterCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimResetCounterReq_struct 
 {	 
 /*-----------------6 / 7 / 2009 11 :53AM-----------------	 
 * doAll parameter is a fix for SCR 1980451 -> 1818954 #9.	 
 * --------------------------------------------------*/	 
 CiBoolean doAll ; /**< Indicates if all counters should be reset ; TRUE: reset all counters ; FALSE: reset the counter for a requested context ID \sa CCI API Ref Manual*/	 
 UINT8 cid ; /**< Context ID , required if doAll == FALSE */	 
 } CiPsPrimResetCounterReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimResetCounterCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimResetCounterCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimCounterInd_struct 
 {	 
 UINT8 cid ; /**< PDP context ID , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 UINT32 totalULBytes ; /**< Total bytes sent on uplink ( uncompressed ) */	 
 UINT32 totalDLBytes ; /**< Total bytes received on downlink ( uncompressed ) */	 
 } CiPsPrimCounterInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSendDataReq_struct 
 {	 
 UINT16 pcktsize ; /* Packet size range: 0 - 10000 , default 1472 */	 
 UINT16 pcktcount ; /* Number of packets to send: 1 - 20 , default 1 */	 
 UINT8 nsapi ; /* PDP context ID */	 
 UINT8 PAD1 ;	 
 UINT8 PAD2 ;	 
 UINT8 PAD3 ;	 
 } CiPsPrimSendDataReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSendDataCnf_struct 
 {	 
 CiPsRc rc ;	 
 } CiPsPrimSendDataCnf;

//ICAT EXPORTED ENUM 
 typedef enum CiAbgpRequestStatusTag 
 {	 
 CI_ABGP_APN_OK , /**< OK */	 
 CI_ABGP_APN_NRAM_ERROR , /**< APN NRAM error */	 
 CI_ABGP_APN_RECORD_NOT_FOUND , /**< APN record not found */	 
	 
 CI_ABGP_ALLIGN = 0xFFFF	 
	 
 } _CiPsSimResult;

typedef UINT16 CiPsSimResult ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetAclReq_struct 
 {	 
 CiBoolean simAclPresent ;	 
 CiBoolean simAclEnable ; /**< TRUE: enable ACL service ; FALSE: disable ACL service \sa CCI API Ref Manual */	 
 CiBoolean psAclPresent ;	 
 CiBoolean psAclEnable ;	 
 } CiPsPrimSetAclReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetAclCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiPsSimResult SimCause ; /**< SIM result code \sa CiPsSimResult */	 
 CiBoolean simAclEnable ; /**< Service status ; TRUE: enabled , FALSE: disabled \sa CCI API Ref Manual*/	 
 CiBoolean psAclEnable ;	 
 } CiPsPrimSetAclCnf;

typedef CiEmptyPrim CiPsPrimGetAclSizeReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetAclSizeCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiPsSimResult SimCause ; /**< SIM result code \sa CiPsSimResult */	 
 UINT8 totalNumApns ; /**< Number of APNs currently held in SIM file EF_ACL */	 
 } CiPsPrimGetAclSizeCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimReadAclEntryReq_struct 
 {	 
 UINT8 Index ; /**< Index into ACL list */	 
 } CiPsPrimReadAclEntryReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimReadAclEntryReqCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiPsSimResult SimCause ; /**< SIM result code \sa CiPsSimResult */	 
 CiLongAdrInfo apn ; /**< Requested APN in string format \sa CCI API Ref Manual*/	 
 } CiPsPrimReadAclEntryCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEditAclEntryReq_struct 
 {	 
 CiEditCmdType EditCommand ; /**< Edit command ( add , delete , or replace ) \sa CCI API Ref Manual*/	 
 UINT8 position ; /**< Index into ACL list */	 
 /*To add the " Network provided APN " to the APN Control List , the length of the APN should be set to 0 */	 
 CiLongAdrInfo apn ; /**< APN in string format ; required for add or replace commands \sa CCI API Ref Manual*/	 
 } CiPsPrimEditAclEntryReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEditAclEntryCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiPsSimResult SimCause ; /**< SIM result code \sa CiPsSimResult */	 
 UINT8 position ; /**< Index in ACL list */	 
 } CiPsPrimEditAclEntryCnf;

//ICAT EXPORTED ENUM 
 typedef enum CIPSAUTHENTICATIONTYPE_TAG 
 {	 
 CI_PS_AUTHENTICATION_TYPE_NONE , /**< No authentication protocol */	 
 CI_PS_AUTHENTICATION_TYPE_PAP , /**< Password authentication protocol */	 
 CI_PS_AUTHENTICATION_TYPE_CHAP , /**< Challenge-Handshake authentication protocol */	 
 CI_PS_AUTHENTICATION_TYPE_PAP_CHAP , /**< PAP preferred , CHAP as secondary */ /*Lilei , CQ00115591 , 20190724 */	 
 CI_PS_AUTHENTICATION_TYPE_CHAP_PAP , /**< CHAP preferred , PAP as secondary */ /*Lilei , CQ00115591 , 20191021 */	 
 CI_PS_AUTHENTICATION_TYPE_PPP_CHAP , /**< Challenge-Handshake authentication protocol for PPP */ /*Lilei , CQ00111775 , 20180814 */	 
	 
 CI_PS_AUTHENTICATION_TYPE_NUM	 
	 
 } _CiPsAuthenticationType;

typedef UINT8 CiPsAuthenticationType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimAuthenticateReq_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier */	 
 CiPsAuthenticationType AuthenticationType ; /**< Authentication type. \sa CiPsAuthenticationType */	 
 /* !!!!!!!!!!!!!!!!!!!	 
 * When RIL completes the transition to contiguous memory , all CCI_xx_CONTIGUOUS	 
 * & CCI_APP_NONCONTIGUOUS flags must be removed.	 
 * ONLY the code BETWEEN the following 2 comment lines will REMAIN:	 
 * # Start Contiguous Code Section # and # End Contiguous Code Section #	 
 * All other code OUTSIDE these comments must be REMOVED - ( The backwards compatible code )	 
 */	 
 /* # Start Contiguous Code Section # */	 
 CiStringExt UserName ; /**< UserName octets. \sa CCI API Ref Manual */	 
 CiStringExt Password ; /**< Password octets. \sa CCI API Ref Manual */	 
 /* # End Contiguous Code Section # */	 
	 
 } CiPsPrimAuthenticateReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimAuthenticateCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimAuthenticateCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetDefaultPdpAuthenticateReq_struct 
 {	 
 UINT8 modeType ; // 0 - not save to NVM , 1 - save to NUM ;	 
 CiPsAuthenticationType authenticationType ; /**< Authentication type. \sa CiPsAuthenticationType */	 
 CiBoolean authInfoPresent ;	 
 CiStringExt userName ; /**< UserName octets. \sa CCI API Ref Manual */	 
 CiStringExt password ; /**< Password octets. \sa CCI API Ref Manual */	 
 } CiPsPrimSetDefaultPdpAuthenticateReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetDefaultPdpAuthenticateCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimSetDefaultPdpAuthenticateCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetDefaultPdpAuthenticateReq_Tag 
 {	 
 UINT8 modeType ; // 0 - current used , 1 - from NVM	 
 } CiPsPrimGetDefaultPdpAuthenticateReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetDefaultPdpAuthenticateCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 CiPsAuthenticationType authenticationType ; /**< Authentication type. \sa CiPsAuthenticationType */	 
 CiStringExt userName ; /**< UserName octets. \sa CCI API Ref Manual */	 
 CiStringExt password ; /**< Password octets. \sa CCI API Ref Manual */	 
 } CiPsPrimGetDefaultPdpAuthenticateCnf;

typedef CiEmptyPrim CiPsPrimFastDormantReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimFastDormantCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimFastDormantCnf;

typedef CiEmptyPrim CiPsPrimGetCurrentJobReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetCurrentJobCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 CiPrimitiveID primId ; /**< Primitive ID. \sa CiPrimitiveID */	 
 } CiPsPrimGetCurrentJobCnf;

//ICAT EXPORTED ENUM 
 typedef enum CIPS_FDY_OPTION 
 {	 
 CIPS_FDY_DISABLE = 0 , /**< Disable PS power consuming control */	 
 CIPS_FDY_ENABLE , /**< Enable PS power consuming control */	 
	 
 /* This one must always be last in the list! */	 
 CIPS_NUM_FDY_OPTIONS /**< Number of options defined */	 
 } _CiPsFDYOpt;

typedef UINT8 CiPsFDYOpt ;
//ICAT EXPORTED STRUCT 
 /** <paramref name= " CI_PS_PRIM_SET_FAST_DORMANCY_CONFIG_REQ " > */ 
 typedef struct CiPsPrimSetFastDormancyConfigReq_struct 
 {	 
 /* add by xwzhou for CQ55965 on 3032014 , begin */	 
 // CiPsFDYOpt type ; / **< Type. \sa CiPsFDYOpt * /	 
 // UINT16 interval ; / **< Trigger Interval ( seconds ) , the default value is 3 * /	 
 INT16 mode ; /**< 0 : disable fast dormancy timer ; 1 : enable fast dormancy timer*/	 
 UINT32 lcdOnTimerMsLength ; /**< ( unit:ms ) , if timer length=0 , disable FD*/	 
 UINT32 lcdOffTimerMsLength ; /**< ( unit:ms ) , if timer length=0 , disable FD*/	 
 UINT32 rel8LcdOnTimerMsLength ; /**< ( unit:ms ) , if timer length=0 , disable FD*/	 
 UINT32 rel8LcdOffTimerMsLength ; /**< ( unit:ms ) , if timer length=0 , disable FD*/	 
 /* add by xwzhou for CQ55965 on 3032014 , end */	 
 } CiPsPrimSetFastDormancyConfigReq;

//ICAT EXPORTED STRUCT 
 /** <paramref name= " CI_PS_PRIM_SET_FAST_DORMANCY_CONFIG_CNF " > */ 
 typedef struct CiPsPrimSetFastDormancyConfigCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimSetFastDormancyConfigCnf;

typedef UINT8 CiPsPdpTriggerType ;
//ICAT EXPORTED STRUCT 
 /** <paramref name= " CI_PS_PRIM_PDP_ACTIVATION_REJECT_CAUSE_IND " > */ 
 typedef struct CiPsPrimPdpActivationRejectCauseInd_struct 
 {	 
 CiPsPdpTriggerType pdpType ;	 
 CiBoolean cidPresent ;	 
 UINT8 cid ;	 
 CiBoolean smCausePresent ;	 
 CiPsEsmCauseType smCause ;	 
 CiPsSmFollowAct smflwAction ;	 
 } CiPsPrimPdpActivationRejectCauseInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimRead4GQosDynParaReq_struct 
 {	 
 UINT8 cid ; /**< PDP Context ID , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } CiPsPrimRead4GQosDynParaReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimRead4GQosDynParaCnf_struct 
 {	 
 CiPsRc rc ;	 
 UINT8 num ;	 
 CiPs4GQosProfile qosProfile [ ( 8 + 7 ) ] ; /**< 4 G QoS profile , optional if rc is not CIRC_PS_SUCCESS ; \sa CiPs3GQosProfile_struct */	 
 } CiPsPrimRead4GQosDynParaCnf;

typedef CiEmptyPrim CiPsPrimRead4GQosDynParaCapsReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimRead4GQosDynParaCapsCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 UINT8 num ;	 
 UINT8 cid [ ( 8 + 7 ) ] ; /**< PDP context ID list , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } CiPsPrimRead4GQosDynParaCapsCnf;

typedef CiEmptyPrim CiPsPrimGet4GEventRepReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet4GEventRepCnf_struct 
 {	 
 CiPsRc rc ;	 
 UINT16 mode ; /**<	 
 *0 buffer unsolicited result codes in the MT ; if MT result code buffer is full , the oldest ones can be discarded. No codes are forwarded to the TE.	 
 *1 discard unsolicited result codes when MT TE link is reserved ( e.g. in on line data mode ) ; otherwise forward them directly to the TE	 
 *2 buffer unsolicited result codes in the MT when MT TE link is reserved ( e.g. in on line data mode ) and flush them to the TE when MT TE link becomes available ; otherwise forward them directly to the TE	 
 */	 
 UINT16 bfr ; /**<	 
 *0 MT buffer of unsolicited result codes defined within this command is cleared when <mode> 1 or 2 is entered	 
 *1 MT buffer of unsolicited result codes defined within this command is flushed to the TE when <mode> 1 or 2 is entered ( OK response shall be given before flushing the codes )	 
 */	 
 } CiPsPrimGet4GEventRepCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSet4GEventRepReq_struct 
 {	 
 UINT16 mode ; /**<	 
 *0 buffer unsolicited result codes in the MT ; if MT result code buffer is full , the oldest ones can be discarded. No codes are forwarded to the TE.	 
 *1 discard unsolicited result codes when MT TE link is reserved ( e.g. in on line data mode ) ; otherwise forward them directly to the TE	 
 *2 buffer unsolicited result codes in the MT when MT TE link is reserved ( e.g. in on line data mode ) and flush them to the TE when MT TE link becomes available ; otherwise forward them directly to the TE	 
 */	 
 UINT16 bfr ; /**<	 
 *0 MT buffer of unsolicited result codes defined within this command is cleared when <mode> 1 or 2 is entered	 
 *1 MT buffer of unsolicited result codes defined within this command is flushed to the TE when <mode> 1 or 2 is entered ( OK response shall be given before flushing the codes )	 
 */	 
 } CiPsPrimSet4GEventRepReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSet4GEventRepCnf_struct 
 {	 
 CiPsRc rc ;	 
	 
 } CiPsPrimSet4GEventRepCnf;

typedef CiEmptyPrim CiPsPrimGet4GEventRepCapsReq ;
//ICAT EXPORTED STRUCT 
 typedef struct SacPsEventReportCaps_struct 
 {	 
 SacPsEventReportMode mode_min ;	 
 SacPsEventReportMode mode_max ;	 
 SacPsEventReportBufferMode buffer_min ;	 
 SacPsEventReportBufferMode buffer_max ;	 
 } 
 SacPsEventReportCaps;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet4GEventRepCapsCnf_struct 
 {	 
 CiPsRc rc ;	 
 SacPsEventReportCaps reportCaps ;	 
 } CiPsPrimGet4GEventRepCapsCnf;

//ICAT EXPORTED ENUM 
 typedef enum CIPS_VOICE_CALL_MODE_TAG 
 {	 
 CIPS_CS_ONLY = 0 ,	 
 CIPS_VOIP_ONLY ,	 
 CIPS_CS_PREFERRED ,	 
 CIPS_VOIP_PREFERRED ,	 
 CIPS_VOICE_CALL_MODE_NUM	 
 } _CiPsVoiceCallMode;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet4GVoiceCallModeReq_struct 
 {	 
 UINT8 cid ; /**< PDP Context ID , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 } CiPsPrimGet4GVoiceCallModeReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet4GVoiceCallModeCnf_struct 
 {	 
 CiPsRc rc ;	 
 UINT16 mode ; /* 0 CS_ONLY	 
 * 1 VOIP_ONLY	 
 * 2 CS_PREFERRED	 
 * 3 VOIP_PREFERRED	 
 */	 
 } CiPsPrimGet4GVoiceCallModeCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSet4GVoiceCallModeReq_struct 
 {	 
 UINT8 cid ; /**< PDP Context ID , [ 0 - CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM-1 ] */	 
 UINT16 mode ; /* 0 CS_ONLY	 
 * 1 VOIP_ONLY	 
 * 2 CS_PREFERRED	 
 * 3 VOIP_PREFERRED	 
 */	 
 } CiPsPrimSet4GVoiceCallModeReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSet4GVoiceCallModeCnf_struct 
 {	 
 CiPsRc rc ;	 
 } CiPsPrimSet4GVoiceCallModeCnf;

typedef CiEmptyPrim CiPsPrimGet4GVoiceCallModeCapsReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet4GVoiceCallModeCapsCnf_struct 
 {	 
 CiPsRc rc ;	 
 UINT16 modebitmap ; /*bit 0 CS_ONLY , will set 1	 
 *bit 1 VOIP_ONLY	 
 *bit 2 CS_PREFERRED	 
 *bit 3 VOIP_PREFERRED	 
 */	 
 } CiPsPrimGet4GVoiceCallModeCapsCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDataCompReportingReq_struct 
 {	 
 UINT8 report ; /**< 0 - Disable reporting ;	 
 1 - Enable reporting ;	 
 2 - Get current setting. \sa CiPsRc. */	 
 } CiPsPrimDataCompReportingReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDataCompReportingCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc. */	 
 CiBoolean dcomp_report_enabled ; /**< FALSE - report is enabled	 
 TRUE - report is disabled */	 
 } CiPsPrimDataCompReportingCnf;

//ICAT EXPORTED ENUM 
 typedef enum CIPS_DCOMP_STATUS_TAG 
 {	 
 CIPS_DCOMP_NONE , /**< Data compression is not in use */	 
 CIPS_DCOMP_BOTH , /**< V42B ITU-T Rec. V.42 bis is in use in	 
 both directions */	 
 CIPS_DCOMP_RX , /**< V42B RD ITU-T Rec. V.42 bis is in use in receive direction only */	 
 CIPS_DCOMP_TX , /**< V42B TD ITU-T Rec. V.42 bis is in use in transmit direction only */	 
 CIPS_DCOMP_TYPE_NUM ,	 
 } _CiPsDcompStatus;

typedef UINT8 CiPsDcompStatus ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDataCompInd_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier. */	 
 CiPsDcompStatus dcomp ; /**< Indicates the data compression status */	 
 } CiPsPrimDataCompInd;

//ICAT EXPORTED STRUCT 
 /** <paramref name= " CI_PS_PRIM_SET_PS_PAGING_CONFIG_REQ " > */ 
 typedef struct CiPsPrimSetPsPagingyConfigReq_struct 
 {	 
	 
 CiBoolean enable ; /**< TRUE: Activate DSDS PS+Paing ; FALSE: Deactivate DSDS PS+Paing \sa CCI API Ref Manual */	 
 } CiPsPrimSetPsPagingyConfigReq;

//ICAT EXPORTED ENUM 
 typedef enum CIPS_VOICE_DOMAIN_PREFERNCE_TAG 
 {	 
 CIPS_VOICE_DOMAIN_CS = 0 ,	 
 CIPS_VOICE_DOMAIN_CS_PREFERRED ,	 
 CIPS_VOICE_DOMAIN_IMS_PS_PREFERRED ,	 
 CIPS_VOICE_DOMAIN_IMS_PS ,	 
 CIPS_VOICE_DOMAIN_PREFERNCE_NUM	 
 } _CiPsVoiceDomainPreference;

typedef UINT8 CiPsVoiceDomainPreference ;
//ICAT EXPORTED STRUCT 
 /** <paramref name= " CI_PS_PRIM_SET_PS_PAGING_CONFIG_CNF " > */ 
 typedef struct CiPsPrimSetPsPagingyConfigCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimSetPsPagingyConfigCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetImsVoiceCallAvailabilityReq_struct 
 {	 
 UINT8 state ; /**< 0 - Voice calls with the IMS are not available , 1 - Voice calls with the IMS are available */	 
 } CiPsPrimSetImsVoiceCallAvailabilityReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetImsVoiceCallAvailabilityCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimSetImsVoiceCallAvailabilityCnf;

typedef CiEmptyPrim CiPsPrimGetImsVoiceCallAvailabilityReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetImsVoiceCallAvailabilityCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 UINT8 state ; /**< 0 - Voice calls with the IMS are not available , 1 - Voice calls with the IMS are available */	 
 } CiPsPrimGetImsVoiceCallAvailabilityCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetImsSmsAvailabilityReq_struct 
 {	 
 UINT8 state ; /**< 0 - SMS using IMS is not available , 1 - SMS using IMS is available */	 
 } CiPsPrimSetImsSmsAvailabilityReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetImsSmsAvailabilityCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimSetImsSmsAvailabilityCnf;

typedef CiEmptyPrim CiPsPrimGetImsSmsAvailabilityReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetImsSmsAvailabilityCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 UINT8 state ; /**< 0 - SMS using IMS is not available , 1 - SMS using IMS is available */	 
 } CiPsPrimGetImsSmsAvailabilityCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetMmImsVoiceTerminationReq_struct 
 {	 
 CiBoolean setting ; /**< If TRUE , Mobility Management for IMS Voice Termination disabled ; if FALSE , Mobility Management for IMS Voice Termination enabled \sa CCI API Ref Manual */	 
 } CiPsPrimSetMmImsVoiceTerminationReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetMmImsVoiceTerminationCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimSetMmImsVoiceTerminationCnf;

typedef CiEmptyPrim CiPsPrimGetMmImsVoiceTerminationReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetMmImsVoiceTerminationCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiBoolean setting ; /**< If TRUE , Mobility Management for IMS Voice Termination disabled ; if FALSE , Mobility Management for IMS Voice Termination enabled \sa CCI API Ref Manual */	 
 } CiPsPrimGetMmImsVoiceTerminationCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDefineDefaultPdpCtxReq_tag 
 {	 
 UINT8 modeType ; // 0 - not save to NVM , 1 - save to NUM ;	 
	 
 CiBoolean pdpTypePresent ;	 
 CiPsPdpType pdpType ;	 
 CiString apn ;	 
 CiBoolean emgIndPresent ;	 
 UINT8 emgInd ;	 
 CiBoolean ipcpReqPresent ;	 
 UINT8 ipcpReq ;	 
 CiBoolean pcscfIpv6ReqPresent ;	 
 UINT8 pcscfIpv6Req ;	 
 CiBoolean imcnSigPresent ;	 
 UINT8 imcnSig ;	 
 CiBoolean dnsIpv6Present ;	 
 UINT8 dnsIpv6 ;	 
 CiBoolean nwBearPresent ;	 
 UINT8 nwBear ;	 
 CiBoolean dsmIpv6HaPresent ;	 
 UINT8 dsmIpv6Ha ;	 
 CiBoolean dsmIpv6PrefPresent ;	 
 UINT8 dsmIpv6Pref ;	 
 CiBoolean dsmIpv6HaIpv4Present ;	 
 UINT8 dsmIpv6HaIpv4 ;	 
 CiBoolean ipViaNasPresent ;	 
 UINT8 ipViaNas ;	 
 CiBoolean ipViaDhcpPresent ;	 
 UINT8 ipViaDhcp ;	 
 CiBoolean pcscfIpv4Present ;	 
 UINT8 pcscfIpv4 ;	 
 CiBoolean dnsIpv4Present ;	 
 UINT8 dnsIpv4 ;	 
 CiBoolean msisdnPresent ;	 
 UINT8 msisdn ;	 
 CiBoolean ifomPresent ;	 
 UINT8 ifom ;	 
 CiBoolean v4mtuPresent ;	 
 UINT8 v4mtu ;	 
 CiBoolean localTftPresent ;	 
 UINT8 localTft ;	 
 CiBoolean etifPresent ;	 
 UINT8 etifFlag ;	 
 CiBoolean roamPdpTypePresent ; /*Lilei , CQ00113795 , 20190514 */	 
 CiPsPdpType roamPdpType ;	 
	 
 CiBoolean nonIpMtuReqPresent ;	 
 UINT8 nonIpMtuReq ;	 
 CiBoolean apnRateControlReqPresent ;	 
 UINT8 apnRateControlReq ;	 
	 
 CiBoolean devicePriorityPresent ;	 
 UINT8 devicePriority ; /* 0 - reserved , 1 - low priority */	 
 } CiPsPrimDefineDefaultPdpCtxReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimDefineDefaultPdpCtxCnf_tag 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimDefineDefaultPdpCtxCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetDefaultPdpReq_Tag 
 {	 
 UINT8 modeType ; // 0 - current used , 1 - from NVM	 
 } CiPsPrimGetDefaultPdpReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetDefaultPdpCtx_tag 
 {	 
 UINT8 modeType ; // 0 - current used , 1 - from NVM	 
 CiString apn ;	 
 CiPsPdpType pdpType ;	 
 UINT8 emgInd ;	 
 UINT8 ipcpReq ;	 
 UINT8 pcscfIpv6Req ;	 
 UINT8 imcnSig ;	 
 UINT8 dnsIpv6 ;	 
 UINT8 nwBear ;	 
 UINT8 dsmIpv6Ha ;	 
 UINT8 dsmIpv6Pref ;	 
 UINT8 dsmIpv6HaIpv4 ;	 
 UINT8 ipViaNas ;	 
 UINT8 ipViaDhcp ;	 
 UINT8 pcscfIpv4 ;	 
 UINT8 dnsIpv4 ;	 
 UINT8 msisdn ;	 
 UINT8 ifom ;	 
 UINT8 v4mtu ;	 
 UINT8 localTft ;	 
 UINT8 etifFlag ;	 
 CiPsPdpType roamPdpType ; /*Lilei , CQ00113795 , 20190514 */	 
	 
 UINT8 nonIpMtuReq ;	 
 UINT8 apnRateControlReq ;	 
	 
 CiBoolean devicePriorityPresent ;	 
 UINT8 devicePriority ; /* 0 - reserved , 1 - low priority */	 
 } CiPsPrimGetDefaultPdpCtx;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetDefaultPdpCtxCnf_tag 
 {	 
 CiPsRc rc ;	 
 CiPsPrimGetDefaultPdpCtx pdpCtx ;	 
 } CiPsPrimGetDefaultPdpCtxCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetApnReq_Tag 
 {	 
 UINT8 wapn ; // which apn , 0 - no action , 1 - the first APN in apn table , 2 - the second APN , ....	 
 UINT8 apncl ; // APN class	 
	 
 CiString apnni ; // APN Network identifier	 
	 
 CiBoolean apnTypePresent ;	 
 UINT8 apnType ; // 0 - invalid , 1 - ipv4 , 2 - ipv6 , 3 - ipv4v6 // CiPsApnAddrType	 
	 
 CiBoolean apnBearPresent ;	 
 UINT8 apnBear ; // 0 - invalid , 1 - LTE // CiPsApnBearType	 
	 
 CiBoolean apnedPresent ;	 
 UINT8 apned ; // 0 - disable , 1 - enable	 
	 
 CiBoolean apnTimePresent ;	 
 UINT32 apnTime ;	 
 } CiPsPrimSetApnReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetApnCnf_tag 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimSetApnCnf;

typedef CiEmptyPrim CiPsPrimGetApnReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsApnInfo_tag 
 {	 
 UINT8 apncl ;	 
 UINT8 apnType ; // 0 - invalid , 1 - ipv4 , 2 - ipv6 , 3 - ipv4v6 // CiPsApnAddrType	 
 UINT8 apnBear ; // 0 - invalid , 1 - LTE // CiPsApnBearType	 
 UINT8 apned ; // 0 - disable , 1 - enable	 
 CiString apnni ;	 
 UINT32 apnTime ;	 
 } CiPsApnInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetApnCnf_Tag 
 {	 
 CiPsRc rc ; // UINT16 , 2 bytes	 
 UINT8 num ;	 
 CiPsApnInfo apnInfo [ 5 ] ;	 
 } CiPsPrimGetApnCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetImsRegStateReq_Tag 
 {	 
 UINT8 state ; /**< 0 - IMS de-registered , 1 - IMS registered , 2 - IMS Call Active , 3 - IMS CALL ringing , 4 - IMS CALL disconncting*/	 
 } CiPsPrimSetImsRegStateReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetImsRegStateCnf_tag 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimSetImsRegStateCnf;

typedef UINT8 CiPsUeToImsEvent ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimUeEventToImsInd_tag 
 {	 
 CiPsUeToImsEvent ueEvent ; // 0 - SIM removed , 1 - IMS APN changed , 2 - other , 3 - T3346 start , 4 - T3346 expiry , 5 - T3346 stop , 6 -ERRC RELEASE.	 
 CiBoolean imsNeedDeReg ; // 0 - IMS not need to re-register , 1 - IMS need to de-register	 
 } CiPsPrimUeEventToImsInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimImsRegInfo_struct 
 {	 
 UINT8 regInfo ;	 
 UINT8 extInfo ;	 
 } CiPsPrimImsRegInfo;

typedef CiEmptyPrim CiPsPrimGetImsRegInfoReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetImsRegInfoCnf_struct // +CIREG: <n> , <reg_info> , <ext_info> 
 {	 
 CiPsRc rc ;	 
 UINT8 reportState ;	 
 CiPsPrimImsRegInfo info ;	 
 } CiPsPrimGetImsRegInfoCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetImsRegInfoIndReq_struct // AT+CIREG=n 
 {	 
 UINT8 reportState ;	 
 } CiPsPrimSetImsRegInfoIndReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetImsRegInfoIndCnf_struct // OK or false 
 {	 
 CiPsRc rc ;	 
 } CiPsPrimSetImsRegInfoIndCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimImsRegInfoInd_struct // +CIREGU: <reg_info> , <ext_info> 
 {	 
 UINT8 reportState ;	 
 CiPsPrimImsRegInfo newInfo ;	 
 } CiPsPrimImsRegInfoInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetVoiceDomainPreferenceReq_struct 
 {	 
 CiBoolean eutran ; /**< If TRUE , E-UTRAN ; if FALSE , UTRAN \sa CCI API Ref Manual */	 
 CiPsVoiceDomainPreference setting ; /**< indicates the voice domain preference of the UE \sa CiPsVoiceDomainPreference */	 
 } CiPsPrimSetVoiceDomainPreferenceReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetVoiceDomainPreferenceCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimSetVoiceDomainPreferenceCnf;

typedef CiEmptyPrim CiPsPrimGetVoiceDomainPreferenceReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetVoiceDomainPreferenceCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 CiPsVoiceDomainPreference utran_setting ; /**< indicates the voice domain preference of the UE for UTRAN \sa CiPsVoiceDomainPreference */	 
 CiPsVoiceDomainPreference eutan_setting ; /**< indicates the voice domain preference of the UE for E-UTRAN \sa CiPsVoiceDomainPreference */	 
 } CiPsPrimGetVoiceDomainPreferenceCnf;

//ICAT EXPORTED ENUM 
 typedef enum CiPsEpsUsageSetting_Tag 
 {	 
 CIPS_EPS_VOICE_CENTRIC = 0 ,	 
 CIPS_EPS_DATA_CENTRIC ,	 
 CIPS_EPS_CENTRIC_NUM	 
 } _CiPsEpsUsageSetting;

typedef UINT8 CiPsEpsUsageSetting ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetEpsUsageSettingReq_struct 
 {	 
 CiPsEpsUsageSetting epsUsageSetting ; // 0 / 1	 
 } CiPsPrimSetEpsUsageSettingReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetEpsUsageSettingCnf_struct 
 {	 
 CiPsRc rc ;	 
 } CiPsPrimSetEpsUsageSettingCnf;

typedef CiEmptyPrim CiPsPrimGetEpsUsageSettingReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetEpsUsageSettingCnf_struct 
 {	 
 CiPsRc rc ;	 
 CiPsEpsUsageSetting epsUsageSetting ; // 0 / 1	 
 } CiPsPrimGetEpsUsageSettingCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetApUniversalSettingReq_struct 
 {	 
 CiBoolean enableDataStatePresent ;	 
 UINT8 enableDataState ; /**< State of Data enable setting. TRUE: enable ; FALSE: disable.\sa CCI API Ref Manual */	 
 } CiPsPrimSetApUniversalSettingReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetApUniversalSettingCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimSetApUniversalSettingCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetPsServiceDomainReq_struct 
 {	 
 CiBoolean psServiceEnable ; /**< State of Data enable setting. TRUE: enable ; FALSE: disable.\sa CCI API Ref Manual */	 
 } CiPsPrimSetPsServiceDomainReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetPsServiceDomainCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } CiPsPrimSetPsServiceDomainCnf;

typedef CiEmptyPrim CiPsPrimGetPsServiceDomainReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetPsServiceDomainCnf_struct 
 {	 
 CiBoolean psServiceEnable ; /**< State of Data enable setting. TRUE: enable ; FALSE: disable.\sa CCI API Ref Manual */	 
 } CiPsPrimGetPsServiceDomainCnf;

typedef UINT8 CiPsImsSrvType ;
typedef UINT8 CiPsImsSrvStatus ;
typedef UINT8 CiPsImsSrvFailCause ;
typedef UINT16 CiPsImsSrvCause ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetImsServiceStatusReq_struct 
 {	 
 CiPsImsSrvType imsSrvType ;	 
 CiPsImsSrvStatus imsSrvStatus ;	 
 CiPsImsSrvFailCause srvFailCause ;	 
 } CiPsPrimSetImsServiceStatusReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetImsServiceStatusCnf_struct 
 {	 
 CiPsRc rc ;	 
 CiPsImsSrvCause cause ;	 
 } CiPsPrimSetImsServiceStatusCnf;

typedef UINT16 CiPsSuspendCause ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSuspendResumeInd_struct 
 {	 
 CiBoolean suspended ; // 0 - resume , 1 - suspended	 
 CiPsSuspendCause suspendReason ;	 
 } CiPsPrimSuspendResumeInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimChapAuthenticateReq_struct 
 {	 
 UINT8 cid ; /**< PDP context identifier. 0xFF for LTE attach PDN ; others for MO */	 
 CiStringExt challenge ; /**< CHAP challenge octets */	 
 CiStringExt response ; /**< CHAP response octets */	 
 } CiPsPrimChapAuthenticateReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimChapAuthenticateCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimChapAuthenticateCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimActivateReconfigPdpCtxReq_struct 
 {	 
 CiPsPdpCtx pdpCtx ; /**< PDP context definition \sa CiPsPdpCtx_struct */	 
 CiBoolean authInfoPresent ;	 
 CiPsAuthenticationType authenticationType ; /**< Authentication type. \sa CiPsAuthenticationType */	 
 CiStringExt userName ; /**< UserName octets. \sa CCI API Ref Manual */	 
 CiStringExt password ; /**< Password octets. \sa CCI API Ref Manual */	 
 } CiPsPrimActivateReconfigPdpCtxReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimActivateReconfigPdpCtxCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimActivateReconfigPdpCtxCnf;

typedef UINT8 CiPsPsmModeType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetPsmConfigReq_struct 
 {	 
 CiPsPsmModeType mode ; /**< Indication to disable or enable the use of PSM in the UE */	 
	 
 CiBoolean requestedPeriodRauPresent ;	 
 UINT8 requestedPeriodRau ; /** <not used , set to 0 */	 
 CiBoolean requestedGprsReadyTimerPresent ;	 
 UINT8 requestedGprsReadyTimer ; /** <not used , set to 0 */	 
	 
 CiBoolean requestedPeriodicTauPresent ;	 
 UINT8 requestedPeriodicTau ; /** <requested T3412 timer value , one byte in an 8 bit format */	 
 CiBoolean requestedActiveTimePresent ;	 
 UINT8 requestedActiveTime ; /** <requested T3324 timer value , one byte in an 8 bit format */	 
 } CiPsPrimSetPsmConfigReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetPsmConfigCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimSetPsmConfigCnf;

typedef CiEmptyPrim CiPsPrimGetPsmConfigReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetPsmConfigCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
	 
 CiPsPsmModeType mode ; /**< Indication to disable or enable the use of PSM in the UE */	 
	 
 CiBoolean requestedPeriodRauPresent ;	 
 UINT8 requestedPeriodRau ; /** <not used , set to 0 */	 
 CiBoolean requestedGprsReadyTimerPresent ;	 
 UINT8 requestedGprsReadyTimer ; /** <not used , set to 0 */	 
	 
 CiBoolean requestedPeriodicTauPresent ;	 
 UINT8 requestedPeriodicTau ; /** <requested T3412 timer value , one byte in an 8 bit format */	 
 CiBoolean requestedActiveTimePresent ;	 
 UINT8 requestedActiveTime ; /** <requested T3324 timer value , one byte in an 8 bit format */	 
 } CiPsPrimGetPsmConfigCnf;

typedef UINT8 CiPsEdrxModeType ;
typedef UINT8 CiPsEdrxActType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetEdrxConfigReq_struct 
 {	 
 CiPsEdrxModeType mode ; /**< indicates to disable or enable the use of eDRX in the UE */	 
	 
 CiPsEdrxActType eDrxAct ; /**< indicates the type of access technology */	 
 CiBoolean requestedEdrxValuePresent ;	 
 UINT8 requestedEdrxValue ; /** <requested eDRX value , half a byte in a 4 bit format */	 
 } CiPsPrimSetEdrxConfigReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetEdrxConfigCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimSetEdrxConfigCnf;

typedef CiEmptyPrim CiPsPrimGetEdrxConfigReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetEdrxConfigCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
	 
 CiPsEdrxActType eDrxAct ; /**< indicates the type of access technology */	 
 CiBoolean requestedEdrxValuePresent ;	 
 UINT8 requestedEdrxValue ; /** <requested eDRX value , half a byte in a 4 bit format */	 
 } CiPsPrimGetEdrxConfigCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEdrxInfoInd_struct 
 {	 
 CiPsEdrxActType eDrxAct ; /**< indicates the type of access technology */	 
	 
 CiBoolean requestedEdrxValuePresent ;	 
 UINT8 requestedEdrxValue ; /** <requested eDRX value , half a byte in a 4 bit format */	 
	 
 CiBoolean nwProvidedEdrxvaluePresent ;	 
 UINT8 nwProvidedEdrxvalue ; /** <NW-provided eDRX value , half a byte in a 4 bit format */	 
 CiBoolean pagingTimerWindowPresent ;	 
 UINT8 pagingTimerWindow ; /** <NW-provided paing time window , half a byte in a 4 bit format */	 
 } CiPsPrimEdrxInfoInd;

typedef CiEmptyPrim CiPsPrimReadEdrxDynParaReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimReadEdrxDynParaCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
	 
 CiPsEdrxActType eDrxAct ; /**< indicates the type of access technology */	 
	 
 CiBoolean requestedEdrxValuePresent ;	 
 UINT8 requestedEdrxValue ; /** <requested eDRX value , half a byte in a 4 bit format */	 
	 
 CiBoolean nwProvidedEdrxvaluePresent ;	 
 UINT8 nwProvidedEdrxvalue ; /** <NW-provided eDRX value , half a byte in a 4 bit format */	 
 CiBoolean pagingTimerWindowPresent ;	 
 UINT8 pagingTimerWindow ; /** <NW-provided paing time window , half a byte in a 4 bit format */	 
 } CiPsPrimReadEdrxDynParaCnf;

typedef UINT8 CiPsCiotOption ;
typedef UINT8 CiPsCiotSupportedUeOpt ;
typedef UINT8 CiPsCiotPreferUeOpt ;
typedef UINT8 CiPsCiotSupportedNwOpt ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetCiotConfigReq_struct 
 {	 
 CiPsCiotOption option ; /**< Enables or disables reporting of unsolicated result codes +CCIOTOPTI */	 
 CiPsCiotSupportedUeOpt supportedUeOpt ; /**< indicates the UE ' s support for CIoT EPS optimization */	 
 CiPsCiotPreferUeOpt preferUeOpt ; /**< indicates the UE ' s preference for CIoT EPS optimization */	 
 } CiPsPrimSetCiotConfigReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetCiotConfigCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimSetCiotConfigCnf;

typedef CiEmptyPrim CiPsPrimGetCiotConfigReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetCiotConfigReq_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
	 
 CiPsCiotOption option ; /**< Enables or disables reporting of unsolicated result codes +CCIOTOPTI */	 
 CiPsCiotSupportedUeOpt supportedUeOpt ; /**< indicates the UE ' s support for CIoT EPS optimization */	 
 CiPsCiotPreferUeOpt preferUeOpt ; /**< indicates the UE ' s preference for CIoT EPS optimization */	 
 } CiPsPrimGetCiotConfigCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimCiotNwInfoInd_struct 
 {	 
 CiPsCiotSupportedNwOpt supportedNwOpt ; /**< indicates the Network support for CIoT EPS optimization */	 
 } CiPsPrimCiotNwInfoInd;

typedef UINT8 CiPsSignallingConnectionOpt ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimConfigSignallingConnectionReq_struct 
 {	 
 CiPsSignallingConnectionOpt option ; /**< Enables or disables reporting of unsolicated result codes +CCIOTOPTI */	 
 } CiPsPrimConfigSignallingConnectionReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimConfigSignallingConnectionCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimConfigSignallingConnectionCnf;

typedef UINT8 CiPsCsconMode ;
typedef UINT8 CiPsCsconState ;
typedef UINT8 CiPsCsconAccess ;
typedef CiEmptyPrim CiPsPrimGetSignallingConnectionStatusReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetSignallingConnectionStatusCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
	 
 CiPsSignallingConnectionOpt option ; /**< Enables or disables reporting of unsolicated result codes +CCIOTOPTI */	 
 CiPsCsconMode mode ; /**<indicates the signalling connection satus */	 
	 
 CiBoolean statePresent ;	 
 CiPsCsconState state ; /**<indicates the CS or PS state while in GERAN and the RRC state information if the MTis in connected mode while in UTRAN and E-UTRAN */	 
	 
 CiBoolean accessPresent ;	 
 CiPsCsconAccess access ; /**<indicates the current radio access type */	 
 } CiPsPrimGetSignallingConnectionStatusCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSignallingConnectionInd_struct 
 {	 
 CiPsCsconMode mode ; /**<indicates the signalling connection satus */	 
	 
 CiBoolean statePresent ;	 
 CiPsCsconState state ; /**<indicates the CS or PS state while in GERAN and the RRC state information if the MTis in connected mode while in UTRAN and E-UTRAN */	 
	 
 CiBoolean accessPresent ;	 
 CiPsCsconAccess access ; /**<indicates the current radio access type */	 
 } CiPsPrimSignallingConnectionInd;

typedef UINT8 CiPsEpsAttachwithPdnOpt ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetInitialPdpActivationOptReq_struct 
 {	 
 UINT8 option ; /**<Activation of PDP context upon attach , only used for 2 / 3 G , 0 - Do not activate , 1 - Always activate , 2 - Activate when not roaming , 3 - No change in current setting only for EUTRAN */	 
 CiPsEpsAttachwithPdnOpt attachWithoutPdn ; /**< EPS attach with or without PDN connection */	 
 } CiPsPrimSetInitialPdpActivationOptReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetInitialPdpActivationOptCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimSetInitialPdpActivationOptCnf;

typedef CiEmptyPrim CiPsPrimGetInitialPdpActivationOptReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetInitialPdpActivationOptCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
	 
 UINT8 option ; /**<Activation of PDP context upon attach , only used for 2 / 3 G , 0 - Do not activate , 1 - Always activate , 2 - Activate when not roaming , 3 - No change in current setting only for EUTRAN */	 
 CiPsEpsAttachwithPdnOpt attachWithoutPdn ; /**< EPS attach with or without PDN connection */	 
 } CiPsPrimGetInitialPdpActivationOptCnf;

typedef UINT8 CiPsApnBackoffTimerOpt ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetApnBackoffTimerStatusReq_struct 
 {	 
 CiPsApnBackoffTimerOpt option ; /**< 0 - Disable presentation of the unsolicited result code , 1 -Enable presentation of the unsolicited result code */	 
 } CiPsPrimSetApnBackoffTimerStatusReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetApnBackoffTimerStatusCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } CiPsPrimSetApnBackoffTimerStatusCnf;

typedef CiEmptyPrim CiPsPrimGetApnBackoffTimerStatusReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetApnBackoffTimerStatusCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
	 
 CiPsApnBackoffTimerOpt option ; /**< 0 - Disable presentation of the unsolicited result code , 1 -Enable presentation of the unsolicited result code */	 
 } CiPsPrimGetApnBackoffTimerStatusCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimApnBackoffTimerStatusReportInd_struct 
 {	 
 CiString apn ;	 
	 
 UINT32 eventType ; /**< 0 - the back-off timer is started , 1 -the backoff timer is stopped , 2 -the back-off timer is expired */	 
 UINT32 backoffTimerValue ; /**< Indicate the residual back-off timer value , in second */	 
	 
 CiBoolean reAttempPresent ;	 
 UINT8 reAttemptRatIndication ; /**< 0 - Re-attempt the seeion management afert inter-system change is allowed , 1 - not allowed , now no need to support it */	 
 UINT8 reAttemptEplmnIndication ; /**<0- Re-attempt the session management in an EPLMN is allowed , 1 - not allowed */	 
	 
 CiBoolean nslpiPresent ;	 
 UINT8 nslpi ; /**< 0 -Indicates that this PDN connection was set to " MS is configured for NAS signalling low priority " , 1 - set to " MS is not configured for NAS signalling low priority " */	 
 UINT8 procedure ; /**<0-all procedures , 1 -PDN connectivity proc , 2 -bearer resource allocation proc , 3 -bearer resource modification proc ,	 
 4 -PDP activation proc , 5 -secondary PDP activation proc , 6 -PDP modification proc */	 
 } CiPsPrimApnBackoffTimerStatusReportInd;

typedef UINT8 CiPs5GNwRegIndFlag ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPs5GNwRegInfo_struct 
 {	 
 CiPsNwRegStatus status ; /**< Network registration status. */	 
	 
 CiBoolean tacPresent ; /**< Indicates if TAC and Cell ID are present. */	 
 UINT32 tac ; /**< String type ; three byte tracking area code in hexadecimal format ( e.g. " 0 C3 " equals 195 in decimal ) */	 
 UINT64 cellId ; /**< string type ; five byte NR cell ID in hexadecimal format. */	 
	 
 CiPsAccTechMode act ; /**< Network access technology ( GSM , UTRAN , LTE etc. ) */	 
	 
 CiStringExt allowedNssai ;	 
	 
 CiBoolean causePresent ; /**< Indicates if causeType and rejectCause are present. >**/	 
 CiPsCauseType causeType ; /**<cause_type>: integer type ; indicates the type of <reject_cause>**/	 
 UINT32 rejectCause ; /**<reject_cause>: integer type ; contains the cause of the failed registration.**/	 
 } 
 CiPs5GNwRegInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEnable5GNwRegIndReq_struct 
 {	 
 CiPs5GNwRegIndFlag flag ; /** report level , n = 0 , 1 , 2 , 3 **/	 
 } CiPsPrimEnable5GNwRegIndReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimEnable5GNwRegIndCnf_struct 
 {	 
 CiPsRc rc ;	 
 } CiPsPrimEnable5GNwRegIndCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrim5GNwRegInd_struct 
 {	 
 CiPs5GNwRegInfo nwRegInfo ; /**< Network registration information. \sa CiPs4GNwRegInfo_struct */	 
 } CiPsPrim5GNwRegInd;

//ICAT EXPORTED STRUCT 
 typedef CiEmptyPrim CiPsPrimGet5GNwRegStatusReq ; 
 
 // ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGet5GNwRegStatusCnf_struct 
 {	 
 CiPsRc rc ;	 
 CiPs5GNwRegIndFlag flag ; /** report level , n = 0 , 1 , 2 , 3 **/	 
 CiPs5GNwRegInfo nwRegInfo ; /**< Network registration information. \sa CiPs4GNwRegInfo_struct */	 
 } CiPsPrimGet5GNwRegStatusCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSendUePolicyReq_struct 
 {	 
 // UINT8 flag ; // 0 : no following UE policy segment ; 1 : more UE policy segment	 
	 
 UINT16 length ;	 
 UINT16 subLength ;	 
 /* the whole data is concatenated by message type ( 1 byte ) , ue policy information length ( 2 byte ) , ue policy information and ue policy classmark ( one byte ) ,	 
 * which are described in chapter 10.100000 .52 of TS27007. if the total size is larger than 2000 -byte , the whole data will be divided into segements ,	 
 * and CiPsPrimSendUePolicyReq takes them one by one. */	 
 UINT8 data [ 2000 ] ;	 
 } 
 CiPsPrimSendUePolicyReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSendUePolicyCnf_struct 
 {	 
 CiPsRc rc ;	 
 } 
 CiPsPrimSendUePolicyCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsDnnTag 
 {	 
 UINT8 length ;	 
 UINT8 name [ 100 ] ;	 
 } 
 CiPsDnn;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsOsIdTag 
 {	 
 UINT8 lenOfOsId ;	 
 UINT8 osId [ 16 ] ;	 
 } 
 CiPsOsId;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsOsAppIdTag 
 {	 
 UINT8 lenOfOsAppId ;	 
 UINT8 osAppId [ 64 ] ;	 
 } 
 CiPsOsAppId;

//ICAT EXPORTED STRUCT 
 typedef struct CIPsOsIdAndOsAppIdTag 
 {	 
 CiPsOsId osId ;	 
 CiPsOsAppId osAppId ;	 
 } 
 CIPsOsIdAndOsAppId;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsTDTag 
 {	 
 BOOL matchAll ;	 
	 
 BOOL osIDAndOsAppIdPresent ;	 
 CIPsOsIdAndOsAppId osIdAndOsAppId ;	 
	 
 BOOL ipv4RemoteAddrPresent ;	 
 UINT8 ipv4Addr [ 4 ] ;	 
 UINT8 ipv4AddrMask [ 4 ] ;	 
	 
 BOOL ipv6RemoteAddrAndPrefixLenPresent ;	 
 UINT8 ipv6Addr [ 16 ] ;	 
 UINT8 prefixLen ;	 
	 
 BOOL protocolIdentifierOrNextHdrPresent ;	 
 UINT8 protocolIdentifierOrNextHdr ;	 
	 
 BOOL singleRemotePortPresent ;	 
 UINT16 portNumber ;	 
	 
 BOOL remotePortRangePresent ;	 
 UINT16 lowLimit ;	 
 UINT16 highLimit ;	 
	 
 BOOL securityParaIndexPresent ;	 
 UINT32 ipsecSecurityParaIndex ;	 
	 
 BOOL typeOfServiceOrTrafficClassPresent ;	 
 UINT8 typeOfServiceOrTrafficClass ;	 
 UINT8 typeOfServiceOrTrafficClassMask ;	 
	 
 /* For " flow label type " , the traffic descriptor component value field shall be encoded as three octets which specify	 
 the IPv6 flow label. The bits 8 through 5 of the first octet shall be spare whereas the remaining 20 bits shall contain	 
 the IPv6 flow label. */	 
 BOOL flowLabelPresent ;	 
 UINT32 ipv6FlowLabel ;	 
	 
 BOOL destMacAddrPresent ;	 
 UINT8 macAddr [ 6 ] ;	 
	 
 /* For " 802.100000 Q C-TAG VID type " , the traffic descriptor component value field shall be encoded as two octets which specify	 
 the VID of the customer-VLAN tag ( C-TAG ) . The bits 8 through 5 of the first octet shall be spare whereas the remaining	 
 12 bits shall contain the VID. */	 
 BOOL ctagVidPresent ;	 
 UINT16 ctagVid ;	 
	 
 /* For " 802.100000 Q S-TAG VID type " , the traffic descriptor component value field shall be encoded as two octets which specify	 
 the VID of the service-VLAN tag ( S-TAG ) . The bits 8 through 5 of the first octet shall be spare whereas the remaining	 
 12 bits shall contain the VID. */	 
 BOOL stagVidPresent ;	 
 UINT16 stagVid ;	 
	 
 /* For " 802.100000 Q C-TAG PCP / DEI type " , the traffic descriptor component value field shall be encoded as one octet which	 
 specifies the 802.100000 Q C-TAG PCP and DEI. The bits 8 through 5 of the octet shall be spare , and the bits 4 through 2	 
 contain the PCP and bit 1 contains the DEI. */	 
 BOOL ctagPcpDeiPresent ;	 
 UINT8 ctagPcp ;	 
 BOOL ctagDei ;	 
	 
 /* For " 802.100000 Q S-TAG PCP / DEI type " , the traffic descriptor component value field shall be encoded as one octet which	 
 specifies the 802.100000 Q S-TAG PCP and DEI. The bits 8 through 5 of the octet shall be spare , and the bits 4 through 2	 
 contain the PCP and bit 1 contains the DEI. */	 
 BOOL stagPcpDeiPresent ;	 
 UINT8 stagPcp ;	 
 BOOL stagDei ;	 
	 
 BOOL etherTypePresent ;	 
 UINT16 etherType ;	 
	 
 BOOL dnnPresent ;	 
 CiPsDnn dnn ;	 
	 
 BOOL connectionCapPresent ;	 
 UINT8 numOfConnectionCap ;	 
 UINT8 connectionCapId [ 4 ] ;	 
	 
 BOOL destFQDNPresent ;	 
 UINT8 lenOfDestFQDN ;	 
 UINT8 fqdn [ 64 ] ;	 
	 
 BOOL osAppIdPresent ;	 
 CiPsOsAppId osAppId ;	 
 } 
 CiPsTD;

//ICAT EXPORTED ENUM 
 typedef enum CiPsSNssaiContentSizeTag 
 {	 
 CI_PS_SNSSAI_CONTENT_SST = 1 ,	 
 CI_PS_SNSSAI_CONTENT_SST_AND_MAPPED_HPLMN_SST = 2 ,	 
 CI_PS_SNSSAI_CONTENT_SST_AND_SD = 4 ,	 
 CI_PS_SNSSAI_CONTENT_SST_SD_AND_MAPPED_HPLMN_SST = 5 ,	 
 CI_PS_SNSSAI_CONTENT_SST_SD_AND_MAPPED_HPLMN_SST_SD = 8 ,	 
 CI_PS_LENGTH_OF_SNSSAI_CONTENT_SIZE	 
 } 
 CiPsSNssaiContentSize;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsSNssaiTag 
 {	 
 CiPsSNssaiContentSize length ;	 
 UINT8 sst ; // Slice / Service type	 
 UINT32 sd ; // Slice Differentiator	 
 UINT8 mappedSst ; // Mapped Home Plmn SST	 
 UINT32 mappedSd ; // Mapped Home Plmn SD	 
 } 
 CiPsSNssai;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsRsdComponentsTag 
 {	 
 /* The " SSC mode type " route selection descriptor component shall not appear more than once in the route selection descriptor. */	 
 BOOL sscModePresent ;	 
 UINT8 sscMode ;	 
	 
 BOOL snssaiPresent ;	 
 UINT8 lenOfSnssai ;	 
 UINT8 numOfSnssai ;	 
 CiPsSNssai snssai [ 3 ] ;	 
	 
 BOOL dnnPresent ;	 
 CiPsDnn dnn ;	 
	 
 /* The " PDU session type " route selection descriptor component shall not appear more than once in the route selection descriptor. */	 
 BOOL pduSessionTypePresent ;	 
 UINT8 pduSessionType ;	 
	 
 /* The " preferred access type " route selection descriptor component shall not appear more than once in the route selection descriptor. */	 
 BOOL preferredAccessTypePresent ;	 
 UINT8 preferredAccessType ;	 
	 
 /* For " non-seamless non-3GPP offload indication type " , the route selection descriptor component shall not include the route selection	 
 descriptor component value field.	 
 The " non-seamless non-3GPP offload indication type " route selection descriptor component shall not appear more than once in the route selection descriptor.	 
 If the " non-seamless non-3GPP offload indication type " route selection descriptor component is included in a route selection descriptor ,	 
 there shall be no route selection descriptor component with a type other than " non-seamless non-3GPP offload indication type " in the route selection descriptor. */	 
 BOOL nonseamlessNon3gppOffloadInd ;	 
 } 
 CiPsRsdComponents;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsRsdTag 
 {	 
 UINT8 precedenceOfRSD ;	 
 CiPsRsdComponents rsdComponents ;	 
 } 
 CiPsRsd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsUrspRuleTag 
 {	 
 UINT8 precedenceOfURSPRule ;	 
 CiPsTD td ;	 
 UINT8 numOfRsd ;	 
 CiPsRsd rsd [ 3 ] ;	 
 } 
 CiPsUrspRule;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsUrspInfoTag 
 {	 
 UINT8 numOfUrspRule ;	 
 CiPsUrspRule urspRule [ 5 ] ;	 
 } CiPsUrspInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimRecvUePolicyIndTag 
 {	 
 UINT32 length ;	 
 UINT8 data [ 1900 ] ;	 
	 
 } 
 CiPsPrimRecvUePolicyInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetUePolicyReportReq_struct 
 {	 
 CiBoolean reporting ; // 0 for disable reporting , 1 for enable reporting	 
 } 
 CiPsPrimSetUePolicyReportReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetUePolicyReportCnf_struct 
 {	 
 CiPsRc rc ;	 
 } 
 CiPsPrimSetUePolicyReportCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetUePolicyReportCnf_struct 
 {	 
 CiPsRc rc ;	 
	 
 CiBoolean reporting ; // 0 for disable reporting , 1 for enable reporting	 
	 
 UINT8 type ; // 0 : UE policy struct ; 1 : raw data for AT command	 
	 
 // UINT8 flag ; // 0 : no following UE policy segment ; 1 : more UE policy segment	 
	 
 UINT16 length ;	 
 UINT16 subLength ;	 
	 
 /* If type is 0 , the whole data is a UE policy structure ; if type is 1 , the whole data is a UE policy raw data , which is described in chapter 10.100000 .51 of TS27007.	 
 * If the total size is larger than 2000 -byte , the whole data will be divided into segements , and CiPsPrimGetUePolicyReportCnf takes them one by one. */	 
 UINT8 data [ 2000 ] ;	 
 } 
 CiPsPrimGetUePolicyReportCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSendUeOsIdReq_struct 
 {	 
 UINT8 number ;	 
 UINT16 ueOsId [ 16 ] ;	 
 } 
 CiPsPrimSendUeOsIdReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSendUeOsIdCnf_struct 
 {	 
 CiPsRc rc ;	 
 } 
 CiPsPrimSendUeOsIdCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSendLadnIndicationReq_struct 
 {	 
 UINT8 number ;	 
 CiString dnn [ 8 ] ;	 
 } 
 CiPsPrimSendLadnIndicationReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSendLadnIndicationCnf_struct 
 {	 
 CiPsRc rc ;	 
 UINT16 length ;	 
 // UINT16 subLength ;	 
 UINT8 data [ 2000 ] ;	 
 } 
 CiPsPrimSendLadnIndicationCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimRecvLadnInformationInd_struct 
 {	 
 UINT16 length ;	 
 // UINT16 subLength ;	 
 UINT8 data [ 2000 ] ;	 
 } 
 CiPsPrimRecvLadnInformationInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetLadnInformationReq_struct 
 {	 
 UINT8 reporting ; // 0 for disable reporting ; 1 for enable reporting.	 
 } 
 CiPsPrimSetLadnInformationReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetLadnInformationCnf_struct 
 {	 
 CiPsRc rc ;	 
 } 
 CiPsPrimSetLadnInformationCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetDefaultNssaiReq_struct 
 {	 
 CiStringExt nssai ;	 
 } 
 CiPsPrimSetDefaultNssaiReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetDefaultNssaiCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } 
 CiPsPrimSetDefaultNssaiCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetPreferredNssaiReq_struct 
 {	 
 CiStringExt nssai ;	 
 } 
 CiPsPrimSetPreferredNssaiReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetPreferredNssaiCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } 
 CiPsPrimSetPreferredNssaiCnf;

//ICAT EXPORTED STRUCT 
 typedef enum CiPsNssaiType_enum 
 {	 
 CI_PS_DEFAULT_NSSAI = 0 , /*0: retrun stored default NSSAI */ /* ; 1 : ; 2 : ; 3 :*/	 
 CI_PS_DEFAULT_REJECT_NSSAI = 1 , /*1: retrun stored default NSSAI and rejected NSSAI ( s ) */	 
 CI_PS_DEFAULT_REJECT_CONFIG_NSSAI = 2 , /*2: retrun stored default NSSAI rejected NSSAI ( S ) and configured NSSAI ( s ) */	 
 CI_PS_DEFAULT_REJECT_CONFIG_ALLOWED_NSSAI = 3 , /*3: retrun stored default NSSAI rejected NSSAI ( S ) , configured NSSAI ( s ) and allowed NSSAI ( s ) */	 
 /* Added by Daniel for CQ00138540 , begin */	 
 CI_PS_REJECT_NSSAI = 4 , /* return rejected NSSAI */	 
 CI_PS_CONFIG_NSSAI = 5 , /* return configured NSSAI */	 
 CI_PS_ALLOWED_NSSAI = 6 , /* return allowed NSSAI */	 
 /* Added by Daniel for CQ00138540 , end */	 
 CI_PS_NUM_NSSAI_TYPE	 
 } 
 _CiPsNssaiType;

typedef UINT8 CiPsNssaiType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsNssaiPerPlmn_struct 
 {	 
 CiStringExt cfgNssai ;	 
 CiStringExt allowedNssai ;	 
 CiPsNetworkId plmnId ;	 
 } 
 CiPsNssaiPerPlmn;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetNssaiReq_struct 
 {	 
 CiPsNssaiType type ;	 
 CiBoolean plmnIdPresent ;	 
 CiPsNetworkId plmnId ;	 
 } 
 CiPsPrimGetNssaiReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimNssaiReportInd_struct 
 {	 
 CiPsNssaiType type ;	 
 CiStringExt nssai ;	 
 CiBoolean plmnIdPresent ;	 
 CiPsNetworkId plmnId ;	 
 } 
 CiPsPrimNssaiReportInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetNssaiCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } 
 CiPsPrimGetNssaiCnf;

//ICAT EXPORTED ENUM 
 typedef enum CiPsMicoModeType_enum 
 {	 
 CI_PS_MICO_DISBABLE = 0 , /** <disable the use of MICO */	 
 CI_PS_MICO_ENABLE = 1 , /** <enable the use of MICO */	 
	 
 CI_PS_MICO_NUM	 
 } _CiPsMicoModeType;

typedef UINT8 CiPsMicoModeType ;
//ICAT EXPORTED ENUM 
 typedef enum CiPsMicoReportOption_enum 
 {	 
 CI_PS_MICO_REPORT_DISABLE = 0 , /**< disable unsolicited result code */	 
 CI_PS_MICO_REPORT_ENABLE , /**< enable unsolicited result code */	 
 CI_PS_MICO_REPORT_STATUS , /**< MICO mode is requested or re-negotiated from the network enabling or disabling */	 
	 
 CI_PS_NUM_MICO_REPORTT_OPTIONS /**< Number of options defined */	 
 } _CiPsMicoRePortOption;

typedef UINT8 CiPsMicoReportOption ;
//ICAT EXPORTED ENUM 
 typedef enum CiPsWusModeType_enum 
 {	 
 CI_PS_WUS_DISBABLE = 0 , /** <disable the use of CWUS */	 
 CI_PS_WUS_ENABLE = 1 , /** <enable the use of CWUS */	 
 CI_PS_WUS_CHANGE = 2 , /** <enable the use of CWUS */	 
	 
 CI_PS_WUS_NUM	 
 } _CiPsWusModeType;

typedef UINT8 CiPsWusModeType ;
//ICAT EXPORTED ENUM 
 typedef enum CiPsRaaiType_enum 
 {	 
 CI_PS_ALL_PLMN_REGISTRATION_AREA_NOT_ALLOCATED = 0 ,	 
 CI_PS_ALL_PLMN_REGISTRATION_AREA_ALLOCATED = 1 ,	 
	 
 CI_PS_RAAI_NUM	 
 } _CiPsRaaiType;

typedef UINT8 CiPsRaaiType ;
//ICAT EXPORTED ENUM 
 typedef enum CiPsSprtiType_enum 
 {	 
 CI_PS_STRICTLY_PERIODIC_REGISTRATION_TIMER_NOT_SUPPORTED = 0 ,	 
 CI_PS_STRICTLY_PERIODIC_REGISTRATION_TIMER_SUPPORTED = 1 ,	 
	 
 CI_PS_SPRTI_NUM	 
 } _CiPsSprtiType;

typedef UINT8 CiPsSprtiType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetMicoReq_struct 
 {	 
 CiPsMicoReportOption option ;	 
 CiPsMicoModeType mode ;	 
 CiBoolean requestedActiveTimePresent ;	 
 UINT8 requestedActiveTime ; /** <requested T3324 timer value , one byte in an 8 bit format */	 
 } 
 CiPsPrimSetMicoReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetMicoCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
	 
 CiPsMicoReportOption option ;	 
 CiPsMicoModeType currentMode ;	 
 CiPsRaaiType raai ;	 
 CiPsSprtiType sprti ;	 
 CiBoolean allocatedActiveTimePresent ;	 
 UINT8 allocatedActiveTime ; /** <allocated T3324 timer value , one byte in an 8 bit format */	 
 } 
 CiPsPrimSetMicoCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetWusReq_struct 
 {	 
 // CiPsCwusReportOption option ;	 
 CiPsWusModeType mode ; // WUS_DISBABLE = 0 , WUS_ENABLE = 1 , WUS_CHANGE = 2 ,	 
 UINT8 WusConditions [ 1 ] ;	 
 } 
 CiPsPrimSetWusReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetWusCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
	 
 CiPsWusModeType mode ; // WUS_DISBABLE = 0 , WUS_ENABLE = 1 , WUS_CHANGE = 2 ,	 
 UINT8 WusConditions [ 1 ] ;	 
 } 
 CiPsPrimSetWusCnf;

typedef CiEmptyPrim CiPsPrimGet5gcapaReq ;
//ICAT EXPORTED ENUM 
 typedef enum _CiPsUeTestTypeTag 
 {	 
 CIPS_UE_STRESS_TEST = 1 ,	 
	 
 NUM_OF_UE_TEST_TYPE	 
 } 
 _CiPsUeTestType;

typedef UINT8 CiPsUeTestType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsStressTestConfigTag 
 {	 
 UINT8 operation ;	 
 UINT8 mode ;	 
 UINT8 direction ;	 
 UINT32 maxDlRate ;	 
 } 
 CiPsStressTestConfig;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetUeTestReqTag 
 {	 
 CiPsUeTestType type ;	 
 UINT8 configData [ 256 ] ;	 
 } 
 CiPsPrimSetUeTestReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetUeTestCnfTag 
 {	 
 CiPsRc rc ;	 
 } 
 CiPsPrimSetUeTestCnf;

//ICAT EXPORTED ENUM 
 typedef enum _CiPsDataOffStatusTag 
 {	 
 CI_PS_DATAOFF_DEACTIVATED = 0 ,	 
 CI_PS_DATAOFF_ACTIVATED ,	 
	 
 CI_PS_NUM_PS_DATAOFF_STATUS	 
 } 
 _CiPsDataOffStatus;

typedef UINT8 CiPsDataOffStatus ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetPsDataOffReqTag 
 {	 
 CiPsDataOffStatus psDataOff ;	 
 } 
 CiPsPrimSetPsDataOffReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetPsDataOffCnfTag 
 {	 
 CiPsRc rc ;	 
 } 
 CiPsPrimSetPsDataOffCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimConfigNasConnectionReleaseReqTag 
 {	 
 UINT8 reserved ;	 
 } 
 CiPsPrimConfigNasConnectionReleaseReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimConfigNasConnectionReleaseCnfTag 
 {	 
 CiPsRc rc ;	 
 } 
 CiPsPrimConfigNasConnectionReleaseCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimExemptServiceListIndTag 
 {	 
	 
	 
 CiBoolean hExemptServiceListPresent ;	 
 UINT8 hExemptServiceList ;	 
	 
 CiBoolean vExemptServiceListPresent ;	 
 UINT8 vExemptServiceList ;	 
	 
 /* exempt service list in EF3GPPPSDATAOFFservicelist */	 
 UINT8 numOfExemptImsService ;	 
 CiStringExt icsi [ 16 ] ;	 
 } 
 CiPsPrimExemptServiceListInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimRecomBitRateQueryReqTag 
 {	 
 UINT8 cid ;	 
	 
 UINT16 bitRate ;	 
 UINT8 direction ;	 
 } 
 CiPsPrimRecomBitRateQueryReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimRecomBitRateQueryCnfTag 
 {	 
 CiPsRc rc ; /**< Result code \sa CiPsRc */	 
 } 
 CiPsPrimRecomBitRateQueryCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimRecomBitRateReportIndTag 
 {	 
 UINT8 number ;	 
 UINT8 cid [ 8 ] ;	 
	 
 UINT16 bitRate ;	 
 UINT8 direction ;	 
 } 
 CiPsPrimRecomBitRateReportInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetRecomBitRateReportReqTag 
 {	 
 CiBoolean rptEnabled ;	 
 } 
 CiPsPrimSetRecomBitRateReportReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetRecomBitRateReportCnfTag 
 {	 
 CiPsRc rc ;	 
 } 
 CiPsPrimSetRecomBitRateReportCnf;

//ICAT EXPORTED STRUCT 
 typedef CiEmptyPrim CiPsPrimGetRecomBitRateReportReq ; 
 
 // ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetRecomBitRateReportCnfTag 
 {	 
 CiPsRc rc ;	 
 CiBoolean rptEnabled ;	 
 } 
 CiPsPrimGetRecomBitRateReportCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsSmPduDnReqContainerTag 
 {	 
 /* Added by Daniel for CQ00146880 , begin */	 
 UINT8 cid ;	 
 /* Added by Daniel for CQ00146880 , end */	 
 UINT8 length ;	 
 UINT8 dnSpecificId [ 253 ] ;	 
 } 
 CiPsSmPduDnReqContainer;

//ICAT EXPORTED ENUM 
 typedef enum _CiPsMbsSessionIdTypeTag 
 {	 
 CI_PS_MBS_SESSION_ID_TMGI ,	 
 CI_PS_MBS_SESSION_ID_IPV4 ,	 
 CI_PS_MBS_SESSION_ID_IPV6 ,	 
	 
 NUM_OF_CI_PS_MBS_SESSION_ID_TYPE	 
 } 
 _CiPsMbsSessionIdType;

typedef UINT8 CiPsMbsSessionIdType ;
//ICAT EXPORTED ENUM 
 typedef enum _CiPsMbsOperationTag 
 {	 
 CI_PS_MBS_OP_JOIN ,	 
 CI_PS_MBS_OP_LEAVE ,	 
	 
 NUM_OF_CI_PS_MBS_OP	 
 } 
 _CiPsMbsOperation;

typedef UINT8 CiPsMbsOperation ;
//ICAT EXPORTED ENUM 
 typedef enum _CiPsMbsSessionStatusReportTypeTag 
 {	 
 CI_PS_MBS_DISABLE_REPORT_MBS_SESSION_STATUS ,	 
 CI_PS_MBS_ENABLE_REPORT_MBS_SESSION_STATUS ,	 
	 
 NUM_OF_CI_PS_MBS_SESSION_REPORT_TYPE	 
 } 
 _CiPsMbsSessionStatusReportType;

typedef UINT8 CiPsMbsSessionStatusReportType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsMbsTmgiTag 
 {	 
 UINT32 serviceId ;	 
 CiBoolean plmnPresent ;	 
 CiPsNetworkId plmn ;	 
 } 
 CiPsMbsTmgi;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsMbsIpAddrTag 
 {	 
 CiPsPdpIpAddr srcAddr ;	 
 CiPsPdpIpAddr dstAddr ;	 
 } 
 CiPsMbsIpAddr;

//ICAT EXPORTED UNION 
 typedef union CiPsPrimMbsSessionIdTag 
 {	 
 CiPsMbsIpAddr ipAddr ;	 
 CiPsMbsTmgi tmgi ;	 
 } 
 CiPsPrimMbsSessionId;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsSetMbsSessionCtxTag 
 {	 
 UINT8 cid ;	 
 CiPsMbsSessionIdType type ;	 
 CiPsPrimMbsSessionId id ;	 
 CiPsMbsOperation operation ;	 
 } 
 CiPsSetMbsSessionCtx;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsSetMbsSessionStatusReportTag 
 {	 
 CiPsMbsSessionStatusReportType reportType ;	 
 } 
 CiPsSetMbsSessionStatusReport;

//ICAT EXPORTED UNION 
 typedef union CiPsSetCommonParasTag 
 {	 
 CiPsSmPduDnReqContainer smPduDnReqContainer ;	 
 CiPsSetMbsSessionCtx setMbsSessionContext ;	 
 CiPsSetMbsSessionStatusReport setMbsSessionStatusReport ;	 
 } 
 CiPsSetCommonParas;

//ICAT EXPORTED ENUM 
 typedef enum _CiPsSetParasTypeTag 
 {	 
 CI_PS_SET_PARAS_NULL ,	 
	 
 CI_PS_SET_SM_PDU_DN_REQUEST_CONTAINER ,	 
 /* Added by Daniel for CQ00146880 , begin */	 
 CI_PS_SET_MBS_SESSION_CONTEXT ,	 
 CI_PS_SET_MBS_SESSION_STATUS_REPORT ,	 
 /* Added by Daniel for CQ00146880 , end */	 
	 
 NUM_OF_CI_PS_SET_PARAS_TYPE	 
 } 
 _CiPsSetParasType;

typedef UINT8 CiPsSetParasType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetParasReqTag 
 {	 
 CiPsSetParasType type ;	 
 /* Modified by Daniel for CQ00146880 , begin */	 
	 
	 
	 
 UINT8 reserved [ 3 ] ;	 
	 
	 
	 
 CiPsSetCommonParas paras ;	 
 /* Modified by Daniel for CQ00146880 , end */	 
 } 
 CiPsPrimSetParasReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetParasCnfTag 
 {	 
 CiPsRc rc ;	 
 } 
 CiPsPrimSetParasCnf;

//ICAT EXPORTED ENUM 
 typedef enum _CiPsGetParasTypeTag 
 {	 
 CI_PS_GET_PARAS_NULL ,	 
	 
 /* Added by Daniel for CQ00146880 , begin */	 
 CI_PS_GET_MBS_SESSION_CONTEXT ,	 
 CI_PS_GET_MBS_SESSION_DYN_PARAS ,	 
 CI_PS_GET_MBS_SESSION_DYN_ACT_PARAS ,	 
 CI_PS_GET_MBS_SESSION_STATUS ,	 
 /* Added by Daniel for CQ00146880 , end */	 
	 
 NUM_OF_CI_PS_GET_PARAS_TYPE	 
 } 
 _CiPsGetParasType;

typedef UINT8 CiPsGetParasType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetParasReqTag 
 {	 
 CiPsGetParasType type ;	 
 /* Modified by Daniel for CQ00146880 , begin */	 
 UINT8 cid ;	 
 UINT8 reserved [ 2 ] ;	 
	 
	 
	 
 /* Modified by Daniel for CQ00146880 , end */	 
 } 
 CiPsPrimGetParasReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimGetParasCnfTag 
 {	 
 CiPsRc rc ;	 
 } 
 CiPsPrimGetParasCnf;

//ICAT EXPORTED ENUM 
 typedef enum _CiPsMbsDecisionTag 
 {	 
 CI_PS_MBS_DECISION_SERVICE_AREA_UPDATE ,	 
 CI_PS_MBS_JOIN_ACCEPTED ,	 
 CI_PS_MBS_JOIN_REJECTED ,	 
 CI_PS_MBS_REMOVE_UE_FROM_MBS_SESSION ,	 
	 
 NUM_OF_CI_PS_MBS_DECISION	 
 } 
 _CiPsMbsDecision;

typedef UINT8 CiPsMbsDecision ;
//ICAT EXPORTED ENUM 
 typedef enum _CiPsMbsRejectCauseTag 
 {	 
 CI_PS_MBS_REJECT_NO_ADDITIONAL_INFO ,	 
 CI_PS_MBS_REJECT_INSUFFICIENT_RESOURCES ,	 
 CI_PS_MBS_REJECT_NOT_AUTHORIZED ,	 
 CI_PS_MBS_REJECT_MBS_SESSION_NOT_START ,	 
 CI_PS_MBS_REJECT_OUT_OF_MBS_SESSION_SERVICE_AREA ,	 
 CI_PS_MBS_REJECT_SESSION_CONTEXT_NOT_FOUND ,	 
 CI_PS_MBS_REJECT_SESSION_RELEASED ,	 
	 
 NUM_OF_CI_PS_MBS_REJECT	 
 } 
 _CiPsMbsRejectCause;

typedef UINT8 CiPsMbsRejectCause ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsMbsTimeTag 
 {	 
 UINT8 year ; /**< Year [ 0 ..99 ] */	 
 UINT8 month ; /**< Month [ 1 ..12 ] */	 
 UINT8 day ; /**< Day [ 1 ..31 ] */	 
 UINT8 hour ; /**< Hour [ 0 ..59 ] */	 
 UINT8 minute ; /**< Minute [ 0 ..59 ] */	 
 UINT8 second ; /**< Second [ 0 ..59 ] */	 
 INT8 locTimeZone ; /**< Local time zone */	 
 } 
 CiPsMbsTime;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsRptMbsSessionCtxTag 
 {	 
 UINT8 numOfMbsSession ;	 
 CiPsSetMbsSessionCtx mbsSessionCtx [ 3 ] ;	 
 } 
 CiPsRptMbsSessionCtx;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsNrTaiTag 
 {	 
 UINT16 mcc ;	 
 UINT16 mnc ;	 
 UINT32 tac ;	 
 } 
 CiPsNrTai;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsNrTaiListTag 
 {	 
 UINT8 numOfNrTai ;	 
 CiPsNrTai nrTai [ 16 ] ;	 
 } 
 CiPsNrTaiList;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsNrCgiTag 
 {	 
 UINT8 cgiData [ 5 ] ;	 
 CiBoolean plmnPresent ;	 
 CiPsNetworkId plmn ;	 
 } 
 CiPsNrCgi;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsNrCgiListTag 
 {	 
 UINT8 numOfNrCgi ;	 
 CiPsNrCgi nrCgi [ 5 ] ;	 
 } 
 CiPsNrCgiList;

//ICAT EXPORTED STRUCT 
 typedef struct CiMbsKeyDomainIDTag 
 {	 
 UINT8 id [ 3 ] ;	 
 } 
 CiMbsKeyDomainID;

//ICAT EXPORTED STRUCT 
 typedef struct CiMbsMskIdTag 
 {	 
 UINT8 id [ 4 ] ;	 
 } 
 CiMbsMskId;

//ICAT EXPORTED STRUCT 
 typedef struct CiMbsMskTag 
 {	 
 UINT8 id [ 16 ] ;	 
 } 
 CiMbsMsk;

//ICAT EXPORTED STRUCT 
 typedef struct CiMbsMtkIdTag 
 {	 
 UINT8 id [ 2 ] ;	 
 } 
 CiMbsMtkId;

//ICAT EXPORTED STRUCT 
 typedef struct CiMbsMtkTag 
 {	 
 UINT8 id [ 16 ] ;	 
 } 
 CiMbsMtk;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsMbsSecurityKeysSetTag 
 {	 
 CiMbsKeyDomainID keyDomainID ;	 
 CiMbsMskId mskId ;	 
 CiMbsMsk msk ;	 
 CiMbsMtkId mtkId ;	 
 CiMbsMtk mtk ;	 
 } 
 CiPsMbsSecurityKeysSet;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsMbsSecurityInfoTag 
 {	 
 UINT8 numOfMbsSecurityKeysSet ;	 
 CiPsMbsSecurityKeysSet mbsSecurityKeysSet [ 6 ] ;	 
 } 
 CiPsMbsSecurityInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsMbsSessionDynParasTag 
 {	 
 UINT8 cid ;	 
 CiPsMbsTmgi tmgi ;	 
 CiPsNrTaiList taiList ;	 
 CiPsNrCgiList cgiList ;	 
 CiPsPdpIpAddr srcAddr ;	 
 CiPsPdpIpAddr dstAddr ;	 
 CiBoolean startTimePresent ;	 
 CiPsMbsTime startTime ;	 
 CiBoolean backOffTimerPresent ;	 
 UINT8 backOffTimer ;	 
 CiPsMbsSecurityInfo mbsSecurityInfo ;	 
 } 
 CiPsMbsSessionDynParas;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsRptMbsSessionDynParasTag 
 {	 
 UINT8 numOfMbsSession ;	 
 CiPsMbsSessionDynParas mbsSessionDynParas [ 3 ] ;	 
 } 
 CiPsRptMbsSessionDynParas;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsMbsSessionStatusTag 
 {	 
 UINT8 cid ;	 
 CiPsMbsTmgi tmgi ;	 
 CiPsMbsDecision decision ;	 
 CiPsMbsRejectCause cause ;	 
 CiPsNrTaiList taiList ;	 
 CiPsNrCgiList cgiList ;	 
 CiPsPdpIpAddr srcAddr ;	 
 CiPsPdpIpAddr dstAddr ;	 
 CiBoolean startTimePresent ;	 
 CiPsMbsTime startTime ;	 
 CiBoolean backOffTimerPresent ;	 
 UINT8 backOffTimer ;	 
 CiPsMbsSecurityInfo mbsSecurityInfo ;	 
 } 
 CiPsMbsSessionStatus;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsRptMbsSessionStatusTag 
 {	 
 UINT8 numOfMbsSession ;	 
 CiPsMbsSessionStatus mbsSessionStatus [ 3 ] ;	 
 } 
 CiPsRptMbsSessionStatus;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsRptMbsSessionStatusReportTag 
 {	 
 CiPsMbsSessionStatusReportType reportType ;	 
 } 
 CiPsRptMbsSessionStatusReport;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsRptMbsSessionActiveCidsTag 
 {	 
 UINT8 numOfCids ;	 
 UINT8 cid [ 3 ] ;	 
 } 
 CiPsRptMbsSessionActiveCids;

//ICAT EXPORTED UNION 
 typedef union CiPsRptCommonParasTag 
 {	 
 CiPsRptMbsSessionCtx mbsSessionCtxRpt ;	 
 CiPsRptMbsSessionDynParas mbsSessionDynParas ;	 
 CiPsRptMbsSessionActiveCids mbsSessionActiveCids ;	 
 CiPsRptMbsSessionStatus mbsSessionStatus ;	 
 CiPsRptMbsSessionStatusReport mbsSessionStatusReport ;	 
 } 
 CiPsRptCommonParas;

//ICAT EXPORTED ENUM 
 typedef enum _CiPsRptParasTypeTag 
 {	 
 CI_PS_RPT_PARAS_NULL ,	 
	 
 /* Added by Daniel for CQ00146880 , begin */	 
 CI_PS_RPT_MBS_SESSION_CONTEXT ,	 
 CI_PS_RPT_MBS_SESSION_DYN_PARAS ,	 
 CI_PS_RPT_MBS_SESSION_DYN_ACT_PARAS ,	 
 CI_PS_RPT_MBS_SESSION_STATUS ,	 
 CI_PS_RPT_MBS_SESSION_STATUS_REPORT ,	 
 /* Added by Daniel for CQ00146880 , end */	 
	 
 NUM_OF_CI_PS_RPT_PARAS_TYPE	 
 } 
 _CiPsRptParasType;

typedef UINT8 CiPsRptParasType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimParasReportIndTag 
 {	 
 CiPsRptParasType type ;	 
 /* Modified by Daniel for CQ00146880 , begin */	 
	 
	 
	 
 UINT8 reserved [ 3 ] ;	 
	 
	 
	 
 CiPsRptCommonParas paras ;	 
 /* Modified by Daniel for CQ00146880 , end */	 
 } 
 CiPsPrimParasReportInd;

//ICAT EXPORTED ENUM 
 typedef enum CiPsSmsOverNasType_enum 
 {	 
 CI_PS_SMS_OVER_NAS_ENABLE = 0 , /** <triggers the UE to request the use of SMS over NAS in 5 GS */	 
 CI_PS_SMS_OVER_NAS_DISABLE = 1 , /** <triggers the UE to request stopping the use of SMS over NAS in 5 GS */	 
	 
 CI_PS_SMS_OVER_NAS_NUM	 
 } _CiPsSmsOverNasType;

typedef UINT8 CiPsSmsOverNasType ;
//ICAT EXPORTED ENUM 
 typedef enum CiPsSmsOverNasReportOption_enum 
 {	 
 CI_PS_SMS_OVER_NAS_REPORT_NO_CHANGE = 0 , /**< no change in current setting of <n> */	 
 CI_PS_SMS_OVER_NAS_REPORT_DISABLE , /**< disable unsolicited result code */	 
 CI_PS_SMS_OVER_NAS_REPORT_ENABLE , /**< enable unsolicited result code */	 
	 
 CI_PS_NUM_OVER_NAS_REPORT_REPORTT_OPTIONS /**< Number of options defined */	 
 } _CiPsSmsOverNasRePortOption;

typedef UINT8 CiPsSmsOverNasReportOption ;
//ICAT EXPORTED ENUM 
 typedef enum CiPsSmsOverNasAvailableStatus_enum 
 {	 
 CI_PS_SMS_OVER_NAS_AVAILABLE_UNKNOWN = 0 , /**< SMS over NAS in 5 GS availability status for the UE is unknown */	 
 CI_PS_SMS_OVER_NAS_NOT_AVAILABLE , /**< SMS over NAS in 5 GS is not available in the network for the UE */	 
 CI_PS_SMS_OVER_NAS_AVAILABLE , /**< SMS over NAS in 5 GS is available in the network for the UE */	 
	 
 CI_PS_NUM_SMS_OVER_NAS_AVAILABLE_STATUS /**< Number of the SMS over NAS in 5 GS availability status */	 
 } _CiPsSmsOverNasAvailableStatus;

typedef UINT8 CiPsSmsOverNasAvailableStatus ;
//ICAT EXPORTED ENUM 
 typedef enum CiPsSmsOverNasAllowedStatus_enum 
 {	 
 CI_PS_SMS_OVER_NAS_ALLOWED_UNKNOWN = 0 , /**< SMS over NAS in 5 GS allowed status for the UE is unknown */	 
 CI_PS_SMS_OVER_NAS_NOT_ALLOWED , /**< UE is not allowed by the network to use SMS over NAS in 5 GS */	 
 CI_PS_SMS_OVER_NAS_ALLOWED , /**< UE is allowed by the network to use SMS over NAS in 5 GS */	 
	 
 CI_PS_NUM_SMS_OVER_NAS_ALLOWED_STATUS /**< Number of the SMS over NAS in 5 GS allowed status */	 
 } _CiPsSmsOverNasAllowedStatus;

typedef UINT8 CiPsSmsOverNasAllowedStatus ;
//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetSmsOverNasReq_struct 
 {	 
 CiPsSmsOverNasReportOption option ;	 
 CiPsSmsOverNasType mode ;	 
 } 
 CiPsPrimSetSmsOverNasReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSetSmsOverNasCnf_struct 
 {	 
 CiPsRc rc ; /**< Result code. \sa CiPsRc */	 
 } 
 CiPsPrimSetSmsOverNasCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPsPrimSendSmsOverNasInd_struct 
 {	 
 CiPsSmsOverNasAvailableStatus available ;	 
 CiPsSmsOverNasAllowedStatus allowed ;	 
 } 
 CiPsPrimSendSmsOverNasInd;

typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef unsigned short WORD ;
typedef int HANDLE ;
typedef HANDLE* LPHANDLE ;
typedef unsigned char* PUINT8 ;
typedef long LONG ;
typedef char* LPCTSTR ;
typedef char* LPTSTR ;
typedef void* LPVOID ;
typedef unsigned int* LPDWORD ;
typedef unsigned int* PDWORD ;
typedef unsigned int* PUINT32 ;
typedef unsigned int UINT ;
typedef INT16 *PINT16 ;
typedef UINT16 *PUINT16 ;
typedef INT8 *PINT8 ;
typedef UINT8 *PUINT8 ;
typedef int utlReturnCode_T , *utlReturnCode_P ;
typedef const utlReturnCode_T *utlReturnCode_P2c ;
typedef unsigned int utlDataId_T , *utlDataId_P ;
typedef unsigned int size_t ;
typedef int ssize_t ;
typedef const utlDataId_T *utlDataId_P2c ;
typedef const utlLinkedListNode_T *utlLinkedListNode_P2c ;
typedef unsigned int utlNodeCount_T ;
typedef const utlLinkedList_T *utlLinkedList_P2c ;
typedef int utlSecond_T ;
typedef int utlNanosecond_T ;
typedef const utlRelativeTime_T *utlRelativeTime_P2c ;
typedef const utlAbsoluteTime_T *utlAbsoluteTime_P2c ;
typedef const utlVString_T *utlVString_P2c ;
typedef signed long utlTimerId_T ;
typedef unsigned long utlTimeOutCount_T , *utlTimeOutCount_P ;
typedef utlReturnCode_T ( *utlTimerFunction_P ) ( const utlTimerId_T id ,
 const utlTimeOutCount_T time_out_count ,
 void *arg_p ,
 const utlAbsoluteTime_P2c curr_time_p ) ;
typedef unsigned int utlMutexAttributes_T , *utlMutexAttributes_P ;
typedef unsigned int utlSemaphoreAttributes_T , *utlSemaphoreAttributes_P ;
typedef int utlStateMachineStateId_T , *utlStateMachineStateId_P ;
typedef int utlStateMachineEventId_T , *utlStateMachineEventId_P ;
typedef utlReturnCode_T ( *utlStateMachineStateFunction_P ) ( const utlStateMachine_P state_machine_p ,
 const utlStateMachineStateId_T state ,
 const utlAbsoluteTime_P2c curr_time_p ,
 void *arg_p ) ;
typedef utlReturnCode_T ( *utlStateMachineEventFunction_P ) ( const utlStateMachine_P state_machine_p ,
 const utlStateMachineStateId_T state ,
 const utlStateMachineEventId_T event ,
 const utlAbsoluteTime_P2c curr_time_p ,
 void *arg_p ,
 va_list va_arg_p ) ;
typedef const utlStateMachineEvent_T *utlStateMachineEvent_P2c ;
typedef const utlStateMachineState_T *utlStateMachineState_P2c ;
typedef unsigned int utlStateMachineFlags_T ;
typedef const utlStateMachine_T *utlStateMachine_P2c ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MAT_MSCLASS ,	 
 MAT_AOPS ,	 
 MAT_AOPSCFG ,	 
 MAT_CLAC ,	 
 MAT_E ,	 
 MAT_I ,	 
 MAT_L ,	 
 MAT_M ,	 
 MAT_O ,	 
 MAT_P ,	 
 MAT_Q ,	 
 MAT_T ,	 
 MAT_V ,	 
 MAT_X ,	 
 MAT_Z ,	 
 MAT_ampC ,	 
 MAT_ampD ,	 
 MAT_ampF ,	 
 MAT_ampS ,	 
 MAT_ampZ ,	 
 MAT_ampM ,	 
 MAT_ampV ,	 
 MAT_ampW ,	 
 MAT_CGMI ,	 
 MAT_CGMM ,	 
 MAT_CGMR ,	 
 MAT_CGSN ,	 
 MAT_CSCS ,	 
 MAT_CIMI ,	 
 MAT_ASTO ,	 
 MAT_GMI ,	 
 MAT_GMM ,	 
 MAT_GMR ,	 
 MAT_GSN ,	 
 MAT_GOI ,	 
 MAT_GCAP ,	 
 MAT_GCI ,	 
 MAT_IPR ,	 
 MAT_ICF ,	 
 MAT_IFC ,	 
 MAT_IDSR ,	 
 MAT_EXAMPLE ,	 
 MAT_RawAT ,	 
 MAT_A ,	 
 MAT_D ,	 
 MAT_H ,	 
 MAT_CSTA ,	 
 MAT_CMOD ,	 
 MAT_CVMOD ,	 
 MAT_CHUP ,	 
 MAT_CBST ,	 
 MAT_CVHU ,	 
 MAT_CRLP ,	 
 MAT_CEER ,	 
 MAT_CMUT ,	 
 MAT_ECHUPVT ,	 
 MAT_CREG ,	 
 MAT_CIND ,	 
 MAT_COPS ,	 
 MAT_CPOL ,	 
 MAT_CLCK ,	 
 MAT_CPWD ,	 
 MAT_CLIP ,	 
 MAT_CLIR ,	 
 MAT_COLP ,	 
 MAT_COLR ,	 
 MAT_CNAP ,	 
 MAT_CCFC ,	 
 MAT_CCWA ,	 
 MAT_FDNCHECK ,	 
 MAT_CHLD ,	 
 MAT_CAOC ,	 
 MAT_VTS ,	 
 MAT_VTD ,	 
 MAT_CSUEPOLICY ,	 
 MAT_CRUEPOLICY ,	 
 MAT_C5GREG ,	 
 MAT_C5GQOS ,	 
 MAT_C5GNSSAI ,	 
 MAT_C5GPNSSAI ,	 
 MAT_C5GNSSAIRDP ,	 
 MAT_APPSTART ,	 
 MAT_SETUEOSID ,	 
 MAT_UTTEST ,	 
 MAT_C5GCAPA ,	 
 MAT_CWUS ,	 
 MAT_CLADN ,	 
 MAT_CMICO ,	 
 MAT_OVERHEAT ,	 
 MAT_VOLTAGEFREQ ,	 
 MAT_CDNID ,	 
 MAT_CAG ,	 
 MAT_C5GUSMS ,	 
 MAT_LOCALURSP ,	 
 MAT_TSNCTRL ,	 
 MAT_CSSN ,	 
 MAT_CLCC ,	 
 MAT_FCLASS ,	 
 MAT_CDU ,	 
 MAT_dollarVTS ,	 
 MAT_starDIALE ,	 
 MAT_CSCB ,	 
 MAT_starCISCC ,	 
 MAT_starCIIND ,	 
 MAT_starIMSSRV ,	 
 MAT_MORESMS ,	 
 MAT_POCCMD ,	 
 MAT_startECCLIST ,	 
 MAT_starCCIREG ,	 
 MAT_CUSD ,	 
 MAT_PEER ,	 
 MAT_CSQ ,	 
 MAT_starREJCUSE ,	 
 MAT_CMUX ,	 
 MAT_NETDMSG ,	 
 MAT_CSQEX ,	 
 MAT_CPAS ,	 
 MAT_CFUN ,	 
 MAT_starCFUN ,	 
 MAT_CPIN ,	 
 MAT_CPIN2 ,	 
 MAT_EPIN ,	 
 MAT_CPINR ,	 
 MAT_starSIMDETEC ,	 
 MAT_CTZR ,	 
 MAT_CTZU ,	 
 MAT_starCTZR ,	 
 MAT_CPBS ,	 
 MAT_CPBR ,	 
 MAT_CPBW ,	 
 MAT_CPBF ,	 
 MAT_CSIM ,	 
 MAT_CRSM ,	 
 MAT_CGLA ,	 
 MAT_CRLA ,	 
 MAT_CCHO ,	 
 MAT_CCHC ,	 
 MAT_MSTK ,	 
 MAT_starEUICC ,	 
 MAT_CACM ,	 
 MAT_CAMM ,	 
 MAT_CCWE ,	 
 MAT_ADMINDATA ,	 
 MAT_CGREG ,	 
 MAT_starREGOPT ,	 
 MAT_CGATT ,	 
 MAT_CGACT ,	 
 MAT_CGDATA ,	 
 MAT_CGDCONT ,	 
 MAT_CGDSCONT ,	 
 MAT_CGQMIN ,	 
 MAT_CGQREQ ,	 
 MAT_CGEQREQ ,	 
 MAT_CGEQMIN ,	 
 MAT_GETIP ,	 
 MAT_starTGSINK ,	 
 MAT_CGSEND ,	 
 MAT_starICSSINK ,	 
 MAT_starAUTHReq ,	 
 MAT_starCHAPAUTH ,	 
 MAT_CMGF ,	 
 MAT_starCMGF ,	 
 MAT_LKSMSSTA ,	 
 MAT_CMSS ,	 
 MAT_CMGS ,	 
 MAT_CMGR ,	 
 MAT_CMGW ,	 
 MAT_CSCA ,	 
 MAT_CNMI ,	 
 MAT_CGSMS ,	 
 MAT_CMMS ,	 
 MAT_CMGD ,	 
 MAT_CMGC ,	 
 MAT_CMGL ,	 
 MAT_CSMS ,	 
 MAT_CPMS ,	 
 MAT_CNMA ,	 
 MAT_CSMP ,	 
 MAT_CSDH ,	 
 MAT_CSAS ,	 
 MAT_CRES ,	 
 MAT_CPNER ,	 
 MAT_CGCI ,	 
 MAT_CGOI ,	 
 MAT_VDUMP ,	 
 MAT_VPDUS ,	 
 MAT_VHDL ,	 
 MAT_VECHO ,	 
 MAT_ATDB ,	 
 MAT_CPUC ,	 
 MAT_CRC ,	 
 MAT_CMEE ,	 
 MAT_CDIP ,	 
 MAT_CPLS ,	 
 MAT_CGCMOD ,	 
 MAT_CNUM ,	 
 MAT_DS ,	 
 MAT_CGTFT ,	 
 MAT_starBAND ,	 
 MAT_starBANDIND ,	 
 MAT_starBANDRD ,	 
 MAT_starCLCK ,	 
 MAT_starMEPCG ,	 
 MAT_starENVSIM ,	 
 MAT_starCNMA ,	 
 MAT_starRSTMEMFULL ,	 
 MAT_starPOWERIND ,	 
 MAT_starFASTDORM ,	 
 MAT_starCellLock ,	 
 MAT_EEMOPT ,	 
 MAT_EEMGINFO ,	 
 MAT_ERGA ,	 
 MAT_ERTCA ,	 
 MAT_starCam_I2C ,	 
 MAT_starISP_REG ,	 
 MAT_starCam_rawdump ,	 
 MAT_starFILETEST ,	 
 MAT_starMRD_CDF ,	 
 MAT_starMRD_IMEI ,	 
 MAT_starMRD_MEP ,	 
 MAT_starMRD_CalInfo ,	 
 MAT_starMRD_MEPPLMN ,	 
 MAT_starMRD_SN ,	 
 MAT_starMRD_ITEM ,	 
 MAT_starMRD_ADC ,	 
 MAT_starMRD_RTPADC ,	 
 // #ifdef AT_PRODUCTION_CMNDS	 
 MAT_starGSMTR ,	 
 // #endif	 
 MAT_starCGSN ,	 
 MAT_starHTCCTO ,	 
 MAT_CMEMFULL ,	 
 MAT_starEHSDPA ,	 
 MAT_TPCN ,	 
 MAT_FWDB ,	 
 MAT_starFDY ,	 
 MAT_xorSYSINFO ,	 
 MAT_starCPBC ,	 
 MAT_starFDNBYPASS ,	 
 MAT_starCSCB ,	 
 MAT_starCBMCS ,	 
 MAT_starNASCHK ,	 
 MAT_CGEQOS ,	 
 MAT_CEREG ,	 
 MAT_CGCONTRDP ,	 
 MAT_CGSCONTRDP ,	 
 MAT_CGTFTRDP ,	 
 MAT_CGEQOSRDP ,	 
 MAT_CGEREP ,	 
 MAT_CEMODE ,	 
 MAT_CGPADDR ,	 
 MAT_xorCACAP ,	 
 MAT_CGCLASS ,	 
 MAT_CESQ ,	 
 MAT_BGLTEPLMN ,	 
 MAT_STARCGDFAUTH ,	 
 MAT_dollarMYMINISYS ,	 
 MAT_dollarMYFOTA ,	 
 MAT_VZWRSRP ,	 
 MAT_VZWRSRQ ,	 
 MAT_starCGDFLT ,	 
 MAT_STARNETACT ,	 
 MAT_STARNETREF ,	 
 MAT_STARNETDNS ,	 
 MAT_STARNETIF ,	 
 MAT_STARNETIFCM ,	 
 MAT_STARMPSAPN ,	 
 MAT_starMODEMRESET ,	 
 MAT_starVZWTESTAPP ,	 
 MAT_VZWAPNE ,	 
 MAT_COPN ,	 
 MAT_starGATR ,	 
 MAT_starGRIP ,	 
 MAT_playMP3 ,	 
 MAT_starCGMR ,	 
 MAT_starCOMCFG ,	 
 MAT_starRFTEMP ,	 
 MAT_startRFTEMPEX ,	 
 MAT_starSOCTEMP ,	 
 MAT_TEMPTEST ,	 
 MAT_BANSELCT ,	 
 MAT_SYSSLEEP ,	 
 MAT_starCGATT ,	 
 MAT_CGPIAF ,	 
 MAT_CIREG ,	 
 MAT_starUSBT ,	 
 MAT_starLTECOEX ,	 
 MAT_LTEPOWER ,	 
 MAT_LTETR ,	 
 MAT_COMMTR ,	 
 MAT_starCSQ ,	 
 MAT_MAXPOWER ,	 
 MAT_SIMDETEC ,	 
 MAT_CPLMNS ,	 
 MAT_WS46 ,	 
 MAT_starCELL ,	 
 MAT_CISRVCC ,	 
 MAT_CEVDP ,	 
 MAT_CEUS ,	 
 /* add for BT SAP */	 
 MAT_BTSTATR ,	 
 MAT_starWBAMR ,	 
 MAT_CNMPSD ,	 
 MAT_starREGMODE ,	 
 MAT_starIMLCONFIG ,	 
 MAT_CCLK ,	 
 MAT_starURSLCT ,	 
 MAT_starCBRAT ,	 
 MAT_starSECCAP ,	 
 MAT_starLTEBAND ,	 
 MAT_starPSTHRESHOLD ,	 
 MAT_LPNWUL ,	 
 MAT_LPLOCVR ,	 
 MAT_LPECID ,	 
 MAT_LPOTDOAABORT ,	 
 MAT_LPOTDOAREQ ,	 
 MAT_AGPSSET ,	 
 MAT_POSFUN ,	 
 MAT_L1DEBUG ,	 
 MAT_DSPINFO ,	 
 MAT_BLACKCELL ,	 
 MAT_CSCO ,	 
 MAT_CHIPSET ,	 
 MAT_FWOPT ,	 
 MAT_CIREP ,	 
 MAT_OPERCFG ,	 
 MAT_starPSDC ,	 
 MAT_CEN ,	 
 MAT_CNEM ,	 
 MAT_CAVIMS ,	 
 MAT_CASIMS ,	 
 MAT_CMMIVT ,	 
 MAT_CPSMS ,	 
 MAT_CEDRXS ,	 
 MAT_CEDRXRDP ,	 
 MAT_CCIOTOPT ,	 
 MAT_CRCES ,	 
 MAT_CSCON ,	 
 MAT_CIPCA ,	 
 MAT_CABTSR ,	 
 MAT_CABTRDP ,	 
 MAT_CGAPNRC ,	 
 MAT_MPBK ,	 
 MAT_CSODCP ,	 
 MAT_CRTDCP ,	 
 MAT_LOG ,	 
 MAT_starDIALMODE ,	 
 MAT_starAPNMODE ,	 
 MAT_starAGDCONT ,	 
 MAT_starAGACT ,	 
 MAT_starLWIPCTRL ,	 
 MAT_starNTP ,	 
 MAT_starCGDCONT ,	 
 MAT_starMTU ,	 
 MAT_plusSWITCHSIM ,	 
 MAT_plusDUALSIM ,	 
 MAT_plusBINDSIM ,	 
 MAT_plusCWRITESIM ,	 
 // #ifdef BT_TEST_SUPPORT 20201214 @xiaokeweng force enable as BT / WIFI could be enable / disable in SDK build	 
 MAT_BTTEST ,	 
 // #endif	 
 MAT_GPSINIT ,	 
 MAT_GPSSLEEP ,	 
 MAT_GPSPF ,	 
 MAT_AGNSSGET ,	 
 MAT_AGNSSSET ,	 
 MAT_GPSST ,	 
 MAT_GPSSET ,	 
 MAT_TRUSTNUM ,	 
 MAT_CHKTRUSTNUM ,	 
 MAT_starISIMAID ,	 
 MAT_CSSAC ,	 
 MAT_MEDCR ,	 
 MAT_AGNSSCFG ,	 
 MAT_UNIKEYINFO ,	 
 MAT_UNIKEYINFOM ,	 
 MAT_UNICERTINFO ,	 
 MAT_UNISHCERTINFO ,	 
 MAT_UNIDELCERTINFO ,	 
 MAT_UNIMQTTCONN ,	 
 MAT_UNIMQTTDISCON ,	 
 MAT_UNIMQTTSTATE ,	 
 MAT_UNIMQTTSUB ,	 
 MAT_UNIMQTTPUB ,	 
 MAT_UNIPSMSET ,	 
 MAT_UNIDMPAPNSET ,	 
 MAT_UNIDMPNETLOG ,	 
 MAT_MIPLMD ,	 
 MAT_STARREADVER ,	 
 MAT_STARREADCPUUID ,	 
 MAT_RESET ,	 
 MAT_RSTSET ,	 
 MAT_DNS ,	 
 MAT_dollarMYPOWEROFF ,	 
 MAT_dollarMYSOCKETLED ,	 
 MAT_dollarMYGMR ,	 
 MAT_dollarMYCCID ,	 
 MAT_dollarMYNETURC ,	 
 MAT_dollarMYTYPE ,	 
 MAT_dollarMYNETCON ,	 
 MAT_dollarMYNETACT ,	 
 MAT_dollarMYIPFILTER ,	 
 MAT_dollarMYNETSRV ,	 
 MAT_dollarMYNETOPEN ,	 
 MAT_dollarMYNETREAD ,	 
 MAT_dollarMYNETWRITE ,	 
 MAT_dollarMYNETCLOSE ,	 
 MAT_dollarMYNETACK ,	 
 MAT_dollarMYNETACCEPT ,	 
 MAT_dollarMYNETCREATE ,	 
 MAT_dollarMYFTPOPEN ,	 
 MAT_dollarMYFTPCLOSE ,	 
 MAT_dollarMYFTPSIZE ,	 
 MAT_dollarMYFTPGET ,	 
 MAT_dollarMYFTPPUT ,	 
 MAT_dollarMYBCCH ,	 
 MAT_dollarMYBAND ,	 
 MAT_dollarMYTIMEUPDATE ,	 
 MAT_dollarMYLACID ,	 
 MAT_dollarMYGPSPOS ,	 
 MAT_dollarMYGETKEY ,	 
 MAT_dollarMYSETINFO ,	 
 MAT_dollarMYSYSINFO ,	 
 MAT_dollarMYSYSINFOURC ,	 
 MAT_dollarMYDOWNLOAD ,	 
 MAT_QICSGP ,	 
 MAT_QIACT ,	 
 MAT_QIDEACT ,	 
 MAT_QIOPEN ,	 
 MAT_QICLOSE ,	 
 MAT_QISTATE ,	 
 MAT_QISEND ,	 
 MAT_QIRD ,	 
 MAT_QISENDEX ,	 
 MAT_QISWTMD ,	 
 MAT_QIGETERROR ,	 
 MAT_QISDE ,	 
 MAT_QICFG ,	 
 MAT_QFTPCFG ,	 
 MAT_QFTPOPEN ,	 
 MAT_QFTPCLOSE ,	 
 MAT_QFTPCWD ,	 
 MAT_QFTPPWD ,	 
 MAT_QFTPPUT ,	 
 MAT_QFTPGET ,	 
 MAT_QFTPSIZE ,	 
 MAT_QFTPDEL ,	 
 MAT_QFTPMKDIR ,	 
 MAT_QFTPRMDIR ,	 
 MAT_QFTPMDTM ,	 
 MAT_QFTPRENAME ,	 
 MAT_QFTPLIST ,	 
 MAT_QFTPNLST ,	 
 MAT_QFTPMLSD ,	 
 MAT_QFTPLEN ,	 
 MAT_QFTPSTAT ,	 
 MAT_TCPKEEPALIVE ,	 
 MAT_QSSLCFG ,	 
 MAT_QSSLOPEN ,	 
 MAT_QSSLSEND ,	 
 MAT_QSSLRECV ,	 
 MAT_QSSLCLOSE ,	 
 MAT_QSSLSTATE ,	 
	 
 MAT_NSOCR ,	 
 MAT_NSOST ,	 
 MAT_NSORF ,	 
 MAT_NSOCL ,	 
 MAT_NCDP ,	 
 MAT_NMGS ,	 
 MAT_NMGR ,	 
 MAT_NNMI ,	 
 MAT_NSMI ,	 
 MAT_NQMGR ,	 
 MAT_NQMGS ,	 
 MAT_NRB ,	 
 MAT_NUESTATS ,	 
 MAT_NEARFCN ,	 
 MAT_NPING ,	 
 MAT_NBAND ,	 
 MAT_NLOGLEVEL ,	 
 MAT_NCONFIG ,	 
 MAT_NTSETID ,	 
 MAT_xorHVER ,	 
 MAT_starPROD ,	 
 MAT_NVMFLUSH ,	 
 MAT_starSLT ,	 
 MAT_starPMICREG ,	 
 MAT_starREGRW ,	 
 MAT_starSSGLPC ,	 
 MAT_ZDON ,	 
 MAT_starASRCOPS ,	 
 MAT_starICCID ,	 
 MAT_CEN1 ,	 
 MAT_CEN2 ,	 
 MAT_starSULOGCFG ,	 
 MAT_starWIFICTRL ,	 
 MAT_starSIMPOLL ,	 
 MAT_cellinfo ,	 
 MAT_starEPIN ,	 
 MAT_starVER ,	 
 // #ifdef WIFI_FUNCTION_SUPPOR 20201214 @xiaokeweng force enable as BT / WIFI could be enable / disable in SDK build	 
 MAT_WIFI_CMD ,	 
 // #endif	 
 MAT_Audio_CMD ,	 
 MAT_starMRDBACKUP ,	 
 MAT_starSELECTVSIM ,	 
 MAT_starAVSIM ,	 
 MAT_starMRDWIFIMAC ,	 
 MAT_starMRDBTID ,	 
 MAT_starAUDNVM ,	 
 MAT_starSPN ,	 
 MAT_CMRSS ,	 
 MAT_CMGSS ,	 
 MAT_CMSMS ,	 
 MAT_CMGMS ,	 
 MAT_PACSP ,	 
 MAT_ENVCFG ,	 
 MAT_CUAD ,	 
 MAT_CECALLINSIDE ,	 
 MAT_CECALL ,	 
 MAT_ECALLDATA ,	 
 MAT_ECALLVOICE ,	 
 MAT_ECALLCFG ,	 
 MAT_ECALLONLY ,	 
 MAT_ECALLREG ,	 
 MAT_ECALLONLYSIM ,	 
 MAT_ECALLMSDGEN ,	 
 MAT_ECALLMSD ,	 
 MAT_ECALLPUSH ,	 
 MAT_ECALLMSDCFG ,	 
 MAT_ECALLMEDIAMSD ,	 
 MAT_IMSECALLSUPT ,	 
 MAT_ECALLONLYREG ,	 
 MAT_ECALLOVERIMS ,	 
 MAT_ECALLSMSNUM ,	 
 MAT_ECALLSMS ,	 
 MAT_ECALLMODE ,	 
 MAT_ECALLTIMER ,	 
 MAT_ECALLMUTESPK ,	 
 MAT_AUDGAIN ,	 
 MAT_AUDREC ,	 
 MAT_AUDRECSTOP ,	 
 MAT_AUDPLAY ,	 
 MAT_AUDPLAYSTOP ,	 
 MAT_STARGETIP ,	 
 MAT_ROAMINGDATA ,	 
 MAT_starDNSCFG ,	 
 MAT_DELFBPLMN ,	 
 MAT_COMFEATURE ,	 
 MAT_RPM ,	 
 MAT_CFGRPMSWITCH ,	 
 MAT_CFGRPMPARA ,	 
 MAT_CFGRPMCOUNTER ,	 
 MAT_CFGRPMCLR ,	 
 MAT_CMER ,	 
 MAT_RESENDPARA ,	 
 MAT_CTRSPSTRT ,	 
 MAT_CTRSPGETINFO ,	 
 MAT_CTRSPPROFACT ,	 
 MAT_CTRSPNTFYLEAB ,	 
 MAT_CTRSPSVN ,	 
 MAT_CR ,	 
 MAT_plusSINGLESIM ,	 
 MAT_SIMSLOT ,	 
 MAT_SVWIFI ,	 
	 
 MAT_AICWIFI ,	 
 MAT_SDIOPHASE ,	 
	 
 MAT_QUEC_I ,	 
 MAT_QUEC_CVERSION ,	 
 MAT_QUEC_CSUB ,	 
 MAT_QUEC_EGMR ,	 
 MAT_QUEC_QGMR ,	 
 MAT_QUEC_QWSETMAC ,	 
 MAT_QUEC_AUTODL ,	 
 MAT_QUEC_ADC ,	 
 MAT_QUEC_QSVN ,	 
 MAT_QUEC_QGSN ,	 
 MAT_QUEC_QINF ,	 
	 
	 
 MAT_starBLACKCELL ,	 
 MAT_starAUTOTZ ,	 
 MAT_UNKNOWN ,	 
 MAT_CMD_UNKNOWN = MAT_UNKNOWN ,	 
	 
 /* response type for MAT */	 
 MAT_RSP_UNKNOWN = 1000 ,	 
 MAT_RSP_OK ,	 
 MAT_RSP_ERROR ,	 
 MAT_RSP_CME_ERROR ,	 
 MAT_RSP_CMS_ERROR ,	 
 MAT_RSP_BUSY ,	 
	 
 /* indication type for MAT */	 
 MAT_IND_CONNECT ,	 
 MAT_IND_NO_CARRIER ,	 
 MAT_IND_RING ,	 
 MAT_IND_NO_ANSWER ,	 
 MAT_IND_NO_DIALTONE ,	 
	 
 /* CC Indication */	 
 MAT_IND_CRING ,	 
 MAT_IND_CCCM ,	 
 MAT_IND_CSSU ,	 
 MAT_IND_CSSI ,	 
 MAT_IND_CR ,	 
 MAT_IND_CEI ,	 
	 
 /* DEV Indication */	 
 MAT_IND_SYSCONFIG ,	 
 MAT_IND_EEMGINFOBASIC ,	 
 MAT_IND_EEMGINFOSVC ,	 
 MAT_IND_EEMGINFOPS ,	 
 MAT_IND_EEMGINFONC ,	 
 MAT_IND_EEMGINBFTM ,	 
 MAT_IND_EEMUMTSSVC ,	 
 MAT_IND_EEMUMTSINTRA ,	 
 MAT_IND_EEMUMTSINTER ,	 
 MAT_IND_EEMUMTSINTERRAT ,	 
 MAT_IND_EEMLTESVC ,	 
 MAT_IND_EEMLTEINTRA ,	 
 MAT_IND_EEMLTEINTER ,	 
 MAT_IND_EEMLTEINTERRAT ,	 
 MAT_IND_SNETIND ,	 
 MAT_IND_LPNWDL ,	 
 MAT_IND_LPSTATE ,	 
 MAT_IND_LPMEAST ,	 
 MAT_IND_LPRESET ,	 
 MAT_IND_DIP ,	 
 MAT_IND_LPOTDOAMEAS ,	 
 /* MM Indication */	 
 MAT_IND_CACAP ,	 
 MAT_IND_MODE ,	 
 MAT_IND_COPN ,	 
 MAT_IND_NITZ ,	 
 MAT_IND_MSRI ,	 
 MAT_IND_HOME_ZONE ,	 
	 
 /* MSG Indication */	 
 MAT_IND_MMSG ,	 
 MAT_IND_CMTI ,	 
 MAT_IND_CBM ,	 
 MAT_IND_CDS ,	 
 MAT_IND_CMT ,	 
	 
 /* PB Indication */	 
 MAT_IND_SCPBR ,	 
 MAT_IND_MPBK ,	 
	 
 /* PS Indication */	 
 MAT_IND_CGEQNEG ,	 
 MAT_IND_CGEV ,	 
	 
 /* SIM Indication */	 
 MAT_IND_COTA ,	 
 MAT_IND_REFRESH ,	 
 MAT_IND_SIM_RESET ,	 
 MAT_IND_CARDMODE ,	 
 MAT_IND_SPN ,	 
	 
 /* SS Indication */	 
 MAT_IND_LPLOC ,	 
 MAT_IND_SSRC ,	 
	 
 /* DAT Indication */	 
 MAT_IND_PSSDC ,	 
	 
 /* the change of sim / usim availability status report */	 
 MAT_IND_BTSSTAT ,	 
	 
 MAT_IND_DSAC ,	 
 MAT_IND_ADMINDATA ,	 
 MAT_IND_CIMI ,	 
 MAT_IND_PSLOAD ,	 
 MAT_IND_RBLOOP ,	 
 MAT_IND_CELL ,	 
 MAT_IND_CIREPI ,	 
 MAT_IND_CIREPH ,	 
 MAT_IND_DATASTATUS ,	 
 MAT_IND_CEDRXP ,	 
 MAT_IND_CCIOTOPTI ,	 
 MAT_IND_CABTSRI ,	 
 MAT_IND_CIREGU ,	 
 MAT_IND_AMRCODEC ,	 
 MAT_IND_CNEC_ESM ,	 
 MAT_IND_ATREADY ,	 
 MAT_IND_PLMNLIST ,	 
 MAT_IND_WIFICELLINFO ,	 
 MAT_C5GURSPQRY ,	 
 MAT_Z5GTD ,	 
 MAT_ASRESC ,	 
 MAT_QNWPREFCFG ,	 
	 
 NUM_OF_MAT_CMD	 
 } MATCmdType , MATRspType;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MAT_SVC_0 ,	 
 MAT_SVC_1 ,	 
 MAT_SVC_2 ,	 
 MAT_SVC_3 ,	 
 MAT_SVC_4 ,	 
 MAT_SVC_5 ,	 
 MAT_SVC_6 ,	 
 MAT_SVC_7 ,	 
 MAT_SVC_8 ,	 
 MAT_SVC_9 ,	 
 MAT_SVC_10 ,	 
 MAT_SVC_11 ,	 
 MAT_SVC_12 ,	 
 MAT_SVC_13 ,	 
 MAT_SVC_14 ,	 
 MAT_SVC_15 ,	 
 MAT_SVC_16 ,	 
 MAT_SVC_17 ,	 
 MAT_SVC_18 ,	 
 MAT_SVC_19 ,	 
 MAT_SVC_20 ,	 
 MAT_SVC_21 ,	 
 MAT_SVC_22 ,	 
 MAT_SVC_23 ,	 
 MAT_SVC_24 ,	 
 MAT_SVC_25 ,	 
 MAT_SVC_26 ,	 
 MAT_SVC_27 ,	 
 MAT_SVC_28 ,	 
 MAT_SVC_29 ,	 
 MAT_SVC_30 ,	 
 MAT_SVC_31 ,	 
 MAT_SVC_32 ,	 
 MAT_SVC_33 ,	 
 MAT_SVC_34 ,	 
 MAT_SVC_35 ,	 
 MAT_SVC_36 ,	 
 MAT_SVC_37 ,	 
 MAT_SVC_38 ,	 
 MAT_SVC_39 ,	 
 MAT_SVC_40 ,	 
 MAT_SVC_41 ,	 
 MAT_SVC_42 ,	 
 MAT_SVC_43 ,	 
 MAT_SVC_44 ,	 
 MAT_SVC_45 ,	 
 MAT_SVC_46 ,	 
 MAT_SVC_47 ,	 
 MAT_SVC_48 ,	 
 MAT_SVC_49 ,	 
 MAT_SVC_50 ,	 
 MAT_SVC_51 ,	 
 MAT_SVC_52 ,	 
 MAT_SVC_53 ,	 
 MAT_SVC_54 ,	 
 MAT_SVC_55 ,	 
 MAT_SVC_56 ,	 
 MAT_SVC_57 ,	 
 MAT_SVC_58 ,	 
 MAT_SVC_59 ,	 
 MAT_SVC_60 ,	 
 MAT_SVC_61 ,	 
 MAT_SVC_62 ,	 
 MAT_SVC_63 ,	 
 NUM_OF_MAT_SVC	 
 } MATSvcId;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MAT_SIM_0 ,	 
 MAT_SIM_1 ,	 
 NUM_OF_MAT_SIM	 
 } MATSimId;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 MAT_OP_UNKNOWN ,	 
 MAT_OP_GET , // ? e.g AT+CREG?	 
 MAT_OP_SET , // = e.g AT+CREG= " "	 
 MAT_OP_ACTION , // e.g AT+CPAS	 
 MAT_OP_SYNTAX , // =? e.g AT+CREG=?	 
 MAT_OP_RESERVWED // reserved for future use if needed	 
 } MATOpCode;

typedef int ( *MATConfIndCB ) ( MATSimId sim_id , MATReturnPara *resp , sipc_cmd_info_t*client_tag ) ;
typedef union utlAtDataValue_U {
 unsigned int decimal ;
 unsigned int hexadecimal ;
 unsigned int binary ;
 char *string_p ;
 char *qstring_p ;
 char *dial_string_p ;
 } utlAtDataValue_T , *utlAtDataValue_P ;
typedef const utlAtParameterValue_T *utlAtParameterValue_P2c ;
typedef const utlAtParameter_T *utlAtParameter_P2c ;
typedef const utlAtDceIoConfig_T *utlAtDceIoConfig_P2c ;
typedef const utlAtSoundConfig_T *utlAtSoundConfig_P2c ;
typedef utlReturnCode_T ( *utlAtGetParameterFunction_P ) ( const utlAtParameterOp_T op , const char *command_name_p , const utlAtParameterValue_P2c parameter_values_p , const size_t num_parameters , const char *info_text_p , unsigned int *xid_p , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtSetParameterFunction_P ) ( const utlAtParameterOp_T op , const char *command_name_p , const utlAtParameterValue_P2c parameter_values_p , const size_t num_parameters , const char *info_text_p , unsigned int *xid_p , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtDceIoConfigFunction_P ) ( const utlAtDceIoConfig_P2c dce_io_config_p , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtSoundConfigFunction_P ) ( const utlAtSoundConfig_P2c sound_config_p , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtSParameterFunction_P ) ( const unsigned int parameter_num , const unsigned int parameter_value , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtSaveDialStringFunction_P ) ( const char *location_name_p , const char *dial_string_p , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtRetrieveDialStringFunction_P ) ( const char **location_name_pp , const char **dial_string_pp , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtReplyFunction_P ) ( const char *string_p , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtTxLineDataFunction_P ) ( const unsigned char *octets_p , const size_t n , void *arg_p ) ;
typedef utlReturnCode_T ( *utlAtDriverRequestFunction_P ) ( const utlAtParser_P parser_p , const utlAtDriverRequest_T request , void *arg_p , ... ) ;
typedef utlReturnCode_T ( *utlAtCommandSyntaxFunction_P ) ( const utlAtParameterOp_T op , const char *command_name_p , const utlAtParameterValue_P2c parameter_values_p , const size_t num_parameters , const char *info_text_p , unsigned int *xid_p , void *arg_p ) ;
typedef unsigned int ( *utlAtGetAtcmdTimeoutValueFunction_P ) ( const utlAtCommand_P2c cmd_p , const utlAtAsyncOp_T op ) ;
typedef int ( *utlAtcmdTimeoutErrorFunction_P ) ( unsigned int atHandle ) ;
typedef void ( *utlAtcmdContinuousTimeoutFunction_P ) ( void ) ;
typedef int ( *utlAtParserTriggerFunction_P ) ( const utlAtParser_P parser_p ) ;
typedef void ( *utlSetAutoAnswerDelay_P ) ( void *arg_p , unsigned int delay_seconds ) ;
typedef void ( *utlGetAutoAnswerDelay_P ) ( void *arg_p , unsigned short *delay_seconds ) ;
typedef utlReturnCode_T ( *utlSendToProxy_P ) ( const char *command_name_p , const utlAtParameterOp_T op , const char *parameters_string_p , unsigned int *xid_p , void *arg_p ) ;
typedef unsigned int ( *utlIsProxyReq_P ) ( const char *cmdName , utlAtParameterOp_T cmdOp , unsigned int parserId ) ;
typedef void ( *utlIncProxyTOCounter_P ) ( unsigned int incValue ) ;
typedef const utlAtCommand_T *utlAtCommand_P2c ;
typedef const utlAtDialStringOptions_T *utlAtDialStringOptions_P2c ;
typedef const utlAtAsyncResponse_T *utlAtAsyncResponse_P2c ;
typedef const utlAtAsyncResponses_T *utlAtAsyncResponses_P2c ;
typedef const utlAtParser_T *utlAtParser_P2c ;
typedef int ( at_rsp_cb_type ) ( char *in_str , int sATPInd , char ind_resp ) ;
//ICAT EXPORTED ENUM 
 typedef enum CI_MM_PRIM 
 {	 
 CI_MM_PRIM_GET_NUM_SUBSCRIBER_NUMBERS_REQ = 1 , /**< \brief Requests the number of subscriber number entries in the MSISDN list \details */	 
 CI_MM_PRIM_GET_NUM_SUBSCRIBER_NUMBERS_CNF , /**< \brief Confirms the request to return the number of subscriber number entries in the MSISDN list \details Requires the SIM to be inserted and ready , plus prior PIN1 validation. */	 
 CI_MM_PRIM_GET_SUBSCRIBER_INFO_REQ , /**< \brief Requests subscriber information for a specified entry in the MSISDN list \details This information is stored on the SIM , so for this request to succeed , the SIM must be inserted and ready.	 
 * Access to the MSISDN list requires prior PIN1 ( CHV1 ) validation.	 
 * Use the CI_MM_PRIM_GET_NUM_SUBSCRIBER_NUMBERS_REQ to determine the number of MSISDN list entries. */	 
 CI_MM_PRIM_GET_SUBSCRIBER_INFO_CNF , /**< \brief Confirms the request to return subscriber information for a specified entry in the MSISDN list \details */	 
 CI_MM_PRIM_GET_SUPPORTED_REGRESULT_OPTIONS_REQ , /**< \brief Requests the supported settings for the unsolicited network registration reporting option \details */	 
 CI_MM_PRIM_GET_SUPPORTED_REGRESULT_OPTIONS_CNF , /**< \brief Confirms the request to return the supported settings for the unsolicited network registration reporting option \details There should be no reason for an error result. */	 
 CI_MM_PRIM_GET_REGRESULT_OPTION_REQ , /**< \brief Requests the current reporting option for Unsolicited Network Registration Result Indications \details See CI_MM_PRIM_SET_REGRESULT_OPTION for default information. */	 
 CI_MM_PRIM_GET_REGRESULT_OPTION_CNF , /**< \brief Confirms the request to return the current reporting option for Unsolicited Network Registration Result Indications \details There should be no reason for an error result. */	 
 CI_MM_PRIM_SET_REGRESULT_OPTION_REQ , /**< \brief Request to set the reporting option for Unsolicited Network Registration Result Indications \details Unsolicited Registration Result Indications ( CI_MM_PRIM_REG_RESULT_IND ) are sent ( if enabled ) only if the reported information	 
 * has changed since the last indication.	 
 * CIMM_REGRESULT_STATUS is the default reporting option. */	 
 CI_MM_PRIM_SET_REGRESULT_OPTION_CNF = 10 , /**< \brief Confirms a request to set the reporting option for Unsolicited Network Registration Result Indications \details */	 
 CI_MM_PRIM_REGRESULT_IND , /**< \brief Indicates the Unsolicited Network Registration Result \details Receipt of this indication ( and the information it contains ) can be configured by the	 
 * CI_MM_PRIM_SET_REGRESULT_OPTION_REQ request.	 
 * If this indication is enabled , the current registration status ( if available ) is reported.	 
 * As a configuration option , current cell information ( if available ) can also be included.	 
 * This information can also be requested at any time , using the CI_CC_PRIM_GET_REGRESULT_INFO_REQ request.	 
 * No explicit response is required. */	 
 CI_MM_PRIM_GET_REGRESULT_INFO_REQ , /**< \brief Requests the most recent registration result information \details See CI_MM_PRIM_SET_REGRESULT_OPTION for default information. */	 
 CI_MM_PRIM_GET_REGRESULT_INFO_CNF , /**< \brief Confirms the request to return the most recent registration result information \details Use the CI_MM_PRIM_GET_REGRESULT_OPTION_REQ request to get the current registration result reporting option.	 
 * This option setting may affect the availability of registration result information.	 
 * The current registration status and location information ( if available ) are included. */	 
 CI_MM_PRIM_GET_SUPPORTED_ID_FORMATS_REQ , /**< \brief Requests a list of supported format indicators for the network / operator ID information	 
 * \details These format indicators are used in the CiMmNetOpIdInfo structure , to indicate how SAC should format the network or operator	 
 * identification information. */	 
 CI_MM_PRIM_GET_SUPPORTED_ID_FORMATS_CNF , /**< \brief Confirms the request and returns a list of supported format indicators for the network / operator ID information	 
 * \details There should be no reason for an error result. */	 
 CI_MM_PRIM_GET_ID_FORMAT_REQ , /**< \brief Requests the currently selected network operator ID format indicator \details The network operator ID format indicator selects which of the supported formats SAC will use to represent the network / operator ID. */	 
 CI_MM_PRIM_GET_ID_FORMAT_CNF , /**< \brief Confirms the request to return the currently selected network operator ID format indicator \details There should be no reason for an error result. */	 
 CI_MM_PRIM_SET_ID_FORMAT_REQ , /**< \brief Requests to set the network / operator ID format indicator \details The network operator ID format indicator selects which of the supported formats SAC will use to represent the network / operator ID	 
 * when reporting network operator information. The default format indicator is set for a numeric network ID. */	 
 CI_MM_PRIM_SET_ID_FORMAT_CNF , /**< \brief Confirms the request to set the network / operator ID format indicator \details */	 
 CI_MM_PRIM_GET_NUM_NETWORK_OPERATORS_REQ = 20 , /**< \brief Requests the number of operators present in the network \details */	 
 CI_MM_PRIM_GET_NUM_NETWORK_OPERATORS_CNF , /**< \brief Confirms the request to get the number of operators present in the network \details */	 
 CI_MM_PRIM_GET_NETWORK_OPERATOR_INFO_REQ , /**< \brief Requests information about a specified operator present in the network \details Use CI_CC_PRIM_GET_NUM_NETWORK_OPERATORS_REQ to determine the number of operators present in the network ,	 
 * if there are any. This number determines the range of values for the Index parameter.	 
 * Index values start at 1 , which indicates the first operator in the network ( usually the home network operator ) . */	 
 CI_MM_PRIM_GET_NETWORK_OPERATOR_INFO_CNF , /**< \brief Confirms the request to get information about a specified operator present in the network \details There may be no operators currently present in the network. In that case , the network operator status information is not	 
 * included.	 
 * Status for network operators present should be indexed in the following order of precedence ( with the highest precedence listed first ) :	 
 * Home network operator ( if present )	 
 * Operators for networks that are referenced in the SIM	 
 * Other network operators that are present	 
 * The network and operator ID information is presented in all supported formats. If information for any of the formats is unavailable , SAC indicates this in the CiMmNetOpStatusInfo structure as follows:	 
 * Unavailable operator ID has its Length field set to zero.	 
 * Unavailable network ID has its fields set to CIMM_COUNTRYCODE_NONE and CIMM_NETWORKCODE_NONE. */	 
 CI_MM_PRIM_GET_NUM_PREFERRED_OPERATORS_REQ , /**< \brief Requests the number of entries in the preferred network operators list \details The preferred network operators list is stored on the SIM in the EFPLMNSel file.	 
 * The maximum number of entries in the EFPLMNSel file is specified when the SIM is provisioned , but the file must accommodate at least	 
 * 8 PLMN entries. See [ 1 ] for more information. */	 
 CI_MM_PRIM_GET_NUM_PREFERRED_OPERATORS_CNF , /**< \brief Confirms the request to get the number of entries in the preferred network operators list \details If the SIM is not present and ready , SAC sets the NumPref parameter to zero. */	 
 CI_MM_PRIM_GET_PREFERRED_OPERATOR_INFO_REQ , /**< \brief Requests information for a specified entry in the preferred network operators list \details Use CI_CC_PRIM_GET_NUM_PREFERRED_OPERATORS_REQ to determine the number of entries in the preferred network	 
 * operators list. This number determines the range of values for the Index parameter.	 
 * The preferred operator list is stored in the EFPLMNSel file on the SIM , and requires the Card Holder Verification password CHV1	 
 * ( if enabled ) to be established before access to this file is allowed.	 
 * The maximum number of entries in the EFPLMNSel file is specified when the SIM is provisioned , but the file must accommodate at least	 
 * 8 PLMN entries. See [ 1 ] for more information. */	 
 CI_MM_PRIM_GET_PREFERRED_OPERATOR_INFO_CNF , /**< \brief Confirms the request to get information for a specified entry in the preferred network operators list	 
 * \details The network / operator ID information is presented in the default format , or in the format set by the most recent	 
 * CI_CC_PRIM_SET_ID_FORMAT_REQ request. */	 
 CI_MM_PRIM_ADD_PREFERRED_OPERATOR_REQ , /**< \brief Requests a new entry to be added to the preferred network operators list \details Adds a new entry to the end of the Preferred Operators List.	 
 * The Preferred Operators List is stored in the EFPLMNSel file on the SIM , and requires a Card Holder Verification password CHV1	 
 * ( if enabled ) to be established before access to this file is allowed. */	 
 CI_MM_PRIM_ADD_PREFERRED_OPERATOR_CNF , /**< \brief Confirms a request to add a new entry to the preferred network operators list \details The network / operator ID information must be presented in the default format , or in the format set by the most recent CI_CC_PRIM_SET_ID_FORMAT_REQ request.	 
 * If the request fails , the list is unchanged. The maximum number of entries in the EFPLMNSel file is specified when the SIM is	 
 * provisioned , but the file must accommodate at least 8 PLMN entries. See [ 1 ] for more information.	 
 * The number of entries in the list is returned regardless of the success / failure of the request. */	 
 CI_MM_PRIM_DELETE_PREFERRED_OPERATOR_REQ = 30 , /**< \brief Requests an entry to be deleted from the preferred network operators list \details Use CI_CC_PRIM_GET_NUM_PREFERRED_OPERATORS_REQ to determine the number of entries in the preferred network	 
 * operators list. This number determines the range of values for the Index parameter.	 
 * The preferred operator list is stored in the EFPLMNSel file on the SIM , and requires a Card Holder Verification password CHV1	 
 * ( if enabled ) to be verified before access to this file is allowed. */	 
 CI_MM_PRIM_DELETE_PREFERRED_OPERATOR_CNF , /**< \brief Confirms a request to delete an entry from the preferred network operators list \details If the request fails , the list is unchanged.	 
 * The maximum number of entries in the EFPLMNSel file is specified when the SIM is provisioned , but the file must accommodate at	 
 * least 8 PLMN entries. See " Cellular Interface Application Programming Interface " , revision i0.6 , for more information.	 
 * The number of entries in the list is returned regardless of the success / failure of the request. */	 
 CI_MM_PRIM_GET_CURRENT_OPERATOR_INFO_REQ , /**< \brief Requests information about the current network operator ( if there is one ) \details */	 
 CI_MM_PRIM_GET_CURRENT_OPERATOR_INFO_CNF , /**< \brief Confirms the request to get information about the current network operator ( if there is one ) \details */	 
	 
 CI_MM_PRIM_AUTO_REGISTER_REQ , /**< \brief Requests automatic registration \details Uses PLNM lists stored on the SIM , so an installed SIM is required.	 
 * The handset is always in automatic PLMN selection mode , except when a manual registration request is received.	 
 * After completing a manual registration operation , SAC resets the registration mode to automatic. Therefore , the application layer	 
 * does not need to use this request to reset the current registration mode to automatic.	 
 * The PLMN selection mode ( registration mode ) is not saved to NVRAM ; it is always set to automatic mode during SAC initialization. */	 
 CI_MM_PRIM_AUTO_REGISTER_CNF , /**< \brief Confirms a request for automatic registration \details */	 
 CI_MM_PRIM_MANUAL_REGISTER_REQ , /**< \brief Requests manual registration \details The registration result itself is relayed by a CI_MM_PRIM_REGRESULT_IND indication. It can also be retrieved on demand , using	 
 * the CI_MM_PRIM_GET_REGRESULT_INFO_REQ request.	 
 * On successful completion of this request , SAC resets the registration mode to CIMM_REGMODE_AUTOMATIC. */	 
 CI_MM_PRIM_MANUAL_REGISTER_CNF , /**< \brief Confirms a request for manual registration \details The registration result is relayed by CI_MM_PRIM_REGRESULT_IND , if this is enabled. The information can also	 
 * be retrieved on demand , using CI_MM_PRIM_GET_REGRESULT_INFO_REQ.	 
 * On successful completion of this request , SAC resets the current registration mode to automatic. */	 
 CI_MM_PRIM_DEREGISTER_REQ , /**< \brief Requests deregistration \details */	 
 CI_MM_PRIM_DEREGISTER_CNF , /**< \brief Confirms a request for deregistration \details The deregistration result is relayed by CI_MM_PRIM_REGRESULT_IND. It can also be retrieved on demand , using	 
 * CI_MM_PRIM_GET_REGRESULT_INFO_REQ. */	 
 CI_MM_PRIM_GET_SIGQUALITY_IND_CONFIG_REQ = 40 , /**< \brief \details NOT SUPPORTED REMOVE FROM API */	 
 CI_MM_PRIM_GET_SIGQUALITY_IND_CONFIG_CNF , /**< \brief \details NOT SUPPORTED REMOVE FROM API */	 
 CI_MM_PRIM_SET_SIGQUALITY_IND_CONFIG_REQ , /**< \brief Requests the current configuration for unsolicited signal quality indications \details Unsolicited signal quality indications can be configured in one of two ways:	 
 * - Report signal quality information periodically. The time interval is specified in 100 ms units.	 
 * - Report signal quality information when the RSS changes by more than a specified threshold. The threshold is specified in dBm.	 
 * These two configuration options are mutually exclusive. */	 
 CI_MM_PRIM_SET_SIGQUALITY_IND_CONFIG_CNF , /**< \brief Confirms a request to set the current configuration for unsolicited signal quality indications \details */	 
 CI_MM_PRIM_SIGQUALITY_INFO_IND , /**< \brief Indicates the unsolicited signal quality \details This indication can be configured by CI_MM_PRIM_SET_SIGQUALITY_IND_CONFIG_REQ.	 
 * The RSS value is reported in dBm , and should be in the range -113dBm through -51 dBm.	 
 * The bit error rate ( BER ) is reported as an encoded value between 0 and 7 . The upper layers should convert this value to a suitable	 
 * BER representation.	 
 * No explicit response is required. */	 
	 
 /*Modified by xwzhou for CQ on 8052013 , begin*/	 
 CI_MM_PRIM_EXTENDED_SIGQUALITY_INFO_IND , // add by xwzhou	 
 /*Modified by xwzhou for CQ on 8052013 , end*/	 
 // SCR #1401348	 
 CI_MM_PRIM_ENABLE_NETWORK_MODE_IND_REQ , /**< \brief Requests that network mode indication be enabled or disabled \details The network mode indication ( if enabled ) is sent whenever the current network mode changes.	 
 * By default , the network mode indication is disabled. */	 
 CI_MM_PRIM_ENABLE_NETWORK_MODE_IND_CNF , /**< \brief Confirms a request to enable or disable network mode indication. \details By default , the network mode indication is disabled. */	 
 CI_MM_PRIM_NETWORK_MODE_IND , /**< \brief Indicates the current network mode \details Each of the CiMmNetworkMode parameters indicates the PDP status for their indicated system:	 
 * - gprsActive ( 1 - gprs is active , 0 - gprs is inactive )	 
 * - egprsActive ( 1 - egprs is active , 0 - egprs is inactive )	 
 * - hsdpaActive ( 1 - hsdpa is active , 0 - hsdpa is inactive )	 
 * - hsupaActive ( 1 - hsupa is active , 0 - hsupa is inactive )	 
 * This indication can be enabled or disabled by CI_MM_PRIM_ENABLE_NETWORK_MODE_IND_REQ.	 
 * By default , this indication is disabled.	 
 * No explicit response is required. */	 
 CI_MM_PRIM_GET_NITZ_INFO_REQ , /**< \brief Requests the current network identity and time zone ( NITZ ) information	 
 * \details NITZ information is updated by the protocol stack whenever it changes , for example , when acquiring or re-acquiring network service. */	 
 CI_MM_PRIM_GET_NITZ_INFO_CNF = 50 , /**< \brief Confirms a request for current network identity and time zone ( NITZ ) information \details */	 
 CI_MM_PRIM_NITZ_INFO_IND , /**< \brief Indicates the status of the current network identity and time zone ( NITZ ) information	 
 * \details NITZ information is reported by the protocol stack whenever it changes , for example , when acquiring or re-acquiring network service.	 
 * NITZ indications are enabled by default. */	 
	 
 CI_MM_PRIM_CIPHERING_STATUS_IND , /**< \brief Indicates a ciphering status change \details	 
 * The protocol stack sends a cipher indication signal to the application layer	 
 * specifying the CS and PS ciphering status. SAC captures this signal and sends a	 
 * ' CiMmPrimCipheringStatusInd ' notification.	 
 * The authentication and ciphering procedure is always initiated and controlled by the network.	 
 * The following events trigger a cipher notification:	 
 * - A request by the network to authenticate and / or set the ciphering mode	 
 * - GPRS authenticate confirmation from the SIM	 
 * - Processing the authentication result	 
 * - Failure to release a CS connection	 
 * - Invalidating the GPRS parameters that are stored on the SIM and	 
 * marking the SIM as invalid for GPRS services	 
 * - Receiving a SYNC signal indicating ciphering mode setting or some channel assignment or modification	 
 *	 
 * An additional parameter is needed to indicate if display of	 
 * the ciphering indicator is required. This parameter is provided in the	 
 * OFM bit ( first bit ) of the ' additional information ' entry ( bytes 2 and 3 ) of the EF_AD ( administrative	 
 * data ) SIM / USIM file.	 
 */	 
 CI_MM_PRIM_AIR_INTERFACE_REJECT_CAUSE_IND , /**< \brief Indicates an air interface reject cause code \details	 
 * The protocol stack sends an air interface reject cause code indication due to errors that	 
 * can occur during MM / GMM procedures such as LU / RA update reject , authentication reject , etc.	 
 * These reject codes are intended to enable vendors to give specific visual / audible feedback to the user.	 
 */	 
 /* Michal Bukai - Selection of preferred PLMN list +CPLS - START */	 
 CI_MM_PRIM_SELECT_PREFERRED_PLMN_LIST_REQ , /**< \brief Requests to select the preferred PLMN list	 
 * \details The selected preffered PLMN list will be used when operation on the list is required */	 
 CI_MM_PRIM_SELECT_PREFERRED_PLMN_LIST_CNF , /**< \brief Confirms the request to select a preferred PLMN list */	 
 CI_MM_PRIM_GET_PREFERRED_PLMN_LIST_REQ , /**< \brief Requests to read what is the selected preferred PLMN list */	 
 CI_MM_PRIM_GET_PREFERRED_PLMN_LIST_CNF , /**< \brief Confirms the response and returns the type of the selected preferred PLMN list */	 
 /* Michal Bukai - Selection of preferred PLMN list +CPLS - END */	 
 CI_MM_PRIM_BANDIND_IND , /**< \brief Indicates the current band	 
 * \details Indications are sent when the band changes and band indications are enabled. */	 
 CI_MM_PRIM_SET_BANDIND_REQ , /**< \brief Requests to enable / disable band indications \details */	 
 CI_MM_PRIM_SET_BANDIND_CNF = 60 , /**< \brief Confirms the request to enable / disable band indications \details */	 
 CI_MM_PRIM_GET_BANDIND_REQ , /**< \brief Requests the status of band indications ( enabled / disabled ) and an indication of the current band \details */	 
 CI_MM_PRIM_GET_BANDIND_CNF , /**< \brief Confirms the request and returns the status of band indications ( enabled / disabled ) and an indication of the current band \details */	 
 CI_MM_PRIM_SERVICE_RESTRICTIONS_IND , /**< \brief Indicates if display of PLMN selection menus is allowed	 
 * \details PLMN selection menu contol information is stored in SIM or USIM in bit ' PLMN Mode ' of file EF-CSP ( see CPHS Version 4.200000 ) .	 
 * On power up the application should assume that display of PLMN selection menus is not allowed.	 
 * This indication is sent on power if display of PLMN selection menus is allowed and whenever there is a change of this bit using OTA reprogramming.	 
 */	 
	 
 /* ADD NEW COMMON PRIMITIVES HERE , BEFORE ' CI_MM_PRIM_LAST_COMMON_PRIM ' */	 
 // Michal Bukai - HOMEZONE support	 
 CI_MM_PRIM_HOMEZONE_IND , /**< \brief Indicates a change in HomeZone indication status \details */	 
 /*Michal Bukai - Cell Lock - Start*/	 
 CI_MM_PRIM_CELL_LOCK_REQ , /**< \brief Requests to activate or to deactivate cell lock \details */	 
 CI_MM_PRIM_CELL_LOCK_CNF , /**< \brief Confirms the request and activates or deactivates cell lock \details */	 
 CI_MM_PRIM_CELL_LOCK_IND , /**< \brief Indicates the status of cell lock \details */	 
 /*Michal Bukai - Cell Lock - End*/	 
	 
 CI_MM_PRIM_SET_FAST_DORMANT_CAP_REQ , /**< \brief Requests to enable / disable fast dormancy capability , it will be saved in NVM \details */	 
 CI_MM_PRIM_SET_FAST_DORMANT_CAP_CNF , /**< \brief Confirms the request to enable / disable fast dormancy capability \details */	 
 CI_MM_PRIM_GET_FAST_DORMANT_CAP_REQ = 70 , /**< \brief Requests the capability of fast dormancy ( enabled / disabled ) \details */	 
 CI_MM_PRIM_GET_FAST_DORMANT_CAP_CNF , /**< \brief Confirms the request and returns the capability of fast dormancy ( enabled / disabled ) \details */	 
	 
 CI_MM_PRIM_SET_NAS_INTEGRITY_CHECK_REQ , /**< \brief Requests to enable / disable NAS integrity check , it will be saved in NVM \details */	 
 CI_MM_PRIM_SET_NAS_INTEGRITY_CHECK_CNF , /**< \brief Confirms the request to enable / disable NAS integrity check \details */	 
 CI_MM_PRIM_GET_NAS_INTEGRITY_CHECK_REQ , /**< \brief Requests the configuration of NAS integrity check ( enabled / disabled ) kept in NVM \details */	 
 CI_MM_PRIM_GET_NAS_INTEGRITY_CHECK_CNF , /**< \brief Confirms the request and returns configuration of NAS integrity check ( enabled / disabled ) \details */	 
	 
 CI_MM_PRIM_GET_NUM_LTE_NETWORK_OPERATORS_REQ , /**< \brief Requests the number of operators present in the network \details */	 
 CI_MM_PRIM_GET_NUM_LTE_NETWORK_OPERATORS_CNF , /**< \brief Confirms the request to get the number of operators present in the network \details */	 
 CI_MM_PRIM_GET_LTE_NETWORK_OPERATOR_INFO_REQ , /**< \brief Requests information about a specified operator present in the network \details Use CI_CC_PRIM_GET_NUM_LTE_NETWORK_OPERATORS_REQ to determine the number of operators present in the network ,	 
 * if there are any. This number determines the range of values for the Index parameter.	 
 * Index values start at 1 , which indicates the first operator in the network ( usually the home network operator ) . */	 
 CI_MM_PRIM_GET_LTE_NETWORK_OPERATOR_INFO_CNF , /**< \brief Confirms the request to get information about a specified operator present in the network \details There may be no operators currently present in the network. In that case , the network operator status information is not*/	 
 CI_MM_PRIM_GET_LTE_BACKGROUND_INFO_REQ = 80 , /**< \brief Requests information about the current network operator ( if there is one ) \details */	 
 CI_MM_PRIM_GET_LTE_BACKGROUND_INFO_CNF , /**< \brief Confirms the request to get information about the current network operator ( if there is one ) \details */	 
 CI_MM_PRIM_SET_LTE_BACKGROUND_INFO_REQ , /**< \brief Requests information about the current network operator ( if there is one ) \details */	 
 CI_MM_PRIM_SET_LTE_BACKGROUND_INFO_CNF , /**< \brief Confirms the request to get information about the current network operator ( if there is one ) \details */	 
	 
 CI_MM_PRIM_CS_SERVICE_NOTIFICATION_IND , /**CSFB indication from APEX_MM*/	 
 CI_MM_PRIM_CS_SERVICE_NOTIFICATION_RSP , /**CSFB respond from AP*/	 
	 
 CI_MM_PRIM_DSAC_STATUS_IND , /**< \brief Indicates domain service access status \details */	 
 CI_MM_PRIM_SET_SRVCC_SUPPORT_REQ , /**< \brief Set SRVCC Support of the UE. The network is updated when changing this parameter. \details */	 
 CI_MM_PRIM_SET_SRVCC_SUPPORT_CNF , /**< \brief Confirms the setting of the SRVCC support. \details */	 
 CI_MM_PRIM_GET_SRVCC_SUPPORT_REQ , /**< \brief Get the SRVCC Support status of the UE. \details */	 
 CI_MM_PRIM_GET_SRVCC_SUPPORT_CNF = 90 , /**< \brief Confirms the request to get the SRVCC support status. \details */	 
 CI_MM_PRIM_SET_IMS_NW_REPORT_MODE_REQ , /**< \brief Set command enables or disables reporting of SRVCC handover information and	 
 * of IMS Voice Over PS sessions ( IMSVOPS ) indicator information \details */	 
 CI_MM_PRIM_SET_IMS_NW_REPORT_MODE_CNF , /**< \brief Confirms the setting of the IMS reporting or SRVCC. \details */	 
 CI_MM_PRIM_GET_IMS_NW_REPORT_MODE_REQ , /**< \brief Get the reporting of SRVCC handover information and of IMS Voice Over PS	 
 * sessions ( IMSVOPS ) indicator information \details */	 
 CI_MM_PRIM_GET_IMS_NW_REPORT_MODE_CNF , /**< \brief Confirms the CI_MM_PRIM_GET_IMS_NW_REPORT_MODE_REQ \details */	 
 CI_MM_PRIM_IMSVOPS_IND , /**< \brief IMS Voice Over PS sessions ( IMSVOPS ) supported indication from the network \details */	 
 CI_MM_PRIM_SRVCC_HANDOVER_IND , /**< \brief Reporting of SRVCC handover information indication \details */	 
 CI_MM_PRIM_SET_EMERGENCY_NUMBER_REPORT_MODE_REQ , /**< \brief Set reporting of new emergency numbers received from the network \details */	 
 CI_MM_PRIM_SET_EMERGENCY_NUMBER_REPORT_MODE_CNF , /**< \brief Confirms the request to set reporting of new emergency numbers received from the network. \details */	 
 CI_MM_PRIM_GET_EMERGENCY_NUMBER_REPORT_REQ , /**< \brief Get the reporting status of new emergency numbers received from the network \details */	 
 CI_MM_PRIM_GET_EMERGENCY_NUMBER_REPORT_CNF = 100 , /**< \brief Confirm the request to get the reporting status of new emergency numbers received from the network. \details */	 
 CI_MM_PRIM_EMERGENCY_NUMBER_REPORT_IND , /**< \brief Unsolicited reporting of emergency numbers received from the network	 
 * \details sent if reporting was set with CI_MM_PRIM_SET_EMERGENCY_NUMBER_REPORT_MODE_REQ*/	 
 CI_MM_PRIM_SET_NW_EMERGENCY_BEARER_SERVICES_REQ , /**< \brief Set command enables reporting of changes in the emergency bearer services support indicators \details */	 
 CI_MM_PRIM_SET_NW_EMERGENCY_BEARER_SERVICES_CNF , /**< \brief Confirmation to the setting of reporting of changes in the emergency bearer services support indicators \details */	 
 CI_MM_PRIM_GET_NW_EMERGENCY_BEARER_SERVICES_REQ , /**< \brief Get the current setting of reporting of changes in the emergency bearer services support indicators \details */	 
 CI_MM_PRIM_GET_NW_EMERGENCY_BEARER_SERVICES_CNF , /**< \brief Response to get the current setting of reporting of changes in the emergency bearer services support indicators	 
 * \details The indications emb_Iu_supp and emb_S1_supp are only set to supported when explicitly signalled from the network*/	 
 CI_MM_PRIM_NW_EMERGENCY_BEARER_SERVICES_IU_IND , /**< \brief Unsolicited reporting of changes in the emergency bearer services support	 
 * indicators according to the network feature support information element , see	 
 * 3 GPP TS 24.008000 subclause 10.500000 .5.23 \details */	 
 CI_MM_PRIM_NW_EMERGENCY_BEARER_SERVICES_S1_IND , /**< \brief Unsolicited reporting of changes in the emergency bearer services support	 
 * indicators according to the EPS network feature support information element , see	 
 * 3 GPP TS 24.301000 subclause 9.900000 .3.12A \details */	 
 CI_MM_PRIM_NW_EMERGENCY_BEARER_SERVICES_5G_IND , /**< \brief Unsolicited reporting of changes in the emergency bearer services support	 
 * indicators according to the 5 GS network feature support information element , see	 
 * 3 GPP TS 24.501000 subclause 9.110000 .3.5 \details */	 
 CI_MM_PRIM_GET_SSAC_STATUS_REQ , /**< \brief Get current status of SSAC ( Service Specific Access Control ) related information \details */	 
 CI_MM_PRIM_GET_SSAC_STATUS_CNF = 110 , /**< \brief Confirmation for the request to get SSAC status \details */	 
 CI_MM_PRIM_GET_SIGQUALITY_INFO_REQ , /**< \brief Request signal quality information */	 
 CI_MM_PRIM_GET_SIGQUALITY_INFO_CNF , /**< \brief Reports the signal quality */	 
	 
 CI_MM_PRIM_WB_CELL_LOCK_REQ , /**< \brief Requests to activate or to deactivate WB-GSM band cell lock \details , used by G+W */	 
 CI_MM_PRIM_WB_CELL_LOCK_CNF , /**< \brief Confirms the request and activates or deactivates WB-GSM band cell lock \details */	 
 /*Michal Bukai - cancel PLMN search ( Samsung ) - Start*/	 
 CI_MM_PRIM_CANCEL_MANUAL_PLMN_SEARCH_REQ , /**< \brief Requests to cancel manual PLMN search	 
 * \details The primitive CI_MM_PRIM_CANCEL_MANUAL_PLMN_SEARCH_REQ is used to trigger abort manual PLMN search.	 
 */	 
 CI_MM_PRIM_CANCEL_MANUAL_PLMN_SEARCH_CNF , /**< \brief Confirms the request and stops the manual PLMN search	 
 * \details If the search will be cencelled successfully CI_MM_PRIM_CANCEL_MANUAL_PLMN_SEARCH_CNF with failure result will be returned.	 
 */	 
 /*Michal Bukai - cancel PLMN search ( Samsung ) - End*/	 
	 
 CI_MM_PRIM_TRIGGER_USER_RESELECTION_REQ , /**< \brief Request to trigger user PLMN selection */	 
 CI_MM_PRIM_TRIGGER_USER_RESELECTION_CNF , /**< \brief confirm that trigger user PLMN selection was received */	 
	 
 CI_MM_PRIM_SET_POWER_UP_PLMN_MODE_REQ , /**< \brief Sets the PLMN selection mode at power up	 
 * \details according to 3 GPP TS 23.122000 , section 4.400000 .3.1 switch on reovery from lack of coverage. */	 
 CI_MM_PRIM_SET_POWER_UP_PLMN_MODE_CNF = 120 , /**< \brief confirm the request to set the PLMN selection mode at power up	 
 * \details according to 3 GPP TS 23.122000 , section 4.400000 .3.1 switch on reovery from lack of coverage.*/	 
 CI_MM_PRIM_GET_POWER_UP_PLMN_MODE_REQ , /**< \brief Gets the PLMN selection mode at power up	 
 * \details according to 3 GPP TS 23.122000 , section 4.400000 .3.1 switch on reovery from lack of coverage.*/	 
 CI_MM_PRIM_GET_POWER_UP_PLMN_MODE_CNF , /**< \brief Confirm the request to get the PLMN selection mode at power up	 
 * \details according to 3 GPP TS 23.122000 , section 4.400000 .3.1 switch on reovery from lack of coverage.*/	 
 CI_MM_PRIM_NETWORK_MODE_REQ ,	 
 CI_MM_PRIM_NETWORK_MODE_CNF ,	 
	 
 CI_MM_PRIM_FIRST_SEARCHED_NETWORK_OPERATOR_IND , /**First searched network operator indication from APEX_MM*/	 
 CI_MM_PRIM_FRAT_LIST_ACTION_REQ ,	 
 CI_MM_PRIM_FRAT_LIST_ACTION_CNF ,	 
 CI_MM_PRIM_GET_FRAT_LIST_REQ ,	 
 CI_MM_PRIM_GET_FRAT_LIST_CNF ,	 
	 
 CI_MM_PRIM_CSG_AUTO_SEARCH_REQ = 130 , /**< \brief Request for Automatic camping on the strongest CSG cell. */	 
 CI_MM_PRIM_CSG_AUTO_SEARCH_CNF , /**< \brief Confirm the request for Automatic camping on the strongest CSG cell. */	 
 CI_MM_PRIM_CSG_LIST_SEARCH_REQ , /**< \brief Request for searching all CSG cells. */	 
 CI_MM_PRIM_CSG_LIST_SEARCH_CNF , /**< \brief List of all the CSG which were found. */	 
 CI_MM_PRIM_CSG_SELECT_REQ , /**< \brief Selects CSG ID , as a result the Comm. will try to camp on it */	 
 CI_MM_PRIM_CSG_SELECT_CNF , /**< \brief Result of selecting CSG ID request. */	 
 CI_MM_PRIM_CSG_SEARCH_STOP_REQ , /**< \brief Request to stop CSG Search. */	 
 CI_MM_PRIM_CSG_SEARCH_STOP_CNF , /**< \brief Confirm that the stop request was received. */	 
 CI_MM_PRIM_REGRESULT_EXTENDED_IND , /** < \brief Indicates the Extended ( csg info ) Unsolicited Network Registration Result \details Receipt of this indication ( and the information it contains ) can be configured by the	 
 * CI_MM_PRIM_SET_REGRESULT_OPTION_REQ request.	 
 * If this indication is enabled , the current registration status ( if available ) is reported.	 
 * As a configuration option , current cell information ( if available ) can also be included.	 
 * This information can also be requested at any time , using the CI_CC_PRIM_GET_REGRESULT_INFO_REQ request.*/	 
 CI_MM_PRIM_SET_SECURITY_CAPABILITY_REQ ,	 
 CI_MM_PRIM_SET_SECURITY_CAPABILITY_CNF = 140 ,	 
 CI_MM_PRIM_GET_SECURITY_CAPABILITY_REQ ,	 
 CI_MM_PRIM_GET_SECURITY_CAPABILITY_CNF ,	 
	 
 CI_MM_PRIM_NETWORK_CELL_MAT_INFO_IND ,	 
	 
 CI_MM_PRIM_EMERGENCY_CALL_STATUS_REQ ,	 
 CI_MM_PRIM_EMERGENCY_CALL_STATUS_CNF ,	 
	 
 CI_MM_PRIM_NEW_ATTACH_IND , /** < \brief Indicates that MM is starting a new ATTACH process */ /* Added by liorgo , for CQ00086808 , 8 / 3 / 2015 */	 
 CI_MM_PRIM_JAMMING_DETECTION_REQ , /**< \brief Request to configure jamming detection.*/	 
 CI_MM_PRIM_JAMMING_DETECTION_CNF , /**< \brief confirtm the reuqest to configure jamming detection.*/	 
 CI_MM_PRIM_GET_JAMMING_DETECTION_STATUS_REQ , /**< \brief request to read jamming detection configuration.*/	 
 CI_MM_PRIM_GET_JAMMING_DETECTION_STATUS_CNF = 150 , /**< \brief The configured valued of the jamming detection.*/	 
 CI_MM_PRIM_JAMMING_DETECTION_IND , /**< \brief unsolicited reporting of change in jamming status.*/	 
	 
 CI_MM_PRIM_SET_GPRS_EGPRS_MULTISLOT_CLASS_REQ , /**< \brief Change the GPRS and EGPRS multislot classes.*/	 
 CI_MM_PRIM_SET_GPRS_EGPRS_MULTISLOT_CLASS_CNF , /**< \brief Confirm the request to change GPRS and EGPRS multislot class.*/	 
 CI_MM_PRIM_GET_GPRS_EGPRS_MULTISLOT_CLASS_REQ , /**< \brief Request to read the GPRS and EGPRS multislot classes.*/	 
 CI_MM_PRIM_GET_GPRS_EGPRS_MULTISLOT_CLASS_CNF , /**< \brief The configured valued of GPRS and EGPRS multislot classes.*/	 
 CI_MM_PRIM_GET_DISPLAY_OPERATOR_NAME_REQ , /**< \brief The command displays the name of the network of the requested type. In case the requested informationis not available , the command displays the network name which is most similar to the requested type.*/	 
 CI_MM_PRIM_GET_DISPLAY_OPERATOR_NAME_CNF , /**< \brief A confirmation for the request command , will return the operator name according to the type that was requested.*/	 
 CI_MM_PRIM_ECALLREG_REQ , /**< \brief Set the forced registration status*/	 
 CI_MM_PRIM_ECALLREG_CNF , /**< \brief Confirms the request to set the forced registration status*/	 
 CI_MM_PRIM_RPM_INFO_REQ = 160 ,	 
 CI_MM_PRIM_RPM_INFO_CNF ,	 
 CI_MM_PRIM_RPM_INFO_IND ,	 
 // add by taow 20171124 CQ00108549 begin	 
 CI_MM_PRIM_SET_NETWORK_SELECTION_REQ ,	 
 CI_MM_PRIM_SET_NETWORK_SELECTION_CNF ,	 
 CI_MM_PRIM_GET_NETWORK_SELECTION_REQ ,	 
 CI_MM_PRIM_GET_NETWORK_SELECTION_CNF ,	 
 CI_MM_PRIM_GET_LTE_CA_INFO_REQ ,	 
 CI_MM_PRIM_GET_LTE_CA_INFO_CNF ,	 
 CI_MM_PRIM_GET_OPERATOR_INFO_REQ ,	 
 CI_MM_PRIM_GET_OPERATOR_INFO_CNF = 170 ,	 
 CI_MM_PRIM_OPERATOR_STATUS_IND ,	 
 // add by taow 20171124 CQ00108549 end	 
	 
 /*20190605 add for IMS BEGIN */	 
 CI_MM_PRIM_GET_ASRCURRENT_OPERATOR_INFO_REQ , /**< \brief Requests information about the current network operator ( if there is one ) \details */	 
 CI_MM_PRIM_GET_ASRCURRENT_OPERATOR_INFO_CNF , /**< \brief Confirms the request to get information about the current network operator ( if there is one ) \details */	 
 /*20190605 add for IMS BEGIN*/	 
	 
 CI_MM_PRIM_GET_CELL_LOCK_INFO_REQ ,	 
 CI_MM_PRIM_GET_CELL_LOCK_INFO_CNF ,	 
 CI_MM_PRIM_NETWORK_SEARCH_IND , /*add CQ00114574 by taow 20190419 */	 
 CI_MM_PRIM_GET_NETWORK_REGISTRATION_INFO_REQ ,	 
 CI_MM_PRIM_GET_NETWORK_REGISTRATION_INFO_CNF ,	 
 /*add by taow CQ00125209 20201020 begin*/	 
 CI_MM_PRIM_SET_OOS_PHASE_PERIOD_REQ ,	 
 CI_MM_PRIM_SET_OOS_PHASE_PERIOD_CNF = 180 ,	 
 CI_MM_PRIM_GET_OOS_PHASE_PERIOD_REQ ,	 
 CI_MM_PRIM_GET_OOS_PHASE_PERIOD_CNF ,	 
 /*add by taow CQ00125209 20201020 end*/	 
 /*add by CQ00130201 taow 20210513 begin*/	 
 CI_MM_PRIM_SET_BANDS_SCAN_CONFIG_REQ ,	 
 CI_MM_PRIM_SET_BANDS_SCAN_CONFIG_CNF ,	 
 CI_MM_PRIM_GET_BANDS_SCAN_CONFIG_REQ ,	 
 CI_MM_PRIM_GET_BANDS_SCAN_CONFIG_CNF ,	 
 CI_MM_PRIM_GET_BANDS_SCAN_REQ ,	 
 CI_MM_PRIM_GET_BANDS_SCAN_CNF ,	 
 CI_MM_PRIM_GET_BANDS_SCAN_IND ,	 
 CI_MM_PRIM_ABORT_BANDS_SCAN_REQ = 190 ,	 
 CI_MM_PRIM_ABORT_BANDS_SCAN_CNF ,	 
	 
 /*add by CQ00130201 taow 20210513 end*/	 
 CI_MM_PRIM_NW_ECALL_OVER_IMS_SUPPORT_IND , /**< \brief Unsolicited reporting of changes in eCall over IMS support indicators according to SIB1 --36.331 rel14 and above \details */	 
 /*20220225 with CQ00135513 for IMSECALL for IMSECALL begin*/	 
 CI_MM_PRIM_IMSECALL_REG_REQ ,	 
 CI_MM_PRIM_IMSECALL_REG_CNF ,	 
 /*20220225 with CQ00135513 for IMSECALL for IMSECALL end */	 
 /*Lilei , CQ00134598 , 20220418 , begin*/	 
 CI_MM_PRIM_SET_RPM_REQ ,	 
 CI_MM_PRIM_SET_RPM_CNF ,	 
 /*Lilei , CQ00134598 , 20220418 , end*/	 
 CI_MM_PRIM_EVENT_REPORT_IND ,	 
 // #if defined ( SUPPORT_NR_CAG )	 
 CI_MM_PRIM_CAG_AUTO_SEARCH_REQ , /**< \brief Request for Automatic camping on the strongest CAG cell. */	 
 CI_MM_PRIM_CAG_AUTO_SEARCH_CNF , /**< \brief Confirm the request for Automatic camping on the strongest CAG cell. */	 
 CI_MM_PRIM_CAG_LIST_SEARCH_REQ , /**< \brief Request for searching all CAG cells. */	 
 CI_MM_PRIM_CAG_LIST_SEARCH_CNF , /**< \brief List of all the CAG which were found. */	 
 CI_MM_PRIM_CAG_SELECT_REQ , /**< \brief Selects CAG ID , as a result the Comm. will try to camp on it */	 
 CI_MM_PRIM_CAG_SELECT_CNF , /**< \brief Result of selecting CAG ID request. */	 
 CI_MM_PRIM_CAG_SEARCH_STOP_REQ , /**< \brief Request to stop CAG Search. */	 
 CI_MM_PRIM_CAG_SEARCH_STOP_CNF , /**< \brief Confirm that the stop request was received. */	 
 // #endif	 
	 
 /* Add by Daniel for CQ00145999 , begin */	 
 CI_MM_PRIM_SET_PARAS_REQ ,	 
 CI_MM_PRIM_SET_PARAS_CNF ,	 
 CI_MM_PRIM_GET_PARAS_REQ ,	 
 CI_MM_PRIM_GET_PARAS_CNF ,	 
 CI_MM_PRIM_PARAS_REPORT_IND ,	 
 /* Add by Daniel for CQ00145999 , end */	 
 /*add for new feature to support VSIM with CQ00141543 20230208 BEGIN*/	 
 CI_MM_PRIM_SET_SELECT_VSIM_REQ ,	 
 CI_MM_PRIM_SET_SELECT_VSIM_CNF ,	 
	 
 CI_MM_PRIM_GET_SELECT_VSIM_REQ ,	 
 CI_MM_PRIM_GET_SELECT_VSIM_CNF ,	 
 /*add for new feature to support VSIM with CQ00141543 20230208 END*/	 
 /* ADD NEW COMMON PRIMITIVES HERE , BEFORE ' CI_MM_PRIM_LAST_COMMON_PRIM ' */	 
 /* END OF COMMON PRIMITIVES LIST */	 
 CI_MM_PRIM_LAST_COMMON_PRIM	 
	 
 /* The customer specific extension primitives are added starting from	 
 * CI_MM_PRIM_firstCustPrim = CI_MM_PRIM_LAST_COMMON_PRIM as the first identifier.	 
 * The actual primitive names and IDs are defined in the associated	 
 * ' ci_mm_cust_xxx.h ' file.	 
 */	 
	 
 /* DO NOT ADD ANY MORE PRIMITIVES HERE */	 
	 
 } _CiMmPrim;

//ICAT EXPORTED ENUM 
 typedef enum CIRC_MM 
 {	 
 CIRC_MM_SUCCESS = 0 , /**< Request completed successfully */	 
 CIRC_MM_FAIL , /**< General failure ( catch-all ) */	 
 CIRC_MM_INCOMPLETE_INFO , /**< Incomplete information for request */	 
 CIRC_MM_INVALID_ADDRESS , /**< Invalid address ( phone number ) */	 
 CIRC_MM_NO_SERVICE , /**< No network service */	 
 CIRC_MM_NOT_REGISTERED , /**< Not currently registered */	 
 CIRC_MM_REJECTED , /**< Request rejected by network */	 
 CIRC_MM_TIMEOUT , /**< Request timed out */	 
 CIRC_MM_UNAVAILABLE , /**< Information not available */	 
 CIRC_MM_NO_MORE_ENTRIES , /**< No more entries in list */	 
 CIRC_MM_NO_MORE_ROOM , /**< No more room in list */	 
 CIRC_MM_PLMN_LIST_SIM_NOK , /**< PLMN list SIM is not OK */	 
 CIRC_MM_PLMN_LIST_NOT_FOUND , /**< PLMN list is not found */	 
 CIRC_MM_PLMN_LIST_NOT_ALLOWED , /**< PLMN list is not allowed */	 
 CIRC_MM_PLMN_LIST_MANUAL_NOT_ALLOWED , /* manual selection of */	 
 CIRC_MM_PLMN_LIST_MANUAL_NOT_ALLOWED_IN_DEDICATED_MODE , /* PLMN list is not allowed in dedicated mode*/	 
 CIRC_MM_INVALID_PARAMETER , /**< Generic error - the requested service primitive has invalid parameters */	 
 CIRC_MM_INVALID_REQ , /**< Generic error - the requested service primitive can not be handled at current state */	 
 CIRC_MM_SIM_NOT_READY , /**< Generic error - the requested service primitive fails because SIM is not ready */	 
	 
 CIRC_MM_CANCELLED , /*procedure was cancelled*/	 
	 
 CIRC_MM_NETWORK_NOT_ALLOWED_EMERGENCY_CALLS_ONLY ,	 
	 
 /* This one must always be last in the list! */	 
 CIRC_MM_NUM_RESCODES /**< Number of result codes defined */	 
 } _CiMmResultCode;

typedef UINT16 CiMmResultCode ;
typedef UINT32 CiMmCause ;
typedef UINT32 CiGmmCause ;
typedef UINT32 CiEmmCause ;
typedef UINT32 CiNmmCause ;
//ICAT EXPORTED ENUM 
 typedef enum CIMM_PLMN_SELECTION_POWER_UP_MODE 
 {	 
 CI_MM_PLMN_SELECTION_POWER_UP_MODE_AUTO = 0 , /**< At power up , use auto plmn selection mode*/	 
 CI_MM_PLMN_SELECTION_POWER_UP_MODE_MANUAL , /**< At power up , use auto manual selection mode */	 
 CI_MM_PLMN_SELECTION_POWER_UP_MODE_LAST_USED , /**< At power up , use last used plmn mode before power down*/	 
	 
 /* This one must always be last in the list! */	 
 CI_MM_NUM_PLMN_SELECTION_POWER_UP_MODE	 
 } _CiMmPowerUpPlmnSelectionMode;

//ICAT EXPORTED ENUM 
 typedef enum CIMM_ERROR_CAUSE_TYPE 
 {	 
 CI_MM_ERROR_CAUSE_TYPE_MM , /**< MM Reject code is received during an MM procedure */	 
 CI_MM_ERROR_CAUSE_TYPE_GMM_NON_COMBINED , /**< GMM Reject code is received during a non-combined GMM procedure for GPRS services */	 
 CI_MM_ERROR_CAUSE_TYPE_GMM_COMBINED_NON_GPRS , /**< GMM reject code is received during a combined GMM procedure for non-GPRS services */	 
 CI_MM_ERROR_CAUSE_TYPE_GMM_COMBINED_GPRS , /**< GMM reject code is received during a combined GMM procedure for GPRS and non-GPRS services */	 
 CI_MM_ERROR_CAUSE_TYPE_EMM_NON_COMBINED , /**< EMM reject code is received for a non combined EMM procedure for EPS services */	 
 CI_MM_ERROR_CAUSE_TYPE_EMM_COMBINED_NON_EPS , /**< EMM reject code is received during a combined procedure for non-EPS services */	 
 CI_MM_ERROR_CAUSE_TYPE_EMM_COMBINED_EPS , /**< EMM reject code is received during a combined procedure for EPS and non-EPS services */	 
	 
 /* This one must always be last in the list! */	 
 CIMM_NUM_ERROR_CAUSE_TYPES	 
 } _CiMmErrorCauseType;

typedef UINT8 CiMmErrorCauseType ;
typedef UINT8 CiMmPowerUpPlmnSelectionMode ;
//ICAT EXPORTED ENUM 
 typedef enum CIMM_SERVICE 
 {	 
 CIMM_SERVICE_ASYNC_MODEM = 0 , /**< Asynchronous modem */	 
 CIMM_SERVICE_SYNC_MODEM , /**< Synchronous modem */	 
 CIMM_SERVICE_PAD_ASYNC , /**< PAD access ( asynchronous ) */	 
 CIMM_SERVICE_PACKET_SYNC , /**< Packet access ( synchronous ) */	 
 CIMM_SERVICE_VOICE , /**< Voice */	 
 CIMM_SERVICE_FAX , /**< Fax */	 
	 
 /* This one must always be last in the list! */	 
 CIMM_NUM_SERVICES /**< Number of network services defined */	 
 } _CiMmService;

typedef UINT8 CiMmService ;
//ICAT EXPORTED ENUM 
 typedef enum CIMM_ITC 
 {	 
 CIMM_ITC_3_1_KHZ = 0 , /**< 3.100000 kHz */	 
 CIMM_ITC_UDI , /**< Unrestricted digital information ( UDI ) */	 
	 
 /* This one must always be last in the list! */	 
 CIMM_NUM_ITC /**< Number of ITC indicators defined */	 
 } _CiMmITC;

typedef UINT8 CiMmITC ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmServiceInfo_struct 
 {	 
 CiBoolean Present ; /**< Service information present? \sa CCI API Ref Manual */	 
 CiMmService SvcType ; /**< Associated service type \sa CiMmService */	 
 CiBsTypeSpeed Speed ; /**< Connection speed \sa CCI API Ref Manual */	 
 CiMmITC Itc ; /**< Information transfer capability \sa CiMmITC. */	 
 } CiMmServiceInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmSubscriberInfo_struct 
 {	 
 CiAddressInfo Number ; /**< Subscriber number ( MSISDN ) \sa CCI API Ref Manual */	 
 CiOptNameInfo AlphaTag ; /**< Associated alpha tag ( optional ) \sa CCI API Ref Manual */	 
 CiMmServiceInfo SvcInfo ; /**< Service information ( optional ) \sa CiMmServiceInfo_struct */	 
 } CiMmSubscriberInfo;

//ICAT EXPORTED ENUM 
 typedef enum CIMM_REGRESULT_OPTION 
 {	 
 CIMM_REGRESULT_DISABLE = 0 , /**< Disable reporting */	 
 CIMM_REGRESULT_STATUS , /**< Report registration status only */	 
 CIMM_REGRESULT_CELLINFO , /**< Report status and current cell information */	 
 CIMM_REGRESULT_MORE_DETAIL , /**< Report more detail info: [ , <cause_type> , <reject_cause> ] ] > */	 
 /* This one must always be last in the list! */	 
 CIMM_NUM_REGRESULT_OPTIONS /**< Number of options defined */	 
 } _CiMmRegResultOption;

typedef UINT8 CiMmRegResultOption ;
//ICAT EXPORTED ENUM 
 typedef enum CIMM_REGSTATUS 
 {	 
 CIMM_REGSTATUS_NOT_SEARCHING = 0 ,	 
 /**< Not registered , not searching operators */	 
 CIMM_REGSTATUS_HOME , /**< Registered with home network */	 
 CIMM_REGSTATUS_SEARCHING , /**< Not registered , searching operators */	 
 CIMM_REGSTATUS_DENIED , /**< Registration denied */	 
 CIMM_REGSTATUS_UNKNOWN , /**< Registration status unknown */	 
 CIMM_REGSTATUS_ROAMING , /**< Registered , roaming */	 
 CIMM_REGSTATUS_SMS_ONLY_HOME , /**< registered for " SMS only " , home network ( applicable only when <AcT> indicates E-UTRAN ) */	 
 CIMM_REGSTATUS_SMS_ONLY_ROAMING , /**< registered for " SMS only " , roaming ( applicable only when <AcT> indicates E-UTRAN ) */	 
 CIMM_REGSTATUS_EMERGENCY_ONLY_NOT_USED , /**< attached for emergency bearer services only ( see NOTE 2 ) ( not applicable ) */	 
 CIMM_REGSTATUS_CSFB_NOT_PREFERRED_HOME , /**<registered for " CSFB not preferred " , home network ( applicable only when <AcT> indicates E-UTRAN ) */	 
 CIMM_REGSTATUS_CSFB_NOT_PREFERRED_ROAMING = 10 , /**<registered for " CSFB not preferred " , roaming ( applicable only when <AcT> indicates E-UTRAN ) */	 
 CIMM_REGSTATUS_ACCESS_TO_RLOS ,	 
 CIMM_REGSTATUS_DISASTER_ROAMING_SERVICES ,	 
 CIMM_REGSTATUS_DISASTER_CONDITION = 13 ,	 
	 
 /**< Only emergency services are available , value of self-defined*/	 
 /*Modified by zuohuaxu for CQ00153422 , begin*/	 
	 
 CIMM_REGSTATUS_EMERGENCY_ONLY = 64 , /**< Not registered for emergency only*/	 
 CIMM_REGSTATUS_ECALL_INACTIVE , /**< eCall only when camp on NR / LTE for eCall over IMS */	 
 /*Modified by zuohuaxu for CQ00153422 , end*/	 
 /* This one must always be last in the list! */	 
 CIMM_NUM_REGSTATUS /**< Number of status values defined */	 
 } _CiMmRegStatus;

typedef UINT8 CiMmRegStatus ;
//ICAT EXPORTED ENUM 
 typedef enum CIMM_REGMODE 
 {	 
 CIMM_REGMODE_AUTOMATIC = 0 , /**< Automatic registration request */	 
 CIMM_REGMODE_MANUAL , /**< Manual registration request */	 
 CIMM_REGMODE_DEREGISTER , /**< Deregistration request */	 
 CIMM_REGMODE_MANUAL_AUTO , /**< Manual request , fallback to automatic */	 
	 
 /* This one must always be last in the list! */	 
 CIMM_NUM_REGMODES /**< Number of mode indicators defined */	 
 } _CiMmRegMode;

typedef UINT8 CiMmRegMode ;
//ICAT EXPORTED ENUM 
 typedef enum CIMM_ACT_TECH_MODE 
 {	 
 CI_MM_ACT_GSM = 0 , /**< GSM */	 
 CI_MM_ACT_GSM_COMPACT , /**< Not supported */	 
 CI_MM_ACT_UTRAN , /**< UTRAN */	 
	 
 CI_MM_ACT_GSM_EGPRS , /**< GSM w / EGPRS */	 
 CI_MM_ACT_UTRAN_HSDPA , /**< UTRAN w / HSDPA */	 
 CI_MM_ACT_UTRAN_HSUPA , /**< UTRAN w / HSUPA */	 
 CI_MM_ACT_UTRAN_HSPA , /**< UTRAN w / HSDPA and HSUPA */	 
 CI_MM_ACT_EUTRAN , /**< E-UTRAN */	 
 CI_MM_ACT_EC_GSM_IOT , /**< EC-GSM-Iot */	 
 CI_MM_ACT_EUTRAN_NB , /**< EUTRAN ( NB-S1 ) */	 
	 
 CI_MM_ACT_EUTRAN_TO_5GCN = 10 , /**<ENDC */	 
 CI_MM_ACT_NR_TO_5GCN , /**<NR */	 
 CI_MM_ACT_NGRAN , /**<NGRAN */	 
 CI_MM_ACT_EUTRAN_NR_DUAL_LINK , /**<E-UTRAN / NR duallink */	 
 CI_MM_ACT_SATELLITE_EUTRAN_NB ,	 
 CI_MM_ACT_SATELLITE_EUTRAN_WB ,	 
 CI_MM_ACT_SATELLITE_NGRAN ,	 
	 
 CI_MM_ACT_UTRAN_HSPA_PLUS = 30 , /**< UTRAN w / HSPA+ */	 
	 
 CI_MM_ACT_EUTRAN_PLUS , /*E-UTRAN CA*/	 
 /* Added by taow 20190708 CQ00115423 , begin */	 
 CI_MM_ACT_UTRAN_DC_HSPA , /*DC-HSPA*/	 
 /* Added by taow 20190708 CQ00115423 , end */	 
	 
 CI_MM_NUM_ACT	 
 } _CiMmAccTechMode;

typedef UINT8 CiMmAccTechMode ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmCellInfo_struct 
 {	 
 CiBoolean Present ; /**< Current cell information present? \sa CCI API Ref Manual */	 
 UINT16 LocArea ; /**< Location area code */	 
 UINT32 CellId ; /**< Cell identifier. GSM case: 16 least significant bits , WCDMA case: CellId - 16 least significant bits , RNCID - 12 most significant bits */	 
	 
 CiMmAccTechMode AcT ; /**< Network access technology ( GSM , UTRAN , LTE etc. ) \sa CiMmAccTechMode */	 
 } CiMmCellInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmRegResultInfo_struct 
 {	 
 CiMmRegStatus Status ; /**< Registration status \sa CiMmRegStatus */	 
 CiMmCellInfo CellInfo ; /**< Current cell information ( optional ) \sa CiMmRegStatus */	 
 } CiMmRegResultInfo;

//ICAT EXPORTED ENUM 
 typedef enum CIMM_NETOP_ID_FORMAT 
 {	 
 CIMM_NETOP_ID_FORMAT_ALPHA_LONG = 0 , /**< Operator ID: long alphanumeric */	 
 CIMM_NETOP_ID_FORMAT_ALPHA_SHORT , /**< Operator ID: short alphanumeric */	 
 CIMM_NETOP_ID_FORMAT_NETWORK , /**< Network ID ( numeric ) */	 
	 
 /* This one must always be last in the list! */	 
 CIMM_NUM_NETOP_ID_FORMATS /**< Number of format indicators defined */	 
 } _CiMmNetOpIdFormat;

typedef UINT8 CiMmNetOpIdFormat ;
//ICAT EXPORTED ENUM 
 typedef enum CIMM_NETOP_DIGIT_MNC 
 {	 
 CIMM_NETOP_TWO_DIGIT_MNC = 2 , /*2 digit */	 
 CIMM_NETOP_THREE_DIGIT_MNC , /*3 digit */	 
 /* This one must always be last in the list! */	 
 CIMM_NUM_NETOP_DIGIT_MNC	 
 } _CiMmNetOpDigitMnc;

typedef UINT8 CiMmNetOpDigitMnc ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmNetworkId_struct 
 {	 
 UINT16 CountryCode ; /**< 3 -digit country code */	 
 UINT16 NetworkCode ; /**< 3 -digit network code */	 
 /*Added by xwzhou on 4092014 for CQ58416 , begin*/	 
 CiMmNetOpDigitMnc MncDigit ; /**< MncDigit \sa CiMmNetOpDigitMnc */	 
 /*Added by xwzhou on 4092014 for CQ58416 , end*/	 
 } CiMmNetworkId;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmOperatorId_struct 
 {	 
 UINT8 Length ; /**< Operator ID length ( characters ) */	 
 char Id [ 32 ] ; /**< Operator ID */	 
 } CiMmOperatorId;

//ICAT EXPORTED UNION 
 typedef union CiMmNetOpId_tag 
 {	 
 CiMmNetworkId NetworkId ; /**< Network ID \sa CiMmNetworkId_struct */	 
 CiMmOperatorId OperatorId ; /**< Operator ID \sa CiMmOperatorId_struct */	 
 } CiMmNetOpIdUnion;

//ICAT EXPORTED STRUCT 
 typedef struct CiNetOpIdInfo_struct 
 {	 
 CiBoolean Present ; /**< Indicates if network or operator ID is present \sa CCI API Ref Manual */	 
 CiMmNetOpIdFormat Format ; /**< ID format: network or operator \sa CiMmNetOpIdFormat */	 
 CiMmNetOpIdUnion CiMmNetOpId ; /**< ID \sa CiMmNetOpId_tag*/	 
	 
 CiMmAccTechMode AccTchMode ; /**< Access radio technology ; default is GSM \sa CiMmAccTechMode */	 
 UINT8 Domain ; /**< CS or PS domain , 0 :CS only ; 1 :PS only ; 2 :Combined CS / PS */	 
	 
 } CiMmNetOpIdInfo;

//ICAT EXPORTED ENUM 
 typedef enum CIMM_NETOPSTATUS 
 {	 
 CIMM_NETOP_UNKNOWN = 0 , /**< Operator status unavailable */	 
 CIMM_NETOP_AVAILABLE , /**< Operator is available */	 
 CIMM_NETOP_CURRENT , /**< Current operator */	 
 CIMM_NETOP_FORBIDDEN , /**< Operator is forbidden */	 
	 
 /* This one must always be last in the list! */	 
 CIMM_NUM_NETOPSTATUS /**< Number of status indicators defined */	 
 } _CiMmNetOpStatus;

typedef UINT8 CiMmNetOpStatus ;
typedef INT16 CiMmRssi ;
typedef UINT8 CiMmEncodedBER ;
typedef UINT8 CiMmRsrq ;
typedef UINT8 CiMmRsrp ;
typedef UINT8 CiMmRscp ;
typedef UINT8 CiMmEcno ;
typedef UINT8 CiMmRxlev ;
//ICAT EXPORTED STRUCT 
 typedef struct CiNetOpStatusInfo_struct 
 {	 
 CiMmNetOpStatus Status ; /**< Network operator status \sa CiMmNetOpStatus */	 
 CiMmRssi Rssi ; /**< RSSI value in dBm \sa CiMmRssi */	 
 CiMmOperatorId LongAlphaId ; /**< Long alphanumeric operator ID \sa CiMmOperatorId_struct */	 
 CiMmOperatorId ShortAlphaId ; /**< Short alphanumeric operator ID \sa CiMmOperatorId */	 
 CiMmNetworkId NetworkId ; /**< Network ID information \sa CiMmNetworkId */	 
 CiMmAccTechMode AccTchMode ; /**< Network access technology ( GSM , UTRAN , etc. ) \sa CiMmAccTechMode */	 
 } CiMmNetOpStatusInfo;

//ICAT EXPORTED ENUM 
 typedef enum CIMM_SIGQUAL_OPTIONS 
 {	 
 CIMM_SIGQUAL_OPTION_INTERVAL = 0 , /**< Report periodically by time interval */	 
 CIMM_SIGQUAL_OPTION_THRESHOLD , /**< Report by threshold */	 
 CIMM_SIGQUAL_OPTION_DISABLE , /**< Disable indications */	 
 /*Lilei , CQ00145804 , 20230907 , begin*/	 
 CIMM_SIGQUAL_OPTION_INTERVAL_AND_THRESHOLD , /**< Report by time interval and threshold */	 
 CIMM_SIGQUAL_OPTION_INTERVAL_OR_THRESHOLD , /**< Report by time interval or threshold */	 
 /*Lilei , CQ00145804 , 20230907 , end*/	 
	 
 /* This one must always be last in the list! */	 
 CIMM_NUM_SIGQUAL_OPTIONS /**< Number of status indicators defined */	 
 } _CiMmSigQualOpts;

typedef UINT8 CiMmSigQualOpts ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmSigQualCfg_Tag 
 {	 
 UINT8 Interval ; /**< Time Interval in 100 ms units. 0 means unconfigured or stay unchanged */	 
 UINT8 Threshold ; /**< Threshold in dBm. 0 means unconfigured or stay unchanged */	 
 } CiMmSigQualCfg;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmSigQualityConfig_struct 
 {	 
 CiMmSigQualOpts Option ; /**< Signal quality report type interval or theshold \sa CiMmSigQualOpts */	 
 CiMmSigQualCfg Cfg ; /**< Configuration \sa CiMmSigQualCfg*/	 
 } CiMmSigQualityConfig;

//ICAT EXPORTED ENUM 
 typedef enum CIMM_NETWORK_MODE 
 {	 
 CI_MM_NETWORK_MODE_GSM = 0 , /**< GSM */	 
 CI_MM_NETWORK_MODE_UMTS , /**< UMTS */	 
	 
 CI_MM_NETWORK_MODE_LTE , /**< TD LTE */	 
	 
 CI_MM_NETWORK_MODE_NR , /**< NR */	 
	 
 CI_MM_NETWORK_MODE_DEFAULT ,	 
 CI_MM_NUM_NETWORK_MODES	 
 } _CiMmNetworkMode;

typedef UINT8 CiMmNetworkMode ;
typedef UINT8 CiMmCellLockActMode ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmSigQualityInfo_struct 
 {	 
 CiMmRssi Rssi ; /**< RSSI value in dBm \sa CiMmRssi */	 
 CiMmEncodedBER BER ; /**< Encoded bit error rate \sa CiMmEncodedBER */	 
 CiMmRsrq Rsrq ; /**Report reference signal received quality*/	 
 CiMmNetworkMode Mode ; /**< Network mode \sa CiMmNetworkMode */	 
 CiBoolean IsLtePsOnly ; /**TRUE: lte ps only , set 3 G / 2 G RSSI to default ; FALSE: just update lte RSSI*/	 
 } CiMmSigQualityInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmSigExtendedQualityInfo_struct 
 {	 
 CiMmRxlev Rxlev ;	 
 CiMmEncodedBER Ber ; // bit error rate	 
 CiMmRscp Rscp ;	 
 CiMmEcno Ecno ; // RadioInfo->receiveQuality	 
 CiMmRsrq Rsrq ;	 
 CiMmRsrp Rsrp ;	 
 // add by taow 20150730 begin	 
 UINT8 LteCqi ;	 
 INT8 SINR ;	 
 // add by taow 20150730 end	 
	 
 UINT8 ssRSRQ ; // 0 ~126 , 255 for not known or not detectable	 
 UINT8 ssRSRP ; // 0 ~126 , 255 for not known or not detectable	 
 UINT8 ssSINR ; // 0 ~127 , 255 for not known or not detectable	 
 } 
 CiMmSigExtendedQualityInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmSigNormalQualityInfo_struct 
 {	 
 UINT8 Rssi ;	 
 CiMmEncodedBER Ber ;	 
 } CiMmSigNormalQualityInfo;

//ICAT EXPORTED ENUM 
 typedef enum CIMM_NETWORK_MODE_REPORT 
 {	 
 CI_MM_NETWORK_MODE_REPORT_GSM = 0 , /**< GSM only */	 
 CI_MM_NETWORK_MODE_REPORT_UMTS , /**< 3 G only */	 
 CI_MM_NETWORK_MODE_REPORT_UMTS_HSDPA , /**< 3 G and HSDPA capabilities */	 
 CI_MM_NETWORK_MODE_REPORT_UMTS_HSUPA , /**< 3 G and HSUPA capabilities */	 
 CI_MM_NETWORK_MODE_REPORT_UMTS_HSDPA_HSUPA , /**< 3 G , HSDPA , and HSDPA capabilities */	 
 CI_MM_NETWORK_MODE_REPORT_GSM_EGPRS , /**< GSM , GPRS , and EGPRS capabilities */	 
 CI_MM_NETWORK_MODE_REPORT_GSM_GPRS , /**< GSM and GPRS capabilities */	 
	 
 CI_MM_NETWORK_MODE_REPORT_UMTS_HSPA_PLUS = 8 , /**< 3 G and HSPA+ capabilities */	 
 CI_MM_NETWORK_MODE_REPORT_LTE , /**< TD LTE capabilities */	 
 CI_MM_NETWORK_MODE_REPORT_NR ,	 
 CI_MM_NUM_NETWORK_MODE_REPORTS	 
 } _CiMmNetworkModeReport;

//ICAT EXPORTED ENUM 
 /** \brief UMTS band values */ 
 /** \remarks Common Data Section */ 
 
 typedef enum CI_UMTS_BANDS_TYPE 
 {	 
 CI_BAND_1 , /**< UMTS Band1 */	 
 CI_BAND_2 , /**< UMTS Band2 */	 
 CI_BAND_3 , /**< UMTS Band3 */	 
 CI_BAND_4 , /**< UMTS Band4 */	 
 CI_BAND_5 , /**< UMTS Band5 */	 
 CI_BAND_6 , /**< UMTS Band6 */	 
 CI_BAND_7 , /**< UMTS Band7 */	 
 CI_BAND_8 , /**< UMTS Band8 */	 
 CI_BAND_9 , /**< UMTS Band9 */	 
 CI_BAND_10 , /**< UMTS Band10 */	 
 CI_BAND_11 , /**< UMTS Band11 */	 
 CI_BAND_12 , /**< UMTS Band12 */	 
 CI_BAND_13 , /**< UMTS Band13 */	 
 CI_BAND_14 , /**< UMTS Band14 */	 
 CI_BAND_15 , /**< UMTS Band15 */	 
 CI_BAND_16 , /**< UMTS Band16 */	 
 CI_BAND_17 , /**< UMTS Band17 */	 
 CI_BAND_18 , /**< UMTS Band18 */	 
 CI_BAND_19 , /**< UMTS Band19 */	 
	 
 CI_BAND_GSM /**< Band GSM */	 
 } _CiUmtsBandsType;

typedef UINT8 CiUmtsBandsType ;
//ICAT EXPORTED ENUM 
 /** \brief LTE band values */ 
 /** \remarks Common Data Section */ 
 
 typedef enum CI_LTE_BANDS_TYPE 
 {	 
 CI_LTE_BAND_1 = 1 , /**< LTE Band1 */	 
 CI_LTE_BAND_2 , /**< LTE Band2 */	 
 CI_LTE_BAND_3 , /**< LTE Band3 */	 
 CI_LTE_BAND_4 , /**< LTE Band4 */	 
 CI_LTE_BAND_5 , /**< LTE Band5 */	 
 CI_LTE_BAND_6 , /**< LTE Band6 */	 
 CI_LTE_BAND_7 , /**< LTE Band7 */	 
 CI_LTE_BAND_8 , /**< LTE Band8 */	 
 CI_LTE_BAND_9 , /**< LTE Band9 */	 
 CI_LTE_BAND_10 , /**< LTE Band10 */	 
	 
 CI_LTE_BAND_11 , /**< LTE Band11 */	 
 CI_LTE_BAND_12 , /**< LTE Band12 */	 
 CI_LTE_BAND_13 , /**< LTE Band13 */	 
 CI_LTE_BAND_14 , /**< LTE Band14 */	 
 CI_LTE_BAND_15 , /**< LTE Band15 */	 
 CI_LTE_BAND_16 , /**< LTE Band16 */	 
 CI_LTE_BAND_17 , /**< LTE Band17 */	 
 CI_LTE_BAND_18 , /**< LTE Band18 */	 
 CI_LTE_BAND_19 , /**< LTE Band19 */	 
 CI_LTE_BAND_20 , /**< LTE Band20 */	 
	 
 CI_LTE_BAND_21 , /**< LTE Band21 */	 
 CI_LTE_BAND_22 , /**< LTE Band22 */	 
 CI_LTE_BAND_23 , /**< LTE Band23 */	 
 CI_LTE_BAND_24 , /**< LTE Band24 */	 
 CI_LTE_BAND_25 , /**< LTE Band25 */	 
 CI_LTE_BAND_26 , /**< LTE Band26 */	 
 CI_LTE_BAND_27 , /**< LTE Band27 */	 
 CI_LTE_BAND_28 , /**< LTE Band28 */	 
 CI_LTE_BAND_29 , /**< LTE Band29 */	 
 CI_LTE_BAND_30 , /**< LTE Band30 */	 
	 
 CI_LTE_BAND_31 , /**< LTE Band31 */	 
 CI_LTE_BAND_32 , /**< LTE Band32 */	 
 CI_LTE_BAND_33 , /**< LTE Band33 */	 
 CI_LTE_BAND_34 , /**< LTE Band34 */	 
 CI_LTE_BAND_35 , /**< LTE Band35 */	 
 CI_LTE_BAND_36 , /**< LTE Band36 */	 
 CI_LTE_BAND_37 , /**< LTE Band37 */	 
 CI_LTE_BAND_38 , /**< LTE Band38 */	 
 CI_LTE_BAND_39 , /**< LTE Band39 */	 
 CI_LTE_BAND_40 , /**< LTE Band40 */	 
	 
 CI_LTE_BAND_41 , /**< LTE Band41 */	 
	 
 CI_LTE_BAND_64 = 64 /**< LTE Band64 */	 
	 
 } _CiLteBandsType;

typedef UINT8 CiLteBandsType ;
typedef UINT16 CiNrBandsType ;
//ICAT EXPORTED ENUM 
 /** \brief GSM band values */ 
 /** \remarks Common Data Section */ 
 
 typedef enum CI_GSM_BANDS_TYPE 
 {	 
 CI_GSM_BAND = 0 , /**< PGSM 900 ( standard or primary ) */	 
 CI_DCS_BAND = 1 , /**< DCS GSM 1800 */	 
 CI_PCS_BAND = 2 , /**< PCS GSM 1900 */	 
 CI_EGSM_BAND = 3 , /**< EGSM 900 ( extended ) */	 
 CI_GSM_450_BAND = 4 , /**< GSM 450 */	 
 CI_GSM_480_BAND = 5 , /**< GSM 480 */	 
 CI_GSM_850_BAND = 6 , /**< GSM 850 */	 
	 
 CI_NUM_BANDS ,	 
	 
	 
	 
 CI_INVALID_BAND = 0xFF /**< Invalid band */	 
 } _CiGsmBandsType;

typedef UINT8 CiGsmBandsType ;
//ICAT EXPORTED UNION 
 typedef union CiMmCurrentBandTag 
 {	 
 CiGsmBandsType gsmBand ; /**< access technology is GSM \sa CiGsmBandsType */	 
 CiGsmBandsType gsmCompactBand ; /**< Not used */	 
 CiUmtsBandsType umtsBand ; /**< access technology is UMTS \sa CiUmtsBandsType */	 
 CiLteBandsType lteBand ; /**< access technology is LTE \sa CiLteBandsType */	 
 CiNrBandsType nrBand ; /**< access technology is NR \sa CiNrBandsType */	 
 } CiMmCurrentBand;

typedef UINT8 CiMmBandType ;
//ICAT EXPORTED STRUCT 
 /** \brief Current band */ 
 /** \remarks Common Data Section */ 
 typedef struct CiMmCurrentbandInfo_struct 
 {	 
 CiMmAccTechMode accessTechnology ; /**< Access technology \sa CiMmAccTechMode */	 
 CiMmCurrentBand currentBand ; /**< Current band \sa CiMmCurrentBandTag */	 
 /*Added by zuohuaxu for CQ00145075 , begin*/	 
 CiMmBandType bandType ; /*FDD-0 , TDD-1*/	 
 /*Added by zuohuaxu for CQ00145075 , end*/	 
 } CiMmCurrentBandInfo;

typedef UINT8 CiMmNetworkModeReport ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmNetworkHsMode_struct 
 {	 
 CiBoolean gprsActive ; /**< TRUE - is active ; FALSE - is not ; \sa CCI API Ref Manual */	 
 CiBoolean egprsActive ; /**< TRUE - is active ; FALSE - is not ; \sa CCI API Ref Manual */	 
 CiBoolean hsdpaActive ; /**< TRUE - is active ; FALSE - is not ; \sa CCI API Ref Manual */	 
 CiBoolean hsupaActive ; /**< TRUE - is active ; FALSE - is not ; \sa CCI API Ref Manual */	 
 CiBoolean hspaPlusActive ; /**< TRUE - is active ; FALSE - is not ; \sa CCI API Ref Manual */	 
 } CiMmNetworkHsMode;

//ICAT EXPORTED ENUM 
 typedef enum CIMM_DSTIND 
 {	 
 CI_MM_DST_IND_NO_ADJUSTMENT = 0 ,	 
 CI_MM_DST_IND_PLUS_ONE_HOUR ,	 
 CI_MM_DST_IND_PLUS_TWO_HOURS ,	 
	 
 CI_MM_NUM_DST_INDS	 
 } _CiMmDstInd;

typedef UINT8 CiMmDstInd ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmUniTime_struct 
 {	 
 UINT8 year ; /**< Year [ 0 ..99 ] */	 
 UINT8 month ; /**< Month [ 1 ..12 ] */	 
 UINT8 day ; /**< Day [ 1 ..31 ] */	 
 UINT8 hour ; /**< Hour [ 0 ..59 ] */	 
 UINT8 minute ; /**< Minute [ 0 ..59 ] */	 
 UINT8 second ; /**< Second [ 0 ..59 ] */	 
 INT8 locTimeZone ; /**< Local time zone */	 
 } CiMmUniTime;

//ICAT EXPORTED ENUM 
 typedef enum CiNetworkNameCodingSchemeTag 
 {	 
 CI_NETWORK_NAME_SMS_CB_CODED = 0 ,	 
 CI_NETWORK_NAME_UCS2_CODED = 1	 
 } 
 _CiNetworkNameCodingScheme;

typedef UINT8 CiNetworkNameCodingScheme ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmLsaIdentity_struct 
 {	 
 UINT8 length ; /**< length */	 
 UINT8 data [ 3 ] ; /**< data */	 
 } CiMmLsaIdentity;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmNetworkName_struct 
 {	 
 CiBoolean extBit ;	 
 CiBoolean addCIBit ; /**<addCountryInitials*/	 
 CiNetworkNameCodingScheme networkNameDCS ; /**< SMS_CB_CODED / UCS2_CODED */	 
 UINT8 networkNameLength ;	 
 UINT8 numOfSpareBitsInLastOctet ;	 
 char networkName [ 64 ] ;	 
	 
 } CiMmNetworkName;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmNitzInfo_struct 
 {	 
 CiMmOperatorId longAlphaId ; /**< Long alphanumeric operator ID \sa CiMmOperatorId_struct */	 
 CiBoolean longAlphaIdPresent ; /**< TRUE - if long alphanumeric ID is present \sa CCI API Ref Manual */	 
 CiMmOperatorId shortAlphaId ; /**< Short alphanumeric operator ID \sa CiMmOperatorId_struct */	 
 CiBoolean shortAlphaIdPresent ; /**< TRUE - if short alphanumeric ID is present \sa CCI API Ref Manual */	 
 CiMmNetworkId networkId ; /**< Network ID information \sa CiMmNetworkId */	 
 CiBoolean networkIdPresent ; /**< TRUE - if Network ID information is present \sa CCI API Ref Manual */	 
 CiMmUniTime uniTime ; /**< Universal time \sa CiMmUniTime_struct */	 
 CiBoolean uniTimePresent ; /**< TRUE - if universal time is present \sa CCI API Ref Manual */	 
 INT8 locTimeZone ; /**< local time zone */	 
 CiBoolean locTimeZonePresent ; /**< TRUE - if local time zone is present \sa CCI API Ref Manual */	 
 CiMmDstInd dstInd ; /**< Daylight savings indicator \sa CiMmDstInd */	 
 CiBoolean dstIndPresent ; /**< TRUE - if daylight saving indicator present \sa CCI API Ref Manual */	 
 CiMmLsaIdentity lsaIdentity ; /**< LSA - localized service area identity \sa CiMmLsaIdentity */	 
 UINT8 domain ; /**< CS or PS domain , 0 :CS ; 1 :PS */	 
	 
 CiNetworkNameCodingScheme networkNameCodingScheme ; /** SMS_CB_CODED / UCS2_CODED */	 
 CiBoolean addCountryInitials ;	 
 /*added by taow 20220708 CQ00137666 begin*/	 
 CiMmNetworkName fullNWName ;	 
 CiMmNetworkName shortNWName ;	 
	 
 UINT32 resrveData [ 4 ] ;	 
 /*added by taow 20220708 CQ00137666 end*/	 
 } CiMmNitzInfo;

//ICAT EXPORTED ENUM 
 typedef enum CIMM_ADDPREFOP_TYPES 
 {	 
 CI_MM_ADDPREFOP_FIRST_AVAILABLE = 0 ,	 
 CI_MM_ADDPREFOP_INSERT_AT_INDEX ,	 
	 
 CI_MM_NUM_ADD_PREFOP_TYPES	 
 } _CiMmAddPrefOpType;

typedef UINT8 CiMmAddPrefOpType ;
//ICAT EXPORTED ENUM 
 typedef enum CIMM_LIST_INDEX_TYPE 
 {	 
 CI_MM_LIST_INDEX_USER_CONTROLLED_WACTSUCCESS = 0 , /**< User controlled PLMN selector with Access Technology EFPLMNwAcT. if not found in the SIM / UICC then select PLMN preferred list EFPLMNsel */	 
 CI_MM_LIST_INDEX_OPERATOR_CONTROLLED_WACT , /**< Operator controlled PLMN selector with Access Technology FOPLMNwAcT */	 
 CI_MM_LIST_INDEX_HPLMN_WACT , /**< HPLMN selector with Access Technology EFHPLMNwAcT */	 
	 
 CI_MM_NUM_LIST_INDEX_TYPES	 
	 
 } _CiMmListIndexType;

typedef UINT8 CiMmListIndexType ;
typedef CiEmptyPrim CiMmPrimGetNumSubscriberNumbersReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetNumSubscriberNumbersCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 UINT8 NumMSISDN ; /**< Number of entries in the MSISDN list */	 
 } CiMmPrimGetNumSubscriberNumbersCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetSubscriberInfoReq_struct 
 {	 
 UINT8 Index ; /**< MSISDN list entry number [ 1 ..number of MSISDN list entries ] */	 
 } CiMmPrimGetSubscriberInfoReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetSubscriberInfoCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 CiMmSubscriberInfo info ; /**< Subscriber information \sa CiMmSubscriberInfo_struct */	 
 } CiMmPrimGetSubscriberInfoCnf;

typedef CiEmptyPrim CiMmPrimGetSupportedRegResultOptionsReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetSupportedRegResultOptionsCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 UINT8 NumOptions ; /**< Number of supported options */	 
 CiMmRegResultOption Option [ CIMM_NUM_REGRESULT_OPTIONS ] ; /**< Supported options \sa CiMmRegResultOption*/	 
 } CiMmPrimGetSupportedRegResultOptionsCnf;

typedef CiEmptyPrim CiMmPrimGetRegResultOptionReq ;
typedef CiEmptyPrim CiMmPrimGetBandIndReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetBandIndCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 CiBoolean enableBandInd ; /**< Enable status \sa CCI API Ref Manual */	 
 CiMmCurrentBandInfo currentBand ; /**< Current band \sa CiMmCurrentbandInfo_struct */	 
	 
 } CiMmPrimGetBandIndCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetRegResultOptionCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 CiMmRegResultOption Option ; /**< Reporting option \sa CiMmRegResultOption */	 
 } CiMmPrimGetRegResultOptionCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetRegResultOptionReq_struct 
 {	 
 CiMmRegResultOption Option ; /**< Reporting option \sa CiMmRegResultOption */	 
 } CiMmPrimSetRegResultOptionReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetBandIndReq_struct 
 {	 
 CiBoolean enableBandInd ; /**< Enable / Disable band indications \sa CCI API Ref Manual */	 
 } CiMmPrimSetBandIndReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetBandIndCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimSetBandIndCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetRegResultOptionCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimSetRegResultOptionCnf;

//ICAT EXPORTED ENUM 
 typedef enum CIMM_CAUSE_TYPE 
 {	 
 CI_MM_CAUSE_TYPE_MM = 0 , /**< Indicates that <reject_cause> contains an MM cause value , see 3 GPP TS 24.008000 [ 8 ] Annex G*/	 
 CI_MM_CAUSE_TYPE_MANUFACTURER , /**< Indicates that <reject_cause> contains a manufacturer specific cause */	 
 CI_MM_CAUSE_NONE ,	 
 CIMM_NUM_CAUSE_TYPE	 
 } _CiMmCauseType;

typedef UINT8 CiMmCauseType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimRegResultInd_struct 
 {	 
 CiMmRegStatus RegStatus ; /**< Registration status \sa CiMmRegStatus */	 
 CiMmCellInfo info ; /**< Current cell information \sa CiMmCellInfo_struct */	 
 /* deleted by xwzhou 9052013 CQ43000 , begin */	 
 // CiMmAccTechMode AccTchMode ; / **< Network access technology \sa CiMmAccTechMode * /	 
 /* deleted by xwzhou 9052013 CQ43000 , end */	 
	 
 CiMmCauseType causeType ; /**< cuase type \sa CiMmCauseType */	 
 UINT16 rejectCause ; /**< contains the cause of the failed registration ( if MM cause type , values define in 3 GPP TS 24.008000 [ 8 ] Annex G ) . The value is of type as defined by causeType */	 
 CiBoolean pscValid ; /**<Indicates if the psc field is valid. The psc should be valid only on UMTS */	 
 UINT16 psc ; /**< Primary scrambling code */	 
 /* add by perse 1032018 add +CREG / +CGREG / +CEREG indication content with rplmn CQ00117472 , begin */	 
 // #if defined ( CRANE_Z1 )	 
 CiMmNetworkId rplmnInfo ; /**reprot rplmn information*/	 
 // #endif	 
 /* add by perse 1032018 add +CREG / +CGREG / +CEREG indication content with rplmn CQ00117472 , end */	 
 } CiMmPrimRegResultInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimBandIndInd_struct 
 {	 
 CiMmCurrentBandInfo currentBand ; /**< Current band \sa CiMmCurrentbandInfo_struct */	 
 } 
 CiMmPrimBandIndInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimEventReportInd_struct 
 {	 
 UINT32 type ; // 0 : eps fallback , 1 : emergency services fallback , 2 : fast return other value FFS	 
 union	 
 {		 
 UINT32 status ; // 0 : start , 1 : success , 2 : failure , ( status event only used by type 0 , 1 , 2 )		 
 } event ;	 
 } 
 CiMmPrimEventReportInd;

typedef CiEmptyPrim CiMmPrimGetRegResultInfoReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetRegResultInfoCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 CiMmRegResultOption option ; /** just the n flag in CREG */	 
 CiMmRegStatus RegStatus ; /**< Registration status \sa CiMmRegStatus */	 
 CiMmCellInfo info ; /**< Current cell information \sa CiMmCellInfo_struct */	 
 /* deleted by xwzhou 9052013 CQ43000 , begin */	 
 // CiMmAccTechMode AccTchMode ; / **< Network access technology \sa CiMmAccTechMode * /	 
 /* deleted by xwzhou 9052013 CQ43000 , end */	 
 CiMmCauseType causeType ; /**< cuase type \sa CiMmCauseType */	 
 UINT16 rejectCause ; /**< contains the cause of the failed registration ( if MM cause type , values define in 3 GPP TS 24.008000 [ 8 ] Annex G ) . The value is of type as defined by causeType */	 
 CiBoolean pscValid ; /**<Indicates if the psc field is valid. The psc should be valid only on UMTS */	 
 UINT16 psc ; /**< Primary scrambling code */	 
 /* add by perse 1032018 add +CREG / +CGREG / +CEREG indication content with rplmn CQ00117472 , begin */	 
 // #if defined ( CRANE_Z1 )	 
 CiMmNetworkId rplmnInfo ; /**reprot rplmn information*/	 
 // #endif	 
 /* add by perse 1032018 add +CREG / +CGREG / +CEREG indication content with rplmn CQ00117472 end */	 
 } CiMmPrimGetRegResultInfoCnf;

typedef CiEmptyPrim CiMmPrimGetSupportedIdFormatsReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetSupportedIdFormatsCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 UINT8 NumFormats ; /**< Number of supported formats */	 
 CiMmNetOpIdFormat Format [ CIMM_NUM_NETOP_ID_FORMATS ] ; /**< Supported formats \sa CiMmNetOpIdFormat */	 
 } CiMmPrimGetSupportedIdFormatsCnf;

typedef CiEmptyPrim CiMmPrimGetIdFormatReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetIdFormatCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 CiMmNetOpIdFormat Format ; /**< Current format \sa CiMmNetOpIdFormat */	 
 } CiMmPrimGetIdFormatCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetIdFormatReq_struct 
 {	 
 CiMmNetOpIdFormat Format ; /**< Current format \sa CiMmNetOpIdFormat */	 
 } CiMmPrimSetIdFormatReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetIdFormatCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimSetIdFormatCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetNumNetworkOperatorsReq_struct 
 {	 
 CiMmNetworkMode networkMode ;	 
 CiBoolean extendedNetworkSearch ; /**<TRUE run extended Network PLMN Search. FALSE run PLMN Search ; add CQ00114574 by taow 20190419 */	 
 } CiMmPrimGetNumNetworkOperatorsReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetNumNetworkOperatorsCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 UINT8 NumOperators ; /**< Number of operators present */	 
 } CiMmPrimGetNumNetworkOperatorsCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetNetworkOperatorInfoReq_struct 
 {	 
 UINT8 Index ; /**< Numeric index , specifying the network operator for which information is requested [ 1 ..number of operators present ] */	 
 } CiMmPrimGetNetworkOperatorInfoReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetNetworkOperatorInfoCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 CiMmNetOpStatusInfo opStatus ; /**< Network operator status information , if available \sa CiNetOpStatusInfo_struct */	 
 } CiMmPrimGetNetworkOperatorInfoCnf;

typedef CiEmptyPrim CiMmPrimGetNumPreferredOperatorsReq ;
//ICAT EXPORTED STRUCT 
 /** <paramref name= " CI_MM_PRIM_GET_NUM_PREFERRED_OPERATORS_CNF " > */ 
 // ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetNumPreferredOperatorsCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 UINT8 NumPref ; /**< Number of entries in the list */	 
 } CiMmPrimGetNumPreferredOperatorsCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetPreferredOperatorInfoReq_struct 
 {	 
 UINT8 Index ; /**< Not in use */	 
 } CiMmPrimGetPreferredOperatorInfoReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetPreferredOperatorInfoCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 CiMmNetOpIdInfo info ; /**< Network / operator ID information \sa CiNetOpIdInfo_struct */	 
 CiMmAccTechMode AccTchMode ; /**< Not in use. Access Radio technology ; default is GSM. \sa CiMmAccTechMode */	 
 } CiMmPrimGetPreferredOperatorInfoCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimAddPreferredOperatorReq_struct 
 {	 
 CiMmNetOpIdInfo info ; /**< New network / operator ID information \sa CiNetOpIdInfo_struct */	 
 CiMmAddPrefOpType addPrefOpType ; /**< \sa CiMmAddPrefOpType */	 
 UINT8 res1U8 ; /**< ( padding ) just in case */	 
 UINT16 index ; /**< Entry number to add */	 
 CiMmAccTechMode AccTchMode ; /**< ACT parameter for COLP command \sa CiMmAccTechMode */	 
 } CiMmPrimAddPreferredOperatorReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimAddPreferredOperatorCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 UINT8 NumPref ; /**< Not in use */	 
 } CiMmPrimAddPreferredOperatorCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimDeletePreferredOperatorReq_struct 
 {	 
 UINT8 Index ; /**< Index ( entry number ) to delete */	 
 } CiMmPrimDeletePreferredOperatorReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimDeletePreferredOperatorCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 UINT8 NumPref ; /**< Not in use */	 
 } CiMmPrimDeletePreferredOperatorCnf;

typedef CiEmptyPrim CiMmPrimGetCurrentOperatorInfoReq ;
typedef CiEmptyPrim CiMmPrimGetAsrCurrentOperatorInfoReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetCurrentOperatorInfoCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 CiMmRegMode RegMode ; /**< Current registration mode \sa CiMmRegMode */	 
 CiMmNetOpIdInfo info [ 2 ] ; /**< Current network / operator ID information \sa CiNetOpIdInfo_struct */	 
 /* deleted by xwzhou 9052013 CQ43000 , begin */	 
 // CiMmAccTechMode AccTchMode ; / **< Access radio technology ; default is GSM \sa CiMmAccTechMode * /	 
 /* deleted by xwzhou 9052013 CQ43000 , end */	 
 } CiMmPrimGetCurrentOperatorInfoCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetAsrCurrentOperatorInfoCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 CiMmRegMode RegMode ; /**< Current registration mode \sa CiMmRegMode */	 
 CiMmNetOpIdInfo info [ 2 ] ; /**< Current network / operator ID information \sa CiNetOpIdInfo_struct */	 
 CiMmNetOpIdInfo info_alpha [ 2 ] ; /**< Current network / operator ID information \sa CiNetOpIdInfo_struct */	 
 CiMmNetOpIdInfo info_longAlpha [ 2 ] ; /**< Current network / operator ID information \sa CiNetOpIdInfo_struct */	 
 } CiMmPrimGetAsrCurrentOperatorInfoCnf;

typedef CiEmptyPrim CiMmPrimNetworkModeReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimNetworkModeCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 CiMmNetworkModeReport mode ; /**< Cell capabilities network mode*/	 
 CiBoolean gprsActive ; /**< Active high speed service: gprsActive ( 1 - gprs is active , 0 - gprs is inactive ) */	 
 CiBoolean egprsActive ; /**< Active high speed service: egprsActive ( 1 - egprs is active , 0 - egprs is inactive ) */	 
 CiBoolean hsdpaActive ; /**< Active high speed service: hsdpaActive ( 1 - hsdpa is active , 0 - hsdpa is inactive ) */	 
 CiBoolean hsupaActive ; /**< Active high speed service: hsupaActive ( 1 - hsupa is active , 0 - hsupa is inactive ) */	 
 CiBoolean epsActive ; /**< Active high speed service: epsActive ( 1 - eps is active , 0 - eps is inactive ) */	 
 CiBoolean dcHsdpaActive ; /**< Active high speed service: dcHsdpaActive ( 1 - dcHsdpa is active , 0 - dcHsdpa is inactive ) */	 
 CiBoolean hspaPlusActive ; /**< Active high speed service: hspaPlusActive ( 1 - HSPA+ is active , 0 - HSPA+ is inactive ) */	 
 } CiMmPrimNetworkModeCnf;

typedef CiEmptyPrim CiMmPrimAutoRegisterReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimAutoRegisterCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimAutoRegisterCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimManualRegisterReq_struct 
 {	 
 CiBoolean AutoFallback ; /**< TRUE - Fallback to automatic registration ; FALSE - No fallback to automatic registration \sa CCI API Ref Manual */	 
 CiMmNetOpIdInfo info ; /**< Network operator identification information \sa CiNetOpIdInfo_struct */	 
 CiMmAccTechMode AccTchMode ; /**< Access radio technology ; default is GSM \sa CiMmAccTechMode */	 
 } CiMmPrimManualRegisterReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimManualRegisterCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimManualRegisterCnf;

typedef CiEmptyPrim CiMmPrimDeregisterReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimDeregisterCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimDeregisterCnf;

typedef CiEmptyPrim CiMmPrimGetSigQualityIndConfigReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetSigQualityIndConfigCnf_struct 
 {	 
 CiMmResultCode Result ;	 
 CiMmSigQualityConfig config ;	 
 } CiMmPrimGetSigQualityIndConfigCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetSigQualityIndConfigReq_struct 
 {	 
 CiMmSigQualityConfig config ; /**< Signal quality configuration \sa CiMmSigQualityConfig_struct */	 
 } CiMmPrimSetSigQualityIndConfigReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetSigQualityIndConfigCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimSetSigQualityIndConfigCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSigQualityInfoInd_struct 
 {	 
 CiMmSigQualityInfo info ; /**< Signal quality information \sa CiMmSigQualityInfo_struct */	 
 } CiMmPrimSigQualityInfoInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimExtendedSigQualityInfoInd_struct 
 {	 
 CiMmSigExtendedQualityInfo info ; /**< Signal quality information \sa CiMmSigExtendedQualityInfo_struct */	 
 } CiMmPrimExtendedSigQualityInfoInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimNormalSigQualityInfoInd_struct 
 {	 
 CiMmSigNormalQualityInfo info ; /**< Signal quality information \sa CiMmSigQualityInfo_struct */	 
 } CiMmPrimNormalSigQualityInfoInd;

//ICAT EXPORTED ENUM 
 typedef enum CIMM_SIGQUALITY_TYPE 
 {	 
 CI_MM_SIGQUALITY_CSQ = 0 , /**< Indicates that <reject_cause> contains an MM cause value , see 3 GPP TS 24.008000 [ 8 ] Annex G*/	 
 CI_MM_SIGQUALITY_ECSQ , /**< Indicates that <reject_cause> contains a manufacturer specific cause */	 
 CI_MM_SIGQUALITY_NONE ,	 
 } _CiMmSigQualityType;

typedef UINT8 CiMmSigQualityType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSigQualityInfoReq_struct 
 {	 
 CiMmSigQualityType sigQualityType ;	 
	 
 } CiMmPrimSigQualityInfoReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSigQualityInfoCnf_struct 
 {	 
 CiMmResultCode result ;	 
 CiMmSigQualityType sigQualityType ;	 
 CiMmSigNormalQualityInfo normalInfo ; /**< Signal quality information \sa CiMmSigQualityInfo_struct */	 
 CiMmSigExtendedQualityInfo extendedInfo ; /**< Signal quality information \sa CiMmSigExtendedQualityInfo_struct */	 
 } CiMmPrimSigQualityInfoCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimEnableNetworkModeIndReq_struct 
 {	 
 CiBoolean enable ; /**< TRUE - Enable network mode indication ; FALSE - Disable network mode indication ( default ) \sa CCI API Ref Manual */	 
 } CiMmPrimEnableNetworkModeIndReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimEnableNetworkModeIndCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimEnableNetworkModeIndCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimNetworkModeInd_struct 
 {	 
 /* Michal Bukai enter Vadim modification for NetworkModeIndication */	 
	 
	 
	 
 CiMmNetworkModeReport mode ; /**< Cell capabilities network mode \sa CiMmNetworkModeReport */	 
 CiBoolean gprsActive ; /**< Active high speed service: gprsActive ( 1 - gprs is active , 0 - gprs is inactive ) \sa CCI API Ref Manual */	 
 CiBoolean egprsActive ; /**< Active high speed service: egprsActive ( 1 - egprs is active , 0 - egprs is inactive ) \sa CCI API Ref Manual */	 
 CiBoolean hsdpaActive ; /**< Active high speed service: hsdpaActive ( 1 - hsdpa is active , 0 - hsdpa is inactive ) \sa CCI API Ref Manual */	 
 CiBoolean hsupaActive ; /**< Active high speed service: hsupaActive ( 1 - hsupa is active , 0 - hsupa is inactive ) \sa CCI API Ref Manual */	 
 CiBoolean hspaPlusActive ; /**< Active high speed service: hspa+ ( 1 - hspa+ is active , 0 - hspa+ is inactive ) \sa CCI API Ref Manual */	 
	 
 CiBoolean epsActive ; /**< Active high speed service: epsActive ( 1 - eps is active , 0 - eps is inactive ) \sa CCI API Ref Manual */	 
	 
 UINT8 domain ; /**< CS or PS domain , 0 :CS only ; 1 :PS only ; 2 :Combined CS / PS */	 
 } CiMmPrimNetworkModeInd;

typedef CiEmptyPrim CiMmPrimGetNitzInfoReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetNitzInfoCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 CiMmNitzInfo info ; /**< NITZ information \sa CiMmNitzInfo_struct */	 
 } CiMmPrimGetNitzInfoCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimNitzInfoInd_struct 
 {	 
 CiMmNitzInfo info ; /**< NITZ information \sa CiMmNitzInfo_struct */	 
 } CiMmPrimNitzInfoInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCipheringStatusInd_struct 
 {	 
 CiBoolean CsCipheringOn ; /**< TRUE if ON , FALSE if OFF ; \sa CCI API Ref Manual */	 
 CiBoolean PsCipheringOn ; /**< TRUE if ON , FALSE if OFF ; \sa CCI API Ref Manual */	 
 CiBoolean CipheringIndicatorOn ; /**< TRUE if required , FALSE if not required ; \sa CCI API Ref Manual */	 
	 
 } CiMmPrimCipheringStatusInd;

typedef UINT8 CiMmRejectType ;
typedef UINT8 CiGmmRejectType ;
typedef UINT8 CiEmmRejectType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimAirInterfaceRejectCauseInd_struct 
 {	 
 CiMmRejectType mmRejectType ; /**< MM Reject type \sa CiMmRejectType */	 
 CiMmCause mmCause ; /**< MM state \sa CiMmCause */	 
	 
 CiGmmRejectType gmmRejectType ; /**< GMM Reject type \sa CiGmmRejectType */	 
 CiGmmCause gmmCause ; /**< GMM state \sa CiGmmCause */	 
	 
 CiEmmRejectType emmRejectType ;	 
 CiEmmCause emmCause ; /**< EMM state \sa CiEmmCause */	 
	 
 CiMmErrorCauseType causeType ;	 
 } CiMmPrimAirInterfaceRejectCauseInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSelectPreferredPlmnListReq_struct 
 {	 
 CiMmListIndexType ListIndex ; /**< Preferred PLMN list type \sa CiMmListIndexType*/	 
 } CiMmPrimSelectPreferredPlmnListReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSelectPreferredPlmnListCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimSelectPreferredPlmnListCnf;

typedef CiEmptyPrim CiMmPrimGetPreferredPlmnListReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetPreferredPlmnListCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 CiMmListIndexType ListIndex ; /**< Preferred PLMN list type \sa CiMmListIndexType*/	 
 } CiMmPrimGetPreferredPlmnListCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimServiceRestrictionsInd_struct 
 {	 
 CiBoolean manualPlmnSelectionAllowed ; /**< TRUE if display of PLMN selection menus is allowed \sa CCI API Ref Manual */	 
 } CiMmPrimServiceRestrictionsInd;

typedef CiEmptyPrim CiMmPrimCancelManualPlmnSearchReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCancelManualPlmnSearchCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimCancelManualPlmnSearchCnf;

typedef CiEmptyPrim CiMmPrimTriggerUserReselectionReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimTriggerUserReselectionCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code */	 
 } CiMmPrimTriggerUserReselectionCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimHomeZoneInd_struct 
 {	 
 CiBoolean ZoneInd ; /**< TRUE - display HomeZone / CitiZone Indication for the zone specified in " ZoneId " field , FALSE -remove HomeZone / CitiZone indication \sa CCI API Ref Manual */	 
 UINT8 ZoneId ; /**< Zone ID*/	 
 CiBoolean IsCityZone ; /**< TRUE - detected zone is CityZone , FALSE - detected zone is HomeZone \sa CCI API Ref Manual */	 
 CiBoolean ZoneTagPreset ; /**< TRUE - Zone TAG is included , FALSE - Zone TAG is not included \sa CCI API Ref Manual */	 
 CiString ZoneTag ; /**< 13 -character string coded in the short message alphabet given in GSM 3.380000 with bit 8 set to Zero. 0xff indicates end of string \sa CCI API Ref Manual */	 
 } CiMmPrimHomeZoneInd;

//ICAT EXPORTED ENUM 
 /** \brief Cell lock modes */ 
 /** \remarks Common Data Section */ 
 typedef enum CIMM_CELL_LOCK_MODE 
 {	 
 CIMM_CELL_LOCK_MODE_NONE = 0 , /**< Cell / Freq Lock and IRAT optimization disabled */	 
 CIMM_CELL_LOCK_MODE_LOCKFREQ = 1 , /**< Freq Lock enabled */	 
 CIMM_CELL_LOCK_MODE_LOCKCELL = 2 , /**< Cell Lock enabled */	 
 // CIMM_CELL_LOCK_MODE_IRAT_OPTIMIZATION = 3 , / **< IRAT optimization for cell reselection enabled * /	 
 CIMM_CELL_LOCK_MODE_LOCKBAND = 3 ,	 
 // CIMM_CELL_LOCK_MODE_SYNC_CELL = 4 , / **< LTE Cell sync detection enabled * /	 
	 
 CIMM_NUM_CELLLOCK_MODES ,	 
 } _CiMmCellLockMode;

typedef UINT8 CiMmCellLockMode ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCellLockReq_struct 
 {	 
 CiMmCellLockMode mode ; /**< Cell lock mode \sa CiMmCellLockMode */	 
 CiMmCellLockActMode act ; /**< Network mode \sa CiMmNetworkMode */	 
 UINT16 bandValue ;	 
 UINT32 freq ; /**< Absolute radio frequency channel number ; GSM number 0 -1023 , TD number 10054 -10121 and 9404 -9596 */	 
 INT16 cellId ; /**< Cell parameter ID This parameter if valid for 3 G cells only 0 -127 , and for TD LTE cells only 0 -503 */	 
 Boolean scsPresent ;	 
 INT8 scs ;	 
 // INT8 tddOffset ; / **< RSCP threshold for IRAT cell reselection ; -115~-25dB and default -85dB* /	 
 } CiMmPrimCellLockReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCellLockCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimCellLockCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCellLockInd_struct 
 {	 
 CiMmCellLockMode mode ; /**< Cell lock mode \sa CiMmCellLockMode */	 
 CiMmNetworkMode networkMode ; /**< Network mode \sa CiMmNetworkMode */	 
	 
 UINT16 arfcn ; /**< Absolute radio frequency channel number ; GSM number 0 -1023 , TD number 10054 -10121 and 9404 -9596 */	 
 UINT8 cellParameterId ; /**< Cell parameter ID This parameter if valid for 3 G cells only 0 -127*/	 
 INT8 tddOffset ; /**< RSCP threshold for IRAT cell reselection ; -115~-25dB and default -85dB*/	 
 } CiMmPrimCellLockInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimWBCellLockReq_struct 
 {	 
 CiBoolean ActivateCellLock ; /**< TRUE - activate cell lock ; FALSE - deactivate cell lock \sa CiBoolean */	 
 CiMmCurrentBandInfo Band ; /**< Band \sa CiMmCurrentbandInfo */	 
 UINT16 arfcn ; /**< Absolute radio frequency channel number ; number 0 -1023 */	 
 UINT16 ScramblingCode ; /**< Primary scrambling code This parameter if valid for 3 G cells only */	 
 } CiMmPrimWBCellLockReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimWBCellLockCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimWBCellLockCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetFastDormantCapReq_struct 
 {	 
 CiBoolean fastDormantEnabled ; /**< TRUE - enabled ; FALSE - disabled ; \sa CCI API Ref Manual */	 
 } CiMmPrimSetFastDormantCapReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetFastDormantCapCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimSetFastDormantCapCnf;

typedef CiEmptyPrim CiMmPrimGetFastDormantCapReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetFastDormantCapCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
	 
 CiBoolean fastDormantEnabled ; /**< TRUE - enabled ; FALSE - disabled ; \sa CCI API Ref Manual */	 
 } CiMmPrimGetFastDormantCapCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetNasIntegrityCheckReq_struct 
 {	 
 CiBoolean integrityCheckEnabled ; /**< TRUE - enabled ; FALSE - disabled ; \sa CCI API Ref Manual */	 
 } CiMmPrimSetNasIntegrityCheckReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetNasIntegrityCheckCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimSetNasIntegrityCheckCnf;

typedef CiEmptyPrim CiMmPrimGetNasIntegrityCheckReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetNasIntegrityCheckCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
	 
 CiBoolean integrityCheckEnabled ; /**< TRUE - enabled ; FALSE - disabled ; \sa CCI API Ref Manual */	 
 } CiMmPrimGetNasIntegrityCheckCnf;

typedef CiEmptyPrim CiMmPrimGetNumLteNetworkOperatorsReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetNumLteNetworkOperatorsCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
 UINT8 NumOperators ; /**< Number of operators present */	 
 } CiMmPrimGetNumLteNetworkOperatorsCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetLteNetworkOperatorInfoReq_struct 
 {	 
 UINT8 Index ; /**< Numeric index , specifying the network operator for which information is requested [ 1 ..number of operators present ] */	 
 } CiMmPrimGetLteNetworkOperatorInfoReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetLteNetworkOperatorInfoCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code \sa CiMmResultCode */	 
	 
 CiMmNetOpStatusInfo opStatus ; /**< Network operator status information , if available \sa CiNetOpStatusInfo_struct */	 
 } CiMmPrimGetLteNetworkOperatorInfoCnf;

typedef CiEmptyPrim CiMmPrimGetLteBackgroundInfoReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetLteBackgroundInfoCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 CiBoolean status ; /**< TRUE - enabled ; FALSE - disabled ; \sa CCI API Ref Manual */	 
 UINT16 interval ; /**< Background search interval in minutes , 0 :immediately ; 15 , 30 , 60 minutes ; 0xFFFF don ' t search*/	 
 } CiMmPrimGetLteBackgroundInfoCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetLteBackgroundInfoReq_struct 
 {	 
 CiBoolean status ; /**< TRUE - enabled ; FALSE - disabled ; \sa CCI API Ref Manual */	 
 UINT16 interval ; /**< Background search interval in minutes , 0 :immediately ; 15 , 30 , 60 minutes ; 0xFFFF don ' t search*/	 
 } CiMmPrimSetLteBackgroundInfoReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetLteBackgroundInfoCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimSetLteBackgroundInfoCnf;

//ICAT EXPORTED ENUM 
 /** \brief Paging Identity Element */ 
 /** \remarks Common Data Section */ 
 typedef enum CIMM_PAGING_IDENTITY_ELEMENT 
 {	 
 CI_MM_CN_PAGING_BY_IMSI ,	 
 CI_MM_CN_PAGING_BY_TMSI ,	 
	 
 CI_MM_NUM_PAGING_IDENTITY_ELEMENT	 
 } _CiMmPagingIdentityElement;

typedef UINT8 CiMmPagingIdentityElement ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmCliElement_struct 
 {	 
 INT8 length ;	 
 INT8 data [ 12 ] ; /* 24008 , 10.500000 .4.9 */	 
 } CiMmCliElement;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmSsCodeElement_struct 
 {	 
 INT8 value ; /* 29002 , 17.700000 .5 */	 
 } CiMmSsCodeElement;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmLcsIndicatorElement_struct 
 {	 
 INT8 value ;	 
 } CiMmLcsIndicatorElement;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmLcsClientIdentityElement_struct 
 {	 
 INT8 length ;	 
 INT8 data [ 255 ] ; /* 29002 , 17.700000 .13 */	 
 } CiMmLcsClientIdentityElement;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCsServiceNotificationInd_struct 
 {	 
 CiMmPagingIdentityElement pagingIdentity ; /* 24301 , 9.900000 .3.25A */	 
 CiBoolean cliPresent ;	 
 CiMmCliElement cli ; /* 24301 , 9.900000 .3.38 */	 
 CiBoolean ssCodePresent ;	 
 CiMmSsCodeElement ssCode ; /* 24301 , 9.900000 .3.39 */	 
 CiBoolean lcsIndicatorPresent ;	 
 CiMmLcsIndicatorElement lcsIndicator ; /* 24301 , 9.900000 .3.40 */	 
 CiBoolean lcsClientIdentityPresent ;	 
 CiMmLcsClientIdentityElement lcsClientIdentity ; /* 24301 , 9.900000 .3.41 */	 
 } CiMmPrimCsServiceNotificationInd;

//ICAT EXPORTED ENUM 
 /** \brief CIMM Respond Value */ 
 /** \remarks Common Data Section */ 
 typedef enum CIMM_RSP_VALUE 
 {	 
 CI_MM_CSFB_ACCEPT ,	 
 CI_MM_CSFB_REJECT ,	 
 CI_MM_CSFB_OTHERS , // set it when timer expired , or others	 
	 
 CI_MM_NUM_RSP_VALUE	 
 } _CiMmRspValue;

typedef UINT8 CiMmRspValue ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCsServiceNotificationRsp_struct 
 {	 
 CiMmRspValue rspValue ;	 
 } CiMmPrimCsServiceNotificationRsp;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimDsacStatusInd_struct 
 {	 
 CiBoolean csDomainBarred ;	 
 CiBoolean psDomainBarred ;	 
 } CiMmPrimDsacStatusInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmSetSrvccSupportReq_struct 
 {	 
 CiBoolean srvcc_status ;	 
 } CiMmSetSrvccSupportReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmSetSrvccSupportCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmSetSrvccSupportCnf;

typedef CiEmptyPrim CiMmGetSrvccSupportReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmGetSrvccSupportCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 CiBoolean srvcc_status ;	 
 } CiMmGetSrvccSupportCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmSetImsNwReportModeReq_struct 
 {	 
 CiBoolean reporting ;	 
 } CiMmSetImsNwReportModeReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmSetImsNwReportModeCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmSetImsNwReportModeCnf;

typedef CiEmptyPrim CiMmGetImsNwReportModeReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmGetImsNwReportModeCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 CiBoolean reporting ;	 
 CiBoolean nwimsvops ;	 
 } CiMmGetImsNwReportModeCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmImsvopsInd_struct 
 {	 
 CiBoolean nwimsvops ;	 
 } CiMmImsvopsInd;

//ICAT EXPORTED ENUM 
 typedef enum CIMM_SRVCC_IND_TYPE 
 {	 
 CI_MM_SRVCC_STARTED = 0 ,	 
 CI_MM_SRVCC_SUCCESSFUL ,	 
 CI_MM_SRVCC_CANCELLED ,	 
 CI_MM_SRVCC_GENERAL_FAILURE ,	 
	 
 /* This one must always be last in the list! */	 
 CI_MM_SRVCC_NUM_TYPE	 
 } _CiMmSrvccType;

typedef UINT8 CiMmSrvccHType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmSrvccHandoverInd_struct 
 {	 
 CiMmSrvccHType srvcch ;	 
 } CiMmSrvccHandoverInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmSetEmergencyNumberReportModeReq_struct 
 {	 
 CiBoolean reporting ;	 
 } CiMmSetEmergencyNumberReportModeReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmSetEmergencyNumberReportModeCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmSetEmergencyNumberReportModeCnf;

typedef CiEmptyPrim CiMmGetEmergencyNumberReportReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmEmergencyNumberInfo_struct 
 {	 
 CHAR dialString [ 40 ] ;	 
 CiBitRange ServiceCat ;	 
 } CiMmEmergencyNumberInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiSubSvcField_struct 
 {	 
 UINT8 length ;	 
 UINT8 value [ 64 ] ;	 
 } CiSubSvcField;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmExtEmergencyNumberInfo_struct 
 {	 
 CHAR dialString [ 40 ] ;	 
 CiSubSvcField subSvcField ;	 
 } CiMmExtEmergencyNumberInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmGetEmergencyNumberReportCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 CiBoolean reporting ; /** 0 - disable ; 1 - enable */	 
 CiMmNetworkId networkId ;	 
 UINT8 numNumbers ; /** num of emergency numbers */	 
 CiMmEmergencyNumberInfo numbers [ 15 ] ;	 
	 
 /*Added by zuohuaxu for CQ00145950 , begin*/	 
 UINT8 validity ; /*0 for valid in the country of the plmn , 1 for only the plmn*/	 
 UINT8 extNumNumbers ; /* num of extended emergency numbers */	 
 CiMmExtEmergencyNumberInfo extNumbers [ 16 ] ;	 
 /*Added by zuohuaxu for CQ00145950 , end*/	 
 } CiMmGetEmergencyNumberReportCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmEmergencyNumberReportInd_struct 
 {	 
 CiBoolean reporting ; /** 0 - disable ; 1 - enable */	 
 CiMmNetworkId networkId ;	 
 UINT8 numNumbers ; /** num of emergency numbers */	 
 CiMmEmergencyNumberInfo numbers [ 15 ] ;	 
	 
 /*Added by zuohuaxu for CQ00145950 , begin*/	 
 UINT8 validity ; /*0 for valid in the country of the plmn , 1 for only the plmn*/	 
 UINT8 extNumNumbers ; /* num of extended emergency numbers */	 
 CiMmExtEmergencyNumberInfo extNumbers [ 16 ] ;	 
 /*Added by zuohuaxu for CQ00145950 , end*/	 
 } CiMmEmergencyNumberReportInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmSetNwEmergencyBearerServicesReq_struct 
 {	 
 CiBoolean reporting ;	 
 } CiMmSetNwEmergencyBearerServicesReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmSetNwEmergencyBearerServicesCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmSetNwEmergencyBearerServicesCnf;

typedef CiEmptyPrim CiMmGetNwEmergencyBearerServicesReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmGetNwEmergencyBearerServicesCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 CiBoolean reporting ;	 
 UINT8 emb_Iu_supp ;	 
 UINT8 emb_S1_supp ;	 
 UINT8 ems_5G_supp ;	 
 UINT8 emf_5G_supp ;	 
 /*Added by zuohuaxu for CQ00145501 , begin*/	 
 UINT8 emc_5G_supp_n3gpp ;	 
 /*Added by zuohuaxu for CQ00145501 , end*/	 
 } CiMmGetNwEmergencyBearerServicesCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmNwEmergencyBearerServicesIuInd_struct 
 {	 
 UINT8 emb_Iu_supp ;	 
 } CiMmNwEmergencyBearerServicesIuInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmNwEmergencyBearerServicesS1Ind_struct 
 {	 
 UINT8 emb_S1_supp ;	 
 } CiMmNwEmergencyBearerServicesS1Ind;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmNwEmergencyBearerServices5GInd_struct 
 {	 
 UINT8 ems_5G_supp ;	 
 UINT8 emf_5G_supp ;	 
 /*Added by zuohuaxu for CQ00145501 , begin*/	 
 UINT8 emc_5G_supp_n3gpp ;	 
 /*Added by zuohuaxu for CQ00145501 , end*/	 
 } CiMmNwEmergencyBearerServices5GInd;

typedef CiEmptyPrim CiMmGetSsacStatusReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmGetSsacStatusCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 UINT8 BFVoice ; /**< parameter shows the barring factor for MMTEL voice ( 0 -16 ) */	 
 UINT8 BFVideo ; /**< parameter shows the barring factor for MMTEL video ( 0 -16 ) */	 
 UINT8 BTVoice ; /**< parameter shows the barring timer for MMTEL voice ( 0 -8 ) */	 
 UINT8 BTVideo ; /**< parameter shows the barring timer for MMTEL video ( 0 -8 ) */	 
 } CiMmGetSsacStatusCnf;

typedef UINT8 CiMmCsgWhiteListType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmCsgInfo_struct 
 {	 
 UINT32 csgId ; /**< indicates the CSG ID of the cells which were found. */	 
 CiBoolean hnbNamePresent ; /**< indicates if HnbName is used. TRUE - HnbName is used. False - HnbName is unused.*/	 
 UINT8 hnbName [ 48 ] ; /**< text of up to 48 chars. */	 
 CiBoolean hnbTypePresent ; /**< indicates if HnbType is used. TRUE - HnbType is used. False - HnbType is unused.*/	 
 UINT8 hnbType [ 12 ] ; /**< Additional information for this CSG. */	 
 CiMmCsgWhiteListType whileListType ;	 
 } CiMmCsgInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmCsgCellInfo_struct 
 {	 
 CiMmCsgInfo csgInfo ; /**< CSG information */	 
 CiMmNetworkId networkId ; /**< Network ID */	 
 CiMmNetworkMode mode ; /**< Cell capabilities network mode */	 
 } CiMmCsgCellInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCsgListSearchReq_struct 
 {	 
 UINT8 csgIndexReq ; /**< indicates the CSG index requsted to get , 0 means beging of the new search , else is reading of the last resulsts from listed offset*/	 
 } CiMmPrimCsgListSearchReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCsgListSearchCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code */	 
 UINT8 totalNumOfCsg ; /**< number of CSG ID which were found 0 ..MAX_NUMBER_OF_CSG ( = 40 ) */	 
 UINT8 startCsgIndex ; /**< first CSG ID inedx which were included in this message */	 
 UINT8 numCsgList ; /**< number of CSG ID which were included in this message */	 
 CiMmCsgCellInfo csgList [ 20 ] ; /**< list of all the CSG which were found and their parameters*/	 
 } CiMmPrimCsgListSearchCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCsgSelectReq_struct 
 {	 
 UINT32 csgId ; /**< indicates the CSG ID of the cells which were found. bits 0 -26: can receive any value. 27 - 31 should be set to 0 .*/	 
 CiMmNetworkId networkId ; /**< Network ID */	 
 CiMmNetworkMode mode ; /**< Selected network mode */	 
 } CiMmPrimCsgSelectReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCsgSelectCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code */	 
 } CiMmPrimCsgSelectCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmCagInfo_struct 
 {	 
 UINT32 cagId ; /**< indicates the CAG ID of the cells which were found. */	 
 UINT8 hrnnLength ; /* hrnn not present if hrnnLength is 0 */	 
 UINT8 hrnn [ 48 ] ;	 
 } CiMmCagInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmCagCellInfo_struct 
 {	 
 CiMmNetworkId networkId ; /**< Network ID */	 
 CiMmNetworkMode mode ; /**< Cell capabilities network mode */	 
 UINT8 numOfCagInfo ;	 
 CiMmCagInfo cagInfo [ 5 ] ; /**< CAG information */	 
 } CiMmCagCellInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCagListSearchReq_struct 
 {	 
 UINT8 cagIndexReq ; /**< indicates the CAG index requsted to get , 0 means beging of the new search , else is reading of the last resulsts from listed offset*/	 
 } CiMmPrimCagListSearchReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCagListSearchCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code */	 
 UINT8 totalNumOfCag ; /**< number of CAG ID which were found 0 ..MAX_NUMBER_OF_CAG ( = 40 ) */	 
 UINT8 startCagIndex ; /**< first CAG ID inedx which were included in this message */	 
 UINT8 numCagList ; /**< number of CAG ID which were included in this message */	 
 CiMmCagCellInfo cagList [ 5 ] ; /**< list of all the CAG which were found and their parameters*/	 
 } CiMmPrimCagListSearchCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCagSelectReq_struct 
 {	 
 UINT32 cagId ; /**< indicates the CAG ID of the cells which were found. bits 0 -26: can receive any value. 27 - 31 should be set to 0 .*/	 
 CiMmNetworkId networkId ; /**< Network ID */	 
 CiMmNetworkMode mode ; /**< Selected network mode */	 
 } CiMmPrimCagSelectReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimCagSelectCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code */	 
 } CiMmPrimCagSelectCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimRegResultExtendedInd_struct 
 {	 
 CiMmRegStatus RegStatus ; /**< Registration status \sa CiMmRegStatus */	 
 CiBoolean csgCellInfoPresent ; /**< Indication if CSG Information field is valid */	 
 CiMmCsgCellInfo csgCellInfo ; /**< CSG Information */	 
	 
 CiBoolean cagCellInfoPresent ; /**< Indication if CAG Information field is valid */	 
 CiMmCagCellInfo cagCellInfo ; /**< CAG Information */	 
	 
 } CiMmPrimRegResultExtendedInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetPowerUpNetworkModeReq_struct 
 {	 
 CiMmPowerUpPlmnSelectionMode mode ; /**< Power up PLMN selection mode \sa CiMmPowerUpPlmnSelectionMode */	 
 } CiMmPrimSetPowerUpNetworkModeReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetPowerUpNetworkModeCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code */	 
 } CiMmPrimSetPowerUpNetworkModeCnf;

typedef CiEmptyPrim CiMmPrimGetPowerUpNetworkModeReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetPowerUpNetworkModeCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code */	 
 CiMmPowerUpPlmnSelectionMode mode ; /**< Power up PLMN selection mode \sa CiMmPowerUpPlmnSelectionMode */	 
 } CiMmPrimGetPowerUpNetworkModeCnf;

typedef UINT32 CiMmFratPlmnAction ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimFratListActionReq_struct 
 {	 
 CiMmFratPlmnAction action ;	 
 CiMmNetworkId newPlmn ; /**< PLMN mnc / mcc to be added to FRAT list*/	 
 } CiMmPrimFratListActionReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimFratListActionCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code */	 
 } CiMmPrimFratListActionCnf;

typedef CiEmptyPrim CiMmPrimGetFratListReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetFratListCnf_struct 
 {	 
 UINT8 listSize ; /**< FRAT list size*/	 
 CiMmNetworkId fratList [ 20 ] ; /**< FRAT list*/	 
 } CiMmPrimGetFratListCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetSecurityCapablilityReq_struct 
 {	 
 CiBoolean nasSecCapPresent ;	 
 CiBoolean umtsRrcCACapPresent ;	 
 CiBoolean umtsRrcIPCapPresent ;	 
	 
 INT32 nasSecCap ;	 
 INT16 umtsRrcCACap ;	 
 INT16 umtsRrcIPCap ;	 
 } CiMmPrimSetSecurityCapablilityReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetSecurityCapablilityCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code */	 
 } CiMmPrimSetSecurityCapablilityCnf;

typedef CiEmptyPrim CiMmPrimGetSecurityCapablilityReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetSecurityCapablilityCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code */	 
	 
 INT32 nasSecCap ;	 
 INT16 umtsRrcCACap ;	 
 INT16 umtsRrcIPCap ;	 
 } CiMmPrimGetSecurityCapablilityCnf;

//ICAT EXPORTED ENUM 
 typedef enum CiMmCipheringIndicatorTag 
 {	 
 CI_MM_CIPHERING_NONE = 0x00 , /* Ciphering not used */	 
 CI_MM_CIPHERING_CS = 0x01 , /* EPS integrity Algorithm EIA / 1 */	 
 CI_MM_CIPHERING_PS = 0x02 ,	 
 CI_MM_CIPHERING_CS_PS = 0x03 ,	 
	 
 } 
 _CiMmCipheringIndicator;

typedef UINT8 CiMmCipheringIndicator ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimNetworkCellMatInfoInd_struct 
 {	 
 CiMmRegMode regMode ; // <mode> , from COPS	 
 CiMmRegStatus stat ; // <stat> , from CREG	 
 CiMmNetOpIdInfo netOpInfo ; // <format> , <oper> , <act> , <domain> , from COPS	 
	 
 CiBoolean lacPresent ; // in LTE NW mode , LAC maybe not existed	 
 UINT16 lac ; // <lac> , two bytes	 
 CiBoolean racPresent ; // in LTE NW mode , no RAC	 
 UINT8 rac ; // <RAC> , one byte	 
 CiBoolean cellIdPresent ;	 
 UINT32 cellId ; // <cellId>	 
 CiBoolean tacPresent ; // in LTE NW mode , no RAC	 
 UINT16 tac ; // <tac> , two bytes	 
	 
 CiBoolean nwDTMSupported ; // whether NW support DTM	 
 CiBoolean volteAvaiable ; // whether NW support VOLTE	 
 CiBoolean imsEmAvaiable ; // whether NW support emergency bearer	 
 CiBoolean t323Avaiable ;	 
	 
 UINT8 mmtelVoiceAcBarringFactor ; /*0-100 , Voice service barring factor. From 0 to 100 where 0 means 0 % probability and 100 means 100 % probability. */	 
 UINT16 mmtelVoiceAcBarringTime ; /*0-512 , Voice service mean access barring time value in seconds. */	 
 UINT8 mmtelVideoAcBarringFactor ; /*0-100 , Video service barring factor. From 0 to 100 where 0 means 0 % probability and 100 means 100 % probability. */	 
 UINT16 mmtelVideoAcBarringTime ; /*0-512 , Video service mean access barring time value in seconds. */	 
	 
 CiBoolean phyCellIdPresent ;	 
 /*LTE: phy cell ID ;	 
 *UMTS Primary scrambling code	 
 *GSM bsic: base station identity code*/	 
 UINT16 phyCellId ;	 
 // add by taow 20150506 for ciphering begin	 
 CiMmCipheringIndicator cipheringIndicator ;	 
 // add by taow 20150506 for ciphering end	 
 /*add by taow 20181102 CQ00112738 begin*/	 
 /*GSM MODE 16 -bit GSM Absolute RF channel number ; this value must be reported *	 
 *wcdma 16 -bit UMTS Absolute RF Channel Number ; this value must be reported	 
 *lte 18 -bit LTE Absolute RF Channel Number ; this value must be reported	 
 */	 
 UINT32 frequency ;	 
 /*add by taow 20181102 CQ00112738 end*/	 
 } CiMmPrimNetworkCellMatInfoInd;

typedef UINT8 CiMmEccStatus ;
typedef UINT8 CiMmEccCnfActStatus ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimEmergencyCallStatusReq_struct 
 {	 
 CiMmEccStatus eccStatus ;	 
 CiMmAccTechMode reqAct ;	 
 } CiMmPrimEmergencyCallStatusReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimEmergencyCallStatusCnf_struct 
 {	 
 CiMmResultCode result ;	 
 CiMmAccTechMode act ;	 
 CiMmEccCnfActStatus actStatus ;	 
 } CiMmPrimEmergencyCallStatusCnf;

typedef CiEmptyPrim CiMmPrimNewAttachInd ;
typedef UINT8 CiMmUcdOpMode ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimJammingDetectionReq_struct 
 {	 
 UINT8 mode ; /**< mode of operation */	 
 UINT8 minNumberOfGsmCarriers ; /**< The number of 2 G carriers */	 
 UINT8 gsmRxLevThreshold ; /**< Threshold level for 2 G carriers */	 
 UINT8 minNumberOfUmtsCarriers ; /**< The number of 3 G carriers */	 
 UINT8 umtsRssiLevThreshold ; /**< Threshold level for 3 G carriers */	 
 CiBoolean networkIdPresent ; /**< If true - a prefered network operator is attached */	 
 CiMmNetworkId networkId ; /**< Prefered network operator */	 
 } CiMmPrimJammingDetectionReq;

//ICAT EXPORTED ENUM 
 typedef enum CIMM_JAMMING_STATUS 
 {	 
 CI_MM_2G_JAMMING_NOT_DETECTED = 0 ,	 
 CI_MM_2G_JAMMING_DETECTED ,	 
 CI_MM_3G_JAMMING_NOT_DETECTED ,	 
 CI_MM_3G_JAMMING_DETECTED	 
 } _CiMmJammingStatus;

typedef UINT8 CiMmJammingStatus ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimJammingDetectionInd_struct 
 {	 
 CiMmJammingStatus active ; /**< jamming status report */	 
 } CiMmPrimJammingDetectionInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimJammingDetectionCnf_struct 
 {	 
 CiMmResultCode result ; /**< result code */	 
 CiBoolean activePresent ; /**< if true - the current jamming status is return */	 
 UINT8 active ; /**< the current jamming status */	 
 } CiMmPrimJammingDetectionCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetGprsEgprsMultislotClassReq_struct 
 {	 
 UINT8 msClassGprs ;	 
 UINT8 msClassEgprs ;	 
 } CiMmPrimSetGprsEgprsMultislotClassReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetGprsEgprsMultislotClassCnf_struct 
 {	 
 CiMmResultCode result ;	 
 } CiMmPrimSetGprsEgprsMultislotClassCnf;

//ICAT EXPORTED ENUM 
 typedef enum CIMM_OPERATOR_NAME_SOURCE 
 {	 
 CI_MM_OPERATOR_NAME_EONS , /**< EF_OPL and EF_PNN files.*/	 
 CI_MM_OPERATOR_NAME_NITZ , /**< NITZ service.*/	 
 CI_MM_OPERATOR_NAME_CPHS , /**< CPHS Operator Name string.*/	 
 CI_MM_OPERATOR_NAME_MT , /**< MT hardcoded operator name.*/	 
 CI_MM_OPERATOR_NAME_INVALID , /**< String containing the operator name to be displayed.*/	 
 } _CiMmOperatorNameSource;

typedef UINT8 CiMmOperatorDisplayType ;
typedef UINT8 CiMmDisplayCondition ;
//ICAT EXPORTED ENUM 
 typedef enum CIMM_OPERATOR_DISPLAY_TYPE 
 {	 
 CI_MM_NUMERIC_FORMAT = 0 , /**< The network name will appear as MCC / MNC , for example 425 / 1 */	 
 CI_MM_SHORT_NAME_IN_ROM , /**< The short network name from the ROM will be returned*/	 
 CI_MM_LONG_NAME_IN_ROM , /**< The long network name from the ROM will be returned*/	 
 CI_MM_SHORT_NAME_CPHS , /**< The short network name that appears in the CPHS files on the SIM will be returned*/	 
 CI_MM_LONG_NAME_CPHS , /**< The long network name that appears in the CPHS files on the SIM will be returned*/	 
 CI_MM_SHORT_NITZ_NAME , /**< The short network name received by NITZ will be returned*/	 
 CI_MM_FULL_NITZ_NAME , /**< The full network name received by NITZ will be returned*/	 
 CI_MM_SERVICE_PROVIDER_NAME , /**< The network name that has been read from the EF_SPN file on the SIM will be returned*/	 
 CI_MM_EONS_SHORT_NAME , /**< The short network name that appears in the EONS files on the SIM will be returned*/	 
 CI_MM_EONS_LONG_NAME , /**< The long network name that appears in the EONS files on the SIM will be returned*/	 
 CI_MM_SHORT_NETWORK_NAME , /**< Not supported - for future use*/	 
 CI_MM_LONG_NETWORK_NAME , /**< Not supported - for future use*/	 
 } _CiMmOperatorDisplayType;

typedef UINT8 CiMmOperatorNameSource ;
//ICAT EXPORTED ENUM 
 typedef enum CIMM_DISPLAY_CONDITION 
 {	 
 CI_MM_SPN_DONT_DISPLAY_PLMN = 0 , /**< display of the registered PLMN is not required when the registered PLMN is either the HPLMN or a PLMN listed in SPDI list*/	 
 CI_MM_SPN_DISPLAY_PLMN , /**< display of the registered PLMN is required when the registered PLMN is either the HPLMN or a PLMN listed in the SPDI list*/	 
 CI_MM_SPN_DISPLAY_NOT_APPLICABLE , /**< */	 
 } _CiMmDisplayCondition;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmOperatorDisplayName_struct 
 {	 
 INT8 Length ; /**< The length of the string*/	 
 char OperatorName [ 128 ] ; /**< String containing the operator name to be displayed.*/	 
 CiMmOperatorNameSource OperatorNameSource ; /**< Source of returned operator name. */	 
 } CiMmOperatorDisplayName;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetDisplayOperatorNameReq_struct 
 {	 
 CiMmOperatorDisplayType type ; /**< Which type of network name should be returned. See CiMmOperatorDisplayType_enum */	 
 } CiMmPrimGetDisplayOperatorNameReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetDisplayOperatorNameCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code */	 
 CiMmOperatorDisplayType type ; /**< Which type of network name should be returned. See CiMmOperatorDisplayType_enum */	 
 CiMmOperatorDisplayName OperatorName ; /**< The requested operator name to be displayed. See CiMmOperatorDisplayName_struct */	 
 CiMmDisplayCondition DisplayCondition ; /**< The display condition indicated by the SIM. see CiMmDisplayCondition_enum*/	 
 } CiMmPrimGetDisplayOperatorNameCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimEcallRegReq_struct 
 {	 
 CiBoolean active ; /**< TRUE - New requested implementaion is acrive ; FALSE - Normal operation*/	 
 } CiMmPrimEcallRegReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimEcallRegCnf_struct 
 {	 
 CiMmResultCode ResultCode ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimEcallRegCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMMRpmPdpBlockInfoTag 
 {	 
 CiString apn ;	 
 UINT16 pdpBlockTime ;	 
 CiMmRpmPdpBlockReason pdpBlockReason ;	 
 /*Lilei , CQ00134598 , 20220418 , begin*/	 
 UINT8 F1 ;	 
 UINT8 F2 ;	 
 UINT8 F3 ;	 
 UINT8 F4 ;	 
 /*Lilei , CQ00134598 , 20220418 , end*/	 
 } CiMMRpmPdpBlockInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmRpmParamsInfoTag 
 {	 
 UINT8 N1const ; /**< Amount of application resets allowed by RPM , this value will be constant either default or value from SIM */	 
	 
 UINT8 N1 ; /**< Amount of application resets allowed by RPM. 0x00 requirement is disabled , 0x01 to 0xFF number of resets per hour */	 
 UINT8 T1 ; /**< Time to wait before PS is reset following a " Permanent " MM / GMM reject cause. 0x00 requirement is disabled , 0x01 to 0xFE in 6 minutes , 0xFF Time value to use T1_ext */	 
 UINT8 F1 ; /**< Counter that decides how many times a PDP activation can be made when PDP activation is ignored by the NW. 0x00 requirement is disabled , 0x01 to 0xFF the max attempts allowed */	 
 UINT8 F2 ; /**< Counter that decides how many times a PDP activation can be made when PDP activation is rejected by the NW with a " Permanent " reject. 0x00 requirement is disabled , 0x01 to 0xFF the max attempts allowed */	 
 UINT8 F3 ; /**< Counter that decides how many times a PDP activation can be made when PDP activation is rejected by the NW with a " Temporary " reject. 0x00 requirement is disabled , 0x01 to 0xFF the max attempts allowed */	 
 UINT8 F4 ; /**< Counter that decides how many times a PDP activation / deactivation pair can be made. 0x00 disabled , 0x01 to 0xFF the max attempts allowed */	 
	 
 UINT8 LR1 ; /**< Every LR1 hours the CBR1 counter will be decremented by 1 ( if it is 0 the requirement is disabled ) */	 
 UINT8 LR2 ; /**< Every LR2 hours the CR1 counter will be decremented by 1 ( if it is 0 the requirement is disabled ) */	 
 UINT8 LR3 ; /**< Every LR3 hours the CPDP1-4 counters will be decremented by 1 ( if it is 0 the requirement is disabled ) */	 
	 
 UINT8 CBR1 ; /**< Counter that holds the number of application resets that were blocked by the RPM because of N1 limitation. 0x00 to 0xFF */	 
 UINT8 CR1 ; /**< Counter that holds the number of times the PS was reset because of T1 expiration. 0x00 to 0xFF */	 
 UINT8 CPDP1 ; /**< Counter that holds the number of times a PDP activation req was rejected by RPM because of F1 limitation. 0x00 to 0xFF */	 
 UINT8 CPDP2 ; /**< Counter that holds the number of times a PDP activation req was rejected by RPM because of F2 limitation. 0x00 to 0xFF */	 
 UINT8 CPDP3 ; /**< Counter that holds the number of times a PDP activation req was rejected by RPM because of F3 limitation. 0x00 to 0xFF */	 
 UINT8 CPDP4 ; /**< Counter that holds the number of times a PDP activation req was rejected by RPM because of F4 limitation. 0x00 to 0xFF */	 
	 
 UINT8 Version ; /**< Holds the current RPM version. 0x00 no version info , 0x01 to 0xFF for Version 1 ~255 */	 
	 
 CiBoolean rpmEnabledFileExists ; /**< TRUE - EF_RPM Enabled flag file exists on SIM , FALSE - EF_RPM Enabled flag file doesn ' t exist on SIM */	 
 CiBoolean rpmParamsExists ; /**< TRUE - EF_RPM Params file exists on SIM , FALSE - EF_RPM Params file doesn ' t exist on SIM */	 
 CiBoolean rpmOperLrCountersExists ; /**< TRUE - EF_RPM Operational Management Counters Leak Rate file exists on SIM , FALSE - EF_RPM Operational Management Counters Leak Rate file doesn ' t exist on SIM */	 
 CiBoolean rpmOperCountersExists ; /**< TRUE - EF_RPM Operational Management Counters file exists on SIM , FALSE - EF_RPM Operational Management Counters file doesn ' t exist on SIM */	 
 CiBoolean rpmVersionExists ; /**< TRUE - EF_RPM Version file exists on SIM , FALSE - EF_RPM Version file doesn ' t exist on SIM */	 
 } CiMmRpmParamsInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimRpmInfoCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 CiBoolean att_SIM ; /**< Is SIM AT&T */	 
 CiBoolean rpmSim ; /**< Is RPM SIM AT&T */	 
 CiBoolean RpmEnabled ; /**< Is RPM enabled */	 
 CiMMRpmPdpBlockInfo pdpBlockInfo [ 4 ] ;	 
 UINT16 resetBlockTime ;	 
	 
 CiMmRpmParamsInfo rpmData ; /**< RPM parameters */	 
 } CiMmPrimRpmInfoCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimRpmInfoInd_struct 
 {	 
 CiBoolean att_SIM ; /**< Is SIM AT&T */	 
 CiBoolean rpmSim ; /**< Is RPM SIM AT&T */	 
 CiBoolean RpmEnabled ; /**< Is RPM enabled */	 
 CiMMRpmPdpBlockInfo pdpBlockInfo [ 4 ] ;	 
 UINT16 resetBlockTime ;	 
	 
 CiMmRpmParamsInfo rpmData ; /**< RPM parameters */	 
 } CiMmPrimRpmInfoInd;

//ICAT EXPORTED ENUM 
 typedef enum CIMmSELECTMODES_TAG 
 {	 
 CI_MM_SELECT_AUTO = 0 ,	 
 CI_MM_SELECT_MANUAL ,	 
	 
 CI_MM_NUM_SELECT_MODES	 
 } _CiMmSelectMode;

typedef UINT8 CiMmSelectMode ;
//ICAT EXPORTED ENUM 
 typedef enum CIMMUSERNWMODES_TAG 
 {	 
 CI_MM_USER_NW_GSM = 0 , /**< GSM */	 
 CI_MM_USER_NW_UMTS , /**< UMTS */	 
 CI_MM_USER_NW_GSM_UMTS , /**< GSM_UMTS */	 
 CI_MM_USER_NW_LTE , /**< LTE */	 
 CI_MM_USER_NW_GSM_LTE , /**< GSM_LTE */	 
 CI_MM_USER_NW_UMTS_LTE , /**< UMTS_LTE */	 
 CI_MM_USER_NW_GSM_UMTS_LTE , /**< GSM_UMTS_LTE */	 
 /*add by taow 20241231 ********** begin*/	 
 CI_MM_USER_NW_NR , /**< NR */	 
 CI_MM_USER_NW_GSM_NR ,	 
 CI_MM_USER_NW_UMTS_NR ,	 
	 
 CI_MM_USER_NW_LTE_NR , /**< LTE_NR */	 
 CI_MM_USER_NW_GSM_UMTS_NR ,	 
 CI_MM_USER_NW_GSM_LTE_NR ,	 
 CI_MM_USER_NW_NW_UMTS_LTE_NR ,	 
 CI_MM_USER_NW_GSM_UMTS_LTE_NR , /**< GSM_UMTS_LTE_NR */	 
 /*add by taow 20241231 ********** end*/	 
	 
 CI_MM_NUM_NW_MODES	 
	 
 } _CiMmUserNetworkMode;

typedef UINT8 CiMmUserNetworkMode ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetNetworkSelectionReq_struct 
 {	 
 CiMmUserNetworkMode preferredMode ;	 
 CiMmSelectMode selectionMode ;	 
 CiMmUserNetworkMode networkMode ;	 
 } CiMmPrimSetNetworkSelectionReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetNetworkSelectionCnf_struct 
 {	 
 CiMmResultCode rc ;	 
 } CiMmPrimSetNetworkSelectionCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmCellMeas_struct 
 {	 
 UINT8 rsrp ;	 
 UINT8 rsrq ;	 
 INT16 rssi ;	 
 INT8 sinr ;	 
 } 
 CiMmCellMeas;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmLteCaBandInfo_struct 
 {	 
 UINT8 dlBandwidth ; /**<0 - 1.400000 M , 1 - 3 M , 2 - 5 M , 3 - 10 M , 4 - 15 M , 5 - 20 M */	 
 UINT16 band ;	 
 UINT32 dlEuArfcn ;	 
 Boolean measValid ;	 
 CiMmCellMeas measResult ;	 
 } 
 CiMmLteCaBandInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmLteRrcPcell_struct 
 {	 
 UINT16 Pci ;	 
 UINT32 tac ;	 
 CiMmLteCaBandInfo BandInfo ;	 
	 
 } CiMmLteRrcPcell;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmLteRrcScell_struct 
 {	 
 UINT16 Pci ;	 
 UINT8 ScellStatus ;	 
 CiMmLteCaBandInfo BandInfo ;	 
 } CiMmLteRrcScell;

typedef CiEmptyPrim CiMmPrimGetLteCaInfoReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetLteCaInfoCnf_struct 
 {	 
 CiMmResultCode rc ;	 
 CiMmLteRrcPcell PcellInfo ;	 
 CiMmLteRrcScell ScellInfo ;	 
 } CiMmPrimGetLteCaInfoCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmNetOpNameInfo_struct 
 {	 
 CiMmOperatorId LongAlphaId ; /**<Long Alphanumeric Operator ID */	 
 CiMmNetworkId NetworkId ; /**< Network ID \sa CiMmNetworkId_struct */	 
 } CiMmNetOpNameInfo;

//ICAT EXPORTED STRUCT 
 typedef CiEmptyPrim CiMmPrimGetOperatorInfoReq ; 
 
 // ICAT EXPORTED ENUM 
 typedef enum CIMMROAMINGSTATUS_TAG 
 {	 
 CI_MM_ROAMING_NONE = 0 ,	 
 CI_MM_ROAMING_ON ,	 
 CI_MM_ROAMING_OFF ,	 
	 
	 
 CI_MM_NUM_ROAMING	 
	 
 } _CiMmRoamingStatus;

typedef UINT8 CiMmRoamingStatus ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetOperatorInfoCnf_struct 
 {	 
 CiMmResultCode Result ; /**< Result code */	 
 CiMmNetOpNameInfo RPlmn ; /**< Current registration mode */	 
 CiMmNetOpNameInfo HPlmn ; /**< Current network / operator ID information */	 
 CiMmRoamingStatus RoamingStatus ;	 
 } CiMmPrimGetOperatorInfoCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimOperatorStatusInd_struct 
 {	 
 CiMmNetOpNameInfo RPlmn ; /**< Current registration mode */	 
 CiMmNetOpNameInfo HPlmn ; /**< Current network / operator ID information */	 
 CiMmRoamingStatus RoamingStatus ;	 
 } CiMmPrimOperatorStatusInd;

typedef UINT8 CiMmNetworkRegStatus ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetNetworkRegistrationStatusInfoCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 CiMmNetworkRegStatus RegStatus ; /**< Registration status \sa CiMmRegStatus */	 
 CiMmAccTechMode AcT ; /**< Network access technology ( GSM , UTRAN , LTE etc. ) \sa CiMmAccTechMode */	 
 } CiMmPrimGetNetworkRegistrationStatusInfoCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimFirstSearchedNetworkOperatorInd_struct 
 {	 
 CiBoolean Present ;	 
 CiMmNetworkId NetworkId ; /**< Network ID information \sa CiMmNetworkId */	 
 CiMmAccTechMode AccTchMode ; /**< Network access technology ( GSM , UTRAN , etc. ) \sa CiMmAccTechMode */	 
 } CiMmPrimFirstSearchedNetworkOperatorInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimNetworkSearchInd_struct 
 {	 
 CiMmAccTechMode Act ; /**< Access radio technology */	 
 UINT8 numPlmns ; /**< Number of shared PLMNs in that cell */	 
 CiMmNetworkId plmns [ 20 ] ;	 
 INT32 rssiOrRscpOrRsrp [ 20 ] ; /**< 2 G:RSSI ; 3 G:RSCP ; 4 G:RSRP*/	 
 INT32 rsrqOrEcno [ 20 ] ; /**< 2 G: NA ; 3 G:rsrqOrEcno means UMTS ecno ; 4 G: rsrqOrEcno means LTE rsrq*/	 
 UINT32 freq [ 20 ] ;	 
	 
 } CiMmPrimNetworkSearchInd;

typedef CiEmptyPrim CiMmPrimGetCellLockInfoReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetCellLockInfoCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 CiMmCellLockMode mode ; /**< Cell lock mode \sa CiMmCellLockMode */	 
 CiMmCellLockActMode act ; /**< Network mode \sa CiMmNetworkMode */	 
 UINT8 bandValue ;	 
 UINT32 freq ; /**< Absolute radio frequency channel number ; GSM number 0 -1023 , TD number 10054 -10121 and 9404 -9596 */	 
 INT16 cellId ; /**< Cell parameter ID This parameter if valid for 3 G cells only 0 -127 , and for TD LTE cells only 0 -503 */	 
 } CiMmPrimGetCellLockInfoCnf;

//ICAT EXPORTED ENUM 
 /** \remarks Common Data Section */ 
 typedef enum CIMM_OOS_MODE 
 {	 
 CIMM_OOS_MODE_DISABLE = 0 ,	 
 CIMM_OOS_MODE_ENABLE ,	 
	 
	 
 CIMM_NUM_OOS_MODES ,	 
 } _CiMmOosMode;

typedef UINT8 CiMmOosMode ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetOosPhasePeriodReq_struct 
 {	 
 CiMmOosMode mode ;	 
 UINT32 oosPhasePeriod [ 3 ] ;	 
	 
 } CiMmPrimSetOosPhasePeriodReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetOosPhasePeriodCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
	 
 } CiMmPrimSetOosPhasePeriodCnf;

typedef CiEmptyPrim CiMmPrimGetOosPhasePeriodReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetOosPhasePeriodCnf_struct 
 {	 
 CiMmResultCode result ;	 
 CiMmOosMode mode ;	 
 UINT32 oosPhasePeriod [ 3 ] ;	 
	 
 } CiMmPrimGetOosPhasePeriodCnf;

//ICAT EXPORTED ENUM 
 typedef enum CIMMGSMBNADSCAN_TAG 
 {	 
 CI_MM_SCAN_GSM_NONE = 0 ,	 
 CI_MM_SCAN_GSM900 = 0x01 , /**< P GSM 900 band */	 
 CI_MM_SCAN_GSM1800 = 0x02 , /**< DCS 1800 band */	 
 CI_MM_SCAN_GSM850 = 0x04 , /**< GSM 850 band */	 
 CI_MM_SCAN_GSM1900 = 0x08 , /**< PCS 1900 band */	 
	 
	 
 CI_MM_NUM_GSM_BAND	 
 } _CiMmGSMBandScan;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetBandsScanConfigReq_struct 
 {	 
 CiMmUserNetworkMode networkMode ;	 
 CiBitRange gsmBand ;	 
 /**< A hexadecimal value that specifies the GSM frequency band. If it is set to 0 , it means not to change GSM frequency band. Range: 0 -FFFFFFFF.	 
 ( e.g.: 0x 3 = 1 ( GSM900 ) + 2 ( GSM1800 ) ) .	 
 0 No change	 
 1 GSM900 -->PGSM_BAND_BIT ( B8 )	 
 2 GSM1800 ( B3 )	 
 4 GSM850 ( B5 )	 
 8 GSM1900 ( B2 )	 
	 
 FFFFFFFF Any frequency band*/	 
 CiBitRange umtsBand ;	 
 /**< A hexadecimal value that specifies the WCDMA frequency band. If it is set to 0 , it means not to change WCDMA frequency band. Range: 0 -FFFFFFFF.	 
 ( e.g.: 0x 13 = 1 ( WCDMA 2100 ) + 2 ( WCDMA 1900 ) + 10 ( WCDMA 850 ) ) .	 
 1 band 1 ( WCDMA 2100 )	 
 2 band 2 ( WCDMA 1900 )	 
 4 band 3 ( ... )	 
 8 band 4 ( WCDMA 1700 )	 
 10 band 5 ( WCDMA 850 )	 
 20 band 6 ( WCDMA 800 )	 
 40 band 7 ( ... )	 
 80 band 8 ( WCDMA 900 )	 
	 
 FFFFFFFF Any frequency band*/	 
	 
 CiBitRange eutranBandL ; /**< Bit mask indicating the required E-UTRAN bands Low part ( bands 1 - 32 ) .*/	 
 CiBitRange eutranBandH ; /**< Bit mask indicating the required E-UTRAN bands High part ( bands 33 - 43 ) .*/	 
 CiBitRange eutranBandExt ; /**< Bit mask indicating the required E-UTRAN bands Extended part ( bands 65 - 69 ) . */	 
 /**< A hexadecimal value that specifies the LTE frequency band. If it is set to 0 or 0x40000000 , it means not to change LTE frequency band. Range: 0 -7FFFFDF3FFF	 
 ( e.g.: 0x15 =0x1 ( LTE B1 ) + 0x4 ( LTE B3 ) + 0x10 ( LTE B5 ) ) .	 
 0x1 ( CM_BAND_PREF_LTE_EUTRAN_BAND1 ) LTE B1	 
 0x4 ( CM_BAND_PREF_LTE_EUTRAN_BAND3 ) LTE B3	 
 0x10 ( CM_BAND_PREF_LTE_EUTRAN_BAND5 ) LTE B5	 
 0x40 ( CM_BAND_PREF_LTE_EUTRAN_BAND7 ) LTE B7	 
 0x80 ( CM_BAND_PREF_LTE_EUTRAN_BAND8 ) LTE B8	 
 0x80000 ( CM_BAND_PREF_LTE_EUTRAN_BAND20 ) LTE B20	 
	 
 ( eutranBandL 0xFFFFFFFF && eutranBandH 0xFFFFFFFF && eutranBandExt 0xFFFFFFFF	 
 ( CM_BAND_PREF_ANY ) Any frequency band ) */	 
 /*add by taow 20241231 ********** begin*/	 
 CiBitRange NrFr1Band [ 4 ] ; /**<Bitmap for NR FR1 BANDS to Scan , n1~n96 */	 
 CiBitRange NrFr2Band ; /**<Bitmap for NR FR2 BANDS to Scan */	 
 /*add by taow 20241231 ********** end*/	 
 } CiMmPrimSetBandsScanConfigReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetBandsScanConfigCnf_struct 
 {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
	 
 } CiMmPrimSetBandsScanConfigCnf;

typedef CiEmptyPrim CiMmPrimGetBandsScanConfigReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetBandsScanConfigCnf_struct 
 {	 
 CiMmResultCode result ;	 
 CiMmUserNetworkMode networkMode ;	 
 CiBitRange gsmBand ;	 
 CiBitRange umtsBand ;	 
	 
 CiBitRange eutranBandL ; /**< Bit mask indicating the required E-UTRAN bands Low part ( bands 1 - 32 ) .*/	 
 CiBitRange eutranBandH ; /**< Bit mask indicating the required E-UTRAN bands High part ( bands 33 - 43 ) .*/	 
 CiBitRange eutranBandExt ; /**< Bit mask indicating the required E-UTRAN bands Extended part ( bands 65 - 69 ) . */	 
 /*add by taow 20241231 ********** begin*/	 
 CiBitRange NrFr1Band [ 4 ] ; /**<Bitmap for NR FR1 BANDS to Scan , n1~n96 */	 
 CiBitRange NrFr2Band ; /**<Bitmap for NR FR2 BANDS to Scan */	 
 /*add by taow 20241231 ********** end*/	 
 } CiMmPrimGetBandsScanConfigCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmBandsScanResultTag /* Used to report frequencies of each PLMN*/ 
 {	 
 CiMmAccTechMode act ; /**< Access radio technology */	 
 UINT32 band ; /**< lte / umts / gsm band*/	 
 UINT32 freq ;	 
 INT32 rscpOrGsmRssi ; /**< 2 G:RSSI ; 3 G:RSCP ; 4 G:RSRP*/	 
 INT32 rsrqOrEcno ; /**< 2 G: NA ; 3 G:rsrqOrEcno means UMTS ecno ; 4 G: rsrqOrEcno means LTE rsrq*/	 
	 
 UINT16 pciOrpscOrbsic ; /**< 2 G:bsic 3 G:psc , 4 G:pci*/	 
 UINT16 tacOrLac ; /**<lte / gsm / umts use*/	 
 UINT32 cellId ; /**<gsm / umts / LTE / NR cellID low 32 bit use*/	 
 INT16 rxlev ; /**< only gsm use */	 
 INT16 c1 ; /**< only gsm use*/	 
 INT16 rssi ; /**< use in LTE , umts */	 
	 
 CiBoolean gprsSupported ; /**<only gsm use*/	 
 CiBoolean cellBarred ; /**< lte / gsm / umts use*/	 
 UINT32 cellIdH ; /**< NR cellID high 14 bit*/	 
 UINT32 reserved2 ;	 
 UINT32 reserved3 ;	 
 UINT32 reserved4 ;	 
 UINT32 reserved5 ;	 
 UINT32 reserved6 ;	 
	 
	 
 } CiMmBandsScanResult;

typedef CiEmptyPrim CiMmPrimGetBandsScanReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetBandsScanCnf_struct 
 {	 
 CiMmResultCode result ;	 
 } CiMmPrimGetBandsScanCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetBandsScanInd_struct 
 {	 
	 
 CiMmOperatorId longAlphaId ; /**< Long alphanumeric operator ID \sa CiMmOperatorId_struct */	 
 CiMmOperatorId shortAlphaId ; /**< Short alphanumeric operator ID \sa CiMmOperatorId */	 
 CiMmNetworkId networkId ; /**< Network ID information \sa CiMmNetworkId */	 
 UINT32 numScanResult ;	 
 CiMmBandsScanResult scanResult [ 20 ] ;	 
	 
 } CiMmPrimGetBandsScanInd;

typedef CiEmptyPrim CiMmPrimAbortBandsScanReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimAbortBandsScanCnf_struct 
 {	 
 CiMmResultCode result ;	 
 } CiMmPrimAbortBandsScanCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimNwEcallOverImsSupportInd_struct {	 
 UINT8 eCall_IMS_supp ;	 
 } CiMmPrimNwEcallOverImsSupportInd;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimImsEcallRegReq_struct 
 {	 
 CiBoolean active ; /**< TRUE - New requested implementaion is acrive ; FALSE - detach operation*/	 
 } CiMmPrimImsEcallRegReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimImsEcallRegCnf_struct 
 {	 
 CiMmResultCode result ;	 
 } CiMmPrimImsEcallRegCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetRpmReq_struct {	 
 UINT8 option ; /**< Set options. 0 : RPM enabled ; 1 : RPM params ; 2 : RPM LR */	 
	 
 CiBoolean rpmEnabledPresent ; /**< whether <rpmEnabled> is set or not */	 
 CiBoolean rpmEnabled ; /**< Is RPM enabled / disabled */	 
	 
 /**< RPM params */	 
 CiBoolean N1constPresent ;	 
 CiBoolean T1Present ;	 
 CiBoolean F1Present ;	 
 CiBoolean F2Present ;	 
 CiBoolean F3Present ;	 
 CiBoolean F4Present ;	 
 UINT8 N1const ; /**< Amount of application resets allowed by RPM , this value will be constant either default or value from SIM */	 
 UINT8 T1 ; /**< Time to wait before PS is reset following a " Permanent " MM / GMM reject cause. 0x00 requirement is disabled , 0x01 to 0xFE in 6 minutes , 0xFF Time value to use T1_ext */	 
 UINT8 F1 ; /**< Counter that decides how many times a PDP activation can be made when PDP activation is ignored by the NW. 0x00 requirement is disabled , 0x01 to 0xFF the max attempts allowed */	 
 UINT8 F2 ; /**< Counter that decides how many times a PDP activation can be made when PDP activation is rejected by the NW with a " Permanent " reject. 0x00 requirement is disabled , 0x01 to 0xFF the max attempts allowed */	 
 UINT8 F3 ; /**< Counter that decides how many times a PDP activation can be made when PDP activation is rejected by the NW with a " Temporary " reject. 0x00 requirement is disabled , 0x01 to 0xFF the max attempts allowed */	 
 UINT8 F4 ; /**< Counter that decides how many times a PDP activation / deactivation pair can be made. 0x00 disabled , 0x01 to 0xFF the max attempts allowed */	 
	 
 /**< RPM LR */	 
 CiBoolean LR1Present ;	 
 CiBoolean LR2Present ;	 
 CiBoolean LR3Present ;	 
 UINT8 LR1 ; /**< Every LR1 hours the CBR1 counter will be decremented by 1 ( if it is 0 the requirement is disabled ) */	 
 UINT8 LR2 ; /**< Every LR2 hours the CR1 counter will be decremented by 1 ( if it is 0 the requirement is disabled ) */	 
 UINT8 LR3 ; /**< Every LR3 hours the CPDP1-4 counters will be decremented by 1 ( if it is 0 the requirement is disabled ) */	 
 } CiMmPrimSetRpmReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetRpmCnf_struct {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimSetRpmCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiPlmnTag 
 {	 
 UINT16 mcc ; /**< 3 -digit country code */	 
 UINT16 mnc ; /**< 3 -digit network code */	 
 UINT16 accessTechnology ;	 
	 
 } CiPlmn;

//ICAT EXPORTED STRUCT 
 typedef struct CiArfcnListForCuTag 
 {	 
 UINT16 accessTechnology ;	 
 UINT8 numArfcn ;	 
 UINT32 arfcnList [ 8 ] ;	 
 // UINT16 redCapCellExist ; / *CQ00151269 by xgh 20240624 begin for clear EARFCN * /	 
 } 
 CiArfcnListForCu;

//ICAT EXPORTED STRUCT 
 typedef struct CiUarfcnInsrtFromCuTag 
 {	 
 CiPlmn reqPlmn ;	 
 CiArfcnListForCu arfcnInsrtFromCu ;	 
 } CiUarfcnInsrtFromCu;

//ICAT EXPORTED STRUCT 
 typedef struct CiUarfcnListForCuTag 
 {	 
 CiPlmn reqPlmn ;	 
 CiArfcnListForCu arfcnListForCu [ 4 ] ;	 
 } CiUarfcnListForCu;

//ICAT EXPORTED STRUCT 
 typedef struct CiModemInfoTag 
 {	 
 UINT8 cat ; /**< 1 , cat1 ; 4 , cat4 ; */	 
 UINT8 rel ; /**<cat1 , 9 or 13 ; cat4 , 4 ; */	 
 UINT8 antType ;	 
 UINT16 xo ; /**<0 , TCXO ; 1 , DCXO*/	 
 UINT8 resev [ 28 ] ;	 
 } CiModemInfo;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetSelectVsimReq_struct {	 
 UINT8 vsimEnable ; /* vsimEnable 0 :disable VSIM ; 1 : enable VSIM*/	 
 UINT8 resevered ;	 
 } CiMmPrimSetSelectVsimReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPriSetSelectVsimCnf_struct {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 } CiMmPrimSetSelectVsimCnf;

typedef CiEmptyPrim CiMmPrimGetSelectVsimReq ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPriGetSelectVsimCnf_struct {	 
 CiMmResultCode result ; /**< Result code \sa CiMmResultCode */	 
 UINT8 vsimEnable ; /* vsimEnable 0 :disable VSIM ; 1 : enable VSIM*/	 
 UINT8 resevered ;	 
 } CiMmPrimGetSelectVsimCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmSetTsnControlTag 
 {	 
 UINT8 ctrlType ; /**< 1 : start , 0 : stop */	 
 UINT8 reportType ; /**< 1 : Periodic , 0 :event */	 
 UINT32 reportDuration ; /**< only valid when reportType=Periodic */	 
 UINT32 bCoderConfig ;	 
 UINT8 reSyncType ; /**< 0 ignore reSyncPeriod , 1 start reSync*/	 
 UINT8 dummy ;	 
 UINT16 reSyncPeriod ; /**< unit s , default 10 s , 5 s , 10 s , 20 s , 60 s , 120 , 300 s , 600 s*/	 
 UINT16 dummy1 ;	 
 UINT32 dummy2 ;	 
 } 
 CiMmSetTsnControl;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmTsnParasTag 
 {	 
 UINT8 ctrlType ;	 
 UINT8 reportType ;	 
 UINT32 reportDuration ;	 
 UINT8 reSyncType ; /**< 0 ignore reSyncPeriod , 1 start reSync*/	 
 UINT8 dummy ;	 
 UINT16 reSyncPeriod ; /**< unit s , default 10 s , 5 s , 10 s , 20 s , 60 s , 120 , 300 s , 600 s*/	 
 UINT16 dummy1 ;	 
 UINT32 dummy2 ;	 
 } 
 CiMmTsnParas;

//ICAT EXPORTED UNION 
 typedef union CiMmSetCommonParasTag 
 {	 
 CiMmSetTsnControl setTsnControl ;	 
 } 
 CiMmSetCommonParas;

//ICAT EXPORTED ENUM 
 typedef enum _CiMmSetParasTypeTag 
 {	 
 CI_MM_SET_PARAS_NULL ,	 
	 
 CI_MM_SET_TSN_CONTROL ,	 
	 
 NUM_OF_CI_MM_SET_PARAS_TYPE	 
 } 
 _CiMmSetParasType;

typedef UINT8 CiMmSetParasType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetParasReqTag 
 {	 
 CiMmSetParasType type ;	 
 UINT8 reserved [ 3 ] ;	 
 CiMmSetCommonParas paras ;	 
 } 
 CiMmPrimSetParasReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimSetParasCnfTag 
 {	 
 CiMmResultCode result ;	 
 } 
 CiMmPrimSetParasCnf;

//ICAT EXPORTED UNION 
 typedef union CiMmCommonParasRptTag 
 {	 
	 
	 
	 
 CiMmTsnParas tsnParas ;	 
 } 
 CiMmCommonParasRpt;

//ICAT EXPORTED ENUM 
 typedef enum _CiMmRptParasTypeTag 
 {	 
 CI_MM_RPT_PARAS_NULL ,	 
	 
 /* Modified by Daniel for SCT505 113238 , begin */	 
	 
	 
	 
 CI_MM_RPT_TSN_PARAS ,	 
 /* Modified by Daniel for SCT505 113238 , end */	 
	 
 NUM_OF_CI_MM_RPT_PARAS_TYPE	 
 } 
 _CiMmRptParasType;

typedef UINT8 CiMmRptParasType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimParasReportIndTag 
 {	 
 CiMmRptParasType type ;	 
 UINT8 reserved [ 3 ] ;	 
 CiMmCommonParasRpt commonParasRpt ;	 
 } 
 CiMmPrimParasReportInd;

//ICAT EXPORTED UNION 
 typedef union CiMmGetCommonParasTag 
 {	 
 CiMmGetTsnControl getTsnControl ;	 
 } 
 CiMmGetCommonParas;

//ICAT EXPORTED ENUM 
 typedef enum _CiMmGetParasTypeTag 
 {	 
 CI_MM_GET_PARAS_NULL ,	 
	 
 CI_MM_GET_TSN_CONTROL ,	 
	 
 NUM_OF_CI_MM_GET_PARAS_TYPE	 
 } 
 _CiMmGetParasType;

typedef UINT8 CiMmGetParasType ;
//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetParasReqTag 
 {	 
 CiMmGetParasType type ;	 
 UINT8 reserved [ 3 ] ;	 
 CiMmGetCommonParas paras ;	 
 } 
 CiMmPrimGetParasReq;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimGetParasCnfTag 
 {	 
 CiMmResultCode result ;	 
 CiMmRptParasType type ;	 
 CiMmCommonParasRpt commonParasRpt ;	 
 } 
 CiMmPrimGetParasCnf;

//ICAT EXPORTED STRUCT 
 typedef struct CiMmPrimNetworkCellMatInfoInd_V0001_struct 
 {	 
 CiMmRegMode regMode ; // <mode> , from COPS	 
 CiMmRegStatus stat ; // <stat> , from CREG	 
 CiMmNetOpIdInfo netOpInfo ; // <format> , <oper> , <act> , <domain> , from COPS	 
	 
 CiBoolean lacPresent ; // in LTE NW mode , LAC maybe not existed	 
 UINT16 lac ; // <lac> , two bytes	 
 CiBoolean racPresent ; // in LTE NW mode , no RAC	 
 UINT8 rac ; // <RAC> , one byte	 
 CiBoolean cellIdPresent ;	 
 UINT32 cellId ; // <cellId>	 
 CiBoolean tacPresent ; // in LTE NW mode , no RAC	 
 UINT16 tac ; // <tac> , two bytes	 
	 
 CiBoolean nwDTMSupported ; // whether NW support DTM	 
 CiBoolean volteAvaiable ; // whether NW support VOLTE	 
 CiBoolean imsEmAvaiable ; // whether NW support emergency bearer	 
 CiBoolean t323Avaiable ;	 
	 
 UINT8 mmtelVoiceAcBarringFactor ; /*0-100 , Voice service barring factor. From 0 to 100 where 0 means 0 % probability and 100 means 100 % probability. */	 
 UINT16 mmtelVoiceAcBarringTime ; /*0-512 , Voice service mean access barring time value in seconds. */	 
 UINT8 mmtelVideoAcBarringFactor ; /*0-100 , Video service barring factor. From 0 to 100 where 0 means 0 % probability and 100 means 100 % probability. */	 
 UINT16 mmtelVideoAcBarringTime ; /*0-512 , Video service mean access barring time value in seconds. */	 
	 
 CiBoolean phyCellIdPresent ;	 
 /*LTE: phy cell ID ;	 
 *UMTS Primary scrambling code	 
 *GSM bsic: base station identity code*/	 
 UINT16 phyCellId ;	 
 // add by taow 20150506 for ciphering begin	 
 CiMmCipheringIndicator cipheringIndicator ;	 
 // add by taow 20150506 for ciphering end	 
	 
 } CiMmPrimNetworkCellMatInfoInd_V0001;

typedef unsigned char Boolean ;
//ICAT EXPORTED ENUM 
 typedef enum TDTypeTag 
 {	 
 TDT_MATCH_ALL = 0x01 ,	 
 TDT_OSID_AND_OSAPPID = 0x08 ,	 
 TDT_IPV4_REMOTE_ADDR = 0x10 ,	 
 TDT_IPV6_REMOTE_ADDR_AND_PREFIX_LEN = 0x21 ,	 
 TDT_PROTOCOL_IDENTIFIER_OR_NEXT_HDR = 0x30 ,	 
 TDT_SINGLE_REMOTE_PORT = 0x50 ,	 
 TDT_REMOTE_PORT_RANGE = 0x51 ,	 
 TDT_IP_3_TYPE = 0x52 ,	 
 TDT_SECURITY_PARA_INDEX = 0x60 ,	 
 TDT_TYPE_OF_SERVICE_OR_TRAFFIC_CLASS = 0x70 ,	 
 TDT_FLOW_LABEL = 0x80 ,	 
 TDT_DEST_MAC_ADDR = 0x81 ,	 
 TDT_8021Q_CTAG_VID = 0x83 ,	 
 TDT_8021Q_STAG_VID = 0x84 ,	 
 TDT_8021Q_CTAG_PCP_DEI = 0x85 ,	 
 TDT_8021Q_STAG_PCP_DEI = 0x86 ,	 
 TDT_ETHERTYPE = 0x87 ,	 
 TDT_DNN = 0x88 ,	 
 TDT_CONNECTION_CAP = 0x90 ,	 
 TDT_DEST_FQDN = 0x91 ,	 
 TDT_OSAPPID = 0xA0 ,	 
	 
 NUM_OF_TDT	 
 } 
 TDType;

//ICAT EXPORTED STRUCT 
 typedef struct _AccessPointNameTag 
 {	 
 UINT8 length ;	 
 UINT8 name [ 100 ] ;	 
 } 
 AccessPointName_t;

typedef AccessPointName_t DNN ;
//ICAT EXPORTED ENUM 
 typedef enum SscModeTag 
 {	 
 SSC_MODE_1 = 1 ,	 
 SSC_MODE_2 ,	 
 SSC_MODE_3 ,	 
	 
 SSC_MODE_RESERVED	 
 } 
 SscMode;

//ICAT EXPORTED STRUCT 
 typedef struct OsIdTag 
 {	 
 UINT8 lenOfOsId ;	 
 UINT8 osId [ 16 ] ;	 
 } 
 OsId;

//ICAT EXPORTED STRUCT 
 typedef struct OsAppIdTag 
 {	 
 UINT8 lenOfOsAppId ;	 
 UINT8 osAppId [ 64 ] ;	 
 } 
 OsAppId;

//ICAT EXPORTED STRUCT 
 typedef struct OsIdAndOsAppIdTag 
 {	 
 OsId osId ;	 
 OsAppId osAppId ;	 
 } 
 OsIdAndOsAppId;

//ICAT EXPORTED STRUCT 
 typedef struct TDTag 
 {	 
 Boolean matchAll ;	 
	 
 Boolean osIDAndOsAppIdPresent ;	 
 OsIdAndOsAppId osIdAndOsAppId ;	 
	 
 Boolean ipv4RemoteAddrPresent ;	 
 UINT8 ipv4Addr [ 4 ] ;	 
 UINT8 ipv4AddrMask [ 4 ] ;	 
	 
 Boolean ipv6RemoteAddrAndPrefixLenPresent ;	 
 UINT8 ipv6Addr [ 16 ] ;	 
 UINT8 prefixLen ;	 
	 
 Boolean protocolIdentifierOrNextHdrPresent ;	 
 UINT8 protocolIdentifierOrNextHdr ;	 
	 
 Boolean singleRemotePortPresent ;	 
 UINT16 portNumber ;	 
	 
 Boolean remotePortRangePresent ;	 
 UINT16 lowLimit ;	 
 UINT16 highLimit ;	 
	 
 Boolean securityParaIndexPresent ;	 
 UINT32 ipsecSecurityParaIndex ;	 
	 
 Boolean typeOfServiceOrTrafficClassPresent ;	 
 UINT8 typeOfServiceOrTrafficClass ;	 
 UINT8 typeOfServiceOrTrafficClassMask ;	 
	 
 /* For " flow label type " , the traffic descriptor component value field shall be encoded as three octets which specify	 
 the IPv6 flow label. The bits 8 through 5 of the first octet shall be spare whereas the remaining 20 bits shall contain	 
 the IPv6 flow label. */	 
 Boolean flowLabelPresent ;	 
 UINT32 ipv6FlowLabel ;	 
	 
 Boolean destMacAddrPresent ;	 
 UINT8 macAddr [ 6 ] ;	 
	 
 /* For " 802.100000 Q C-TAG VID type " , the traffic descriptor component value field shall be encoded as two octets which specify	 
 the VID of the customer-VLAN tag ( C-TAG ) . The bits 8 through 5 of the first octet shall be spare whereas the remaining	 
 12 bits shall contain the VID. */	 
 Boolean ctagVidPresent ;	 
 UINT16 ctagVid ;	 
	 
 /* For " 802.100000 Q S-TAG VID type " , the traffic descriptor component value field shall be encoded as two octets which specify	 
 the VID of the service-VLAN tag ( S-TAG ) . The bits 8 through 5 of the first octet shall be spare whereas the remaining	 
 12 bits shall contain the VID. */	 
 Boolean stagVidPresent ;	 
 UINT16 stagVid ;	 
	 
 /* For " 802.100000 Q C-TAG PCP / DEI type " , the traffic descriptor component value field shall be encoded as one octet which	 
 specifies the 802.100000 Q C-TAG PCP and DEI. The bits 8 through 5 of the octet shall be spare , and the bits 4 through 2	 
 contain the PCP and bit 1 contains the DEI. */	 
 Boolean ctagPcpDeiPresent ;	 
 UINT8 ctagPcp ;	 
 Boolean ctagDei ;	 
	 
 /* For " 802.100000 Q S-TAG PCP / DEI type " , the traffic descriptor component value field shall be encoded as one octet which	 
 specifies the 802.100000 Q S-TAG PCP and DEI. The bits 8 through 5 of the octet shall be spare , and the bits 4 through 2	 
 contain the PCP and bit 1 contains the DEI. */	 
 Boolean stagPcpDeiPresent ;	 
 UINT8 stagPcp ;	 
 Boolean stagDei ;	 
	 
 Boolean etherTypePresent ;	 
 UINT16 etherType ;	 
	 
 Boolean dnnPresent ;	 
 DNN dnn ;	 
	 
 Boolean connectionCapPresent ;	 
 UINT8 numOfConnectionCap ;	 
 UINT8 connectionCapId [ 4 ] ;	 
	 
 Boolean destFQDNPresent ;	 
 UINT8 lenOfDestFQDN ;	 
 UINT8 fqdn [ 64 ] ;	 
	 
 Boolean osAppIdPresent ;	 
 OsAppId osAppId ;	 
 } 
 TD;

//ICAT EXPORTED ENUM 
 typedef enum RSDComponentTypeTag 
 {	 
 RSDT_SSC_MODE = 0x01 ,	 
 RSDT_SNSSAI = 0x02 ,	 
 RSDT_DNN = 0x04 ,	 
 RSDT_PDU_SESSION_TYPE = 0x08 ,	 
 RSDT_PREFERRED_ACCESS_TYPE = 0x10 ,	 
 RSDT_NONSEAMLESS_NON3GPP_OFFLOAD_IND = 0x20 ,	 
	 
 NUM_OF_RSDT	 
 } 
 RSDComponentType;

//ICAT EXPORTED STRUCT 
 typedef struct RSDComponentsTag 
 {	 
 /* The " SSC mode type " route selection descriptor component shall not appear more than once in the route selection descriptor. */	 
 Boolean sscModePresent ;	 
 UINT8 sscMode ;	 
	 
 /* Deleted by Daniel for CQ00139819 , begin */	 
 Boolean snssaiPresent ;	 
 /* Deleted by Daniel for CQ00139819 , end */	 
 UINT8 numOfSnssai ;	 
 PsSNssai snssai [ 3 ] ;	 
	 
 Boolean dnnPresent ;	 
 DNN dnn ;	 
	 
 /* The " PDU session type " route selection descriptor component shall not appear more than once in the route selection descriptor. */	 
 Boolean pduSessionTypePresent ;	 
 UINT8 pduSessionType ;	 
	 
 /* The " preferred access type " route selection descriptor component shall not appear more than once in the route selection descriptor. */	 
 Boolean preferredAccessTypePresent ;	 
 UINT8 preferredAccessType ;	 
	 
 /* For " non-seamless non-3GPP offload indication type " , the route selection descriptor component shall not include the route selection	 
 descriptor component value field.	 
 The " non-seamless non-3GPP offload indication type " route selection descriptor component shall not appear more than once in the route selection descriptor.	 
 If the " non-seamless non-3GPP offload indication type " route selection descriptor component is included in a route selection descriptor ,	 
 there shall be no route selection descriptor component with a type other than " non-seamless non-3GPP offload indication type " in the route selection descriptor. */	 
 Boolean nonseamlessNon3gppOffloadInd ;	 
 } 
 RSDComponents;

//ICAT EXPORTED STRUCT 
 typedef struct RSDTag 
 {	 
 UINT8 precedenceOfRSD ;	 
 RSDComponents rsdComponents ;	 
 } 
 RSD;

//ICAT EXPORTED STRUCT 
 typedef struct URSPRuleTag 
 {	 
 UINT8 precedenceOfURSPRule ;	 
 TD td ;	 
 UINT8 numOfRSD ;	 
 RSD rsd [ 3 ] ;	 
 } 
 URSPRule;

//ICAT EXPORTED STRUCT 
 typedef struct URSPRulesPerPlmnTag 
 {	 
 UINT8 numOfUrspRule ;	 
 URSPRule urspRule [ 5 ] ;	 
 } 
 URSPRulesPerPlmn;

typedef signed int ptrdiff_t ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef long double max_align_t ;
typedef void ( *StateChangedCb ) ( UINT8 , INT32 , void * ) ;
//ICAT EXPORTED ENUM 
 enum 
 {	 
 SIM_0 ,	 
 SIM_1 ,	 
 NUM_OF_SIM	 
 };

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 CHANNEL_MMI ,	 
 CHANNEL_IMS ,	 
 NUM_OF_CHANNEL_TYPE	 
 } AT_CHANNEL_TYPE;

//ICAT EXPORTED STRUCT 
 typedef struct _TelAtpCtrl 
 {	 
 void *taskRef ;	 
 UINT32 iFd ;	 
 char *path ;	 
 UINT32 index ;	 
 TelMsgDataMode smsDataEntryMode ;	 
 BOOL bEnable ;	 
 BOOL bIsSinkMode ;	 
	 
 TelAtpPdpCtx pdpCtx ;	 
 TelAtpTftCtx tftCtx ;	 
	 
 UINT32 reqHandle ;	 
 UINT32 cnmaReply ; /* used in +CNMA */	 
 UINT32 smsOverNas ;	 
 UINT32 cnmaFlag ; // 0 -- AT+CNMA , 1 -- AT*CNMA	 
 UINT8 psCurrCid ;	 
 } TelAtpCtrl;

typedef void ( *wifi_event_cb_t ) ( char *iccid , char *mac ) ;
typedef void ( *ql_wifi_event_cb_t ) ( QL_WIFI_TYPE_E type , char *mac , char *ip ) ;
typedef void ( *nwst_callback_fn ) ( int event ) ;
typedef UINT8 u8_t ;
typedef UINT16 u16_t ;
typedef UINT32 u32_t ;
typedef UINT64 u64_t ;
typedef INT8 s8_t ;
typedef INT16 s16_t ;
typedef INT32 s32_t ;
typedef void* msg_t ;
typedef u32_t mem_ptr_t ;
typedef unsigned long long uint64_t ;
typedef union {
 ip4_addr_t ip4 ;
 ip6_addr_t ip6 ;
 } ipX_addr_t ;
typedef s8_t err_t ;
typedef u16_t mem_size_t ;
typedef UINT32 sys_prot_t ;
typedef void * sys_mutex_t ;
typedef void * sys_sem_t ;
typedef void * sys_mbox_t ;
typedef void * sys_thread_t ;
typedef void ( *eventCallback ) ( void *pArgs , int evt ) ;
typedef void ( *printCallback ) ( char *printstr ) ;
typedef void ( *lwip_thread_fn ) ( void *pArgs ) ;
typedef err_t ( *lwip_run_entry ) ( void *pArgs ) ;
typedef err_t ( *pbuf_bypass_fn ) ( void *p , void *inp ) ;
typedef void* ( *alloc_fn ) ( size_t size ) ;
typedef void ( *free_fn ) ( pmsg* msg ) ;
typedef void ( * socket_callback ) ( int s , int evt , u16_t len ) ;
typedef u32_t socklen_t ;
typedef int ( *netif_lowoutput_fn ) ( u8_t *data , u32_t len , void *msg ) ;
typedef void ( *tcp_err_fn ) ( void *arg , err_t err ) ;
typedef u32_t tcpwnd_size_t ;
typedef u16_t tcpflags_t ;
typedef unsigned int time_t ;
typedef u32_t in_addr_t ;
typedef void ( * sys_timeout_handler ) ( void *arg ) ;
typedef void ( *tcpip_init_done_fn ) ( void *arg ) ;
typedef void ( *tcpip_callback_fn ) ( void *ctx ) ;
typedef void ( *tcpip_select_fn ) ( int event , void *ctx , void *arg ) ;
typedef lwip_mq* mq_t ;
typedef void ( *lwip_nwst_callback_fn ) ( int event ) ;
