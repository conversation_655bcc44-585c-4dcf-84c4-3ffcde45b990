# 1 "L:/PLT/softutil/softutil/src/simPin.c"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
 *               MODULE IMPLEMENTATION FILE
 *******************************************************************************
 * Filename: simPin.c
 ******************************************************************************/

// The SIM PIN code is saved in the special area, which is
//   - non cleared upon the COMM restart
//   - visible by COMM but not visible by the APPS
// COMM "knows" to reset this area on it's "first start of life" but keep it on every Silent-Reset
// ------------
// The PIN code is given as ROW-BYTE-BUFFER with specified byte-lenght.
// The PIN encoding is the caller procedure responsibility!!! The buffer is ROW-DATA for the Platform-Service!!!
// If the given lenght is bigger than AREA, the number of really saved bytes (less than required) would be returned.
// The user must provide the correct LENGHT to be read or saved
//   and should check the Required LEN is equal to returned LENGHT bytes to be sure the operation succeed.
// LEGHT=0 means "no PIN code saved".
// The usimPin_validInside() provided to check-only the PIN is saved or not.

# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
/* string.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.11 */
/* Copyright (C) Codemist Ltd., 1988-1993.                        */
/* Copyright 1991-1993 ARM Limited. All rights reserved.          */
/* version 0.04 */

/*
 * RCS $Revision$
 * Checkin $Date$
 */

/*
 * string.h declares one type and several functions, and defines one macro
 * useful for manipulating character arrays and other objects treated as
 * character arrays. Various methods are used for determining the lengths of
 * the arrays, but in all cases a char * or void * argument points to the
 * initial (lowest addresses) character of the array. If an array is written
 * beyond the end of an object, the behaviour is undefined.
 */












# 38 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 54 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"




extern __declspec(__nothrow) void *memcpy(void * __restrict /*s1*/,
                    const void * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) void *memmove(void * /*s1*/,
                    const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. Copying takes place as if the n characters from the
    * object pointed to by s2 are first copied into a temporary array of n
    * characters that does not overlap the objects pointed to by s1 and s2,
    * and then the n characters from the temporary array are copied into the
    * object pointed to by s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strcpy(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string pointed to by s2 (including the terminating nul
    * character) into the array pointed to by s1. If copying takes place
    * between objects that overlap, the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncpy(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies not more than n characters (characters that follow a null
    * character are not copied) from the array pointed to by s2 into the array
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */

extern __declspec(__nothrow) char *strcat(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends a copy of the string pointed to by s2 (including the terminating
    * null character) to the end of the string pointed to by s1. The initial
    * character of s2 overwrites the null character at the end of s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncat(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends not more than n characters (a null character and characters that
    * follow it are not appended) from the array pointed to by s2 to the end of
    * the string pointed to by s1. The initial character of s2 overwrites the
    * null character at the end of s1. A terminating null character is always
    * appended to the result.
    * Returns: the value of s1.
    */

/*
 * The sign of a nonzero value returned by the comparison functions is
 * determined by the sign of the difference between the values of the first
 * pair of characters (both interpreted as unsigned char) that differ in the
 * objects being compared.
 */

extern __declspec(__nothrow) int memcmp(const void * /*s1*/, const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the first n characters of the object pointed to by s1 to the
    * first n characters of the object pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the object pointed to by s1 is greater than, equal to, or
    *          less than the object pointed to by s2.
    */
extern __declspec(__nothrow) int strcmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcasecmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2,
    * case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncasecmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2, case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcoll(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2, both
    * interpreted as appropriate to the LC_COLLATE category of the current
    * locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2 when both are interpreted
    *          as appropriate to the current locale.
    */

extern __declspec(__nothrow) size_t strxfrm(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * transforms the string pointed to by s2 and places the resulting string
    * into the array pointed to by s1. The transformation function is such that
    * if the strcmp function is applied to two transformed strings, it returns
    * a value greater than, equal to or less than zero, corresponding to the
    * result of the strcoll function applied to the same two original strings.
    * No more than n characters are placed into the resulting array pointed to
    * by s1, including the terminating null character. If n is zero, s1 is
    * permitted to be a null pointer. If copying takes place between objects
    * that overlap, the behaviour is undefined.
    * Returns: The length of the transformed string is returned (not including
    *          the terminating null character). If the value returned is n or
    *          more, the contents of the array pointed to by s1 are
    *          indeterminate.
    */


# 193 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) void *memchr(const void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an unsigned char) in the
    * initial n characters (each interpreted as unsigned char) of the object
    * pointed to by s.
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the object.
    */

# 209 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an char) in the string
    * pointed to by s (including the terminating null character).
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the string.
    */

extern __declspec(__nothrow) size_t strcspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters not from the string pointed to by
    * s2. The terminating null character is not considered part of s2.
    * Returns: the length of the segment.
    */

# 232 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strpbrk(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of any
    * character from the string pointed to by s2.
    * Returns: returns a pointer to the character, or a null pointer if no
    *          character form s2 occurs in s1.
    */

# 247 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strrchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the last occurence of c (converted to a char) in the string
    * pointed to by s. The terminating null character is considered part of
    * the string.
    * Returns: returns a pointer to the character, or a null pointer if c does
    *          not occur in the string.
    */

extern __declspec(__nothrow) size_t strspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters from the string pointed to by S2
    * Returns: the length of the segment.
    */

# 270 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strstr(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of the
    * sequence of characters (excluding the terminating null character) in the
    * string pointed to by s2.
    * Returns: a pointer to the located string, or a null pointer if the string
    *          is not found.
    */

extern __declspec(__nothrow) char *strtok(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) char *_strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

extern __declspec(__nothrow) char *strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

   /*
    * A sequence of calls to the strtok function breaks the string pointed to
    * by s1 into a sequence of tokens, each of which is delimited by a
    * character from the string pointed to by s2. The first call in the
    * sequence has s1 as its first argument, and is followed by calls with a
    * null pointer as their first argument. The separator string pointed to by
    * s2 may be different from call to call.
    * The first call in the sequence searches for the first character that is
    * not contained in the current separator string s2. If no such character
    * is found, then there are no tokens in s1 and the strtok function returns
    * a null pointer. If such a character is found, it is the start of the
    * first token.
    * The strtok function then searches from there for a character that is
    * contained in the current separator string. If no such character is found,
    * the current token extends to the end of the string pointed to by s1, and
    * subsequent searches for a token will fail. If such a character is found,
    * it is overwritten by a null character, which terminates the current
    * token. The strtok function saves a pointer to the following character,
    * from which the next search for a token will start.
    * Each subsequent call, with a null pointer as the value for the first
    * argument, starts searching from the saved pointer and behaves as
    * described above.
    * Returns: pointer to the first character of a token, or a null pointer if
    *          there is no token.
    *
    * strtok_r() is a common extension which works exactly like
    * strtok(), but instead of storing its state in a hidden
    * library variable, requires the user to pass in a pointer to a
    * char * variable which will be used instead. Any sequence of
    * calls to strtok_r() passing the same char ** pointer should
    * behave exactly like the corresponding sequence of calls to
    * strtok(). This means that strtok_r() can safely be used in
    * multi-threaded programs, and also that you can tokenise two
    * strings in parallel.
    */

extern __declspec(__nothrow) void *memset(void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));
   /*
    * copies the value of c (converted to an unsigned char) into each of the
    * first n charactes of the object pointed to by s.
    * Returns: the value of s.
    */
extern __declspec(__nothrow) char *strerror(int /*errnum*/);
   /*
    * maps the error number in errnum to an error message string.
    * Returns: a pointer to the string, the contents of which are
    *          implementation-defined. The array pointed to shall not be
    *          modified by the program, but may be overwritten by a
    *          subsequent call to the strerror function.
    */
extern __declspec(__nothrow) size_t strlen(const char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * computes the length of the string pointed to by s.
    * Returns: the number of characters that precede the terminating null
    *          character.
    */

extern __declspec(__nothrow) size_t strlcpy(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string src into the string dst, using no more than
    * len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src. Thus, the operation
    * succeeded without truncation if and only if ret < len;
    * otherwise, the value in ret tells you how big to make dst if
    * you decide to reallocate it. (That value does _not_ include
    * the NUL.)
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) size_t strlcat(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * concatenates the string src to the string dst, using no more
    * than len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src plus the original length
    * of dst. Thus, the operation succeeded without truncation if
    * and only if ret < len; otherwise, the value in ret tells you
    * how big to make dst if you decide to reallocate it. (That
    * value does _not_ include the NUL.)
    * 
    * If no NUL is encountered within the first len bytes of dst,
    * then the length of dst is considered to have been equal to
    * len for the purposes of the return value (as if there were a
    * NUL at dst[len]). Thus, the return value in this case is len
    * + strlen(src).
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) void _membitcpybl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpybb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
    /*
     * Copies or moves a piece of memory from one place to another,
     * with one-bit granularity. So you can start or finish a copy
     * part way through a byte, and you can copy between regions
     * with different alignment within a byte.
     * 
     * All these functions have the same prototype: two void *
     * pointers for destination and source, then two integers
     * giving the bit offset from those pointers, and finally the
     * number of bits to copy.
     * 
     * Just like memcpy and memmove, the "cpy" functions copy as
     * fast as they can in the assumption that the memory regions
     * do not overlap, while the "move" functions cope correctly
     * with overlap.
     *
     * Treating memory as a stream of individual bits requires
     * defining a convention about what order those bits are
     * considered to be arranged in. The above functions support
     * multiple conventions:
     * 
     *  - the "bl" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in little-endian fashion, so that the LSB comes
     *    first. (For example, membitcpybl(a,b,0,7,1) would copy
     *    the MSB of the byte at b to the LSB of the byte at a.)
     * 
     *  - the "bb" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in big-endian fashion, so that the MSB comes
     *    first.
     * 
     *  - the "hl" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in little-endian fashion.
     * 
     *  - the "hb" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in big-endian fashion.
     * 
     *  - the "wl" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in little-endian fashion.
     * 
     *  - the "wb" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in big-endian fashion.
     */







# 502 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"



/* end of string.h */

# 26 "L:/PLT/softutil/softutil/src/simPin.c"
# 1 "L:/PLT/csw/BSP/inc/privateAreaTable.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
 *               MODULE IMPLEMENTATION FILE
 *******************************************************************************
 * Filename: privateAreaTable.h
 ******************************************************************************/



// Private Area is NON-Initialized, NON-cacheable area

# 1 "L:/PLT/csw/platform/inc/global_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/****************************************************************************
 *
 * Name:          global_types.h
 *
 * Description:   Standard type definitions
 *
 ****************************************************************************
 *
 *
 *
 *
 ****************************************************************************
 *                  Copyright (c) Intel 2000
 ***************************************************************************/




# 1 "L:/PLT/csw/platform/inc/gbl_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ============================================================================
File        : gbl_types.h
Description : Global types file for testing the
              os/kal package.

Notes       : This file is only used to test the compilation and
              archiving for the os/kal package.

Copyright 2001, Intel Corporation, All rights reserved.
============================================================================ */




/* Use the Xscale environment types */
# 1 "L:/PLT/env/win32/inc/xscale_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : xscale_types.h
Description : Global types file for the Xscale environment.

Notes       : This file is designed for use in the arm environment
              and is referenced from the gbl_types.h file. Use of
			  this file requires ENV_XSCALE to be defined in xscale_env.mak.
              
Copyright 2001, Intel Corporation, All rights reserved.
=========================================================================== */




typedef unsigned char	BOOL;
typedef unsigned char   UINT8;
typedef unsigned short  UINT16;
typedef unsigned long   UINT32;

typedef char            CHAR;
typedef signed char     INT8;
typedef signed short    INT16;
typedef signed long     INT32;













/*                         end of xscale_types.h
--------------------------------------------------------------------------- */



# 23 "L:/PLT/csw/platform/inc/gbl_types.h"


/* Use the NordHeim environment types */




/* Use the Arm environment types */




/* Use the Gnu environment types */




/* Use the Microsoft Visual C environment types */




  /* Standard typedefs */
  typedef unsigned char   Bool;         /* Boolean                        */

  /* Standard typedefs - to retain compatibility with TDMA */
  typedef UINT8           		 BYTE;         			/* Unsigned 8-bit quantity        */
  typedef UINT8            		 UBYTE;        			/* Unsigned 8-bit quantity        */
  typedef UINT16          		 UWORD;        			/* Unsigned 16-bit quantity       */
  typedef UINT16          		 WORD;         			/* Unsigned 16-bit quantity       */
  typedef INT16           		 SWORD;        			/* Signed 16-bit quantity         */
  typedef UINT32                 DWORD;        			/* Unsigned 32-bit quantity       */
  typedef unsigned long long     UINT64;                /* Unsigned 64-bit quantity       */
  typedef void* 		         VOID_PTR;























  /* A NULL value is required such that it is not mistaken for a valid */
  /* value which includes values in the range of modulo 64. */


  /* Definition of NULL is required */















/*                      end of gbl_types.h
--------------------------------------------------------------------------- */



# 25 "L:/PLT/csw/platform/inc/global_types.h"
# 1 "L:/PLT/hal/core/inc/utils.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

//Copyright 2005, Intel Corporation, All rights reserved.
/************************************************************************/
/*   Utils.h - 															*/
/* 																		*/
/************************************************************************/




# 1 "L:/PLT/csw/platform/inc/global_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/****************************************************************************
 *
 * Name:          global_types.h
 *
 * Description:   Standard type definitions
 *
 ****************************************************************************
 *
 *
 *
 *
 ****************************************************************************
 *                  Copyright (c) Intel 2000
 ***************************************************************************/

# 64 "L:/PLT/csw/platform/inc/global_types.h"

# 16 "L:/PLT/hal/core/inc/utils.h"
//should be used in BootLoader & Flasher ONLY
//#define LOW_LEVEL_ASSERTS_ONLY

//#if defined (_TAVOR_HARBELL_) || defined(SILICON_PV2)

// To Be Deleted...




typedef enum
{
	CPU_HERMON_B0 = 0,
	CPU_HERMON_B1,
	CPU_HERMON_B2,
	CPU_HERMON_B3,
	CPU_HERMON_TCB874,
	CPU_HERMONL_A0,
	CPU_HERMONC_A0,
	CPU_HERMON_TCC874,
	CPU_HERMONEL_A0,
	CPU_MANITOBA_OTHER,
	CPU_BVD,
	CPU_TAVOR_A0,
	CPU_TAVOR_B0,
	CPU_TAVOR_B1,
	CPU_TAVOR_B2,
	CPU_TAVOR_PV_A0,
	CPU_TAVOR_PV_B0,
	CPU_TAVOR_PV_C0,
	CPU_TTC,
	CPU_OTHER
}CPU_Version;  //if this enum changed, update also the CPU_Version_str[]

// Returns the CPU version according to the above list
CPU_Version GetCpuVersion(void);

typedef enum CPU_family_tag
{
	CPU_TAVOR_PV2,		//Z0, A0, B0
	CPU_TAVOR_MG1,		// Z0=A0, A1, B0
	CPU_TAVOR_MG2,		// A0
	CPU_ESHEL,		// A0
	CPU_NEVO,		// A0
	CPU_ESHEL_LTE,	// A0
	CPU_FAMILY_UNKN
}CPU_family;

CPU_family GetCpuFamily(void);
// Returns the original CPSR
// exp: old_level = disableInterrupts();
// old_level MUST be local automatic variable !!
unsigned long disableInterrupts(void);

// Returns the original CPSR
// exp: old_level = enableInterrupts();
// old_level MUST be local automatic variable !!
unsigned long enableInterrupts(void);

// Restores the IF bits in the CPSR to that value of the CPSR passed.
// The latter should be obtained from the enable/disable functions
// exp: restoreInterrupts(old_level);
// old_level MUST be local automatic variable !!
void restoreInterrupts(unsigned long ir);

// Count Leading Zeros
// Returns: 0 for 0x8xxxxxxx; 1 for 0x04xxxxxx; 31 for 0x00000001; 32 for 0
int _clz(unsigned long x);

// revert
// Returns: reverted endian value.change the order of bytes in the 32bit parameter from big to little endian and vice versa.
unsigned long _rev(unsigned long x);

// CP14 functions
void _xsFreqChange(void);

// Enter idle mode
void _xsGoIdle(void); //just idle the Xscale core
void setIdle(void);   //same as previous
void setIdleExt(UINT32 newXPCR, UINT32 oldXPCR); //idle the core with shutting down the MEMC and modifying PMU.XPCR

//
// General: soft-restart the image
//
void doRestart(void);

// Function analog of ASSERT
void fatalError(int condition);

void asm_isb(void);
void asm_dsb(void);

// enable performance count
void enable_performance_count(void);

// Get performance count
UINT32 get_performance_count(void);

// Set performance count
void set_performance_count(UINT32 value);


// Assert macros





extern void utilsAssertFail(const char      *cond,
                            const char      *file,
                            signed short    line,
                            unsigned char   allowDiag);




//regular ASSERTs
# 141 "L:/PLT/hal/core/inc/utils.h"

# 150 "L:/PLT/hal/core/inc/utils.h"

# 158 "L:/PLT/hal/core/inc/utils.h"













//
// CP14: Performance monitoring unit access
//

// Read/Set PMNC (Control Register, see Elkart core EAS chapter 8)
// Here are the bit definitions:
# 184 "L:/PLT/hal/core/inc/utils.h"



void   cp14SetPMNC(UINT32 value);
UINT32 cp14ReadPMNC(void);

// Read the Clock Counter register (core clock or same/64 depending on the PMNC_CCNT_DIV64 bit - below)
UINT32 cp_ReadCCNT(void);  // NEW generic name. OLD & NEW are aliased
UINT32 cp14ReadCCNT(void); // OLD non-generic name, to be obsolete.
UINT32 cp14SetCCNT(UINT32 value);
UINT32  cp14ReadEVTSEL(void);
void  cp14SetEVTSEL(UINT32 value);

UINT32 getCpRateKHz(void); // returns CP-counter rate in kHz or 0-unknown/default. Depends upon Core frequency


//
// CP6: WS Primary INTC co-processor bus access
//

UINT32 cp6ReadICPR(void);
UINT32 cp6ReadICIP(void);
UINT32 cp6ReadICFP(void);
UINT32 cp6ReadICHP(void);
UINT32 cp6ReadICMR(void);
void cp6WriteICMR(UINT32 value);







//#if defined (_TAVOR_HARBELL_)|| defined(SILICON_PV2)

//ON-CHIP trace buffer is not supported on TAVOR A0 but on B0 only with JTAG protocol
// Let's put macro-stubs meanwhile



//#define ReadTBREG(x)                0
//#define ReadCHKPT1(x)               0
//#define ReadCHKPT0(x)               0
# 243 "L:/PLT/hal/core/inc/utils.h"

// CPSR mode
# 256 "L:/PLT/hal/core/inc/utils.h"


UINT32 ReadSP(void);
UINT32 Read_SPSR(void);
UINT32 ReadCPSR(void);
UINT32 ReadMode_R13(UINT32 mode);
UINT32 ReadMode_R14(UINT32 mode);

// Set SP for the CPU mode specified by CPSR
void SetMode_R13(UINT32 mode, UINT32 sp);

// Set SP and SL (v7) for the current CPU mode
void SetSystemStack(UINT32 sp, UINT32 limit);

// Reads the r0-r14,pc,cpsr values into the given buffer (see EE_RegInfo_Data_t)
void   ReadRegisterContext(UINT32* pBuffer);

// Restores r0-r13,pc,cpsr values from the given buffer (see EE_RegInfo_Data_t)
// LR is not restored!
// Jumps to pBuffer->PC
void   RestoreRegisterContext(UINT32* pBuffer);

// Restores r0-r12 values from the given buffer (see EE_RegInfo_Data_t)
// r13, LR, CPSR are not restored!
// Returns from exception mode and jumps to pBuffer->PC
void   RestoreRegisterContextEx(UINT32* pBuffer, UINT32 setExcModeSP);

//#if !defined (_TAVOR_HARBELL_) && !defined(SILICON_PV2) /* XSCALE only */
# 306 "L:/PLT/hal/core/inc/utils.h"

void	doTurboFrequencyChange(UINT32 fBit,UINT32 tBit);
UINT32	GetTurboFrequencyChangeCfgBits(void *pRegAddress);
UINT32	RunOperationUnderSpecificStack_ASM(void *pFuncAddress,void *pStackAddress, UINT32 funcParam1);



# 26 "L:/PLT/csw/platform/inc/global_types.h"

  /* Standard typedefs */
  typedef volatile UINT8  *V_UINT8_PTR;  /* Ptr to volatile unsigned 8-bit quantity       */
  typedef volatile UINT16 *V_UINT16_PTR; /* Ptr to volatile unsigned 16-bit quantity       */
  typedef volatile UINT32 *V_UINT32_PTR; /* Ptr to volatile unsigned 32-bit quantity       */

  typedef unsigned int    U32Bits;
  typedef BOOL BOOLEAN;


  typedef const char *    SwVersion;



  /* Handy macros */
# 47 "L:/PLT/csw/platform/inc/global_types.h"


  /* Bit fields macros */
  // Yaeli Karni - need to work also when number GT 32 ! (march 06)


//strncat by shashal 



 







# 17 "L:/PLT/csw/BSP/inc/privateAreaTable.h"

//#if defined (_TAVOR_HARBELL_)|| defined(SILICON_PV2)  

extern UINT32  Image$$DDR_PRIVATE_RW_AREA$$Base;



# 30 "L:/PLT/csw/BSP/inc/privateAreaTable.h"



# 27 "L:/PLT/softutil/softutil/src/simPin.c"
# 1 "L:/PLT/csw/BSP/inc/loadTable.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

/***************************************************************************
*               MODULE IMPLEMENTATION FILE
****************************************************************************
*
* Filename: loadTable.h
*
* The OBM is responsible to copy image from flash to the DDR.
* It doesn't know about real image size and always copy the maximum 7MB.
* The problems:
*  - long time for copying (about 2 sec),
*  - all ERR/spy/debug buffers are overwriten.
*
* SOLUTION:
* Put the Image BEGIN and END address onto predefined area - offset_0x1C0 size 0x40
* Add the the text-signature to recognize are these addresses present in image or not.
* The signature is following the BEGIN/END and is next ":BEGIN:END:LOAD_TABLE_SIGNATURE"
* OBM should check signature in flash and if it is present MAY use the size=(END-BEGIN).
* If signature is invalid the default 7MB is used.
* The IMAGE_END region added into scatter file
*
******************************************************************************/

/*=======================================================================*/
/*        NOTE: This file may be used by OBM or WIN-CE                   */
/*=======================================================================*/




# 58 "L:/PLT/csw/BSP/inc/loadTable.h"
# 1 "L:/PLT/csw/BSP/inc/bsp_config.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/



/*
 * NOTE:
 *
 * Beware to chose right stack sizes.
 * Improper stack sizes may cause severe SW failures !!!
 */








//#if defined (_TAVOR_HARBELL_) || defined(SILICON_PV2)
# 49 "L:/PLT/csw/BSP/inc/bsp_config.h"

//------------------------------------------------------
//shachar - test platform for audio codec - CCR or Micco
//------------------------------------------------------
typedef enum{
	MICCO_CONNECTED,
	CCR_CONNECTED
}MICCO_CCR_ON_BOARD;







/* UART */
//#if defined (_TAVOR_HARBELL_) || defined (_TAVOR_BOERNE_) || defined(SILICON_TTC) || defined(SILICON_PV2)






//#if defined (_TAVOR_HARBELL_)|| defined(SILICON_TTC) || defined(SILICON_PV2)
# 80 "L:/PLT/csw/BSP/inc/bsp_config.h"

/* DSP BOOT */




// silicon support MSA control (reset and boot assistance): either Hermon or TTC CP core
//#define MSA_INIT



// TTC specific









# 60 "L:/PLT/csw/BSP/inc/loadTable.h"
# 1 "L:/PLT/csw/platform/inc/hal_cfg.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/







# 1 "L:/PLT/csw/SysCfg/inc/syscfg.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/************************************************************************/
/* Filename: SysCfg.h                                                   */
/*                                                                      */
/* Author:   Anton                                                      */
/*                                                                      */
/* Description: all SW mini switches that are related to Flavor         */
/* should be activate only here.                                        */
/* Remarks:                                                             */
/*                                                                      */
/* Created: 15/08/2007                                                  */
/*                                                                      */
/* Modified:                                                            */
/************************************************************************/




# 1 "L:/PLT/csw/platform/inc/hal_cfg.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/




# 24 "L:/PLT/csw/SysCfg/inc/syscfg.h"







//#if defined(SILICON_TTC_CORE_SEAGULL) || defined(_TAVOR_HARBELL_) || defined(SILICON_PV2)




# 29 "L:/PLT/csw/platform/inc/hal_cfg.h"

/*******************************************************************************
 *                      E r r o r s   V a l i d a t i o n
 ******************************************************************************/

// #if defined(SILICON_TTC) && (defined(_HERMON_B0_SILICON_) || defined(TAVOR) || defined(_TAVOR_HARBELL_) || defined(_TAVOR_BOERNE_)|| defined(SILICON_PV2)) 
//#if defined(SILICON_TTC) && (defined(_HERMON_B0_SILICON_) || defined(_TAVOR_HARBELL_) || defined(_TAVOR_BOERNE_)|| defined(SILICON_PV2)) 




////////////////////////////////////////////////////////////////////////////////////////////////////
//                Basic configurations!	  - Miniplat Uses this basic configuration
////////////////////////////////////////////////////////////////////////////////////////////////////





/* Use this line to DISABLE the use of the WATCHDOG kick*/



# 59 "L:/PLT/csw/platform/inc/hal_cfg.h"

//Assuming DIAGUART as the default choice for MINIPLAT




# 73 "L:/PLT/csw/platform/inc/hal_cfg.h"




//////////////////////////////////////////////////////////////////////////////////////////////
//From this point all thge defines are beyond Miniplat
///////////////////////////////////////////////////////////////////////////////////////////////////

//MBMINIPLAT ALL THE DEFINES BELOW DEFINED BEYOND MINIPLAT




//#if defined(FLAVOR_APP) || !defined(FLAVOR_DUALCORE)




//#if defined(FLAVOR_COM) || !defined(FLAVOR_DUALCORE)






/*******************************************************************************
 *                      H E R M O N   B 0
 ******************************************************************************/
# 230 "L:/PLT/csw/platform/inc/hal_cfg.h"
//#define HARBELL_WDT_DISABLE <<-- not used

















//
// Hermon project specific block
//
# 264 "L:/PLT/csw/platform/inc/hal_cfg.h"

/*******************************************************************************
 *                      G e n e r a l   F l a g s
 ******************************************************************************/


/* Use this line to DISABLE the use of XIRQ working through SINTC (choose
 * to work directly to Primary INTC */
//#define XIRQ_VIA_SINTC_DISABLE

/* Enable this line to workaround the RTC "SHIFT after power-up" problem */
//#define RTC_SHIFT_WORKAROUND

/* Start camera demo mode by default */
//#define CAMERA_RUN_ON_START
/*******************************************************************************
 *                      O v e r r i d e   F l a g s
 ******************************************************************************/

/* Enable/Disable Secondary INTC */






# 299 "L:/PLT/csw/platform/inc/hal_cfg.h"

//
// Special configuration:
// ####################### SA on A0 ##########################
// see plw.mak, TV containing SA and A0
//
# 319 "L:/PLT/csw/platform/inc/hal_cfg.h"



// Drivers Validation Team(DVT)  Defines.
# 342 "L:/PLT/csw/platform/inc/hal_cfg.h"


// Non-HARBELL platforms
//#define COMMON_DVT_ENABLE
//#define DIAG_TEST_ENABLE
//#define I2C_TEST_ENABLE
//#define DMA_TEST_ENABLE
//#define RTCC_TEST_ENABLE
//#define RTC_TEST_ENABLE
//#define FDI_TEST_ENABLE
//#define FAT12_TEST_ENABLE
//#define TIMER_TEST_ENABLE
//#define MMCSD_TEST_ENABLE
//#define WATCHDOG_TEST_ENABLE
//#define USIM_TEST_ENABLE
//#define UART_TEST_ENABLE
//#define LCDIF_TEST_ENABLE
//#define INTC_TEST_ENABLE
//#define SSP_TEST_ENABLE
//#define KEYPAD_SILICON_TEST_ENABLE
// Harbell
    //#define COMMON_DVT_ENABLE
    //#define I2C_TEST_ENABLE
    //#define UART_TEST_ENABLE
    //#define UART_PC_TEST_ENABLE



	//#define IPC_TEST_DVT_ENABLE
    //#define TIMER_TEST_DVT_ENABLE
	//#define INTC_TEST_DVT_ENABLE
	//#define LDMA_TEST_DVT_ENABLE
	//#define RM_TEST_DVT_ENABLE
	//#define SSP_TEST_DVT_ENABLE
    //#define USIM_TEST_DVT_ENABLE
	//#define FDI_TEST_ENABLE
# 61 "L:/PLT/csw/BSP/inc/loadTable.h"


//#if defined (_TAVOR_HARBELL_) || defined (_TAVOR_BOERNE_)
# 70 "L:/PLT/csw/BSP/inc/loadTable.h"
//#if defined (_TAVOR_HARBELL_) || defined(SILICON_PV2)
# 89 "L:/PLT/csw/BSP/inc/loadTable.h"


//Offset of the LOAD_TABLE = 1C0 (in the First Flash-block)






//++++++++++++++++++++++++++++++++++++++++++++++++++
//AREA_1: LOADTABLE_HEADER

typedef union{
	struct loadtable_init_routine{
		UINT32 b2init;                         /* branch to init routine */
		UINT32 init;                           /* image init routine */
	}init_routine;

	UINT8 filer[64];            /* max size*/
}LOADTABLE_AREA_HEADER;

//++++++++++++++++++++++++++++++++++++++++++++++++++
//AREA_2: VERSION & SIGNATURE
typedef enum {
    XIP,
    PSRAM
}CP_EXECUTE_MODE;

typedef enum {
    NBIOT,
    CATM,
    LTEONLY,
    LTEGSM
}PS_MODE;


typedef union{
	struct loadtable_version_info{
		char anti_rollback_version[16];   /* anti-roll back version , co-work with boot33 */
		char execute_mode[8];             /* XIP or PSRAM 		char execute_mode[8]
*/
		char ps_mode[8];                  /* LTEONLY/LG/CAT1/NBIOT */
		char image_info[50];              /* filled by external script ,default IMG_INFO as index*/
	}version_info;

	UINT8 filer[96];     /* max size*/
}LOADTABLE_AREA_VER_INFO;

//++++++++++++++++++++++++++++++++++++++++++++++++++
//AREA_3: RW COMPRESS REGION SYMBOL




typedef struct
{
	char    RW_REGION_MARK[7];
	char    RW_REGION_MARK_NUM;
	char    RW_REGION_NAME[8];
	UINT32  RW_REGION_EXEC_ADDR;
	UINT32  RW_REGION_LOAD_ADDR;
	UINT32  RW_REGION_LENGTH;
	UINT32  RW_REGION_COMPRESSED_ADDR;
}rw_region_item;




typedef union{
	rw_region_item compress_rw_region_list[64];

	UINT8 filer[2048];       /* max size*/
}LOADTABLE_AREA_RW_CPZ_INFO;

//++++++++++++++++++++++++++++++++++++++++++++++++++
//AREA_4: ARMLINKE SYMBOL LIST
typedef struct{
	char	name[12];
	UINT32	value;
}armlink_symbol_item;


typedef union{
	struct loadtable_armlink_symbol_info{
		//cp.bin
		armlink_symbol_item cp_exec_addr;
		armlink_symbol_item cp_load_addr;
		armlink_symbol_item image_end;
		armlink_symbol_item binary_size;
		//dsp.bin
		armlink_symbol_item dsp_begin_addr;
		armlink_symbol_item dsp_end_addr;
		//rf.bin
		armlink_symbol_item rf_load_addr_z2;
		armlink_symbol_item rf_load_addr_a0;
		//rd.bin
		armlink_symbol_item rd_begin_addr;
		armlink_symbol_item rd_end_addr;
		//apn.bin
		armlink_symbol_item apn_begin_addr;
		armlink_symbol_item apn_end_addr;
		//fota_param
		armlink_symbol_item fota_param_start_address;
		armlink_symbol_item fota_param_end_address;
		//updater
		armlink_symbol_item updater_start_address;
		armlink_symbol_item updater_end_address;
		//fota_pkg
		armlink_symbol_item fota_pkg_start_address;
		armlink_symbol_item fota_pkg_end_address;
		//nvm
		armlink_symbol_item nvm_start_address;
		armlink_symbol_item nvm_end_address;
		//factory_a
		armlink_symbol_item factory_a_start_address;
		armlink_symbol_item factory_a_end_address;
		//factory_b
		armlink_symbol_item factory_b_start_address;
		armlink_symbol_item factory_b_end_address;
		//ddr_ro
		armlink_symbol_item ddr_ro_exec_address;
		armlink_symbol_item ddr_ro_exec_size_address;

	}armlink_symbol_info;

	UINT8 filer[512];       /* max size*/
}LOADTABLE_AREA_ARMLINK_SYMBOL;

//++++++++++++++++++++++++++++++++++++++++++++++++++
//AREA_5: FUNCTIONAL VAL
typedef struct{
	char	name[12];
	UINT32	value;
}functional_item;


typedef union{
	struct loadtable_func_val{
		functional_item vergin;                    /* vergin mark */
		functional_item number_of_life;            /* number_of_life++ for each bootup,design for SIMPIN lock detect */
		functional_item uart_printf_enable;        /* enable uart printf after CP init*/
		functional_item fatal_printf_enable;       /* enable fatal printf after CP init*/
		functional_item default_core_freq;         /* TODO:further porting for def core freq*/
		functional_item default_core_voltage;      /* TODO:further porting for bootup core voltage*/
	}func_val;

	UINT8 filer[256];     /* max size*/
}LOADTABLE_AREA_FUNC_VAL;


typedef struct
{
	UINT32 b2init;                         /* branch to init routine */
	UINT32 init;                           /* image init routine */
}ImageInitTableType;



typedef struct
{



	UINT32 magic;                           /* ddr transfer flag magic value */
	UINT32 address;                         /* ddr transfer flag address     */
	UINT32 length;                          /* ddr transfer flag length      */
	UINT32 reserved[1];                     /* ddr transfer flag reserved     */
}DDRTransferFlagInitTable;


typedef struct
{
	UINT32 b2init;                         /* branch to init routine */
	UINT32 init;                           /* image init routine */
	UINT32 addrLoadTableRWcopy;            /* This CONST table is copied into RW area pointed by this*/
	UINT32 ee_postmortem;                  /* EE_PostmortemDesc addr (should be on offset 0xC */
	UINT32 numOfLife;                      /* Increment for every restart (life) */

    UINT32 diag_p_diagIntIfQPtrData;       //Pointer to DIAG-Pointer
    UINT32 diag_p_diagIntIfReportsList;    //Pointer to DIAG-Pointer
    UINT32 spare_CCCC;                     //spare currently used with "CCCC" pattern
                                           // one-direction Apps2com channel for generic command/data
	UINT32 ACIPCBegin;
	UINT32 ACIPCEnd;
	UINT32 LTEUpBegin;
	UINT32 LTEUpEnd;
	UINT32 LTEDownBegin;
	UINT32 LTEDownEnd;

    //UINT16 apps2commDataFormat;
    UINT32 apps2commDataLen;
    UINT8  apps2commDataBuf[48];
	UINT32 CPLoadAddr;
	UINT32 MSALoadAddrStart;
	UINT32 MSALoadAddrEnd;

	UINT32 ACIPCPSDownlinkBegin;
	UINT32 ACIPCPSDownlinkEnd;
	UINT32 ACIPCPSUplinkBegin;
	UINT32 ACIPCPSUplinkEnd;
	UINT32 ACIPCOtherPortDlBegin;
	UINT32 ACIPCOtherPortDlEnd;
	UINT32 ACIPCOtherPortUlBegin;
	UINT32 ACIPCOtherPortUlEnd;
	UINT32 ACIPCDIAGPortDlBegin;
	UINT32 ACIPCDIAGPortDlEnd;
	UINT32 ACIPCDIAGPortUlBegin;
	UINT32 ACIPCDIAGPortUlEnd;
	UINT32 ACIPCPSDKeySectionBegin;
	UINT32 ACIPCOtherKeySectionBegin;
	UINT32 ACIPCDIAGKeySectionBegin;
	UINT32 MemLogBegin;
	UINT32 MemLogEnd;
	UINT32 NetworkModeIndication; //TD or WB
	//add some region for AP dynamic get CP load address
    UINT32 RfBinBegin;
    UINT32 RfBinEnd;
    UINT32 LinkType; // single link or dual link
    UINT32 RFSBegin;
	UINT32 RFSEnd;
	UINT32 MEPOTABegin;
	UINT32 MEPOTAEnd;
	UINT32 NVMOTABegin;
	UINT32 NVMOTAEnd;




	UINT32 BX2LoadAddrStart;
	UINT32 BX2LoadAddrEnd;
}LoadTableDynamicType;

typedef struct
{
	union
	{
		LoadTableDynamicType stLoadTable;
		UINT8  filler[0x1c0-4];
	}u;

	UINT32 imageBegin;                     // image addresses are in HARBELL address space 0xD0??.????
    UINT32 imageEnd;                       // for BOERNE use conversion to 0xBF??.????
    char   Signature[16];
    UINT32 sharedFreeBegin;
    UINT32 sharedFreeEnd;
    UINT32 ramRWbegin;
    UINT32 ramRWend;
	UINT32 spare_EEEE;                     //spare currently used with "EEEE" pattern
    UINT32 ReliableDataBegin;
    UINT32 ReliableDataEnd;

    char   OBM_VerString[8];  //All "OBM" here are temp solution
    UINT32 OBM_Data32bits;

	LOADTABLE_AREA_HEADER	      lt_area_header;
	LOADTABLE_AREA_RW_CPZ_INFO    lt_area_rw_cpz_info;
	LOADTABLE_AREA_ARMLINK_SYMBOL lt_area_armlink_symbol;

}LoadTableType; /*total size 512bytes */



//#define OBM_DATA_SIGNATURE_STR          "OBM"
//#define OBM_DATA_SIGNATURE_STR_LEN       3      /*not including endZero */
//#define OBM_DATA_DATALEN                sizeof(UINT32)  /*current data is 32 bits */



extern LoadTableType loadTableRWcopy;  //Defined in the EEIRAMBuffer.c


//#define NVM_RFS_BIN_MRD_SIZE (1024*128)
/***************************************************************************************/




UINT32  getCommImageBaseAddr(void);
void    getNonCache_PS_DLUL_MemAddr(UINT32* begin, UINT32* end);
void    getPS_DLUL_MemAddr_ForUsb(UINT32* begin, UINT32* end);
void    getAppComShareMemAddr(UINT32* begin, UINT32* end);
void    getAppComBx2RoMemAddr(UINT32* begin, UINT32* end);
void    getAppComRFMemAddr(UINT32* begin, UINT32* end);
void    commImageTableInit(void);
void    DDRTransFlagImageInit(void);
UINT32  getCommNumOfLife(void);
void    incrementCommNumOfLife(void);
BOOL    StrtupIsPowerup(void);
void    printBX2RFBin(void);


# 391 "L:/PLT/csw/BSP/inc/loadTable.h"


extern LoadTableType  loadTable;   /* do NOT use "const" in EXTERN prototype */


extern LoadTableType  *pLoadTable;

UINT32 get_nr_dsp_shm_begin_address(void);
UINT32 get_nr_dsp_shm_end_address(void);

//#endif// defined (_TAVOR_HARBELL_) || defined (_TAVOR_BOERNE_)
# 28 "L:/PLT/softutil/softutil/src/simPin.c"

//Modify for LTG_DS PS_Unify





    extern BOOL GLFeatureFlag;


UINT8 PrivateArea_simPinCode[2][16] = {{0xFF},{0xFF}};
UINT32 waitforintdebug;
/***********************************************************/
int  simPin_save(unsigned char* pinBuf, int len, int cardNo)
{
    int i;


        cardNo ^= (int)GLFeatureFlag;

    memset(PrivateArea_simPinCode[cardNo], 0xFF, 16);

    if(len > 16)     len = 16;
    for(i=0; i<len; i++)
        PrivateArea_simPinCode[cardNo][i] = (UINT8) pinBuf[i];
    return(len);
}


/***********************************************************/
int  simPin_read(unsigned char* pinBuf, int len, int cardNo)
{
    int i;


        cardNo ^= (int)GLFeatureFlag;


    if(len > 16)     len = 16;

    for(i=0; i<len; i++)
    {
        if(PrivateArea_simPinCode[cardNo][i] == 0xFF)
        {
            len = i;
            break;
        }
        pinBuf[i] = (unsigned char) PrivateArea_simPinCode[cardNo][i];
    }
    return(len);
}


/***********************************************************/
void simPin_invalidate (int cardNo)
{

        cardNo ^= (int)GLFeatureFlag;

    memset(PrivateArea_simPinCode[cardNo], 0xFF, 16);
}

