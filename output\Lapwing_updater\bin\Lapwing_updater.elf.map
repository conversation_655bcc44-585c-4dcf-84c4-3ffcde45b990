Component: ARM Compiler 5.06 update 4 (build 422) Tool: armlink [4d35d2]

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv_div0_cr4.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv_rt_cr4.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  rtudiv10.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv_rt_div0_cr4.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv_cr4.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv5.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memmove_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memmove.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  c89vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/unhosted.s                       0x00000000   Number         0  uread4.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\FM.c     0x00000000   Number         0  FM.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\FM_ext.c 0x00000000   Number         0  FM_ext.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\Flash.c  0x00000000   Number         0  Flash.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\FreqChange.c 0x00000000   Number         0  FreqChange.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\LzFind.c 0x00000000   Number         0  LzFind.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\LzmaDec.c 0x00000000   Number         0  LzmaDec.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\LzmaEnc.c 0x00000000   Number         0  LzmaEnc.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\LzmaLib.c 0x00000000   Number         0  LzmaLib.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\adc_pm803.c 0x00000000   Number         0  adc_pm803.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\asr_lzma.c 0x00000000   Number         0  asr_lzma.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\guilin.c 0x00000000   Number         0  guilin.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\guilin_lite.c 0x00000000   Number         0  guilin_lite.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\plat_api.c 0x00000000   Number         0  plat_api.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\pmic.c   0x00000000   Number         0  pmic.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\qspi_host.c 0x00000000   Number         0  qspi_host.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\spi_nand.c 0x00000000   Number         0  spi_nand.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\spi_nor.c 0x00000000   Number         0  spi_nor.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\system.c 0x00000000   Number         0  system.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\tim.c    0x00000000   Number         0  tim.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\tinyalloc.c 0x00000000   Number         0  tinyalloc.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\uart.c   0x00000000   Number         0  uart.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\updater_table.c 0x00000000   Number         0  updater_table.o ABSOLUTE
    D:\xy695\PLT\startup\common\src\utilities.c 0x00000000   Number         0  utilities.o ABSOLUTE
    D:\xy695\PLT\startup\updater\src\StartUp.s 0x00000000   Number         0  StartUp.o ABSOLUTE
    D:\xy695\PLT\startup\updater\src\bspatch.c 0x00000000   Number         0  bspatch.o ABSOLUTE
    D:\xy695\PLT\startup\updater\src\main.c  0x00000000   Number         0  main.o ABSOLUTE
    D:\xy695\PLT\startup\updater\src\version_block.c 0x00000000   Number         0  version_block.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    i.Backward                               0x00001038   Section        0  LzmaEnc.o(i.Backward)
    Backward                                 0x00001038   ARM Code     172  LzmaEnc.o(i.Backward)
    i.Bt2_MatchFinder_GetMatches             0x000010e4   Section        0  LzFind.o(i.Bt2_MatchFinder_GetMatches)
    Bt2_MatchFinder_GetMatches               0x000010e4   ARM Code     196  LzFind.o(i.Bt2_MatchFinder_GetMatches)
    i.Bt2_MatchFinder_Skip                   0x000011a8   Section        0  LzFind.o(i.Bt2_MatchFinder_Skip)
    Bt2_MatchFinder_Skip                     0x000011a8   ARM Code     164  LzFind.o(i.Bt2_MatchFinder_Skip)
    i.Bt3_MatchFinder_GetMatches             0x0000124c   Section        0  LzFind.o(i.Bt3_MatchFinder_GetMatches)
    Bt3_MatchFinder_GetMatches               0x0000124c   ARM Code     440  LzFind.o(i.Bt3_MatchFinder_GetMatches)
    i.Bt3_MatchFinder_Skip                   0x00001404   Section        0  LzFind.o(i.Bt3_MatchFinder_Skip)
    Bt3_MatchFinder_Skip                     0x00001404   ARM Code     220  LzFind.o(i.Bt3_MatchFinder_Skip)
    i.Bt4_MatchFinder_GetMatches             0x000014e0   Section        0  LzFind.o(i.Bt4_MatchFinder_GetMatches)
    Bt4_MatchFinder_GetMatches               0x000014e0   ARM Code     532  LzFind.o(i.Bt4_MatchFinder_GetMatches)
    i.Bt4_MatchFinder_Skip                   0x000016f4   Section        0  LzFind.o(i.Bt4_MatchFinder_Skip)
    Bt4_MatchFinder_Skip                     0x000016f4   ARM Code     268  LzFind.o(i.Bt4_MatchFinder_Skip)
    i.CheckErrors                            0x00001800   Section        0  LzmaEnc.o(i.CheckErrors)
    CheckErrors                              0x00001800   ARM Code      76  LzmaEnc.o(i.CheckErrors)
    i.FillAlignPrices                        0x0000184c   Section        0  LzmaEnc.o(i.FillAlignPrices)
    FillAlignPrices                          0x0000184c   ARM Code      88  LzmaEnc.o(i.FillAlignPrices)
    i.FillDistancesPrices                    0x000018a4   Section        0  LzmaEnc.o(i.FillDistancesPrices)
    FillDistancesPrices                      0x000018a4   ARM Code     340  LzmaEnc.o(i.FillDistancesPrices)
    i.Flush                                  0x000019fc   Section        0  LzmaEnc.o(i.Flush)
    Flush                                    0x000019fc   ARM Code     104  LzmaEnc.o(i.Flush)
    i.GetMatchesSpec1                        0x00001a68   Section        0  LzFind.o(i.GetMatchesSpec1)
    i.GetOptimum                             0x00001b9c   Section        0  LzmaEnc.o(i.GetOptimum)
    GetOptimum                               0x00001b9c   ARM Code    4628  LzmaEnc.o(i.GetOptimum)
    i.GetOptimumFast                         0x00002dc4   Section        0  LzmaEnc.o(i.GetOptimumFast)
    GetOptimumFast                           0x00002dc4   ARM Code     756  LzmaEnc.o(i.GetOptimumFast)
    i.GetPureRepPrice                        0x000030b8   Section        0  LzmaEnc.o(i.GetPureRepPrice)
    GetPureRepPrice                          0x000030b8   ARM Code     184  LzmaEnc.o(i.GetPureRepPrice)
    i.GetRepLen1Price                        0x00003170   Section        0  LzmaEnc.o(i.GetRepLen1Price)
    GetRepLen1Price                          0x00003170   ARM Code      76  LzmaEnc.o(i.GetRepLen1Price)
    i.GetRepPrice                            0x000031bc   Section        0  LzmaEnc.o(i.GetRepPrice)
    GetRepPrice                              0x000031bc   ARM Code      60  LzmaEnc.o(i.GetRepPrice)
    i.Hc4_MatchFinder_GetMatches             0x000031f8   Section        0  LzFind.o(i.Hc4_MatchFinder_GetMatches)
    Hc4_MatchFinder_GetMatches               0x000031f8   ARM Code     500  LzFind.o(i.Hc4_MatchFinder_GetMatches)
    i.Hc4_MatchFinder_Skip                   0x000033ec   Section        0  LzFind.o(i.Hc4_MatchFinder_Skip)
    Hc4_MatchFinder_Skip                     0x000033ec   ARM Code     228  LzFind.o(i.Hc4_MatchFinder_Skip)
    i.Hc_GetMatchesSpec                      0x000034d0   Section        0  LzFind.o(i.Hc_GetMatchesSpec)
    Hc_GetMatchesSpec                        0x000034d0   ARM Code     184  LzFind.o(i.Hc_GetMatchesSpec)
    i.LenEnc_Encode2                         0x00003588   Section        0  LzmaEnc.o(i.LenEnc_Encode2)
    LenEnc_Encode2                           0x00003588   ARM Code     212  LzmaEnc.o(i.LenEnc_Encode2)
    i.LenEnc_Init                            0x0000365c   Section        0  LzmaEnc.o(i.LenEnc_Init)
    LenEnc_Init                              0x0000365c   ARM Code      96  LzmaEnc.o(i.LenEnc_Init)
    i.LenEnc_SetPrices                       0x000036bc   Section        0  LzmaEnc.o(i.LenEnc_SetPrices)
    LenEnc_SetPrices                         0x000036bc   ARM Code     236  LzmaEnc.o(i.LenEnc_SetPrices)
    i.LenPriceEnc_UpdateTable                0x000037a8   Section        0  LzmaEnc.o(i.LenPriceEnc_UpdateTable)
    LenPriceEnc_UpdateTable                  0x000037a8   ARM Code      72  LzmaEnc.o(i.LenPriceEnc_UpdateTable)
    i.LenPriceEnc_UpdateTables               0x000037f0   Section        0  LzmaEnc.o(i.LenPriceEnc_UpdateTables)
    LenPriceEnc_UpdateTables                 0x000037f0   ARM Code      56  LzmaEnc.o(i.LenPriceEnc_UpdateTables)
    i.LitEnc_Encode                          0x00003828   Section        0  LzmaEnc.o(i.LitEnc_Encode)
    LitEnc_Encode                            0x00003828   ARM Code      52  LzmaEnc.o(i.LitEnc_Encode)
    i.LitEnc_GetPrice                        0x0000385c   Section        0  LzmaEnc.o(i.LitEnc_GetPrice)
    LitEnc_GetPrice                          0x0000385c   ARM Code      72  LzmaEnc.o(i.LitEnc_GetPrice)
    i.LitEnc_GetPriceMatched                 0x000038a4   Section        0  LzmaEnc.o(i.LitEnc_GetPriceMatched)
    LitEnc_GetPriceMatched                   0x000038a4   ARM Code      96  LzmaEnc.o(i.LitEnc_GetPriceMatched)
    i.LzInWindow_Free                        0x00003904   Section        0  LzFind.o(i.LzInWindow_Free)
    LzInWindow_Free                          0x00003904   ARM Code      52  LzFind.o(i.LzInWindow_Free)
    i.LzmaCompress                           0x00003938   Section        0  LzmaLib.o(i.LzmaCompress)
    i.LzmaCompress_internal                  0x000039bc   Section        0  LzmaLib.o(i.LzmaCompress_internal)
    i.LzmaDec_AllocateProbs                  0x00003a20   Section        0  LzmaDec.o(i.LzmaDec_AllocateProbs)
    i.LzmaDec_AllocateProbs2                 0x00003a64   Section        0  LzmaDec.o(i.LzmaDec_AllocateProbs2)
    LzmaDec_AllocateProbs2                   0x00003a64   ARM Code     120  LzmaDec.o(i.LzmaDec_AllocateProbs2)
    i.LzmaDec_DecodeReal                     0x00003adc   Section        0  LzmaDec.o(i.LzmaDec_DecodeReal)
    LzmaDec_DecodeReal                       0x00003adc   ARM Code    3080  LzmaDec.o(i.LzmaDec_DecodeReal)
    i.LzmaDec_DecodeReal2                    0x000046e8   Section        0  LzmaDec.o(i.LzmaDec_DecodeReal2)
    LzmaDec_DecodeReal2                      0x000046e8   ARM Code     160  LzmaDec.o(i.LzmaDec_DecodeReal2)
    i.LzmaDec_DecodeToDic                    0x00004788   Section        0  LzmaDec.o(i.LzmaDec_DecodeToDic)
    i.LzmaDec_FreeProbs                      0x00004aa4   Section        0  LzmaDec.o(i.LzmaDec_FreeProbs)
    i.LzmaDec_Init                           0x00004acc   Section        0  LzmaDec.o(i.LzmaDec_Init)
    i.LzmaDec_InitDicAndState                0x00004ae0   Section        0  LzmaDec.o(i.LzmaDec_InitDicAndState)
    i.LzmaDec_TryDummy                       0x00004b14   Section        0  LzmaDec.o(i.LzmaDec_TryDummy)
    LzmaDec_TryDummy                         0x00004b14   ARM Code    1360  LzmaDec.o(i.LzmaDec_TryDummy)
    i.LzmaDec_WriteRem                       0x00005064   Section        0  LzmaDec.o(i.LzmaDec_WriteRem)
    LzmaDec_WriteRem                         0x00005064   ARM Code     156  LzmaDec.o(i.LzmaDec_WriteRem)
    i.LzmaDecode                             0x00005100   Section        0  LzmaDec.o(i.LzmaDecode)
    i.LzmaEncProps_Init                      0x000051c0   Section        0  LzmaEnc.o(i.LzmaEncProps_Init)
    i.LzmaEncProps_Normalize                 0x000051fc   Section        0  LzmaEnc.o(i.LzmaEncProps_Normalize)
    i.LzmaEnc_Alloc                          0x00005318   Section        0  LzmaEnc.o(i.LzmaEnc_Alloc)
    LzmaEnc_Alloc                            0x00005318   ARM Code     340  LzmaEnc.o(i.LzmaEnc_Alloc)
    i.LzmaEnc_AllocAndInit                   0x00005470   Section        0  LzmaEnc.o(i.LzmaEnc_AllocAndInit)
    LzmaEnc_AllocAndInit                     0x00005470   ARM Code     120  LzmaEnc.o(i.LzmaEnc_AllocAndInit)
    i.LzmaEnc_CodeOneBlock                   0x000054ec   Section        0  LzmaEnc.o(i.LzmaEnc_CodeOneBlock)
    LzmaEnc_CodeOneBlock                     0x000054ec   ARM Code    1700  LzmaEnc.o(i.LzmaEnc_CodeOneBlock)
    i.LzmaEnc_Construct                      0x00005bb8   Section        0  LzmaEnc.o(i.LzmaEnc_Construct)
    i.LzmaEnc_Create                         0x00005c2c   Section        0  LzmaEnc.o(i.LzmaEnc_Create)
    i.LzmaEnc_Destroy                        0x00005c50   Section        0  LzmaEnc.o(i.LzmaEnc_Destroy)
    i.LzmaEnc_Destruct                       0x00005c74   Section        0  LzmaEnc.o(i.LzmaEnc_Destruct)
    i.LzmaEnc_Encode                         0x00005cbc   Section        0  LzmaEnc.o(i.LzmaEnc_Encode)
    i.LzmaEnc_FastPosInit                    0x00005d9c   Section        0  LzmaEnc.o(i.LzmaEnc_FastPosInit)
    i.LzmaEnc_FreeLits                       0x00005df0   Section        0  LzmaEnc.o(i.LzmaEnc_FreeLits)
    i.LzmaEnc_Init                           0x00005e34   Section        0  LzmaEnc.o(i.LzmaEnc_Init)
    i.LzmaEnc_InitPriceTables                0x00005fc8   Section        0  LzmaEnc.o(i.LzmaEnc_InitPriceTables)
    i.LzmaEnc_InitPrices                     0x0000601c   Section        0  LzmaEnc.o(i.LzmaEnc_InitPrices)
    i.LzmaEnc_MemEncode                      0x000060a8   Section        0  LzmaEnc.o(i.LzmaEnc_MemEncode)
    i.LzmaEnc_SetProps                       0x00006138   Section        0  LzmaEnc.o(i.LzmaEnc_SetProps)
    i.LzmaEnc_WriteProperties                0x00006240   Section        0  LzmaEnc.o(i.LzmaEnc_WriteProperties)
    i.LzmaEncode                             0x000062ec   Section        0  LzmaEnc.o(i.LzmaEncode)
    i.LzmaProps_Decode                       0x00006388   Section        0  LzmaDec.o(i.LzmaProps_Decode)
    i.LzmaUncompress                         0x00006410   Section        0  LzmaLib.o(i.LzmaUncompress)
    i.MatchFinder_CheckLimits                0x0000648c   Section        0  LzFind.o(i.MatchFinder_CheckLimits)
    MatchFinder_CheckLimits                  0x0000648c   ARM Code     160  LzFind.o(i.MatchFinder_CheckLimits)
    i.MatchFinder_Construct                  0x0000652c   Section        0  LzFind.o(i.MatchFinder_Construct)
    i.MatchFinder_Create                     0x000065a4   Section        0  LzFind.o(i.MatchFinder_Create)
    i.MatchFinder_CreateVTable               0x00006778   Section        0  LzFind.o(i.MatchFinder_CreateVTable)
    i.MatchFinder_Free                       0x00006818   Section        0  LzFind.o(i.MatchFinder_Free)
    i.MatchFinder_FreeThisClassMemory        0x00006838   Section        0  LzFind.o(i.MatchFinder_FreeThisClassMemory)
    MatchFinder_FreeThisClassMemory          0x00006838   ARM Code      40  LzFind.o(i.MatchFinder_FreeThisClassMemory)
    i.MatchFinder_GetIndexByte               0x00006860   Section        0  LzFind.o(i.MatchFinder_GetIndexByte)
    i.MatchFinder_GetNumAvailableBytes       0x0000686c   Section        0  LzFind.o(i.MatchFinder_GetNumAvailableBytes)
    i.MatchFinder_GetPointerToCurrentPos     0x0000687c   Section        0  LzFind.o(i.MatchFinder_GetPointerToCurrentPos)
    i.MatchFinder_Init                       0x00006884   Section        0  LzFind.o(i.MatchFinder_Init)
    i.MatchFinder_MoveBlock                  0x000068e0   Section        0  LzFind.o(i.MatchFinder_MoveBlock)
    i.MatchFinder_MovePos                    0x00006920   Section        0  LzFind.o(i.MatchFinder_MovePos)
    MatchFinder_MovePos                      0x00006920   ARM Code      52  LzFind.o(i.MatchFinder_MovePos)
    i.MatchFinder_NeedMove                   0x00006954   Section        0  LzFind.o(i.MatchFinder_NeedMove)
    i.MatchFinder_Normalize3                 0x0000697c   Section        0  LzFind.o(i.MatchFinder_Normalize3)
    i.MatchFinder_ReadBlock                  0x000069a8   Section        0  LzFind.o(i.MatchFinder_ReadBlock)
    MatchFinder_ReadBlock                    0x000069a8   ARM Code     156  LzFind.o(i.MatchFinder_ReadBlock)
    i.MatchFinder_ReduceOffsets              0x00006a44   Section        0  LzFind.o(i.MatchFinder_ReduceOffsets)
    i.MatchFinder_SetLimits                  0x00006a6c   Section        0  LzFind.o(i.MatchFinder_SetLimits)
    MatchFinder_SetLimits                    0x00006a6c   ARM Code     108  LzFind.o(i.MatchFinder_SetLimits)
    i.MovePos                                0x00006ad8   Section        0  LzmaEnc.o(i.MovePos)
    MovePos                                  0x00006ad8   ARM Code      40  LzmaEnc.o(i.MovePos)
    i.MyAlloc                                0x00006b00   Section        0  LzmaLib.o(i.MyAlloc)
    i.MyRead                                 0x00006b0c   Section        0  LzmaEnc.o(i.MyRead)
    MyRead                                   0x00006b0c   ARM Code      84  LzmaEnc.o(i.MyRead)
    i.MyWrite                                0x00006b60   Section        0  LzmaEnc.o(i.MyWrite)
    MyWrite                                  0x00006b60   ARM Code      76  LzmaEnc.o(i.MyWrite)
    i.RangeEnc_EncodeBit                     0x00006bac   Section        0  LzmaEnc.o(i.RangeEnc_EncodeBit)
    RangeEnc_EncodeBit                       0x00006bac   ARM Code     112  LzmaEnc.o(i.RangeEnc_EncodeBit)
    i.RangeEnc_EncodeDirectBits              0x00006c1c   Section        0  LzmaEnc.o(i.RangeEnc_EncodeDirectBits)
    RangeEnc_EncodeDirectBits                0x00006c1c   ARM Code     108  LzmaEnc.o(i.RangeEnc_EncodeDirectBits)
    i.RangeEnc_FlushStream                   0x00006c88   Section        0  LzmaEnc.o(i.RangeEnc_FlushStream)
    RangeEnc_FlushStream                     0x00006c88   ARM Code      88  LzmaEnc.o(i.RangeEnc_FlushStream)
    i.RangeEnc_Init                          0x00006ce0   Section        0  LzmaEnc.o(i.RangeEnc_Init)
    RangeEnc_Init                            0x00006ce0   ARM Code      72  LzmaEnc.o(i.RangeEnc_Init)
    i.RangeEnc_ShiftLow                      0x00006d28   Section        0  LzmaEnc.o(i.RangeEnc_ShiftLow)
    RangeEnc_ShiftLow                        0x00006d28   ARM Code     152  LzmaEnc.o(i.RangeEnc_ShiftLow)
    i.RcTree_Encode                          0x00006dc0   Section        0  LzmaEnc.o(i.RcTree_Encode)
    RcTree_Encode                            0x00006dc0   ARM Code      72  LzmaEnc.o(i.RcTree_Encode)
    i.RcTree_GetPrice                        0x00006e08   Section        0  LzmaEnc.o(i.RcTree_GetPrice)
    RcTree_GetPrice                          0x00006e08   ARM Code      76  LzmaEnc.o(i.RcTree_GetPrice)
    i.RcTree_ReverseEncode                   0x00006e54   Section        0  LzmaEnc.o(i.RcTree_ReverseEncode)
    RcTree_ReverseEncode                     0x00006e54   ARM Code      76  LzmaEnc.o(i.RcTree_ReverseEncode)
    i.RcTree_ReverseGetPrice                 0x00006ea0   Section        0  LzmaEnc.o(i.RcTree_ReverseGetPrice)
    RcTree_ReverseGetPrice                   0x00006ea0   ARM Code      80  LzmaEnc.o(i.RcTree_ReverseGetPrice)
    i.ReadMatchDistances                     0x00006ef0   Section        0  LzmaEnc.o(i.ReadMatchDistances)
    ReadMatchDistances                       0x00006ef0   ARM Code     184  LzmaEnc.o(i.ReadMatchDistances)
    i.SkipMatchesSpec                        0x00006fa8   Section        0  LzFind.o(i.SkipMatchesSpec)
    SkipMatchesSpec                          0x00006fa8   ARM Code     228  LzFind.o(i.SkipMatchesSpec)
    i.SzAlloc                                0x0000708c   Section        0  LzmaLib.o(i.SzAlloc)
    SzAlloc                                  0x0000708c   ARM Code       8  LzmaLib.o(i.SzAlloc)
    i.SzFree                                 0x00007094   Section        0  LzmaLib.o(i.SzFree)
    SzFree                                   0x00007094   ARM Code       8  LzmaLib.o(i.SzFree)
    i.WriteEndMarker                         0x0000709c   Section        0  LzmaEnc.o(i.WriteEndMarker)
    WriteEndMarker                           0x0000709c   ARM Code     228  LzmaEnc.o(i.WriteEndMarker)
    i.alloc_block                            0x00007188   Section        0  tinyalloc.o(i.alloc_block)
    alloc_block                              0x00007188   ARM Code     304  tinyalloc.o(i.alloc_block)
    i.compact                                0x000072bc   Section        0  tinyalloc.o(i.compact)
    compact                                  0x000072bc   ARM Code     176  tinyalloc.o(i.compact)
    i.free                                   0x00007370   Section        0  tinyalloc.o(i.free)
    i.insert_block                           0x00007374   Section        0  tinyalloc.o(i.insert_block)
    insert_block                             0x00007374   ARM Code      72  tinyalloc.o(i.insert_block)
    i.malloc                                 0x000073c0   Section        0  tinyalloc.o(i.malloc)
    i.malloc_init                            0x000073c4   Section        0  tinyalloc.o(i.malloc_init)
    i.ta_alloc                               0x000073c8   Section        0  tinyalloc.o(i.ta_alloc)
    i.ta_free                                0x00007428   Section        0  tinyalloc.o(i.ta_free)
    i.ta_init                                0x00007484   Section        0  tinyalloc.o(i.ta_init)
    i.tiny_free                              0x000074e4   Section        0  tinyalloc.o(i.tiny_free)
    i.tiny_malloc                            0x000074e8   Section        0  tinyalloc.o(i.tiny_malloc)
    .constdata                               0x000074ec   Section       24  LzmaDec.o(.constdata)
    kLiteralNextStates                       0x000074ec   Data          24  LzmaDec.o(.constdata)
    .constdata                               0x00007504   Section      192  LzmaEnc.o(.constdata)
    kLiteralNextStates                       0x00007504   Data          48  LzmaEnc.o(.constdata)
    kMatchNextStates                         0x00007534   Data          48  LzmaEnc.o(.constdata)
    kRepNextStates                           0x00007564   Data          48  LzmaEnc.o(.constdata)
    kShortRepNextStates                      0x00007594   Data          48  LzmaEnc.o(.constdata)
    .constdata                               0x000075c4   Section        9  tinyalloc.o(.constdata)
    __FUNCTION__                             0x000075c4   Data           9  tinyalloc.o(.constdata)
    IMG_HEADER_INFO                          0x06000000   Section      768  version_block.o(IMG_HEADER_INFO)
    Init                                     0x06000300   Section      992  StartUp.o(Init)
    i.bootloader                             0x060006e0   Section        0  main.o(i.bootloader)
    i.get_fbf_start_addr                     0x06000b84   Section        0  main.o(i.get_fbf_start_addr)
    .ARM.Collect$$_printf_percent$$00000000  0x06001200   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x06001200   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x06001206   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x0600120c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x06001212   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x06001218   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0600121e   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x06001224   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0600122e   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x06001234   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x0600123a   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x06001240   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x06001246   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x0600124c   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x06001252   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x06001258   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0600125e   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x06001264   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x0600126a   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x06001274   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x0600127a   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x06001280   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x06001286   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x0600128c   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000013          0x06001290   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000017          0x06001290   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .text                                    0x06001290   Section      816  lludiv5.o(.text)
    .text                                    0x060015c0   Section       38  llushr.o(.text)
    .text                                    0x060015e8   Section        0  c89vsnprintf.o(.text)
    .text                                    0x0600161c   Section        0  memcmp.o(.text)
    .text                                    0x06001674   Section        0  strncmp.o(.text)
    .text                                    0x06001764   Section      212  rt_memcpy.o(.text)
    .text                                    0x06001838   Section      212  rt_memmove.o(.text)
    .text                                    0x0600190c   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x06001978   Section       68  rt_memclr.o(.text)
    .text                                    0x060019bc   Section       78  rt_memclr_w.o(.text)
    .text                                    0x06001a0a   Section       22  uread4.o(.text)
    .text                                    0x06001a20   Section        0  _printf_pad.o(.text)
    .text                                    0x06001a6e   Section        0  _printf_truncate.o(.text)
    .text                                    0x06001a92   Section        0  _printf_str.o(.text)
    .text                                    0x06001ae4   Section        0  _printf_dec.o(.text)
    .text                                    0x06001b54   Section        0  _printf_charcount.o(.text)
    .text                                    0x06001b7c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x06001b7d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x06001bac   Section        0  _sputc.o(.text)
    .text                                    0x06001bb6   Section        0  _snputc.o(.text)
    .text                                    0x06001bc8   Section        0  _printf_wctomb.o(.text)
    .text                                    0x06001c84   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x06001d00   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x06001d01   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x06001d70   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x06001d71   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x06001e04   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x06001f8c   Section      128  rt_memmove_w.o(.text)
    .text                                    0x0600200c   Section      152  lludiv10.o(.text)
    .text                                    0x060020a4   Section        0  _printf_intcommon.o(.text)
    .text                                    0x06002156   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x06002159   Thumb Code   430  _printf_fp_dec.o(.text)
    .text                                    0x06002564   Section        0  _printf_char.o(.text)
    .text                                    0x06002590   Section        0  _printf_wchar.o(.text)
    .text                                    0x060025bc   Section        0  _wcrtomb.o(.text)
    .text                                    0x060025fc   Section       44  rtudiv10.o(.text)
    .text                                    0x06002628   Section       16  rt_ctype_table.o(.text)
    .text                                    0x06002638   Section        8  rt_locale.o(.text)
    .text                                    0x06002640   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x060026c0   Section        0  bigflt0.o(.text)
    .text                                    0x06002798   Section      244  strcmp.o(.text)
    CL$$btod_d2e                             0x0600288c   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x060028ca   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x06002910   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x06002970   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x06002ca8   Section      198  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x06002d6e   Section       40  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x06002d96   Section       40  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x06002dbe   Section      580  btod.o(CL$$btod_mult_common)
    i.CalcImageChecksum                      0x0600302c   Section        0  bspatch.o(i.CalcImageChecksum)
    i.CheckFMEraseState                      0x0600304c   Section        0  FM.o(i.CheckFMEraseState)
    i.CheckIfSkip                            0x06003088   Section        0  Flash.o(i.CheckIfSkip)
    i.CheckReserved                          0x060030c8   Section        0  tim.o(i.CheckReserved)
    i.ClearFM                                0x0600310c   Section        0  FM.o(i.ClearFM)
    i.Configure_Flashes                      0x06003174   Section        0  Flash.o(i.Configure_Flashes)
    i.Delay                                  0x06003260   Section        0  plat_api.o(i.Delay)
    i.EraseFlash                             0x06003298   Section        0  Flash.o(i.EraseFlash)
    i.FOTA_Init_Heap                         0x06003448   Section        0  bspatch.o(i.FOTA_Init_Heap)
    i.FindPackageInReserved                  0x06003454   Section        0  tim.o(i.FindPackageInReserved)
    i.GetABBTState                           0x060034d4   Section        0  FM_ext.o(i.GetABBTState)
    i.GetBlockSize                           0x06003500   Section        0  Flash.o(i.GetBlockSize)
    i.GetFMProperties                        0x06003510   Section        0  FM.o(i.GetFMProperties)
    i.GetFlashProperties                     0x0600351c   Section        0  Flash.o(i.GetFlashProperties)
    i.GetFlashType                           0x06003540   Section        0  Flash.o(i.GetFlashType)
    i.GetInitRoutine                         0x060035c0   Section        0  Flash.o(i.GetInitRoutine)
    i.GetOSCR0                               0x060035f4   Section        0  plat_api.o(i.GetOSCR0)
    i.GetPMICID                              0x06003618   Section        0  pmic.o(i.GetPMICID)
    i.GetPageSize                            0x06003680   Section        0  Flash.o(i.GetPageSize)
    i.GetPartitionOffset                     0x06003690   Section        0  FM.o(i.GetPartitionOffset)
    i.GetTimPointer                          0x060036a0   Section        0  tim.o(i.GetTimPointer)
    i.GetUseSpareArea                        0x060036ac   Section        0  Flash.o(i.GetUseSpareArea)
    i.GuilinClkInit                          0x060036c0   Section        0  guilin.o(i.GuilinClkInit)
    i.GuilinLiteClkInit                      0x06003818   Section        0  guilin_lite.o(i.GuilinLiteClkInit)
    i.GuilinLiteRead                         0x06003850   Section        0  guilin_lite.o(i.GuilinLiteRead)
    i.GuilinLiteWrite                        0x06003888   Section        0  guilin_lite.o(i.GuilinLiteWrite)
    i.GuilinLite_VBUCK1_CFG                  0x060038c0   Section        0  guilin_lite.o(i.GuilinLite_VBUCK1_CFG)
    i.GuilinLite_VBUCK_Set_VOUT              0x060038d8   Section        0  guilin_lite.o(i.GuilinLite_VBUCK_Set_VOUT)
    i.GuilinRead                             0x06003940   Section        0  guilin.o(i.GuilinRead)
    i.GuilinWrite                            0x0600398c   Section        0  guilin.o(i.GuilinWrite)
    i.Guilin_VBUCK1_CFG                      0x060039dc   Section        0  guilin.o(i.Guilin_VBUCK1_CFG)
    i.Guilin_VBUCK_Set_VOUT                  0x06003a18   Section        0  guilin.o(i.Guilin_VBUCK_Set_VOUT)
    i.I2CReceive                             0x06003ac8   Section        0  pmic.o(i.I2CReceive)
    i.I2CSend                                0x06003acc   Section        0  pmic.o(i.I2CSend)
    i.InitSODTimer                           0x06003ad0   Section        0  plat_api.o(i.InitSODTimer)
    i.InitializeQSPIDevice                   0x06003b10   Section        0  spi_nor.o(i.InitializeQSPIDevice)
    i.InitializeQSPINAND                     0x06003c8c   Section        0  spi_nand.o(i.InitializeQSPINAND)
    i.IsUpdaterSupportQueryFotaInfo          0x06003e18   Section        0  updater_table.o(i.IsUpdaterSupportQueryFotaInfo)
    i.LocateBlock                            0x06003e38   Section        0  FM.o(i.LocateBlock)
    i.MastI2CReceive                         0x06003e74   Section        0  pmic.o(i.MastI2CReceive)
    MastI2CReceive                           0x06003e74   ARM Code     260  pmic.o(i.MastI2CReceive)
    i.MastI2CSend                            0x06003fec   Section        0  pmic.o(i.MastI2CSend)
    MastI2CSend                              0x06003fec   ARM Code     244  pmic.o(i.MastI2CSend)
    i.OTA_Check_FotaImage                    0x0600411c   Section        0  bspatch.o(i.OTA_Check_FotaImage)
    i.OTA_Get_Fota_Progress                  0x060041e4   Section        0  bspatch.o(i.OTA_Get_Fota_Progress)
    i.OTA_Update_FotaImage                   0x0600420c   Section        0  bspatch.o(i.OTA_Update_FotaImage)
    i.OTA_Update_FotaParam                   0x060046f4   Section        0  bspatch.o(i.OTA_Update_FotaParam)
    i.PI2C_READ_REG                          0x060048e4   Section        0  pmic.o(i.PI2C_READ_REG)
    PI2C_READ_REG                            0x060048e4   ARM Code     132  pmic.o(i.PI2C_READ_REG)
    i.PI2C_WRITE_REG                         0x06004998   Section        0  pmic.o(i.PI2C_WRITE_REG)
    PI2C_WRITE_REG                           0x06004998   ARM Code     156  pmic.o(i.PI2C_WRITE_REG)
    i.PM812_VBUCK1_CFG                       0x06004a64   Section        0  pmic.o(i.PM812_VBUCK1_CFG)
    i.PMIC_FAULT_WU_ENABLE                   0x06004aa0   Section        0  pmic.o(i.PMIC_FAULT_WU_ENABLE)
    i.PMIC_FAULT_WU_EN_ENABLE                0x06004af4   Section        0  pmic.o(i.PMIC_FAULT_WU_EN_ENABLE)
    i.PMIC_ID_GET                            0x06004b48   Section        0  pmic.o(i.PMIC_ID_GET)
    i.PMIC_IS_PM802                          0x06004b74   Section        0  pmic.o(i.PMIC_IS_PM802)
    i.PMIC_IS_PM802S                         0x06004b90   Section        0  pmic.o(i.PMIC_IS_PM802S)
    i.PMIC_IS_PM803                          0x06004ba4   Section        0  pmic.o(i.PMIC_IS_PM803)
    i.PMIC_Init                              0x06004bbc   Section        0  pmic.o(i.PMIC_Init)
    i.PMIC_READ_REG_BASE                     0x06004c50   Section        0  pmic.o(i.PMIC_READ_REG_BASE)
    i.PMIC_SW_RESET                          0x06004c5c   Section        0  pmic.o(i.PMIC_SW_RESET)
    i.PMIC_WRITE_REG_BASE                    0x06004d00   Section        0  pmic.o(i.PMIC_WRITE_REG_BASE)
    i.PMIC_WRITE_REG_POWER                   0x06004d10   Section        0  pmic.o(i.PMIC_WRITE_REG_POWER)
    i.PP_Switch                              0x06004d20   Section        0  FreqChange.o(i.PP_Switch)
    i.PlatformVcoreConfigLow                 0x06004e80   Section        0  pmic.o(i.PlatformVcoreConfigLow)
    i.ReadFlash                              0x06004ec8   Section        0  Flash.o(i.ReadFlash)
    i.RelocateBlock                          0x06005010   Section        0  FM.o(i.RelocateBlock)
    i.RelocateBlock_LegacyExt                0x0600506c   Section        0  FM_ext.o(i.RelocateBlock_LegacyExt)
    i.ResetFlash                             0x06005074   Section        0  Flash.o(i.ResetFlash)
    i.ScrubBlock_LegacyExt                   0x0600509c   Section        0  FM_ext.o(i.ScrubBlock_LegacyExt)
    i.SetABBTState                           0x06005224   Section        0  FM_ext.o(i.SetABBTState)
    i.SetBBTState                            0x0600528c   Section        0  FM.o(i.SetBBTState)
    i.SetTIMPointers                         0x060052fc   Section        0  tim.o(i.SetTIMPointers)
    i.System_Adiff_Backup                    0x0600538c   Section        0  bspatch.o(i.System_Adiff_Backup)
    i.System_Adiff_Check_Dfota_File          0x06005840   Section        0  bspatch.o(i.System_Adiff_Check_Dfota_File)
    i.System_Adiff_Check_Old_Image           0x060059a0   Section        0  bspatch.o(i.System_Adiff_Check_Old_Image)
    i.System_Adiff_Move_Old_Backup           0x06005a08   Section        0  bspatch.o(i.System_Adiff_Move_Old_Backup)
    i.System_Adiff_Power_Off_Protection      0x06005c2c   Section        0  bspatch.o(i.System_Adiff_Power_Off_Protection)
    i.System_Adiff_Replace_Image             0x06005d68   Section        0  bspatch.o(i.System_Adiff_Replace_Image)
    i.System_Adiff_Update_Image              0x0600675c   Section        0  bspatch.o(i.System_Adiff_Update_Image)
    i.System_DFOTA_Upgrade_Adiff             0x060067e0   Section        0  bspatch.o(i.System_DFOTA_Upgrade_Adiff)
    i.System_FOTA_Upgrade_Adiff              0x06006a70   Section        0  bspatch.o(i.System_FOTA_Upgrade_Adiff)
    i.USTICAI2CReadDi_AUDIO                  0x06006d58   Section        0  pmic.o(i.USTICAI2CReadDi_AUDIO)
    i.USTICAI2CReadDi_GPADC                  0x06006d64   Section        0  pmic.o(i.USTICAI2CReadDi_GPADC)
    i.USTICAI2CReadDi_TEST                   0x06006d70   Section        0  pmic.o(i.USTICAI2CReadDi_TEST)
    i.USTICAI2CReadDi_base                   0x06006d7c   Section        0  pmic.o(i.USTICAI2CReadDi_base)
    i.USTICAI2CReadDi_power                  0x06006d88   Section        0  pmic.o(i.USTICAI2CReadDi_power)
    i.USTICAI2CWriteDi_AUDIO                 0x06006d94   Section        0  pmic.o(i.USTICAI2CWriteDi_AUDIO)
    i.USTICAI2CWriteDi_GPADC                 0x06006da4   Section        0  pmic.o(i.USTICAI2CWriteDi_GPADC)
    i.USTICAI2CWriteDi_TEST                  0x06006db4   Section        0  pmic.o(i.USTICAI2CWriteDi_TEST)
    i.USTICAI2CWriteDi_base                  0x06006dc4   Section        0  pmic.o(i.USTICAI2CWriteDi_base)
    i.USTICAI2CWriteDi_power                 0x06006dd4   Section        0  pmic.o(i.USTICAI2CWriteDi_power)
    i.Voltage_set_main                       0x06006de4   Section        0  pmic.o(i.Voltage_set_main)
    i.WriteFlash                             0x06006e04   Section        0  Flash.o(i.WriteFlash)
    i.__ARM_fpclassify                       0x0600703c   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__spi_nand_do_read_page                0x06007064   Section        0  spi_nand.o(i.__spi_nand_do_read_page)
    __spi_nand_do_read_page                  0x06007064   ARM Code     300  spi_nand.o(i.__spi_nand_do_read_page)
    i.__spi_nand_write                       0x06007208   Section        0  spi_nand.o(i.__spi_nand_write)
    __spi_nand_write                         0x06007208   ARM Code     256  spi_nand.o(i.__spi_nand_write)
    i._is_digit                              0x06007358   Section        0  __printf_wp.o(i._is_digit)
    i.aclk_dfc_switch                        0x06007368   Section        0  FreqChange.o(i.aclk_dfc_switch)
    i.ap_uart_putc                           0x060073e0   Section        0  uart.o(i.ap_uart_putc)
    i.bzpacth                                0x06007400   Section        0  bspatch.o(i.bzpacth)
    i.check_if_DCS_mode                      0x060075d4   Section        0  utilities.o(i.check_if_DCS_mode)
    i.check_old_image_checksum               0x06007640   Section        0  bspatch.o(i.check_old_image_checksum)
    i.cp_uart_init                           0x060076f0   Section        0  uart.o(i.cp_uart_init)
    i.datamodule_fota                        0x06007784   Section        0  bspatch.o(i.datamodule_fota)
    i.dck_dfc_switch                         0x06007d40   Section        0  FreqChange.o(i.dck_dfc_switch)
    i.enable_PLL                             0x06007dec   Section        0  FreqChange.o(i.enable_PLL)
    i.fm_spi_nand_ecc_status                 0x06007e8c   Section        0  spi_nand.o(i.fm_spi_nand_ecc_status)
    fm_spi_nand_ecc_status                   0x06007e8c   ARM Code     160  spi_nand.o(i.fm_spi_nand_ecc_status)
    i.gd_spi_nand_ecc_status                 0x06007f4c   Section        0  spi_nand.o(i.gd_spi_nand_ecc_status)
    gd_spi_nand_ecc_status                   0x06007f4c   ARM Code     164  spi_nand.o(i.gd_spi_nand_ecc_status)
    i.gd_spi_nand_ecc_status2                0x06008010   Section        0  spi_nand.o(i.gd_spi_nand_ecc_status2)
    gd_spi_nand_ecc_status2                  0x06008010   ARM Code     180  spi_nand.o(i.gd_spi_nand_ecc_status2)
    i.generic_spi_nand_ecc_status            0x06008104   Section        0  spi_nand.o(i.generic_spi_nand_ecc_status)
    generic_spi_nand_ecc_status              0x06008104   ARM Code      88  spi_nand.o(i.generic_spi_nand_ecc_status)
    i.get_updater_function_magic_type        0x0600817c   Section        0  updater_table.o(i.get_updater_function_magic_type)
    i.ilog2                                  0x0600818c   Section        0  spi_nand.o(i.ilog2)
    ilog2                                    0x0600818c   ARM Code      36  spi_nand.o(i.ilog2)
    i.ilog2                                  0x060081b0   Section        0  spi_nor.o(i.ilog2)
    ilog2                                    0x060081b0   ARM Code      36  spi_nor.o(i.ilog2)
    i.mdelay                                 0x060081d4   Section        0  pmic.o(i.mdelay)
    i.micron_spi_nand_ecc_status             0x0600820c   Section        0  spi_nand.o(i.micron_spi_nand_ecc_status)
    micron_spi_nand_ecc_status               0x0600820c   ARM Code     128  spi_nand.o(i.micron_spi_nand_ecc_status)
    i.mpu_value_check                        0x060082ac   Section        0  system.o(i.mpu_value_check)
    i.mxic2_spi_nand_ecc_status              0x06008480   Section        0  spi_nand.o(i.mxic2_spi_nand_ecc_status)
    mxic2_spi_nand_ecc_status                0x06008480   ARM Code     172  spi_nand.o(i.mxic2_spi_nand_ecc_status)
    i.mxic_spi_nand_ecc_status               0x0600854c   Section        0  spi_nand.o(i.mxic_spi_nand_ecc_status)
    mxic_spi_nand_ecc_status                 0x0600854c   ARM Code      52  spi_nand.o(i.mxic_spi_nand_ecc_status)
    i.offtin                                 0x06008580   Section        0  bspatch.o(i.offtin)
    offtin                                   0x06008580   ARM Code     208  bspatch.o(i.offtin)
    i.qspi_cmd_done_interrupt                0x06008650   Section        0  qspi_host.o(i.qspi_cmd_done_interrupt)
    i.qspi_cmd_done_pio                      0x060086b8   Section        0  qspi_host.o(i.qspi_cmd_done_pio)
    i.qspi_config_lookup_tbl                 0x06008788   Section        0  qspi_host.o(i.qspi_config_lookup_tbl)
    qspi_config_lookup_tbl                   0x06008788   ARM Code     652  qspi_host.o(i.qspi_config_lookup_tbl)
    i.qspi_config_mfp                        0x06008a14   Section        0  qspi_host.o(i.qspi_config_mfp)
    qspi_config_mfp                          0x06008a14   ARM Code      96  qspi_host.o(i.qspi_config_mfp)
    i.qspi_enable_xip                        0x06008a78   Section        0  qspi_host.o(i.qspi_enable_xip)
    i.qspi_enter_mode                        0x06008b0c   Section        0  qspi_host.o(i.qspi_enter_mode)
    qspi_enter_mode                          0x06008b0c   ARM Code      36  qspi_host.o(i.qspi_enter_mode)
    i.qspi_host_init                         0x06008b38   Section        0  qspi_host.o(i.qspi_host_init)
    i.qspi_init_ahb                          0x06008c80   Section        0  qspi_host.o(i.qspi_init_ahb)
    i.qspi_invalid_ahb                       0x06008d30   Section        0  qspi_host.o(i.qspi_invalid_ahb)
    qspi_invalid_ahb                         0x06008d30   ARM Code      72  qspi_host.o(i.qspi_invalid_ahb)
    i.qspi_poll_rx_buff                      0x06008d80   Section        0  qspi_host.o(i.qspi_poll_rx_buff)
    qspi_poll_rx_buff                        0x06008d80   ARM Code     276  qspi_host.o(i.qspi_poll_rx_buff)
    i.qspi_preinit_lookup_tbl                0x06008ed4   Section        0  qspi_host.o(i.qspi_preinit_lookup_tbl)
    i.qspi_prepare_transmit                  0x06008f90   Section        0  qspi_host.o(i.qspi_prepare_transmit)
    qspi_prepare_transmit                    0x06008f90   ARM Code     248  qspi_host.o(i.qspi_prepare_transmit)
    i.qspi_set_func_clk                      0x0600908c   Section        0  qspi_host.o(i.qspi_set_func_clk)
    i.qspi_set_func_clk_fc                   0x0600914c   Section        0  qspi_host.o(i.qspi_set_func_clk_fc)
    qspi_set_func_clk_fc                     0x0600914c   ARM Code     240  qspi_host.o(i.qspi_set_func_clk_fc)
    i.qspi_start_cmd                         0x0600925c   Section        0  qspi_host.o(i.qspi_start_cmd)
    i.qspi_wait_cmd_done                     0x0600955c   Section        0  qspi_host.o(i.qspi_wait_cmd_done)
    qspi_wait_cmd_done                       0x0600955c   ARM Code     536  qspi_host.o(i.qspi_wait_cmd_done)
    i.qspi_write_rbct                        0x06009774   Section        0  qspi_host.o(i.qspi_write_rbct)
    qspi_write_rbct                          0x06009774   ARM Code      68  qspi_host.o(i.qspi_write_rbct)
    i.qspi_write_sfar                        0x060097c0   Section        0  qspi_host.o(i.qspi_write_sfar)
    qspi_write_sfar                          0x060097c0   ARM Code      64  qspi_host.o(i.qspi_write_sfar)
    i.qspi_writel_check                      0x06009804   Section        0  qspi_host.o(i.qspi_writel_check)
    qspi_writel_check                        0x06009804   ARM Code      28  qspi_host.o(i.qspi_writel_check)
    i.qspi_writel_clear                      0x06009820   Section        0  qspi_host.o(i.qspi_writel_clear)
    qspi_writel_clear                        0x06009820   ARM Code      28  qspi_host.o(i.qspi_writel_clear)
    i.sanitize_string                        0x0600983c   Section        0  spi_nand.o(i.sanitize_string)
    sanitize_string                          0x0600983c   ARM Code     100  spi_nand.o(i.sanitize_string)
    i.spi_nand_change_mode                   0x060098a0   Section        0  spi_nand.o(i.spi_nand_change_mode)
    spi_nand_change_mode                     0x060098a0   ARM Code     132  spi_nand.o(i.spi_nand_change_mode)
    i.spi_nand_detect_onfi                   0x06009924   Section        0  spi_nand.o(i.spi_nand_detect_onfi)
    spi_nand_detect_onfi                     0x06009924   ARM Code     400  spi_nand.o(i.spi_nand_detect_onfi)
    i.spi_nand_disable_ecc                   0x06009b0c   Section        0  spi_nand.o(i.spi_nand_disable_ecc)
    spi_nand_disable_ecc                     0x06009b0c   ARM Code     128  spi_nand.o(i.spi_nand_disable_ecc)
    i.spi_nand_do_erase                      0x06009b8c   Section        0  spi_nand.o(i.spi_nand_do_erase)
    spi_nand_do_erase                        0x06009b8c   ARM Code      20  spi_nand.o(i.spi_nand_do_erase)
    i.spi_nand_do_read                       0x06009ba4   Section        0  spi_nand.o(i.spi_nand_do_read)
    spi_nand_do_read                         0x06009ba4   ARM Code      80  spi_nand.o(i.spi_nand_do_read)
    i.spi_nand_do_read_page                  0x06009c28   Section        0  spi_nand.o(i.spi_nand_do_read_page)
    spi_nand_do_read_page                    0x06009c28   ARM Code     196  spi_nand.o(i.spi_nand_do_read_page)
    i.spi_nand_do_reset                      0x06009cec   Section        0  spi_nand.o(i.spi_nand_do_reset)
    spi_nand_do_reset                        0x06009cec   ARM Code       8  spi_nand.o(i.spi_nand_do_reset)
    i.spi_nand_do_write                      0x06009cf8   Section        0  spi_nand.o(i.spi_nand_do_write)
    spi_nand_do_write                        0x06009cf8   ARM Code      36  spi_nand.o(i.spi_nand_do_write)
    i.spi_nand_do_write_page                 0x06009d20   Section        0  spi_nand.o(i.spi_nand_do_write_page)
    spi_nand_do_write_page                   0x06009d20   ARM Code     472  spi_nand.o(i.spi_nand_do_write_page)
    i.spi_nand_enable_ecc                    0x06009f84   Section        0  spi_nand.o(i.spi_nand_enable_ecc)
    spi_nand_enable_ecc                      0x06009f84   ARM Code     132  spi_nand.o(i.spi_nand_enable_ecc)
    i.spi_nand_erase                         0x0600a008   Section        0  spi_nand.o(i.spi_nand_erase)
    i.spi_nand_gen_fbbt                      0x0600a1d0   Section        0  spi_nand.o(i.spi_nand_gen_fbbt)
    spi_nand_gen_fbbt                        0x0600a1d0   ARM Code     304  spi_nand.o(i.spi_nand_gen_fbbt)
    i.spi_nand_get_cfg                       0x0600a348   Section        0  spi_nand.o(i.spi_nand_get_cfg)
    spi_nand_get_cfg                         0x0600a348   ARM Code      12  spi_nand.o(i.spi_nand_get_cfg)
    i.spi_nand_init                          0x0600a354   Section        0  spi_nand.o(i.spi_nand_init)
    i.spi_nand_lock_block                    0x0600a6e4   Section        0  spi_nand.o(i.spi_nand_lock_block)
    i.spi_nand_program_data_to_cache         0x0600a6f8   Section        0  spi_nand.o(i.spi_nand_program_data_to_cache)
    spi_nand_program_data_to_cache           0x0600a6f8   ARM Code     156  spi_nand.o(i.spi_nand_program_data_to_cache)
    i.spi_nand_read_from_cache               0x0600a794   Section        0  spi_nand.o(i.spi_nand_read_from_cache)
    spi_nand_read_from_cache                 0x0600a794   ARM Code     192  spi_nand.o(i.spi_nand_read_from_cache)
    i.spi_nand_read_page_to_cache            0x0600a854   Section        0  spi_nand.o(i.spi_nand_read_page_to_cache)
    spi_nand_read_page_to_cache              0x0600a854   ARM Code      92  spi_nand.o(i.spi_nand_read_page_to_cache)
    i.spi_nand_read_pages                    0x0600a8b0   Section        0  spi_nand.o(i.spi_nand_read_pages)
    spi_nand_read_pages                      0x0600a8b0   ARM Code     244  spi_nand.o(i.spi_nand_read_pages)
    i.spi_nand_read_reg                      0x0600a9c4   Section        0  spi_nand.o(i.spi_nand_read_reg)
    spi_nand_read_reg                        0x0600a9c4   ARM Code     108  spi_nand.o(i.spi_nand_read_reg)
    i.spi_nand_reset                         0x0600aa4c   Section        0  spi_nand.o(i.spi_nand_reset)
    spi_nand_reset                           0x0600aa4c   ARM Code      80  spi_nand.o(i.spi_nand_reset)
    i.spi_nand_scan_id_table                 0x0600aab8   Section        0  spi_nand.o(i.spi_nand_scan_id_table)
    spi_nand_scan_id_table                   0x0600aab8   ARM Code     220  spi_nand.o(i.spi_nand_scan_id_table)
    i.spi_nand_set_cfg                       0x0600abbc   Section        0  spi_nand.o(i.spi_nand_set_cfg)
    spi_nand_set_cfg                         0x0600abbc   ARM Code      12  spi_nand.o(i.spi_nand_set_cfg)
    i.spi_nand_set_rd_wr_op                  0x0600abc8   Section        0  spi_nand.o(i.spi_nand_set_rd_wr_op)
    spi_nand_set_rd_wr_op                    0x0600abc8   ARM Code     136  spi_nand.o(i.spi_nand_set_rd_wr_op)
    i.spi_nand_wait                          0x0600ac80   Section        0  spi_nand.o(i.spi_nand_wait)
    spi_nand_wait                            0x0600ac80   ARM Code     108  spi_nand.o(i.spi_nand_wait)
    i.spi_nand_write_enable                  0x0600ad10   Section        0  spi_nand.o(i.spi_nand_write_enable)
    spi_nand_write_enable                    0x0600ad10   ARM Code      60  spi_nand.o(i.spi_nand_write_enable)
    i.spi_nand_write_reg                     0x0600ad4c   Section        0  spi_nand.o(i.spi_nand_write_reg)
    spi_nand_write_reg                       0x0600ad4c   ARM Code     112  spi_nand.o(i.spi_nand_write_reg)
    i.spi_nor_do_erase                       0x0600add8   Section        0  spi_nor.o(i.spi_nor_do_erase)
    spi_nor_do_erase                         0x0600add8   ARM Code      20  spi_nor.o(i.spi_nor_do_erase)
    i.spi_nor_do_read                        0x0600adf0   Section        0  spi_nor.o(i.spi_nor_do_read)
    spi_nor_do_read                          0x0600adf0   ARM Code      16  spi_nor.o(i.spi_nor_do_read)
    i.spi_nor_do_write                       0x0600ae04   Section        0  spi_nor.o(i.spi_nor_do_write)
    spi_nor_do_write                         0x0600ae04   ARM Code      16  spi_nor.o(i.spi_nor_do_write)
    i.spi_nor_enable_4byte_mode              0x0600ae18   Section        0  spi_nor.o(i.spi_nor_enable_4byte_mode)
    spi_nor_enable_4byte_mode                0x0600ae18   ARM Code     228  spi_nor.o(i.spi_nor_enable_4byte_mode)
    i.spi_nor_erase                          0x0600af88   Section        0  spi_nor.o(i.spi_nor_erase)
    i.spi_nor_erase_block                    0x0600b1bc   Section        0  spi_nor.o(i.spi_nor_erase_block)
    spi_nor_erase_block                      0x0600b1bc   ARM Code     152  spi_nor.o(i.spi_nor_erase_block)
    i.spi_nor_init                           0x0600b254   Section        0  spi_nor.o(i.spi_nor_init)
    i.spi_nor_read                           0x0600b500   Section        0  spi_nor.o(i.spi_nor_read)
    spi_nor_read                             0x0600b500   ARM Code     424  spi_nor.o(i.spi_nor_read)
    i.spi_nor_read_status                    0x0600b6f4   Section        0  spi_nor.o(i.spi_nor_read_status)
    spi_nor_read_status                      0x0600b6f4   ARM Code     100  spi_nor.o(i.spi_nor_read_status)
    i.spi_nor_read_status1                   0x0600b770   Section        0  spi_nor.o(i.spi_nor_read_status1)
    spi_nor_read_status1                     0x0600b770   ARM Code      16  spi_nor.o(i.spi_nor_read_status1)
    i.spi_nor_read_status2                   0x0600b780   Section        0  spi_nor.o(i.spi_nor_read_status2)
    spi_nor_read_status2                     0x0600b780   ARM Code      60  spi_nor.o(i.spi_nor_read_status2)
    i.spi_nor_reset                          0x0600b7bc   Section        0  spi_nor.o(i.spi_nor_reset)
    spi_nor_reset                            0x0600b7bc   ARM Code     144  spi_nor.o(i.spi_nor_reset)
    i.spi_nor_set_quad                       0x0600b884   Section        0  spi_nor.o(i.spi_nor_set_quad)
    spi_nor_set_quad                         0x0600b884   ARM Code     412  spi_nor.o(i.spi_nor_set_quad)
    i.spi_nor_set_rd_wr_op                   0x0600bab4   Section        0  spi_nor.o(i.spi_nor_set_rd_wr_op)
    spi_nor_set_rd_wr_op                     0x0600bab4   ARM Code     148  spi_nor.o(i.spi_nor_set_rd_wr_op)
    i.spi_nor_wait                           0x0600bb78   Section        0  spi_nor.o(i.spi_nor_wait)
    spi_nor_wait                             0x0600bb78   ARM Code      56  spi_nor.o(i.spi_nor_wait)
    i.spi_nor_write                          0x0600bbb0   Section        0  spi_nor.o(i.spi_nor_write)
    spi_nor_write                            0x0600bbb0   ARM Code     192  spi_nor.o(i.spi_nor_write)
    i.spi_nor_write_enable                   0x0600bcb8   Section        0  spi_nor.o(i.spi_nor_write_enable)
    spi_nor_write_enable                     0x0600bcb8   ARM Code      72  spi_nor.o(i.spi_nor_write_enable)
    i.spi_nor_write_page                     0x0600bd00   Section        0  spi_nor.o(i.spi_nor_write_page)
    spi_nor_write_page                       0x0600bd00   ARM Code     212  spi_nor.o(i.spi_nor_write_page)
    i.spi_nor_write_status1                  0x0600bdfc   Section        0  spi_nor.o(i.spi_nor_write_status1)
    spi_nor_write_status1                    0x0600bdfc   ARM Code      92  spi_nor.o(i.spi_nor_write_status1)
    i.uart_printf                            0x0600be70   Section        0  uart.o(i.uart_printf)
    i.updater_get_fota_solution              0x0600bed4   Section        0  updater_table.o(i.updater_get_fota_solution)
    i.updater_get_lcd_info                   0x0600bfa0   Section        0  updater_table.o(i.updater_get_lcd_info)
    i.updater_get_version_info               0x0600c064   Section        0  updater_table.o(i.updater_get_version_info)
    i.updater_header_table_init              0x0600c12c   Section        0  updater_table.o(i.updater_header_table_init)
    i.ustica_I2CConfigureDi                  0x0600c284   Section        0  pmic.o(i.ustica_I2CConfigureDi)
    i.ustica_I2CEnableclockandPin            0x0600c300   Section        0  pmic.o(i.ustica_I2CEnableclockandPin)
    i.ustica_I2CIntLISRDirect                0x0600c318   Section        0  pmic.o(i.ustica_I2CIntLISRDirect)
    ustica_I2CIntLISRDirect                  0x0600c318   ARM Code     180  pmic.o(i.ustica_I2CIntLISRDirect)
    i.ustica_I2CMasterReceiveDataDirect      0x0600c3d4   Section        0  pmic.o(i.ustica_I2CMasterReceiveDataDirect)
    i.ustica_I2CMasterSendDataDirect         0x0600c4c4   Section        0  pmic.o(i.ustica_I2CMasterSendDataDirect)
    i.ustica_i2cWaitStatusDirect             0x0600c4fc   Section        0  pmic.o(i.ustica_i2cWaitStatusDirect)
    ustica_i2cWaitStatusDirect               0x0600c4fc   ARM Code     136  pmic.o(i.ustica_i2cWaitStatusDirect)
    i.ustica_masterSendDirect                0x0600c588   Section        0  pmic.o(i.ustica_masterSendDirect)
    ustica_masterSendDirect                  0x0600c588   ARM Code     440  pmic.o(i.ustica_masterSendDirect)
    i.uudelay                                0x0600c744   Section        0  pmic.o(i.uudelay)
    i.xtx2_spi_nand_ecc_status               0x0600c768   Section        0  spi_nand.o(i.xtx2_spi_nand_ecc_status)
    xtx2_spi_nand_ecc_status                 0x0600c768   ARM Code     128  spi_nand.o(i.xtx2_spi_nand_ecc_status)
    i.xtx_spi_nand_ecc_status                0x0600c7e8   Section        0  spi_nand.o(i.xtx_spi_nand_ecc_status)
    xtx_spi_nand_ecc_status                  0x0600c7e8   ARM Code      72  spi_nand.o(i.xtx_spi_nand_ecc_status)
    i.yxsc_spi_nand_ecc_status               0x0600c830   Section        0  spi_nand.o(i.yxsc_spi_nand_ecc_status)
    yxsc_spi_nand_ecc_status                 0x0600c830   ARM Code     148  spi_nand.o(i.yxsc_spi_nand_ecc_status)
    locale$$code                             0x0600c8e4   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x0600c910   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$printf1                            0x0600c93c   Section        4  printf1.o(x$fpl$printf1)
    x$fpl$usenofp                            0x0600c940   Section        0  usenofp.o(x$fpl$usenofp)
    x$sdiv                                   0x0600c940   Section        6  aeabi_sdiv_cr4.o(x$sdiv)
    x$sdivmod                                0x0600c946   Section       12  aeabi_sdiv_cr4.o(x$sdivmod)
    x$udiv                                   0x0600c95c   Section        6  aeabi_sdiv_cr4.o(x$udiv)
    x$udivmod                                0x0600c962   Section       12  aeabi_sdiv_cr4.o(x$udivmod)
    .constdata                               0x0600c96e   Section      175  spi_nand.o(.constdata)
    __func__                                 0x0600c96e   Data          28  spi_nand.o(.constdata)
    __func__                                 0x0600c98a   Data          27  spi_nand.o(.constdata)
    __func__                                 0x0600c9a5   Data          23  spi_nand.o(.constdata)
    __func__                                 0x0600c9bc   Data          25  spi_nand.o(.constdata)
    __func__                                 0x0600c9d5   Data          24  spi_nand.o(.constdata)
    __func__                                 0x0600c9ed   Data          16  spi_nand.o(.constdata)
    __func__                                 0x0600c9fd   Data          17  spi_nand.o(.constdata)
    __func__                                 0x0600ca0e   Data          15  spi_nand.o(.constdata)
    .constdata                               0x0600ca1d   Section       41  spi_nor.o(.constdata)
    __func__                                 0x0600ca1d   Data          14  spi_nor.o(.constdata)
    __func__                                 0x0600ca2b   Data          13  spi_nor.o(.constdata)
    __func__                                 0x0600ca38   Data          14  spi_nor.o(.constdata)
    .constdata                               0x0600ca46   Section      285  updater_table.o(.constdata)
    __func__                                 0x0600ca46   Data          26  updater_table.o(.constdata)
    __func__                                 0x0600ca60   Data          26  updater_table.o(.constdata)
    __func__                                 0x0600ca7a   Data          25  updater_table.o(.constdata)
    __func__                                 0x0600ca93   Data          21  updater_table.o(.constdata)
    __func__                                 0x0600caa8   Data          33  updater_table.o(.constdata)
    __func__                                 0x0600cac9   Data          33  updater_table.o(.constdata)
    __func__                                 0x0600caea   Data          29  updater_table.o(.constdata)
    __func__                                 0x0600cb07   Data          30  updater_table.o(.constdata)
    __func__                                 0x0600cb25   Data          29  updater_table.o(.constdata)
    __func__                                 0x0600cb42   Data          33  updater_table.o(.constdata)
    .constdata                               0x0600cb63   Section       56  bspatch.o(.constdata)
    __FUNCTION__                             0x0600cb63   Data          26  bspatch.o(.constdata)
    __FUNCTION__                             0x0600cb7d   Data          30  bspatch.o(.constdata)
    .constdata                               0x0600cb9c   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x0600cb9c   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x0600cba4   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x0600cba4   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x0600cbb8   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x0600cbcc   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0600cbcc   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x0600cbe0   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0600cbe0   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0600cc1c   Data          64  bigflt0.o(.constdata)
    .conststring                             0x0600cc74   Section       77  pmic.o(.conststring)
    .conststring                             0x0600ccc4   Section      487  spi_nand.o(.conststring)
    .conststring                             0x0600ceac   Section      362  spi_nor.o(.conststring)
    .conststring                             0x0600d018   Section      445  bspatch.o(.conststring)
    locale$$data                             0x0600d1d8   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0600d1dc   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0600d1e4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0600d1f0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0600d1f2   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0600d1f3   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x0600d1f4   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x0600d1f4   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x0600d1f8   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x0600d200   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x0600d304   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x06051200   Section        4  plat_api.o(.data)
    misc_SOD_OSCR0                           0x06051200   Data           4  plat_api.o(.data)
    .data                                    0x06051204   Section       12  pmic.o(.data)
    _repeat_start                            0x06051205   Data           1  pmic.o(.data)
    pmic_complete_id                         0x06051207   Data           1  pmic.o(.data)
    .data                                    0x06051210   Section        1  FreqChange.o(.data)
    voltageSet                               0x06051210   Data           1  FreqChange.o(.data)
    .data                                    0x06051214   Section        8  Flash.o(.data)
    CurrentFlashBootType                     0x06051215   Data           1  Flash.o(.data)
    .data                                    0x0605121c   Section     2198  spi_nand.o(.data)
    spi_nand_table                           0x0605121c   Data        1820  spi_nand.o(.data)
    cmd_table                                0x06051938   Data         378  spi_nand.o(.data)
    .data                                    0x06051ab4   Section     1454  spi_nor.o(.data)
    spi_nor_table                            0x06051ab4   Data         992  spi_nor.o(.data)
    cmd_table                                0x06051e94   Data         462  spi_nor.o(.data)
    .data                                    0x06052064   Section       12  FM.o(.data)
    .data                                    0x06052070   Section        8  LzmaLib.o(.data)
    g_Alloc                                  0x06052070   Data           8  LzmaLib.o(.data)
    .data                                    0x06052078   Section       12  tinyalloc.o(.data)
    heap                                     0x06052080   Data           4  tinyalloc.o(.data)
    .data                                    0x06052084   Section        8  updater_table.o(.data)
    s_updater_function_magic_type            0x06052084   Data           1  updater_table.o(.data)
    init_flag                                0x06052085   Data           1  updater_table.o(.data)
    pUpdaterHeaderInfo                       0x06052088   Data           4  updater_table.o(.data)
    .data                                    0x0605208c   Section        2  utilities.o(.data)
    isDcsMode                                0x0605208c   Data           1  utilities.o(.data)
    init_var                                 0x0605208d   Data           1  utilities.o(.data)
    .data                                    0x06052090   Section       16  bspatch.o(.data)
    _fota_image_all_num                      0x06052090   Data           4  bspatch.o(.data)
    fota_image_num                           0x06052094   Data           4  bspatch.o(.data)
    FOTA_OFFSET                              0x06052098   Data           4  bspatch.o(.data)
    .bss                                     0x060520a0   Section       80  pmic.o(.bss)
    _receiveReqParams                        0x060520e0   Data          16  pmic.o(.bss)
    .bss                                     0x060520f0   Section      160  Flash.o(.bss)
    .bss                                     0x06052190   Section       68  qspi_host.o(.bss)
    qspi_host                                0x06052190   Data          68  qspi_host.o(.bss)
    .bss                                     0x060521d8   Section      360  spi_nand.o(.bss)
    nand_chip                                0x060521d8   Data         360  spi_nand.o(.bss)
    .bss                                     0x06052340   Section      360  spi_nor.o(.bss)
    nor_chip                                 0x06052340   Data         360  spi_nor.o(.bss)
    .bss                                     0x060524a8   Section       84  FM.o(.bss)
    .bss                                     0x060524fc   Section       20  tim.o(.bss)
    sTim                                     0x060524fc   Data          20  tim.o(.bss)
    .bss                                     0x06052510   Section       20  rt_locale.o(.bss)
    __rt_locale_data                         0x06052510   Data          20  rt_locale.o(.bss)
    STACK                                    0x06052528   Section    40960  StartUp.o(STACK)
    Stack_Top                                0x0605c528   Data           0  StartUp.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$ARM_ISAv7$E$P$J$D$K$B$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$STANDARDLIB$THUMB2LIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    Image$$DTCM$$RW$$Length                  0x00000000   Number         0  anon$$obj.o ABSOLUTE
    Image$$DTCM$$ZI$$Length                  0x00000000   Number         0  anon$$obj.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_fp_hex                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    Image$$DATA$$RW$$Length                  0x00000ea0   Number         0  anon$$obj.o ABSOLUTE
    Long ARM to Thumb Veneer to __aeabi_uread4 0x00001000   ARM Code       8  anon$$obj.o(Veneer$$Code)
    Image$$ITCM$$Base                        0x00001000   Number         0  anon$$obj.o ABSOLUTE
    Long ARM to ARM Veneer to __aeabi_memcpy 0x00001008   ARM Code       8  anon$$obj.o(Veneer$$Code)
    Long ARM to ARM Veneer to __aeabi_memcpy4 0x00001010   ARM Code       8  anon$$obj.o(Veneer$$Code)
    Long ARM to Thumb Veneer to __aeabi_uidivmod 0x00001018   ARM Code       8  anon$$obj.o(Veneer$$Code)
    Long ARM to Thumb Veneer to __aeabi_uidiv 0x00001020   ARM Code       8  anon$$obj.o(Veneer$$Code)
    Long ARM to ARM Veneer to __aeabi_memmove 0x00001028   ARM Code       8  anon$$obj.o(Veneer$$Code)
    Long ARM to ARM Veneer to uart_printf    0x00001030   ARM Code       8  anon$$obj.o(Veneer$$Code)
    GetMatchesSpec1                          0x00001a68   ARM Code     308  LzFind.o(i.GetMatchesSpec1)
    LzmaCompress                             0x00003938   ARM Code     132  LzmaLib.o(i.LzmaCompress)
    LzmaCompress_internal                    0x000039bc   ARM Code      96  LzmaLib.o(i.LzmaCompress_internal)
    LzmaDec_AllocateProbs                    0x00003a20   ARM Code      68  LzmaDec.o(i.LzmaDec_AllocateProbs)
    LzmaDec_DecodeToDic                      0x00004788   ARM Code     796  LzmaDec.o(i.LzmaDec_DecodeToDic)
    LzmaDec_FreeProbs                        0x00004aa4   ARM Code      40  LzmaDec.o(i.LzmaDec_FreeProbs)
    LzmaDec_Init                             0x00004acc   ARM Code      20  LzmaDec.o(i.LzmaDec_Init)
    LzmaDec_InitDicAndState                  0x00004ae0   ARM Code      52  LzmaDec.o(i.LzmaDec_InitDicAndState)
    LzmaDecode                               0x00005100   ARM Code     192  LzmaDec.o(i.LzmaDecode)
    LzmaEncProps_Init                        0x000051c0   ARM Code      60  LzmaEnc.o(i.LzmaEncProps_Init)
    LzmaEncProps_Normalize                   0x000051fc   ARM Code     284  LzmaEnc.o(i.LzmaEncProps_Normalize)
    LzmaEnc_Construct                        0x00005bb8   ARM Code     108  LzmaEnc.o(i.LzmaEnc_Construct)
    LzmaEnc_Create                           0x00005c2c   ARM Code      32  LzmaEnc.o(i.LzmaEnc_Create)
    LzmaEnc_Destroy                          0x00005c50   ARM Code      36  LzmaEnc.o(i.LzmaEnc_Destroy)
    LzmaEnc_Destruct                         0x00005c74   ARM Code      72  LzmaEnc.o(i.LzmaEnc_Destruct)
    LzmaEnc_Encode                           0x00005cbc   ARM Code     208  LzmaEnc.o(i.LzmaEnc_Encode)
    LzmaEnc_FastPosInit                      0x00005d9c   ARM Code      84  LzmaEnc.o(i.LzmaEnc_FastPosInit)
    LzmaEnc_FreeLits                         0x00005df0   ARM Code      68  LzmaEnc.o(i.LzmaEnc_FreeLits)
    LzmaEnc_Init                             0x00005e34   ARM Code     400  LzmaEnc.o(i.LzmaEnc_Init)
    LzmaEnc_InitPriceTables                  0x00005fc8   ARM Code      84  LzmaEnc.o(i.LzmaEnc_InitPriceTables)
    LzmaEnc_InitPrices                       0x0000601c   ARM Code     132  LzmaEnc.o(i.LzmaEnc_InitPrices)
    LzmaEnc_MemEncode                        0x000060a8   ARM Code     136  LzmaEnc.o(i.LzmaEnc_MemEncode)
    LzmaEnc_SetProps                         0x00006138   ARM Code     260  LzmaEnc.o(i.LzmaEnc_SetProps)
    LzmaEnc_WriteProperties                  0x00006240   ARM Code     168  LzmaEnc.o(i.LzmaEnc_WriteProperties)
    LzmaEncode                               0x000062ec   ARM Code     156  LzmaEnc.o(i.LzmaEncode)
    LzmaProps_Decode                         0x00006388   ARM Code     136  LzmaDec.o(i.LzmaProps_Decode)
    LzmaUncompress                           0x00006410   ARM Code     120  LzmaLib.o(i.LzmaUncompress)
    MatchFinder_Construct                    0x0000652c   ARM Code     116  LzFind.o(i.MatchFinder_Construct)
    MatchFinder_Create                       0x000065a4   ARM Code     468  LzFind.o(i.MatchFinder_Create)
    Image$$ITCM$$Length                      0x000065d0   Number         0  anon$$obj.o ABSOLUTE
    MatchFinder_CreateVTable                 0x00006778   ARM Code     112  LzFind.o(i.MatchFinder_CreateVTable)
    MatchFinder_Free                         0x00006818   ARM Code      32  LzFind.o(i.MatchFinder_Free)
    MatchFinder_GetIndexByte                 0x00006860   ARM Code      12  LzFind.o(i.MatchFinder_GetIndexByte)
    MatchFinder_GetNumAvailableBytes         0x0000686c   ARM Code      16  LzFind.o(i.MatchFinder_GetNumAvailableBytes)
    MatchFinder_GetPointerToCurrentPos       0x0000687c   ARM Code       8  LzFind.o(i.MatchFinder_GetPointerToCurrentPos)
    MatchFinder_Init                         0x00006884   ARM Code      92  LzFind.o(i.MatchFinder_Init)
    MatchFinder_MoveBlock                    0x000068e0   ARM Code      64  LzFind.o(i.MatchFinder_MoveBlock)
    MatchFinder_NeedMove                     0x00006954   ARM Code      40  LzFind.o(i.MatchFinder_NeedMove)
    MatchFinder_Normalize3                   0x0000697c   ARM Code      44  LzFind.o(i.MatchFinder_Normalize3)
    MatchFinder_ReduceOffsets                0x00006a44   ARM Code      40  LzFind.o(i.MatchFinder_ReduceOffsets)
    MyAlloc                                  0x00006b00   ARM Code      12  LzmaLib.o(i.MyAlloc)
    free                                     0x00007370   ARM Code       4  tinyalloc.o(i.free)
    malloc                                   0x000073c0   ARM Code       4  tinyalloc.o(i.malloc)
    malloc_init                              0x000073c4   ARM Code       4  tinyalloc.o(i.malloc_init)
    ta_alloc                                 0x000073c8   ARM Code      52  tinyalloc.o(i.ta_alloc)
    ta_free                                  0x00007428   ARM Code      88  tinyalloc.o(i.ta_free)
    ta_init                                  0x00007484   ARM Code      92  tinyalloc.o(i.ta_init)
    tiny_free                                0x000074e4   ARM Code       4  tinyalloc.o(i.tiny_free)
    tiny_malloc                              0x000074e8   ARM Code       4  tinyalloc.o(i.tiny_malloc)
    Image$$DATA$$ZI$$Length                  0x0000a488   Number         0  anon$$obj.o ABSOLUTE
    Image$$CODE$$Length                      0x0000c104   Number         0  anon$$obj.o ABSOLUTE
    Image$$INIT$$Base                        0x06000000   Number         0  anon$$obj.o ABSOLUTE
    Load$$INIT$$Base                         0x06000000   Number         0  anon$$obj.o ABSOLUTE
    updater_header                           0x06000000   Data         768  version_block.o(IMG_HEADER_INFO)
    Reset_Handler                            0x06000300   ARM Code       0  StartUp.o(Init)
    TransferControl                          0x060003c4   ARM Code       0  StartUp.o(Init)
    sctlr_get                                0x060003d4   ARM Code       0  StartUp.o(Init)
    sctlr_set                                0x060003dc   ARM Code       0  StartUp.o(Init)
    mpu_get_region_num                       0x060003e8   ARM Code       0  StartUp.o(Init)
    mpu_set_region_num                       0x060003f0   ARM Code       0  StartUp.o(Init)
    mpu_get_region_base_addr                 0x060003fc   ARM Code       0  StartUp.o(Init)
    mpu_set_region_base_addr                 0x06000404   ARM Code       0  StartUp.o(Init)
    mpu_get_region_size                      0x06000410   ARM Code       0  StartUp.o(Init)
    mpu_set_region_size                      0x06000420   ARM Code       0  StartUp.o(Init)
    mpu_get_region_size_enable               0x06000444   ARM Code       0  StartUp.o(Init)
    mpu_get_region_access_ctrl               0x0600044c   ARM Code       0  StartUp.o(Init)
    mpu_set_region_access_ctrl               0x06000454   ARM Code       0  StartUp.o(Init)
    mpu_enable_region                        0x06000460   ARM Code       0  StartUp.o(Init)
    CPUCleanDCacheLine                       0x0600047c   ARM Code       0  StartUp.o(Init)
    CPUInvalidateDCacheLine                  0x0600048c   ARM Code       0  StartUp.o(Init)
    CORTEX_MPU_Region_Init                   0x0600049c   ARM Code       0  StartUp.o(Init)
    bootloader                               0x060006e0   ARM Code    1024  main.o(i.bootloader)
    get_fbf_start_addr                       0x06000b84   ARM Code     104  main.o(i.get_fbf_start_addr)
    Load$$ITCM$$Base                         0x06000c40   Number         0  anon$$obj.o ABSOLUTE
    Image$$CODE$$Base                        0x06001200   Number         0  anon$$obj.o ABSOLUTE
    _printf_n                                0x06001201   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x06001201   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x06001207   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x0600120d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x06001213   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x06001219   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0600121f   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x06001225   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0600122f   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x06001235   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x0600123b   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x06001241   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x06001247   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x0600124d   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x06001253   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x06001259   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0600125f   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x06001265   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x0600126b   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x06001275   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x0600127b   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x06001281   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x06001287   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x0600128d   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __aeabi_uldivmod                         0x06001290   ARM Code       0  lludiv5.o(.text)
    __rt_lib_init_lc_ctype_2                 0x06001291   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_numeric_2               0x06001291   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    _ll_udiv                                 0x06001290   ARM Code     816  lludiv5.o(.text)
    _ll_udiv_donemoving                      0x060012a4   ARM Code       0  lludiv5.o(.text)
    __aeabi_llsr                             0x060015c1   Thumb Code     0  llushr.o(.text)
    _ll_ushift_r                             0x060015c1   Thumb Code    38  llushr.o(.text)
    __c89vsnprintf                           0x060015e9   Thumb Code    48  c89vsnprintf.o(.text)
    memcmp                                   0x0600161d   Thumb Code    88  memcmp.o(.text)
    strncmp                                  0x06001674   ARM Code     236  strncmp.o(.text)
    __aeabi_memcpy                           0x06001764   ARM Code       0  rt_memcpy.o(.text)
    __rt_memcpy                              0x06001764   ARM Code     212  rt_memcpy.o(.text)
    _memcpy_lastbytes                        0x06001818   ARM Code       0  rt_memcpy.o(.text)
    __aeabi_memmove                          0x06001838   ARM Code       0  rt_memmove.o(.text)
    __rt_memmove                             0x06001838   ARM Code     212  rt_memmove.o(.text)
    __memmove_lastfew                        0x060018ec   ARM Code       0  rt_memmove.o(.text)
    __aeabi_memcpy4                          0x0600190c   ARM Code       0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x0600190c   ARM Code       0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x0600190c   ARM Code     100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x06001958   ARM Code       0  rt_memcpy_w.o(.text)
    Inline ARM to Thumb Veneer to __aeabi_memclr 0x06001970   ARM Code       8  rt_memclr.o(.text)
    __aeabi_memclr                           0x06001979   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x06001979   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x0600197d   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x060019bd   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x060019bd   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x060019bd   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x060019c1   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_uread4                           0x06001a0b   Thumb Code     0  uread4.o(.text)
    __rt_uread4                              0x06001a0b   Thumb Code    22  uread4.o(.text)
    _printf_pre_padding                      0x06001a21   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x06001a4d   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x06001a6f   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x06001a81   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x06001a93   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x06001ae5   Thumb Code    96  _printf_dec.o(.text)
    _printf_charcount                        0x06001b55   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x06001b87   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x06001bad   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x06001bb7   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x06001bc9   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x06001c85   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x06001d01   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x06001d43   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x06001d5b   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x06001d71   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x06001dc7   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x06001de3   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x06001def   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x06001e05   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __aeabi_memmove4                         0x06001f8c   ARM Code       0  rt_memmove_w.o(.text)
    __aeabi_memmove8                         0x06001f8c   ARM Code       0  rt_memmove_w.o(.text)
    __rt_memmove_w                           0x06001f8c   ARM Code     128  rt_memmove_w.o(.text)
    __memmove_aligned                        0x06001fb8   ARM Code       0  rt_memmove_w.o(.text)
    __memmove_lastfew_aligned                0x06001ff4   ARM Code       0  rt_memmove_w.o(.text)
    _ll_udiv10                               0x0600200c   ARM Code     152  lludiv10.o(.text)
    _printf_int_common                       0x060020a5   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x06002157   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x06002307   Thumb Code   606  _printf_fp_dec.o(.text)
    _printf_cs_common                        0x06002565   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x06002579   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x06002589   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x06002591   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x060025a5   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x060025b5   Thumb Code     8  _printf_wchar.o(.text)
    _wcrtomb                                 0x060025bd   Thumb Code    64  _wcrtomb.o(.text)
    __rt_udiv10                              0x060025fc   ARM Code      44  rtudiv10.o(.text)
    __rt_ctype_table                         0x06002629   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x06002639   Thumb Code     8  rt_locale.o(.text)
    _printf_fp_infnan                        0x06002641   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x060026c1   Thumb Code   210  bigflt0.o(.text)
    strcmp                                   0x06002798   ARM Code     240  strcmp.o(.text)
    _btod_d2e                                0x0600288d   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x060028cb   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x06002911   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x06002971   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x06002ca9   Thumb Code   198  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x06002d6f   Thumb Code    40  btod.o(CL$$btod_ediv)
    _btod_emul                               0x06002d97   Thumb Code    40  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x06002dbf   Thumb Code   580  btod.o(CL$$btod_mult_common)
    Long ARM to ARM Veneer to malloc_init    0x06003004   ARM Code       8  anon$$obj.o(Veneer$$Code)
    Long ARM to ARM Veneer to malloc         0x0600300c   ARM Code       8  anon$$obj.o(Veneer$$Code)
    Long ARM to ARM Veneer to free           0x06003014   ARM Code       8  anon$$obj.o(Veneer$$Code)
    Long ARM to ARM Veneer to LzmaUncompress 0x0600301c   ARM Code       8  anon$$obj.o(Veneer$$Code)
    Long ARM to ARM Veneer to LzmaCompress   0x06003024   ARM Code       8  anon$$obj.o(Veneer$$Code)
    CalcImageChecksum                        0x0600302c   ARM Code      32  bspatch.o(i.CalcImageChecksum)
    CheckFMEraseState                        0x0600304c   ARM Code      56  FM.o(i.CheckFMEraseState)
    CheckIfSkip                              0x06003088   ARM Code      60  Flash.o(i.CheckIfSkip)
    CheckReserved                            0x060030c8   ARM Code      60  tim.o(i.CheckReserved)
    ClearFM                                  0x0600310c   ARM Code      96  FM.o(i.ClearFM)
    Configure_Flashes                        0x06003174   ARM Code     236  Flash.o(i.Configure_Flashes)
    Delay                                    0x06003260   ARM Code      56  plat_api.o(i.Delay)
    EraseFlash                               0x06003298   ARM Code     376  Flash.o(i.EraseFlash)
    FOTA_Init_Heap                           0x06003448   ARM Code      12  bspatch.o(i.FOTA_Init_Heap)
    FindPackageInReserved                    0x06003454   ARM Code     124  tim.o(i.FindPackageInReserved)
    GetABBTState                             0x060034d4   ARM Code      44  FM_ext.o(i.GetABBTState)
    GetBlockSize                             0x06003500   ARM Code      16  Flash.o(i.GetBlockSize)
    GetFMProperties                          0x06003510   ARM Code       8  FM.o(i.GetFMProperties)
    GetFlashProperties                       0x0600351c   ARM Code      32  Flash.o(i.GetFlashProperties)
    GetFlashType                             0x06003540   ARM Code      76  Flash.o(i.GetFlashType)
    GetInitRoutine                           0x060035c0   ARM Code      44  Flash.o(i.GetInitRoutine)
    GetOSCR0                                 0x060035f4   ARM Code      32  plat_api.o(i.GetOSCR0)
    GetPMICID                                0x06003618   ARM Code      80  pmic.o(i.GetPMICID)
    GetPageSize                              0x06003680   ARM Code      16  Flash.o(i.GetPageSize)
    GetPartitionOffset                       0x06003690   ARM Code      12  FM.o(i.GetPartitionOffset)
    GetTimPointer                            0x060036a0   ARM Code       8  tim.o(i.GetTimPointer)
    GetUseSpareArea                          0x060036ac   ARM Code      20  Flash.o(i.GetUseSpareArea)
    GuilinClkInit                            0x060036c0   ARM Code     344  guilin.o(i.GuilinClkInit)
    GuilinLiteClkInit                        0x06003818   ARM Code      56  guilin_lite.o(i.GuilinLiteClkInit)
    GuilinLiteRead                           0x06003850   ARM Code      56  guilin_lite.o(i.GuilinLiteRead)
    GuilinLiteWrite                          0x06003888   ARM Code      56  guilin_lite.o(i.GuilinLiteWrite)
    GuilinLite_VBUCK1_CFG                    0x060038c0   ARM Code      24  guilin_lite.o(i.GuilinLite_VBUCK1_CFG)
    GuilinLite_VBUCK_Set_VOUT                0x060038d8   ARM Code     104  guilin_lite.o(i.GuilinLite_VBUCK_Set_VOUT)
    GuilinRead                               0x06003940   ARM Code      76  guilin.o(i.GuilinRead)
    GuilinWrite                              0x0600398c   ARM Code      80  guilin.o(i.GuilinWrite)
    Guilin_VBUCK1_CFG                        0x060039dc   ARM Code      32  guilin.o(i.Guilin_VBUCK1_CFG)
    Guilin_VBUCK_Set_VOUT                    0x06003a18   ARM Code     176  guilin.o(i.Guilin_VBUCK_Set_VOUT)
    I2CReceive                               0x06003ac8   ARM Code       4  pmic.o(i.I2CReceive)
    I2CSend                                  0x06003acc   ARM Code       4  pmic.o(i.I2CSend)
    InitSODTimer                             0x06003ad0   ARM Code      56  plat_api.o(i.InitSODTimer)
    InitializeQSPIDevice                     0x06003b10   ARM Code     296  spi_nor.o(i.InitializeQSPIDevice)
    InitializeQSPINAND                       0x06003c8c   ARM Code     304  spi_nand.o(i.InitializeQSPINAND)
    IsUpdaterSupportQueryFotaInfo            0x06003e18   ARM Code      28  updater_table.o(i.IsUpdaterSupportQueryFotaInfo)
    LocateBlock                              0x06003e38   ARM Code      56  FM.o(i.LocateBlock)
    OTA_Check_FotaImage                      0x0600411c   ARM Code     148  bspatch.o(i.OTA_Check_FotaImage)
    OTA_Get_Fota_Progress                    0x060041e4   ARM Code      36  bspatch.o(i.OTA_Get_Fota_Progress)
    OTA_Update_FotaImage                     0x0600420c   ARM Code    1128  bspatch.o(i.OTA_Update_FotaImage)
    OTA_Update_FotaParam                     0x060046f4   ARM Code     252  bspatch.o(i.OTA_Update_FotaParam)
    PM812_VBUCK1_CFG                         0x06004a64   ARM Code      32  pmic.o(i.PM812_VBUCK1_CFG)
    PMIC_FAULT_WU_ENABLE                     0x06004aa0   ARM Code      80  pmic.o(i.PMIC_FAULT_WU_ENABLE)
    PMIC_FAULT_WU_EN_ENABLE                  0x06004af4   ARM Code      80  pmic.o(i.PMIC_FAULT_WU_EN_ENABLE)
    PMIC_ID_GET                              0x06004b48   ARM Code      40  pmic.o(i.PMIC_ID_GET)
    PMIC_IS_PM802                            0x06004b74   ARM Code      24  pmic.o(i.PMIC_IS_PM802)
    PMIC_IS_PM802S                           0x06004b90   ARM Code      20  pmic.o(i.PMIC_IS_PM802S)
    PMIC_IS_PM803                            0x06004ba4   ARM Code      24  pmic.o(i.PMIC_IS_PM803)
    PMIC_Init                                0x06004bbc   ARM Code      88  pmic.o(i.PMIC_Init)
    PMIC_READ_REG_BASE                       0x06004c50   ARM Code      12  pmic.o(i.PMIC_READ_REG_BASE)
    PMIC_SW_RESET                            0x06004c5c   ARM Code     128  pmic.o(i.PMIC_SW_RESET)
    PMIC_WRITE_REG_BASE                      0x06004d00   ARM Code      16  pmic.o(i.PMIC_WRITE_REG_BASE)
    PMIC_WRITE_REG_POWER                     0x06004d10   ARM Code      16  pmic.o(i.PMIC_WRITE_REG_POWER)
    PP_Switch                                0x06004d20   ARM Code     244  FreqChange.o(i.PP_Switch)
    PlatformVcoreConfigLow                   0x06004e80   ARM Code      72  pmic.o(i.PlatformVcoreConfigLow)
    ReadFlash                                0x06004ec8   ARM Code     328  Flash.o(i.ReadFlash)
    RelocateBlock                            0x06005010   ARM Code      88  FM.o(i.RelocateBlock)
    RelocateBlock_LegacyExt                  0x0600506c   ARM Code       8  FM_ext.o(i.RelocateBlock_LegacyExt)
    ResetFlash                               0x06005074   ARM Code      40  Flash.o(i.ResetFlash)
    ScrubBlock_LegacyExt                     0x0600509c   ARM Code     360  FM_ext.o(i.ScrubBlock_LegacyExt)
    SetABBTState                             0x06005224   ARM Code     104  FM_ext.o(i.SetABBTState)
    SetBBTState                              0x0600528c   ARM Code     108  FM.o(i.SetBBTState)
    SetTIMPointers                           0x060052fc   ARM Code     140  tim.o(i.SetTIMPointers)
    System_Adiff_Backup                      0x0600538c   ARM Code    1100  bspatch.o(i.System_Adiff_Backup)
    System_Adiff_Check_Dfota_File            0x06005840   ARM Code     212  bspatch.o(i.System_Adiff_Check_Dfota_File)
    System_Adiff_Check_Old_Image             0x060059a0   ARM Code     104  bspatch.o(i.System_Adiff_Check_Old_Image)
    System_Adiff_Move_Old_Backup             0x06005a08   ARM Code     332  bspatch.o(i.System_Adiff_Move_Old_Backup)
    System_Adiff_Power_Off_Protection        0x06005c2c   ARM Code     228  bspatch.o(i.System_Adiff_Power_Off_Protection)
    System_Adiff_Replace_Image               0x06005d68   ARM Code    2488  bspatch.o(i.System_Adiff_Replace_Image)
    System_Adiff_Update_Image                0x0600675c   ARM Code      96  bspatch.o(i.System_Adiff_Update_Image)
    System_DFOTA_Upgrade_Adiff               0x060067e0   ARM Code     656  bspatch.o(i.System_DFOTA_Upgrade_Adiff)
    System_FOTA_Upgrade_Adiff                0x06006a70   ARM Code     744  bspatch.o(i.System_FOTA_Upgrade_Adiff)
    USTICAI2CReadDi_AUDIO                    0x06006d58   ARM Code      12  pmic.o(i.USTICAI2CReadDi_AUDIO)
    USTICAI2CReadDi_GPADC                    0x06006d64   ARM Code      12  pmic.o(i.USTICAI2CReadDi_GPADC)
    USTICAI2CReadDi_TEST                     0x06006d70   ARM Code      12  pmic.o(i.USTICAI2CReadDi_TEST)
    USTICAI2CReadDi_base                     0x06006d7c   ARM Code      12  pmic.o(i.USTICAI2CReadDi_base)
    USTICAI2CReadDi_power                    0x06006d88   ARM Code      12  pmic.o(i.USTICAI2CReadDi_power)
    USTICAI2CWriteDi_AUDIO                   0x06006d94   ARM Code      16  pmic.o(i.USTICAI2CWriteDi_AUDIO)
    USTICAI2CWriteDi_GPADC                   0x06006da4   ARM Code      16  pmic.o(i.USTICAI2CWriteDi_GPADC)
    USTICAI2CWriteDi_TEST                    0x06006db4   ARM Code      16  pmic.o(i.USTICAI2CWriteDi_TEST)
    USTICAI2CWriteDi_base                    0x06006dc4   ARM Code      16  pmic.o(i.USTICAI2CWriteDi_base)
    USTICAI2CWriteDi_power                   0x06006dd4   ARM Code      16  pmic.o(i.USTICAI2CWriteDi_power)
    Voltage_set_main                         0x06006de4   ARM Code      32  pmic.o(i.Voltage_set_main)
    WriteFlash                               0x06006e04   ARM Code     512  Flash.o(i.WriteFlash)
    __ARM_fpclassify                         0x0600703d   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    Load$$CODE$$Base                         0x06007210   Number         0  anon$$obj.o ABSOLUTE
    _is_digit                                0x06007359   Thumb Code    14  __printf_wp.o(i._is_digit)
    aclk_dfc_switch                          0x06007368   ARM Code      84  FreqChange.o(i.aclk_dfc_switch)
    ap_uart_putc                             0x060073e0   ARM Code      28  uart.o(i.ap_uart_putc)
    bzpacth                                  0x06007400   ARM Code     412  bspatch.o(i.bzpacth)
    check_if_DCS_mode                        0x060075d4   ARM Code      68  utilities.o(i.check_if_DCS_mode)
    check_old_image_checksum                 0x06007640   ARM Code     172  bspatch.o(i.check_old_image_checksum)
    cp_uart_init                             0x060076f0   ARM Code     132  uart.o(i.cp_uart_init)
    datamodule_fota                          0x06007784   ARM Code    1416  bspatch.o(i.datamodule_fota)
    dck_dfc_switch                           0x06007d40   ARM Code     128  FreqChange.o(i.dck_dfc_switch)
    enable_PLL                               0x06007dec   ARM Code     112  FreqChange.o(i.enable_PLL)
    get_updater_function_magic_type          0x0600817c   ARM Code      12  updater_table.o(i.get_updater_function_magic_type)
    mdelay                                   0x060081d4   ARM Code      56  pmic.o(i.mdelay)
    mpu_value_check                          0x060082ac   ARM Code     248  system.o(i.mpu_value_check)
    qspi_cmd_done_interrupt                  0x06008650   ARM Code      92  qspi_host.o(i.qspi_cmd_done_interrupt)
    qspi_cmd_done_pio                        0x060086b8   ARM Code     204  qspi_host.o(i.qspi_cmd_done_pio)
    qspi_enable_xip                          0x06008a78   ARM Code     100  qspi_host.o(i.qspi_enable_xip)
    qspi_host_init                           0x06008b38   ARM Code     268  qspi_host.o(i.qspi_host_init)
    qspi_init_ahb                            0x06008c80   ARM Code     140  qspi_host.o(i.qspi_init_ahb)
    qspi_preinit_lookup_tbl                  0x06008ed4   ARM Code     132  qspi_host.o(i.qspi_preinit_lookup_tbl)
    qspi_set_func_clk                        0x0600908c   ARM Code     140  qspi_host.o(i.qspi_set_func_clk)
    qspi_start_cmd                           0x0600925c   ARM Code     692  qspi_host.o(i.qspi_start_cmd)
    spi_nand_erase                           0x0600a008   ARM Code     300  spi_nand.o(i.spi_nand_erase)
    spi_nand_init                            0x0600a354   ARM Code     912  spi_nand.o(i.spi_nand_init)
    spi_nand_lock_block                      0x0600a6e4   ARM Code      20  spi_nand.o(i.spi_nand_lock_block)
    spi_nor_erase                            0x0600af88   ARM Code     432  spi_nor.o(i.spi_nor_erase)
    spi_nor_init                             0x0600b254   ARM Code     552  spi_nor.o(i.spi_nor_init)
    uart_printf                              0x0600be70   ARM Code      96  uart.o(i.uart_printf)
    updater_get_fota_solution                0x0600bed4   ARM Code      60  updater_table.o(i.updater_get_fota_solution)
    updater_get_lcd_info                     0x0600bfa0   ARM Code      60  updater_table.o(i.updater_get_lcd_info)
    updater_get_version_info                 0x0600c064   ARM Code      60  updater_table.o(i.updater_get_version_info)
    updater_header_table_init                0x0600c12c   ARM Code     200  updater_table.o(i.updater_header_table_init)
    ustica_I2CConfigureDi                    0x0600c284   ARM Code     116  pmic.o(i.ustica_I2CConfigureDi)
    ustica_I2CEnableclockandPin              0x0600c300   ARM Code      20  pmic.o(i.ustica_I2CEnableclockandPin)
    ustica_I2CMasterReceiveDataDirect        0x0600c3d4   ARM Code     232  pmic.o(i.ustica_I2CMasterReceiveDataDirect)
    ustica_I2CMasterSendDataDirect           0x0600c4c4   ARM Code      52  pmic.o(i.ustica_I2CMasterSendDataDirect)
    uudelay                                  0x0600c744   ARM Code      36  pmic.o(i.uudelay)
    _get_lc_numeric                          0x0600c8e5   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x0600c911   Thumb Code    44  lc_ctype_c.o(locale$$code)
    _printf_fp_dec                           0x0600c93d   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x0600c940   Number         0  usenofp.o(x$fpl$usenofp)
    __aeabi_idiv                             0x0600c941   Thumb Code     6  aeabi_sdiv_cr4.o(x$sdiv)
    __aeabi_idivmod                          0x0600c947   Thumb Code    12  aeabi_sdiv_cr4.o(x$sdivmod)
    Inline ARM to Thumb Veneer to __aeabi_uidiv 0x0600c954   ARM Code       8  aeabi_sdiv_cr4.o(x$udiv)
    __aeabi_uidiv                            0x0600c95d   Thumb Code     6  aeabi_sdiv_cr4.o(x$udiv)
    __aeabi_uidivmod                         0x0600c963   Thumb Code    12  aeabi_sdiv_cr4.o(x$udivmod)
    __ctype                                  0x0600d201   Data           0  lc_ctype_c.o(locale$$data)
    Load$$DATA$$RW$$Base                     0x06013314   Number         0  anon$$obj.o ABSOLUTE
    Load$$DTCM$$RW$$Base                     0x060141b4   Number         0  anon$$obj.o ABSOLUTE
    Image$$DATA$$RW$$Base                    0x06051200   Number         0  anon$$obj.o ABSOLUTE
    pmic_id                                  0x06051204   Data           1  pmic.o(.data)
    SysRestartReasonGlobal                   0x06051206   Data           1  pmic.o(.data)
    I2C_REGISTER_BASE_ADDR                   0x06051208   Data           4  pmic.o(.data)
    SysRestartReasonGetFlag                  0x0605120c   Data           4  pmic.o(.data)
    flash_type                               0x06051214   Data           1  Flash.o(.data)
    pSkipAddress                             0x06051218   Data           4  Flash.o(.data)
    FM_SPACE                                 0x06052064   Data           4  FM.o(.data)
    OBM_PartitionOffset                      0x06052068   Data           4  FM.o(.data)
    pFM_SPACE                                0x0605206c   Data           4  FM.o(.data)
    TA_HEAP_START                            0x06052078   Data           4  tinyalloc.o(.data)
    TA_HEAP_LIMIT                            0x0605207c   Data           4  tinyalloc.o(.data)
    OBM_SET_UPDATER_FLASH_ADDR               0x0605209c   Data           4  bspatch.o(.data)
    I2CStatusArray                           0x060520a0   Data          64  pmic.o(.bss)
    Image$$DATA$$ZI$$Base                    0x060520a0   Number         0  anon$$obj.o ABSOLUTE
    FlashProp                                0x060520f0   Data         160  Flash.o(.bss)
    FMProperties                             0x060524a8   Data          84  FM.o(.bss)
    Image$$DTCM$$RW$$Base                    0xb0021c00   Number         0  anon$$obj.o ABSOLUTE
    Image$$DTCM$$ZI$$Base                    0xb0021c00   Number         0  anon$$obj.o ABSOLUTE



==============================================================================

Memory Map of the image

  Image Entry point : 0x06000300

  Load Region LOAD (Base: 0x06000000, Size: 0x000141b4, Max: 0xffffffff, ABSOLUTE)

    Execution Region ITCM (Base: 0x00001000, Size: 0x000065d0, Max: 0x0000f000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00001000   0x00000008   Ven    RO         3819    Veneer$$Code        anon$$obj.o
    0x00001008   0x00000008   Ven    RO         3820    Veneer$$Code        anon$$obj.o
    0x00001010   0x00000008   Ven    RO         3821    Veneer$$Code        anon$$obj.o
    0x00001018   0x00000008   Ven    RO         3822    Veneer$$Code        anon$$obj.o
    0x00001020   0x00000008   Ven    RO         3823    Veneer$$Code        anon$$obj.o
    0x00001028   0x00000008   Ven    RO         3824    Veneer$$Code        anon$$obj.o
    0x00001030   0x00000008   Ven    RO         3825    Veneer$$Code        anon$$obj.o
    0x00001038   0x000000ac   Code   RO         1814    i.Backward          LzmaEnc.o
    0x000010e4   0x000000c4   Code   RO         2148    i.Bt2_MatchFinder_GetMatches  LzFind.o
    0x000011a8   0x000000a4   Code   RO         2149    i.Bt2_MatchFinder_Skip  LzFind.o
    0x0000124c   0x000001b8   Code   RO         2152    i.Bt3_MatchFinder_GetMatches  LzFind.o
    0x00001404   0x000000dc   Code   RO         2153    i.Bt3_MatchFinder_Skip  LzFind.o
    0x000014e0   0x00000214   Code   RO         2154    i.Bt4_MatchFinder_GetMatches  LzFind.o
    0x000016f4   0x0000010c   Code   RO         2155    i.Bt4_MatchFinder_Skip  LzFind.o
    0x00001800   0x0000004c   Code   RO         1815    i.CheckErrors       LzmaEnc.o
    0x0000184c   0x00000058   Code   RO         1816    i.FillAlignPrices   LzmaEnc.o
    0x000018a4   0x00000158   Code   RO         1817    i.FillDistancesPrices  LzmaEnc.o
    0x000019fc   0x0000006c   Code   RO         1818    i.Flush             LzmaEnc.o
    0x00001a68   0x00000134   Code   RO         2156    i.GetMatchesSpec1   LzFind.o
    0x00001b9c   0x00001228   Code   RO         1819    i.GetOptimum        LzmaEnc.o
    0x00002dc4   0x000002f4   Code   RO         1820    i.GetOptimumFast    LzmaEnc.o
    0x000030b8   0x000000b8   Code   RO         1821    i.GetPureRepPrice   LzmaEnc.o
    0x00003170   0x0000004c   Code   RO         1822    i.GetRepLen1Price   LzmaEnc.o
    0x000031bc   0x0000003c   Code   RO         1823    i.GetRepPrice       LzmaEnc.o
    0x000031f8   0x000001f4   Code   RO         2159    i.Hc4_MatchFinder_GetMatches  LzFind.o
    0x000033ec   0x000000e4   Code   RO         2160    i.Hc4_MatchFinder_Skip  LzFind.o
    0x000034d0   0x000000b8   Code   RO         2161    i.Hc_GetMatchesSpec  LzFind.o
    0x00003588   0x000000d4   Code   RO         1824    i.LenEnc_Encode2    LzmaEnc.o
    0x0000365c   0x00000060   Code   RO         1825    i.LenEnc_Init       LzmaEnc.o
    0x000036bc   0x000000ec   Code   RO         1826    i.LenEnc_SetPrices  LzmaEnc.o
    0x000037a8   0x00000048   Code   RO         1827    i.LenPriceEnc_UpdateTable  LzmaEnc.o
    0x000037f0   0x00000038   Code   RO         1828    i.LenPriceEnc_UpdateTables  LzmaEnc.o
    0x00003828   0x00000034   Code   RO         1829    i.LitEnc_Encode     LzmaEnc.o
    0x0000385c   0x00000048   Code   RO         1830    i.LitEnc_GetPrice   LzmaEnc.o
    0x000038a4   0x00000060   Code   RO         1831    i.LitEnc_GetPriceMatched  LzmaEnc.o
    0x00003904   0x00000034   Code   RO         2162    i.LzInWindow_Free   LzFind.o
    0x00003938   0x00000084   Code   RO         2341    i.LzmaCompress      LzmaLib.o
    0x000039bc   0x00000064   Code   RO         2342    i.LzmaCompress_internal  LzmaLib.o
    0x00003a20   0x00000044   Code   RO         1700    i.LzmaDec_AllocateProbs  LzmaDec.o
    0x00003a64   0x00000078   Code   RO         1701    i.LzmaDec_AllocateProbs2  LzmaDec.o
    0x00003adc   0x00000c0c   Code   RO         1702    i.LzmaDec_DecodeReal  LzmaDec.o
    0x000046e8   0x000000a0   Code   RO         1703    i.LzmaDec_DecodeReal2  LzmaDec.o
    0x00004788   0x0000031c   Code   RO         1705    i.LzmaDec_DecodeToDic  LzmaDec.o
    0x00004aa4   0x00000028   Code   RO         1708    i.LzmaDec_FreeProbs  LzmaDec.o
    0x00004acc   0x00000014   Code   RO         1709    i.LzmaDec_Init      LzmaDec.o
    0x00004ae0   0x00000034   Code   RO         1710    i.LzmaDec_InitDicAndState  LzmaDec.o
    0x00004b14   0x00000550   Code   RO         1711    i.LzmaDec_TryDummy  LzmaDec.o
    0x00005064   0x0000009c   Code   RO         1712    i.LzmaDec_WriteRem  LzmaDec.o
    0x00005100   0x000000c0   Code   RO         1713    i.LzmaDecode        LzmaDec.o
    0x000051c0   0x0000003c   Code   RO         1833    i.LzmaEncProps_Init  LzmaEnc.o
    0x000051fc   0x0000011c   Code   RO         1834    i.LzmaEncProps_Normalize  LzmaEnc.o
    0x00005318   0x00000158   Code   RO         1835    i.LzmaEnc_Alloc     LzmaEnc.o
    0x00005470   0x0000007c   Code   RO         1836    i.LzmaEnc_AllocAndInit  LzmaEnc.o
    0x000054ec   0x000006cc   Code   RO         1837    i.LzmaEnc_CodeOneBlock  LzmaEnc.o
    0x00005bb8   0x00000074   Code   RO         1839    i.LzmaEnc_Construct  LzmaEnc.o
    0x00005c2c   0x00000024   Code   RO         1840    i.LzmaEnc_Create    LzmaEnc.o
    0x00005c50   0x00000024   Code   RO         1841    i.LzmaEnc_Destroy   LzmaEnc.o
    0x00005c74   0x00000048   Code   RO         1842    i.LzmaEnc_Destruct  LzmaEnc.o
    0x00005cbc   0x000000e0   Code   RO         1843    i.LzmaEnc_Encode    LzmaEnc.o
    0x00005d9c   0x00000054   Code   RO         1844    i.LzmaEnc_FastPosInit  LzmaEnc.o
    0x00005df0   0x00000044   Code   RO         1846    i.LzmaEnc_FreeLits  LzmaEnc.o
    0x00005e34   0x00000194   Code   RO         1849    i.LzmaEnc_Init      LzmaEnc.o
    0x00005fc8   0x00000054   Code   RO         1850    i.LzmaEnc_InitPriceTables  LzmaEnc.o
    0x0000601c   0x0000008c   Code   RO         1851    i.LzmaEnc_InitPrices  LzmaEnc.o
    0x000060a8   0x00000090   Code   RO         1852    i.LzmaEnc_MemEncode  LzmaEnc.o
    0x00006138   0x00000108   Code   RO         1857    i.LzmaEnc_SetProps  LzmaEnc.o
    0x00006240   0x000000ac   Code   RO         1858    i.LzmaEnc_WriteProperties  LzmaEnc.o
    0x000062ec   0x0000009c   Code   RO         1859    i.LzmaEncode        LzmaEnc.o
    0x00006388   0x00000088   Code   RO         1714    i.LzmaProps_Decode  LzmaDec.o
    0x00006410   0x0000007c   Code   RO         2343    i.LzmaUncompress    LzmaLib.o
    0x0000648c   0x000000a0   Code   RO         2163    i.MatchFinder_CheckLimits  LzFind.o
    0x0000652c   0x00000078   Code   RO         2164    i.MatchFinder_Construct  LzFind.o
    0x000065a4   0x000001d4   Code   RO         2165    i.MatchFinder_Create  LzFind.o
    0x00006778   0x000000a0   Code   RO         2166    i.MatchFinder_CreateVTable  LzFind.o
    0x00006818   0x00000020   Code   RO         2167    i.MatchFinder_Free  LzFind.o
    0x00006838   0x00000028   Code   RO         2168    i.MatchFinder_FreeThisClassMemory  LzFind.o
    0x00006860   0x0000000c   Code   RO         2169    i.MatchFinder_GetIndexByte  LzFind.o
    0x0000686c   0x00000010   Code   RO         2170    i.MatchFinder_GetNumAvailableBytes  LzFind.o
    0x0000687c   0x00000008   Code   RO         2171    i.MatchFinder_GetPointerToCurrentPos  LzFind.o
    0x00006884   0x0000005c   Code   RO         2172    i.MatchFinder_Init  LzFind.o
    0x000068e0   0x00000040   Code   RO         2173    i.MatchFinder_MoveBlock  LzFind.o
    0x00006920   0x00000034   Code   RO         2174    i.MatchFinder_MovePos  LzFind.o
    0x00006954   0x00000028   Code   RO         2175    i.MatchFinder_NeedMove  LzFind.o
    0x0000697c   0x0000002c   Code   RO         2176    i.MatchFinder_Normalize3  LzFind.o
    0x000069a8   0x0000009c   Code   RO         2177    i.MatchFinder_ReadBlock  LzFind.o
    0x00006a44   0x00000028   Code   RO         2179    i.MatchFinder_ReduceOffsets  LzFind.o
    0x00006a6c   0x0000006c   Code   RO         2180    i.MatchFinder_SetLimits  LzFind.o
    0x00006ad8   0x00000028   Code   RO         1860    i.MovePos           LzmaEnc.o
    0x00006b00   0x0000000c   Code   RO         2344    i.MyAlloc           LzmaLib.o
    0x00006b0c   0x00000054   Code   RO         1861    i.MyRead            LzmaEnc.o
    0x00006b60   0x0000004c   Code   RO         1862    i.MyWrite           LzmaEnc.o
    0x00006bac   0x00000070   Code   RO         1863    i.RangeEnc_EncodeBit  LzmaEnc.o
    0x00006c1c   0x0000006c   Code   RO         1864    i.RangeEnc_EncodeDirectBits  LzmaEnc.o
    0x00006c88   0x00000058   Code   RO         1865    i.RangeEnc_FlushStream  LzmaEnc.o
    0x00006ce0   0x00000048   Code   RO         1866    i.RangeEnc_Init     LzmaEnc.o
    0x00006d28   0x00000098   Code   RO         1867    i.RangeEnc_ShiftLow  LzmaEnc.o
    0x00006dc0   0x00000048   Code   RO         1868    i.RcTree_Encode     LzmaEnc.o
    0x00006e08   0x0000004c   Code   RO         1869    i.RcTree_GetPrice   LzmaEnc.o
    0x00006e54   0x0000004c   Code   RO         1870    i.RcTree_ReverseEncode  LzmaEnc.o
    0x00006ea0   0x00000050   Code   RO         1871    i.RcTree_ReverseGetPrice  LzmaEnc.o
    0x00006ef0   0x000000b8   Code   RO         1872    i.ReadMatchDistances  LzmaEnc.o
    0x00006fa8   0x000000e4   Code   RO         2181    i.SkipMatchesSpec   LzFind.o
    0x0000708c   0x00000008   Code   RO         2346    i.SzAlloc           LzmaLib.o
    0x00007094   0x00000008   Code   RO         2347    i.SzFree            LzmaLib.o
    0x0000709c   0x000000ec   Code   RO         1873    i.WriteEndMarker    LzmaEnc.o
    0x00007188   0x00000134   Code   RO         2406    i.alloc_block       tinyalloc.o
    0x000072bc   0x000000b4   Code   RO         2407    i.compact           tinyalloc.o
    0x00007370   0x00000004   Code   RO         2409    i.free              tinyalloc.o
    0x00007374   0x0000004c   Code   RO         2410    i.insert_block      tinyalloc.o
    0x000073c0   0x00000004   Code   RO         2411    i.malloc            tinyalloc.o
    0x000073c4   0x00000004   Code   RO         2412    i.malloc_init       tinyalloc.o
    0x000073c8   0x00000060   Code   RO         2413    i.ta_alloc          tinyalloc.o
    0x00007428   0x0000005c   Code   RO         2416    i.ta_free           tinyalloc.o
    0x00007484   0x00000060   Code   RO         2417    i.ta_init           tinyalloc.o
    0x000074e4   0x00000004   Code   RO         2421    i.tiny_free         tinyalloc.o
    0x000074e8   0x00000004   Code   RO         2422    i.tiny_malloc       tinyalloc.o
    0x000074ec   0x00000018   Data   RO         1715    .constdata          LzmaDec.o
    0x00007504   0x000000c0   Data   RO         1874    .constdata          LzmaEnc.o
    0x000075c4   0x00000009   Data   RO         2423    .constdata          tinyalloc.o


    Execution Region INIT (Base: 0x06000000, Size: 0x00000c40, Max: 0x00001200, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x06000000   0x00000300   Data   RO         3420    IMG_HEADER_INFO     version_block.o
    0x06000300   0x000003e0   Code   RO         3270  * Init                StartUp.o
    0x060006e0   0x000004a4   Code   RO         3434    i.bootloader        main.o
    0x06000b84   0x000000bc   Code   RO         3435    i.get_fbf_start_addr  main.o


    Execution Region CODE (Base: 0x06001200, Size: 0x0000c104, Max: 0x00050000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x06001200   0x00000000   Code   RO         3633    .ARM.Collect$$_printf_percent$$00000000  c_2.l(_printf_percent.o)
    0x06001200   0x00000006   Code   RO         3622    .ARM.Collect$$_printf_percent$$00000001  c_2.l(_printf_n.o)
    0x06001206   0x00000006   Code   RO         3624    .ARM.Collect$$_printf_percent$$00000002  c_2.l(_printf_p.o)
    0x0600120c   0x00000006   Code   RO         3629    .ARM.Collect$$_printf_percent$$00000003  c_2.l(_printf_f.o)
    0x06001212   0x00000006   Code   RO         3630    .ARM.Collect$$_printf_percent$$00000004  c_2.l(_printf_e.o)
    0x06001218   0x00000006   Code   RO         3631    .ARM.Collect$$_printf_percent$$00000005  c_2.l(_printf_g.o)
    0x0600121e   0x00000006   Code   RO         3632    .ARM.Collect$$_printf_percent$$00000006  c_2.l(_printf_a.o)
    0x06001224   0x0000000a   Code   RO         3637    .ARM.Collect$$_printf_percent$$00000007  c_2.l(_printf_ll.o)
    0x0600122e   0x00000006   Code   RO         3626    .ARM.Collect$$_printf_percent$$00000008  c_2.l(_printf_i.o)
    0x06001234   0x00000006   Code   RO         3627    .ARM.Collect$$_printf_percent$$00000009  c_2.l(_printf_d.o)
    0x0600123a   0x00000006   Code   RO         3628    .ARM.Collect$$_printf_percent$$0000000A  c_2.l(_printf_u.o)
    0x06001240   0x00000006   Code   RO         3625    .ARM.Collect$$_printf_percent$$0000000B  c_2.l(_printf_o.o)
    0x06001246   0x00000006   Code   RO         3623    .ARM.Collect$$_printf_percent$$0000000C  c_2.l(_printf_x.o)
    0x0600124c   0x00000006   Code   RO         3634    .ARM.Collect$$_printf_percent$$0000000D  c_2.l(_printf_lli.o)
    0x06001252   0x00000006   Code   RO         3635    .ARM.Collect$$_printf_percent$$0000000E  c_2.l(_printf_lld.o)
    0x06001258   0x00000006   Code   RO         3636    .ARM.Collect$$_printf_percent$$0000000F  c_2.l(_printf_llu.o)
    0x0600125e   0x00000006   Code   RO         3641    .ARM.Collect$$_printf_percent$$00000010  c_2.l(_printf_llo.o)
    0x06001264   0x00000006   Code   RO         3642    .ARM.Collect$$_printf_percent$$00000011  c_2.l(_printf_llx.o)
    0x0600126a   0x0000000a   Code   RO         3638    .ARM.Collect$$_printf_percent$$00000012  c_2.l(_printf_l.o)
    0x06001274   0x00000006   Code   RO         3620    .ARM.Collect$$_printf_percent$$00000013  c_2.l(_printf_c.o)
    0x0600127a   0x00000006   Code   RO         3621    .ARM.Collect$$_printf_percent$$00000014  c_2.l(_printf_s.o)
    0x06001280   0x00000006   Code   RO         3639    .ARM.Collect$$_printf_percent$$00000015  c_2.l(_printf_lc.o)
    0x06001286   0x00000006   Code   RO         3640    .ARM.Collect$$_printf_percent$$00000016  c_2.l(_printf_ls.o)
    0x0600128c   0x00000004   Code   RO         3661    .ARM.Collect$$_printf_percent$$00000017  c_2.l(_printf_percent_end.o)
    0x06001290   0x00000000   Code   RO         3741    .ARM.Collect$$libinit$$00000013  c_2.l(libinit2.o)
    0x06001290   0x00000000   Code   RO         3745    .ARM.Collect$$libinit$$00000017  c_2.l(libinit2.o)
    0x06001290   0x00000330   Code   RO         3470    .text               c_2.l(lludiv5.o)
    0x060015c0   0x00000026   Code   RO         3472    .text               c_2.l(llushr.o)
    0x060015e6   0x00000002   PAD
    0x060015e8   0x00000034   Code   RO         3474    .text               c_2.l(c89vsnprintf.o)
    0x0600161c   0x00000058   Code   RO         3476    .text               c_2.l(memcmp.o)
    0x06001674   0x000000f0   Code   RO         3480    .text               c_2.l(strncmp.o)
    0x06001764   0x000000d4   Code   RO         3482    .text               c_2.l(rt_memcpy.o)
    0x06001838   0x000000d4   Code   RO         3484    .text               c_2.l(rt_memmove.o)
    0x0600190c   0x00000064   Code   RO         3486    .text               c_2.l(rt_memcpy_w.o)
    0x06001970   0x00000008   Ven    RO         3490    .text               c_2.l(rt_memclr.o)
    0x06001978   0x00000044   Code   RO         3490    .text               c_2.l(rt_memclr.o)
    0x060019bc   0x0000004e   Code   RO         3492    .text               c_2.l(rt_memclr_w.o)
    0x06001a0a   0x00000016   Code   RO         3496    .text               c_2.l(uread4.o)
    0x06001a20   0x0000004e   Code   RO         3552    .text               c_2.l(_printf_pad.o)
    0x06001a6e   0x00000024   Code   RO         3554    .text               c_2.l(_printf_truncate.o)
    0x06001a92   0x00000052   Code   RO         3556    .text               c_2.l(_printf_str.o)
    0x06001ae4   0x00000070   Code   RO         3558    .text               c_2.l(_printf_dec.o)
    0x06001b54   0x00000028   Code   RO         3560    .text               c_2.l(_printf_charcount.o)
    0x06001b7c   0x00000030   Code   RO         3562    .text               c_2.l(_printf_char_common.o)
    0x06001bac   0x0000000a   Code   RO         3564    .text               c_2.l(_sputc.o)
    0x06001bb6   0x00000010   Code   RO         3566    .text               c_2.l(_snputc.o)
    0x06001bc6   0x00000002   PAD
    0x06001bc8   0x000000bc   Code   RO         3568    .text               c_2.l(_printf_wctomb.o)
    0x06001c84   0x0000007c   Code   RO         3571    .text               c_2.l(_printf_longlong_dec.o)
    0x06001d00   0x00000070   Code   RO         3577    .text               c_2.l(_printf_oct_int_ll.o)
    0x06001d70   0x00000094   Code   RO         3597    .text               c_2.l(_printf_hex_int_ll_ptr.o)
    0x06001e04   0x00000188   Code   RO         3617    .text               c_2.l(__printf_flags_ss_wp.o)
    0x06001f8c   0x00000080   Code   RO         3643    .text               c_2.l(rt_memmove_w.o)
    0x0600200c   0x00000098   Code   RO         3649    .text               c_2.l(lludiv10.o)
    0x060020a4   0x000000b2   Code   RO         3651    .text               c_2.l(_printf_intcommon.o)
    0x06002156   0x0000040e   Code   RO         3653    .text               c_2.l(_printf_fp_dec.o)
    0x06002564   0x0000002c   Code   RO         3657    .text               c_2.l(_printf_char.o)
    0x06002590   0x0000002c   Code   RO         3659    .text               c_2.l(_printf_wchar.o)
    0x060025bc   0x00000040   Code   RO         3662    .text               c_2.l(_wcrtomb.o)
    0x060025fc   0x0000002c   Code   RO         3664    .text               c_2.l(rtudiv10.o)
    0x06002628   0x00000010   Code   RO         3666    .text               c_2.l(rt_ctype_table.o)
    0x06002638   0x00000008   Code   RO         3668    .text               c_2.l(rt_locale.o)
    0x06002640   0x00000080   Code   RO         3673    .text               c_2.l(_printf_fp_infnan.o)
    0x060026c0   0x000000d8   Code   RO         3675    .text               c_2.l(bigflt0.o)
    0x06002798   0x000000f4   Code   RO         3724    .text               c_2.l(strcmp.o)
    0x0600288c   0x0000003e   Code   RO         3678    CL$$btod_d2e        c_2.l(btod.o)
    0x060028ca   0x00000046   Code   RO         3680    CL$$btod_d2e_denorm_low  c_2.l(btod.o)
    0x06002910   0x00000060   Code   RO         3679    CL$$btod_d2e_norm_op1  c_2.l(btod.o)
    0x06002970   0x00000338   Code   RO         3688    CL$$btod_div_common  c_2.l(btod.o)
    0x06002ca8   0x000000c6   Code   RO         3685    CL$$btod_e2e        c_2.l(btod.o)
    0x06002d6e   0x00000028   Code   RO         3682    CL$$btod_ediv       c_2.l(btod.o)
    0x06002d96   0x00000028   Code   RO         3681    CL$$btod_emul       c_2.l(btod.o)
    0x06002dbe   0x00000244   Code   RO         3687    CL$$btod_mult_common  c_2.l(btod.o)
    0x06003002   0x00000002   PAD
    0x06003004   0x00000008   Ven    RO         3826    Veneer$$Code        anon$$obj.o
    0x0600300c   0x00000008   Ven    RO         3827    Veneer$$Code        anon$$obj.o
    0x06003014   0x00000008   Ven    RO         3828    Veneer$$Code        anon$$obj.o
    0x0600301c   0x00000008   Ven    RO         3829    Veneer$$Code        anon$$obj.o
    0x06003024   0x00000008   Ven    RO         3830    Veneer$$Code        anon$$obj.o
    0x0600302c   0x00000020   Code   RO         3271    i.CalcImageChecksum  bspatch.o
    0x0600304c   0x0000003c   Code   RO         1421    i.CheckFMEraseState  FM.o
    0x06003088   0x00000040   Code   RO          589    i.CheckIfSkip       Flash.o
    0x060030c8   0x00000044   Code   RO         2511    i.CheckReserved     tim.o
    0x0600310c   0x00000068   Code   RO         1422    i.ClearFM           FM.o
    0x06003174   0x000000ec   Code   RO          590    i.Configure_Flashes  Flash.o
    0x06003260   0x00000038   Code   RO            1    i.Delay             plat_api.o
    0x06003298   0x000001b0   Code   RO          594    i.EraseFlash        Flash.o
    0x06003448   0x0000000c   Code   RO         3272    i.FOTA_Init_Heap    bspatch.o
    0x06003454   0x00000080   Code   RO         2514    i.FindPackageInReserved  tim.o
    0x060034d4   0x0000002c   Code   RO         1551    i.GetABBTState      FM_ext.o
    0x06003500   0x00000010   Code   RO          596    i.GetBlockSize      Flash.o
    0x06003510   0x0000000c   Code   RO         1428    i.GetFMProperties   FM.o
    0x0600351c   0x00000024   Code   RO          599    i.GetFlashProperties  Flash.o
    0x06003540   0x00000080   Code   RO          600    i.GetFlashType      Flash.o
    0x060035c0   0x00000034   Code   RO          601    i.GetInitRoutine    Flash.o
    0x060035f4   0x00000024   Code   RO            2    i.GetOSCR0          plat_api.o
    0x06003618   0x00000068   Code   RO           57    i.GetPMICID         pmic.o
    0x06003680   0x00000010   Code   RO          602    i.GetPageSize       Flash.o
    0x06003690   0x00000010   Code   RO         1429    i.GetPartitionOffset  FM.o
    0x060036a0   0x0000000c   Code   RO         2516    i.GetTimPointer     tim.o
    0x060036ac   0x00000014   Code   RO          605    i.GetUseSpareArea   Flash.o
    0x060036c0   0x00000158   Code   RO         2793    i.GuilinClkInit     guilin.o
    0x06003818   0x00000038   Code   RO         2986    i.GuilinLiteClkInit  guilin_lite.o
    0x06003850   0x00000038   Code   RO         2988    i.GuilinLiteRead    guilin_lite.o
    0x06003888   0x00000038   Code   RO         2989    i.GuilinLiteWrite   guilin_lite.o
    0x060038c0   0x00000018   Code   RO         3008    i.GuilinLite_VBUCK1_CFG  guilin_lite.o
    0x060038d8   0x00000068   Code   RO         3013    i.GuilinLite_VBUCK_Set_VOUT  guilin_lite.o
    0x06003940   0x0000004c   Code   RO         2794    i.GuilinRead        guilin.o
    0x0600398c   0x00000050   Code   RO         2795    i.GuilinWrite       guilin.o
    0x060039dc   0x0000003c   Code   RO         2813    i.Guilin_VBUCK1_CFG  guilin.o
    0x06003a18   0x000000b0   Code   RO         2819    i.Guilin_VBUCK_Set_VOUT  guilin.o
    0x06003ac8   0x00000004   Code   RO           59    i.I2CReceive        pmic.o
    0x06003acc   0x00000004   Code   RO           61    i.I2CSend           pmic.o
    0x06003ad0   0x00000040   Code   RO            3    i.InitSODTimer      plat_api.o
    0x06003b10   0x0000017c   Code   RO         1246    i.InitializeQSPIDevice  spi_nor.o
    0x06003c8c   0x0000018c   Code   RO          993    i.InitializeQSPINAND  spi_nand.o
    0x06003e18   0x00000020   Code   RO         2570    i.IsUpdaterSupportQueryFotaInfo  updater_table.o
    0x06003e38   0x0000003c   Code   RO         1432    i.LocateBlock       FM.o
    0x06003e74   0x00000178   Code   RO           62    i.MastI2CReceive    pmic.o
    0x06003fec   0x00000130   Code   RO           63    i.MastI2CSend       pmic.o
    0x0600411c   0x000000c8   Code   RO         3274    i.OTA_Check_FotaImage  bspatch.o
    0x060041e4   0x00000028   Code   RO         3275    i.OTA_Get_Fota_Progress  bspatch.o
    0x0600420c   0x000004e8   Code   RO         3276    i.OTA_Update_FotaImage  bspatch.o
    0x060046f4   0x000001f0   Code   RO         3277    i.OTA_Update_FotaParam  bspatch.o
    0x060048e4   0x000000b4   Code   RO           64    i.PI2C_READ_REG     pmic.o
    0x06004998   0x000000cc   Code   RO           65    i.PI2C_WRITE_REG    pmic.o
    0x06004a64   0x0000003c   Code   RO           79    i.PM812_VBUCK1_CFG  pmic.o
    0x06004aa0   0x00000054   Code   RO           81    i.PMIC_FAULT_WU_ENABLE  pmic.o
    0x06004af4   0x00000054   Code   RO           82    i.PMIC_FAULT_WU_EN_ENABLE  pmic.o
    0x06004b48   0x0000002c   Code   RO           84    i.PMIC_ID_GET       pmic.o
    0x06004b74   0x0000001c   Code   RO           85    i.PMIC_IS_PM802     pmic.o
    0x06004b90   0x00000014   Code   RO           86    i.PMIC_IS_PM802S    pmic.o
    0x06004ba4   0x00000018   Code   RO           87    i.PMIC_IS_PM803     pmic.o
    0x06004bbc   0x00000094   Code   RO           92    i.PMIC_Init         pmic.o
    0x06004c50   0x0000000c   Code   RO           95    i.PMIC_READ_REG_BASE  pmic.o
    0x06004c5c   0x000000a4   Code   RO           98    i.PMIC_SW_RESET     pmic.o
    0x06004d00   0x00000010   Code   RO          100    i.PMIC_WRITE_REG_BASE  pmic.o
    0x06004d10   0x00000010   Code   RO          102    i.PMIC_WRITE_REG_POWER  pmic.o
    0x06004d20   0x00000160   Code   RO          503    i.PP_Switch         FreqChange.o
    0x06004e80   0x00000048   Code   RO          104    i.PlatformVcoreConfigLow  pmic.o
    0x06004ec8   0x00000148   Code   RO          607    i.ReadFlash         Flash.o
    0x06005010   0x0000005c   Code   RO         1433    i.RelocateBlock     FM.o
    0x0600506c   0x00000008   Code   RO         1555    i.RelocateBlock_LegacyExt  FM_ext.o
    0x06005074   0x00000028   Code   RO          610    i.ResetFlash        Flash.o
    0x0600509c   0x00000188   Code   RO         1558    i.ScrubBlock_LegacyExt  FM_ext.o
    0x06005224   0x00000068   Code   RO         1559    i.SetABBTState      FM_ext.o
    0x0600528c   0x00000070   Code   RO         1437    i.SetBBTState       FM.o
    0x060052fc   0x00000090   Code   RO         2517    i.SetTIMPointers    tim.o
    0x0600538c   0x000004b4   Code   RO         3278    i.System_Adiff_Backup  bspatch.o
    0x06005840   0x00000160   Code   RO         3279    i.System_Adiff_Check_Dfota_File  bspatch.o
    0x060059a0   0x00000068   Code   RO         3280    i.System_Adiff_Check_Old_Image  bspatch.o
    0x06005a08   0x00000224   Code   RO         3281    i.System_Adiff_Move_Old_Backup  bspatch.o
    0x06005c2c   0x0000013c   Code   RO         3282    i.System_Adiff_Power_Off_Protection  bspatch.o
    0x06005d68   0x000009f4   Code   RO         3283    i.System_Adiff_Replace_Image  bspatch.o
    0x0600675c   0x00000084   Code   RO         3284    i.System_Adiff_Update_Image  bspatch.o
    0x060067e0   0x00000290   Code   RO         3285    i.System_DFOTA_Upgrade_Adiff  bspatch.o
    0x06006a70   0x000002e8   Code   RO         3286    i.System_FOTA_Upgrade_Adiff  bspatch.o
    0x06006d58   0x0000000c   Code   RO          107    i.USTICAI2CReadDi_AUDIO  pmic.o
    0x06006d64   0x0000000c   Code   RO          108    i.USTICAI2CReadDi_GPADC  pmic.o
    0x06006d70   0x0000000c   Code   RO          109    i.USTICAI2CReadDi_TEST  pmic.o
    0x06006d7c   0x0000000c   Code   RO          110    i.USTICAI2CReadDi_base  pmic.o
    0x06006d88   0x0000000c   Code   RO          111    i.USTICAI2CReadDi_power  pmic.o
    0x06006d94   0x00000010   Code   RO          112    i.USTICAI2CWriteDi_AUDIO  pmic.o
    0x06006da4   0x00000010   Code   RO          113    i.USTICAI2CWriteDi_GPADC  pmic.o
    0x06006db4   0x00000010   Code   RO          114    i.USTICAI2CWriteDi_TEST  pmic.o
    0x06006dc4   0x00000010   Code   RO          115    i.USTICAI2CWriteDi_base  pmic.o
    0x06006dd4   0x00000010   Code   RO          116    i.USTICAI2CWriteDi_power  pmic.o
    0x06006de4   0x00000020   Code   RO          118    i.Voltage_set_main  pmic.o
    0x06006e04   0x00000238   Code   RO          615    i.WriteFlash        Flash.o
    0x0600703c   0x00000028   Code   RO         3710    i.__ARM_fpclassify  m_2s.l(fpclassify.o)
    0x06007064   0x000001a4   Code   RO          994    i.__spi_nand_do_read_page  spi_nand.o
    0x06007208   0x00000150   Code   RO          995    i.__spi_nand_write  spi_nand.o
    0x06007358   0x0000000e   Code   RO         3610    i._is_digit         c_2.l(__printf_wp.o)
    0x06007366   0x00000002   PAD
    0x06007368   0x00000078   Code   RO          504    i.aclk_dfc_switch   FreqChange.o
    0x060073e0   0x00000020   Code   RO          541    i.ap_uart_putc      uart.o
    0x06007400   0x000001d4   Code   RO         3287    i.bzpacth           bspatch.o
    0x060075d4   0x0000006c   Code   RO         2702    i.check_if_DCS_mode  utilities.o
    0x06007640   0x000000b0   Code   RO         3288    i.check_old_image_checksum  bspatch.o
    0x060076f0   0x00000094   Code   RO          542    i.cp_uart_init      uart.o
    0x06007784   0x000005bc   Code   RO         3289    i.datamodule_fota   bspatch.o
    0x06007d40   0x000000ac   Code   RO          505    i.dck_dfc_switch    FreqChange.o
    0x06007dec   0x000000a0   Code   RO          506    i.enable_PLL        FreqChange.o
    0x06007e8c   0x000000c0   Code   RO          996    i.fm_spi_nand_ecc_status  spi_nand.o
    0x06007f4c   0x000000c4   Code   RO          997    i.gd_spi_nand_ecc_status  spi_nand.o
    0x06008010   0x000000f4   Code   RO          998    i.gd_spi_nand_ecc_status2  spi_nand.o
    0x06008104   0x00000078   Code   RO          999    i.generic_spi_nand_ecc_status  spi_nand.o
    0x0600817c   0x00000010   Code   RO         2574    i.get_updater_function_magic_type  updater_table.o
    0x0600818c   0x00000024   Code   RO         1000    i.ilog2             spi_nand.o
    0x060081b0   0x00000024   Code   RO         1249    i.ilog2             spi_nor.o
    0x060081d4   0x00000038   Code   RO          121    i.mdelay            pmic.o
    0x0600820c   0x000000a0   Code   RO         1001    i.micron_spi_nand_ecc_status  spi_nand.o
    0x060082ac   0x000001d4   Code   RO         1660    i.mpu_value_check   system.o
    0x06008480   0x000000cc   Code   RO         1002    i.mxic2_spi_nand_ecc_status  spi_nand.o
    0x0600854c   0x00000034   Code   RO         1003    i.mxic_spi_nand_ecc_status  spi_nand.o
    0x06008580   0x000000d0   Code   RO         3293    i.offtin            bspatch.o
    0x06008650   0x00000068   Code   RO          803    i.qspi_cmd_done_interrupt  qspi_host.o
    0x060086b8   0x000000d0   Code   RO          804    i.qspi_cmd_done_pio  qspi_host.o
    0x06008788   0x0000028c   Code   RO          806    i.qspi_config_lookup_tbl  qspi_host.o
    0x06008a14   0x00000064   Code   RO          807    i.qspi_config_mfp   qspi_host.o
    0x06008a78   0x00000094   Code   RO          809    i.qspi_enable_xip   qspi_host.o
    0x06008b0c   0x0000002c   Code   RO          810    i.qspi_enter_mode   qspi_host.o
    0x06008b38   0x00000148   Code   RO          812    i.qspi_host_init    qspi_host.o
    0x06008c80   0x000000b0   Code   RO          813    i.qspi_init_ahb     qspi_host.o
    0x06008d30   0x00000050   Code   RO          814    i.qspi_invalid_ahb  qspi_host.o
    0x06008d80   0x00000154   Code   RO          817    i.qspi_poll_rx_buff  qspi_host.o
    0x06008ed4   0x000000bc   Code   RO          818    i.qspi_preinit_lookup_tbl  qspi_host.o
    0x06008f90   0x000000fc   Code   RO          819    i.qspi_prepare_transmit  qspi_host.o
    0x0600908c   0x000000c0   Code   RO          820    i.qspi_set_func_clk  qspi_host.o
    0x0600914c   0x00000110   Code   RO          821    i.qspi_set_func_clk_fc  qspi_host.o
    0x0600925c   0x00000300   Code   RO          823    i.qspi_start_cmd    qspi_host.o
    0x0600955c   0x00000218   Code   RO          825    i.qspi_wait_cmd_done  qspi_host.o
    0x06009774   0x0000004c   Code   RO          826    i.qspi_write_rbct   qspi_host.o
    0x060097c0   0x00000044   Code   RO          827    i.qspi_write_sfar   qspi_host.o
    0x06009804   0x0000001c   Code   RO          828    i.qspi_writel_check  qspi_host.o
    0x06009820   0x0000001c   Code   RO          829    i.qspi_writel_clear  qspi_host.o
    0x0600983c   0x00000064   Code   RO         1005    i.sanitize_string   spi_nand.o
    0x060098a0   0x00000084   Code   RO         1006    i.spi_nand_change_mode  spi_nand.o
    0x06009924   0x000001e8   Code   RO         1007    i.spi_nand_detect_onfi  spi_nand.o
    0x06009b0c   0x00000080   Code   RO         1008    i.spi_nand_disable_ecc  spi_nand.o
    0x06009b8c   0x00000018   Code   RO         1009    i.spi_nand_do_erase  spi_nand.o
    0x06009ba4   0x00000084   Code   RO         1010    i.spi_nand_do_read  spi_nand.o
    0x06009c28   0x000000c4   Code   RO         1011    i.spi_nand_do_read_page  spi_nand.o
    0x06009cec   0x0000000c   Code   RO         1012    i.spi_nand_do_reset  spi_nand.o
    0x06009cf8   0x00000028   Code   RO         1013    i.spi_nand_do_write  spi_nand.o
    0x06009d20   0x00000264   Code   RO         1014    i.spi_nand_do_write_page  spi_nand.o
    0x06009f84   0x00000084   Code   RO         1015    i.spi_nand_enable_ecc  spi_nand.o
    0x0600a008   0x000001c8   Code   RO         1016    i.spi_nand_erase    spi_nand.o
    0x0600a1d0   0x00000178   Code   RO         1017    i.spi_nand_gen_fbbt  spi_nand.o
    0x0600a348   0x0000000c   Code   RO         1018    i.spi_nand_get_cfg  spi_nand.o
    0x0600a354   0x00000390   Code   RO         1019    i.spi_nand_init     spi_nand.o
    0x0600a6e4   0x00000014   Code   RO         1020    i.spi_nand_lock_block  spi_nand.o
    0x0600a6f8   0x0000009c   Code   RO         1021    i.spi_nand_program_data_to_cache  spi_nand.o
    0x0600a794   0x000000c0   Code   RO         1022    i.spi_nand_read_from_cache  spi_nand.o
    0x0600a854   0x0000005c   Code   RO         1023    i.spi_nand_read_page_to_cache  spi_nand.o
    0x0600a8b0   0x00000114   Code   RO         1024    i.spi_nand_read_pages  spi_nand.o
    0x0600a9c4   0x00000088   Code   RO         1025    i.spi_nand_read_reg  spi_nand.o
    0x0600aa4c   0x0000006c   Code   RO         1026    i.spi_nand_reset    spi_nand.o
    0x0600aab8   0x00000104   Code   RO         1027    i.spi_nand_scan_id_table  spi_nand.o
    0x0600abbc   0x0000000c   Code   RO         1028    i.spi_nand_set_cfg  spi_nand.o
    0x0600abc8   0x000000b8   Code   RO         1029    i.spi_nand_set_rd_wr_op  spi_nand.o
    0x0600ac80   0x00000090   Code   RO         1030    i.spi_nand_wait     spi_nand.o
    0x0600ad10   0x0000003c   Code   RO         1031    i.spi_nand_write_enable  spi_nand.o
    0x0600ad4c   0x0000008c   Code   RO         1032    i.spi_nand_write_reg  spi_nand.o
    0x0600add8   0x00000018   Code   RO         1251    i.spi_nor_do_erase  spi_nor.o
    0x0600adf0   0x00000014   Code   RO         1252    i.spi_nor_do_read   spi_nor.o
    0x0600ae04   0x00000014   Code   RO         1254    i.spi_nor_do_write  spi_nor.o
    0x0600ae18   0x00000170   Code   RO         1255    i.spi_nor_enable_4byte_mode  spi_nor.o
    0x0600af88   0x00000234   Code   RO         1256    i.spi_nor_erase     spi_nor.o
    0x0600b1bc   0x00000098   Code   RO         1257    i.spi_nor_erase_block  spi_nor.o
    0x0600b254   0x000002ac   Code   RO         1258    i.spi_nor_init      spi_nor.o
    0x0600b500   0x000001f4   Code   RO         1259    i.spi_nor_read      spi_nor.o
    0x0600b6f4   0x0000007c   Code   RO         1261    i.spi_nor_read_status  spi_nor.o
    0x0600b770   0x00000010   Code   RO         1262    i.spi_nor_read_status1  spi_nor.o
    0x0600b780   0x0000003c   Code   RO         1263    i.spi_nor_read_status2  spi_nor.o
    0x0600b7bc   0x000000c8   Code   RO         1264    i.spi_nor_reset     spi_nor.o
    0x0600b884   0x00000230   Code   RO         1266    i.spi_nor_set_quad  spi_nor.o
    0x0600bab4   0x000000c4   Code   RO         1267    i.spi_nor_set_rd_wr_op  spi_nor.o
    0x0600bb78   0x00000038   Code   RO         1270    i.spi_nor_wait      spi_nor.o
    0x0600bbb0   0x00000108   Code   RO         1271    i.spi_nor_write     spi_nor.o
    0x0600bcb8   0x00000048   Code   RO         1272    i.spi_nor_write_enable  spi_nor.o
    0x0600bd00   0x000000fc   Code   RO         1273    i.spi_nor_write_page  spi_nor.o
    0x0600bdfc   0x00000074   Code   RO         1274    i.spi_nor_write_status1  spi_nor.o
    0x0600be70   0x00000064   Code   RO          546    i.uart_printf       uart.o
    0x0600bed4   0x000000cc   Code   RO         2582    i.updater_get_fota_solution  updater_table.o
    0x0600bfa0   0x000000c4   Code   RO         2584    i.updater_get_lcd_info  updater_table.o
    0x0600c064   0x000000c8   Code   RO         2585    i.updater_get_version_info  updater_table.o
    0x0600c12c   0x00000158   Code   RO         2586    i.updater_header_table_init  updater_table.o
    0x0600c284   0x0000007c   Code   RO          125    i.ustica_I2CConfigureDi  pmic.o
    0x0600c300   0x00000018   Code   RO          126    i.ustica_I2CEnableclockandPin  pmic.o
    0x0600c318   0x000000bc   Code   RO          128    i.ustica_I2CIntLISRDirect  pmic.o
    0x0600c3d4   0x000000f0   Code   RO          129    i.ustica_I2CMasterReceiveDataDirect  pmic.o
    0x0600c4c4   0x00000038   Code   RO          130    i.ustica_I2CMasterSendDataDirect  pmic.o
    0x0600c4fc   0x0000008c   Code   RO          131    i.ustica_i2cWaitStatusDirect  pmic.o
    0x0600c588   0x000001bc   Code   RO          132    i.ustica_masterSendDirect  pmic.o
    0x0600c744   0x00000024   Code   RO          133    i.uudelay           pmic.o
    0x0600c768   0x00000080   Code   RO         1033    i.xtx2_spi_nand_ecc_status  spi_nand.o
    0x0600c7e8   0x00000048   Code   RO         1034    i.xtx_spi_nand_ecc_status  spi_nand.o
    0x0600c830   0x000000b4   Code   RO         1035    i.yxsc_spi_nand_ecc_status  spi_nand.o
    0x0600c8e4   0x0000002c   Code   RO         3701    locale$$code        c_2.l(lc_numeric_c.o)
    0x0600c910   0x0000002c   Code   RO         3718    locale$$code        c_2.l(lc_ctype_c.o)
    0x0600c93c   0x00000004   Code   RO         3645    x$fpl$printf1       fz_2s.l(printf1.o)
    0x0600c940   0x00000000   Code   RO         3709    x$fpl$usenofp       fz_2s.l(usenofp.o)
    0x0600c940   0x00000006   Code   RO         3499    x$sdiv              c_2.l(aeabi_sdiv_cr4.o)
    0x0600c946   0x0000000c   Code   RO         3500    x$sdivmod           c_2.l(aeabi_sdiv_cr4.o)
    0x0600c952   0x00000002   PAD
    0x0600c954   0x00000008   Ven    RO         3501    x$udiv              c_2.l(aeabi_sdiv_cr4.o)
    0x0600c95c   0x00000006   Code   RO         3501    x$udiv              c_2.l(aeabi_sdiv_cr4.o)
    0x0600c962   0x0000000c   Code   RO         3502    x$udivmod           c_2.l(aeabi_sdiv_cr4.o)
    0x0600c96e   0x000000af   Data   RO         1037    .constdata          spi_nand.o
    0x0600ca1d   0x00000029   Data   RO         1277    .constdata          spi_nor.o
    0x0600ca46   0x0000011d   Data   RO         2587    .constdata          updater_table.o
    0x0600cb63   0x00000038   Data   RO         3294    .constdata          bspatch.o
    0x0600cb9b   0x00000001   PAD
    0x0600cb9c   0x00000008   Data   RO         3569    .constdata          c_2.l(_printf_wctomb.o)
    0x0600cba4   0x00000028   Data   RO         3598    .constdata          c_2.l(_printf_hex_int_ll_ptr.o)
    0x0600cbcc   0x00000011   Data   RO         3618    .constdata          c_2.l(__printf_flags_ss_wp.o)
    0x0600cbdd   0x00000003   PAD
    0x0600cbe0   0x00000094   Data   RO         3676    .constdata          c_2.l(bigflt0.o)
    0x0600cc74   0x0000004d   Data   RO          136    .conststring        pmic.o
    0x0600ccc1   0x00000003   PAD
    0x0600ccc4   0x000001e7   Data   RO         1038    .conststring        spi_nand.o
    0x0600ceab   0x00000001   PAD
    0x0600ceac   0x0000016a   Data   RO         1278    .conststring        spi_nor.o
    0x0600d016   0x00000002   PAD
    0x0600d018   0x000001bd   Data   RO         3295    .conststring        bspatch.o
    0x0600d1d5   0x00000003   PAD
    0x0600d1d8   0x0000001c   Data   RO         3700    locale$$data        c_2.l(lc_numeric_c.o)
    0x0600d1f4   0x00000110   Data   RO         3717    locale$$data        c_2.l(lc_ctype_c.o)


    Execution Region DATA (Base: 0x06051200, Size: 0x0000b328, Max: 0x00070000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x06051200   0x00000004   Data   RW            4    .data               plat_api.o
    0x06051204   0x0000000c   Data   RW          137    .data               pmic.o
    0x06051210   0x00000001   Data   RW          507    .data               FreqChange.o
    0x06051211   0x00000003   PAD
    0x06051214   0x00000008   Data   RW          618    .data               Flash.o
    0x0605121c   0x00000896   Data   RW         1039    .data               spi_nand.o
    0x06051ab2   0x00000002   PAD
    0x06051ab4   0x000005ae   Data   RW         1279    .data               spi_nor.o
    0x06052062   0x00000002   PAD
    0x06052064   0x0000000c   Data   RW         1440    .data               FM.o
    0x06052070   0x00000008   Data   RW         2349    .data               LzmaLib.o
    0x06052078   0x0000000c   Data   RW         2424    .data               tinyalloc.o
    0x06052084   0x00000008   Data   RW         2588    .data               updater_table.o
    0x0605208c   0x00000002   Data   RW         2710    .data               utilities.o
    0x0605208e   0x00000002   PAD
    0x06052090   0x00000010   Data   RW         3296    .data               bspatch.o
    0x060520a0   0x00000050   Zero   RW          134    .bss                pmic.o
    0x060520f0   0x000000a0   Zero   RW          617    .bss                Flash.o
    0x06052190   0x00000044   Zero   RW          831    .bss                qspi_host.o
    0x060521d4   0x00000004   PAD
    0x060521d8   0x00000168   Zero   RW         1036    .bss                spi_nand.o
    0x06052340   0x00000168   Zero   RW         1276    .bss                spi_nor.o
    0x060524a8   0x00000054   Zero   RW         1439    .bss                FM.o
    0x060524fc   0x00000014   Zero   RW         2519    .bss                tim.o
    0x06052510   0x00000014   Zero   RW         3669    .bss                c_2.l(rt_locale.o)
    0x06052524   0x00000004   PAD
    0x06052528   0x0000a000   Zero   RW         3269    STACK               StartUp.o


    Execution Region DTCM (Base: 0xb0021c00, Size: 0x00000000, Max: 0x0000e400, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region USB_SQU (Base: 0xd1000000, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       456         32          0         12         84       5079   FM.o
       548         32          0          0          0       4314   FM_ext.o
      1936        180          0          8        160      38646   Flash.o
       804        236          0          1          0       4696   FreqChange.o
      4932         52          0          0          0      24747   LzFind.o
      6184          4         24          0          0      25315   LzmaDec.o
     13412        140        192          0          0      56548   LzmaEnc.o
       384          8          0          8          0       9949   LzmaLib.o
       992         48          0          0      40960          0   StartUp.o
     10960       3484        501         16          0      22382   bspatch.o
       736         28          0          0          0       3831   guilin.o
       296          0          0          0          0       3627   guilin_lite.o
      1376        636          0          0          0       4606   main.o
       156         12          0          4          0      15582   plat_api.o
      3428        476         77         12         80      95894   pmic.o
      4588        844          0          0         68      23786   qspi_host.o
      8268       1576        662       2198        360      44796   spi_nand.o
      4664        988        403       1454        360      19558   spi_nor.o
       468        220          0          0          0        722   system.o
       352         20          0          0         20       3133   tim.o
       868         64          9         12          0       7786   tinyalloc.o
       280         24          0          0          0       1712   uart.o
       992        572        285          8          0      10869   updater_table.o
       108         40          0          2          0        692   utilities.o
         0          0        768          0          0        976   version_block.o

    ----------------------------------------------------------------------
     67284       <USER>       <GROUP>       3744      42096     429246   Object Totals
        96         48          0          0          0          0   (incl. Generated)
         0          0         13          9          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       112         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1038          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
        44          0          0          0          0        272   aeabi_sdiv_cr4.o
       216          6        148          0          0         96   bigflt0.o
      1910        128          0          0          0        672   btod.o
        52          4          0          0          0         80   c89vsnprintf.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         0          0          0          0          0          0   libinit2.o
       152          0          0          0          0         80   lludiv10.o
       816         64          0          0          0         84   lludiv5.o
        38          0          0          0          0         68   llushr.o
        88          0          0          0          0         76   memcmp.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0         20         68   rt_locale.o
        76          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       212          0          0          0          0         68   rt_memcpy.o
       100          0          0          0          0         80   rt_memcpy_w.o
       212          0          0          0          0         68   rt_memmove.o
       128          0          0          0          0         80   rt_memmove_w.o
        44          0          0          0          0         68   rtudiv10.o
       244          4          0          0          0         80   strcmp.o
       240          4          0          0          0         80   strncmp.o
        22          0          0          0          0         68   uread4.o
         4          0          0          0          0         68   printf1.o
         0          0          0          0          0          0   usenofp.o
        40          0          0          0          0         68   fpclassify.o

    ----------------------------------------------------------------------
      7878        <USER>        <GROUP>          0         24       4588   Library Totals
        10          0          3          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7824        316        513          0         20       4452   c_2.l
         4          0          0          0          0         68   fz_2s.l
        40          0          0          0          0         68   m_2s.l

    ----------------------------------------------------------------------
      7878        <USER>        <GROUP>          0         24       4588   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     75162      10080       3450       3744      42120     415530   Grand Totals
     75162      10080       3450       3744      42120     415530   ELF Image Totals
     75162      10080       3450       3744          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                78612 (  76.77kB)
    Total RW  Size (RW Data + ZI Data)             45864 (  44.79kB)
    Total ROM Size (Code + RO Data + RW Data)      82356 (  80.43kB)

==============================================================================

