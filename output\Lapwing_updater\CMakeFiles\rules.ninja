# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.19

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: UPDATER
# Configurations: 
# =============================================================================
# =============================================================================

#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER__Lapwing_updater_
  depfile = $DEP_FILE
  deps = gcc
  command = F:\DS-5V5~1.0\sw\ARMCOM~1.06U\bin\armasm.exe --via=$RSP_FILE -o $out $in
  description = Building ASM object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for compiling C files.

rule C_COMPILER__Lapwing_updater_
  depfile = $DEP_FILE
  deps = gcc
  command = F:\DS-5V5~1.0\sw\ARMCOM~1.06U\bin\armcc.exe --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C executable.

rule C_EXECUTABLE_LINKER__Lapwing_updater_
  command = cmd.exe /C "$PRE_LINK && F:\DS-5V5~1.0\sw\ARMCOM~1.06U\bin\armlink.exe $LINK_FLAGS --via=$RSP_FILE -o $TARGET_FILE --list $TARGET_FILE.map && $POST_BUILD"
  description = Linking C executable $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = D:\xy695\ENV\cmake\windows-x86\bin\cmake.exe --regenerate-during-build -SD:\xy695\PLT\startup\updater -BD:\xy695\output\Lapwing_updater
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = D:\xy695\ENV\misc\windows-x86\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = D:\xy695\ENV\misc\windows-x86\ninja.exe -t targets
  description = All primary targets available:

