# 1 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"
/*******************************************************************************
 *
 * Copyright (c) 2013, 2014 Intel Corporation and others.
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v2.0
 * and Eclipse Distribution License v1.0 which accompany this distribution.
 *
 * The Eclipse Public License is available at
 *    http://www.eclipse.org/legal/epl-v20.html
 * The Eclipse Distribution License is available at
 *    http://www.eclipse.org/org/documents/edl-v10.php.
 *
 * Contributors:
 *    <PERSON>, Intel Corporation - initial API and implementation
 *    <PERSON>, Sierra Wireless
 *    Bosch Software Innovations GmbH - Please refer to git log
 *    Pascal Rieux - Please refer to git log
 *    <PERSON>, AMETEK, Inc. - Please refer to git log
 *
 *******************************************************************************/

/*
 *  Resources:
 *
 *          Name                       | ID | Operations | Instances | Mandatory |  Type    |  Range  | Units |
 *  Short ID                           |  0 |     R      |  Single   |    Yes    | Integer  | 1-65535 |       |
 *  Lifetime                           |  1 |    R/W     |  Single   |    Yes    | Integer  |         |   s   |
 *  Default Min Period                 |  2 |    R/W     |  Single   |    No     | Integer  |         |   s   |
 *  Default Max Period                 |  3 |    R/W     |  Single   |    No     | Integer  |         |   s   |
 *  Disable                            |  4 |     E      |  Single   |    No     |          |         |       |
 *  Disable Timeout                    |  5 |    R/W     |  Single   |    No     | Integer  |         |   s   |
 *  Notification Storing               |  6 |    R/W     |  Single   |    Yes    | Boolean  |         |       |
 *  Binding                            |  7 |    R/W     |  Single   |    Yes    | String   |         |       |
 *  Registration Update                |  8 |     E      |  Single   |    Yes    |          |         |       |
#ifndef LWM2M_VERSION_1_0
 *  Registration Priority Order        | 13 |    R/W     |  Single   |    No     | Unsigned |         |       |
 *  Initial Registration Delay Timer   | 14 |    R/W     |  Single   |    No     | Unsigned |         |   s   |
 *  Registration Failure Block         | 15 |    R/W     |  Single   |    No     | Boolean  |         |       |
 *  Bootstrap on Registration Failure  | 16 |    R/W     |  Single   |    No     | Boolean  |         |       |
 *  Communication Retry Count          | 17 |    R/W     |  Single   |    No     | Unsigned |         |       |
 *  Communication Retry Timer          | 18 |    R/W     |  Single   |    No     | Unsigned |         |   s   |
 *  Communication Sequence Delay Timer | 19 |    R/W     |  Single   |    No     | Unsigned |         |   s   |
 *  Communication Sequence Retry Count | 20 |    R/W     |  Single   |    No     | Unsigned |         |       |
#endif
 *
 */

# 1 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"
/*******************************************************************************
 *
 * Copyright (c) 2013, 2014 Intel Corporation and others.
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v2.0
 * and Eclipse Distribution License v1.0 which accompany this distribution.
 *
 * The Eclipse Public License is available at
 *    http://www.eclipse.org/legal/epl-v20.html
 * The Eclipse Distribution License is available at
 *    http://www.eclipse.org/org/documents/edl-v10.php.
 *
 * Contributors:
 *    David Navarro, Intel Corporation - initial API and implementation
 *    Fabien Fleutot - Please refer to git log
 *    Simon Bernard - Please refer to git log
 *    Toby Jaffey - Please refer to git log
 *    Julien Vermillard - Please refer to git log
 *    Bosch Software Innovations GmbH - Please refer to git log
 *    Pascal Rieux - Please refer to git log
 *    Ville Skyttä - Please refer to git log
 *    Scott Bertin, AMETEK, Inc. - Please refer to git log
 *    Tuve Nordius, Husqvarna Group - Please refer to git log
 *
 *******************************************************************************/

/*
 Copyright (c) 2013, 2014 Intel Corporation

 Redistribution and use in source and binary forms, with or without modification,
 are permitted provided that the following conditions are met:

     * Redistributions of source code must retain the above copyright notice,
       this list of conditions and the following disclaimer.
     * Redistributions in binary form must reproduce the above copyright notice,
       this list of conditions and the following disclaimer in the documentation
       and/or other materials provided with the distribution.
     * Neither the name of Intel Corporation nor the names of its contributors
       may be used to endorse or promote products derived from this software
       without specific prior written permission.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 THE POSSIBILITY OF SUCH DAMAGE.

 David Navarro <<EMAIL>>

*/








# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
/* Copyright (C) ARM Ltd., 1999,2014 */
/* All rights reserved */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */









    /* armcc has builtin '__int64' which can be used in --strict mode */
# 27 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
    /* armclang and non-strict armcc allow 'long long' in system headers */











# 46 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"


/*
 * 'signed' is redundant below, except for 'signed char' and if
 * the typedef is used to declare a bitfield.
 */

    /* 7.18.1.1 */

    /* exact-width signed integer types */
typedef   signed          char int8_t;
typedef   signed short     int int16_t;
typedef   signed           int int32_t;
typedef   signed       __int64 int64_t;

    /* exact-width unsigned integer types */
typedef unsigned          char uint8_t;
typedef unsigned short     int uint16_t;
typedef unsigned           int uint32_t;
typedef unsigned       __int64 uint64_t;

    /* 7.18.1.2 */

    /* smallest type of at least n bits */
    /* minimum-width signed integer types */
typedef   signed          char int_least8_t;
typedef   signed short     int int_least16_t;
typedef   signed           int int_least32_t;
typedef   signed       __int64 int_least64_t;

    /* minimum-width unsigned integer types */
typedef unsigned          char uint_least8_t;
typedef unsigned short     int uint_least16_t;
typedef unsigned           int uint_least32_t;
typedef unsigned       __int64 uint_least64_t;

    /* 7.18.1.3 */

    /* fastest minimum-width signed integer types */
typedef   signed           int int_fast8_t;
typedef   signed           int int_fast16_t;
typedef   signed           int int_fast32_t;
typedef   signed       __int64 int_fast64_t;

    /* fastest minimum-width unsigned integer types */
typedef unsigned           int uint_fast8_t;
typedef unsigned           int uint_fast16_t;
typedef unsigned           int uint_fast32_t;
typedef unsigned       __int64 uint_fast64_t;

    /* 7.18.1.4 integer types capable of holding object pointers */




typedef   signed           int intptr_t;
typedef unsigned           int uintptr_t;


    /* 7.18.1.5 greatest-width integer types */
typedef   signed     long long intmax_t;
typedef unsigned     long long uintmax_t;




    /* 7.18.2.1 */

    /* minimum values of exact-width signed integer types */





    /* maximum values of exact-width signed integer types */





    /* maximum values of exact-width unsigned integer types */





    /* 7.18.2.2 */

    /* minimum values of minimum-width signed integer types */





    /* maximum values of minimum-width signed integer types */





    /* maximum values of minimum-width unsigned integer types */





    /* 7.18.2.3 */

    /* minimum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width unsigned integer types */





    /* 7.18.2.4 */

    /* minimum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding unsigned integer type */






    /* 7.18.2.5 */

    /* minimum value of greatest-width signed integer type */


    /* maximum value of greatest-width signed integer type */


    /* maximum value of greatest-width unsigned integer type */


    /* 7.18.3 */

    /* limits of ptrdiff_t */
# 216 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of sig_atomic_t */



    /* limit of size_t */






    /* limits of wchar_t */
    /* NB we have to undef and redef because they're defined in both
     * stdint.h and wchar.h */



# 241 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of wint_t */







    /* 7.18.4.1 macros for minimum-width integer constants */










    /* 7.18.4.2 macros for greatest-width integer constants */











# 305 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"






/* end of stdint.h */
# 65 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"
/* stddef.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.1.4 */

/* Copyright (C) ARM Ltd., 1999
 * All rights reserved
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */

/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991 ARM Limited. All rights reserved.             */
/* version 0.05 */

/*
 * The following types and macros are defined in several headers referred to in
 * the descriptions of the functions declared in that header. They are also
 * defined in this header file.
 */





# 34 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"




  typedef signed int ptrdiff_t;



 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 57 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



  /* unconditional in non-strict C for consistency of debug info */



      typedef unsigned short wchar_t; /* also in <stdlib.h> and <inttypes.h> */
# 82 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



   /* null pointer constant. */




  /* EDG uses __INTADDR__ to avoid errors when strict */




  typedef long double max_align_t;









# 114 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



/* end of stddef.h */

# 66 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdbool.h"
/* stdbool.h: ISO/IEC 9899:1999 (C99), section 7.16 */

/* Copyright (C) ARM Ltd., 2002
 * All rights reserved
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */





# 25 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdbool.h"



# 67 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"
//#include <time.h>

/** LWM2M_RTOS */
# 1 "L:/PLT/pcac/lwm2m_wakaama/adaptor/lwm2m_config.h"
/*
 * @Author: jingquanxu
 * @Date: 2021-01-28 17:06:35
 * @LastEditTime: 
 * @Description: config file for lwm2m
 */




# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"
/* time.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.12 */
/* Copyright (C) Codemist Ltd., 1988-1993.                      */
/* Copyright 1991-1993 ARM Limited. All rights reserved.        */
/* version 0.03 */

/*
 * time.h declares two macros, four types and several functions for
 * manipulating time. Many functions deal with a calendar time that
 * represents the current date (according to the Gregorian
 * calendar) and time. Some functions deal with local time, which
 * is the calendar time expressed for some specific time zone, and
 * with Daylight Savings Time, which is a temporary change in the
 * algorithm for determining local time.
 */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */













# 41 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 57 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"




    /* CLOCKS_PER_SEC: the number per second of the value returned by the
     * clock function. */
# 73 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"





typedef unsigned int clock_t;    /* cpu time type */
typedef unsigned int time_t;     /* date/time in unix secs past 1-Jan-70 */

#pragma push
#pragma anon_unions

struct tm {
    int tm_sec;   /* seconds after the minute, 0 to 60
                     (0 - 60 allows for the occasional leap second) */
    int tm_min;   /* minutes after the hour, 0 to 59 */
    int tm_hour;  /* hours since midnight, 0 to 23 */
    int tm_mday;  /* day of the month, 1 to 31 */
    int tm_mon;   /* months since January, 0 to 11 */
    int tm_year;  /* years since 1900 */
    int tm_wday;  /* days since Sunday, 0 to 6 */
    int tm_yday;  /* days since January 1, 0 to 365 */
    int tm_isdst; /* Daylight Savings Time flag */
    union {       /* ABI-required extra fields, in a variety of types */
        struct {
            int __extra_1, __extra_2;
        };
        struct {
            long __extra_1_long, __extra_2_long;
        };
        struct {
            char *__extra_1_cptr, *__extra_2_cptr;
        };
        struct {
            void *__extra_1_vptr, *__extra_2_vptr;
        };
    };
};

#pragma pop

   /* struct tm holds the components of a calendar time, called the broken-down
    * time. The value of tm_isdst is positive if Daylight Savings Time is in
    * effect, zero if Daylight Savings Time is not in effect, and negative if
    * the information is not available.
    */

extern __declspec(__nothrow) clock_t clock(void);
   /* determines the processor time used.
    * Returns: the implementation's best approximation to the processor time
    *          used by the program since program invocation. The time in
    *          seconds is the value returned divided by the value of the macro
    *          CLK_TCK. The value (clock_t)-1 is returned if the processor time
    *          used is not available.
    */
extern __declspec(__nothrow) double difftime(time_t /*time1*/, time_t /*time0*/);
   /*
    * computes the difference between two calendar times: time1 - time0.
    * Returns: the difference expressed in seconds as a double.
    */
extern __declspec(__nothrow) time_t mktime(struct tm * /*timeptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the broken-down time, expressed as local time, in the structure
    * pointed to by timeptr into a calendar time value with the same encoding
    * as that of the values returned by the time function. The original values
    * of the tm_wday and tm_yday components of the structure are ignored, and
    * the original values of the other components are not restricted to the
    * ranges indicated above. On successful completion, the values of the
    * tm_wday and tm_yday structure components are set appropriately, and the
    * other components are set to represent the specified calendar time, but
    * with their values forced to the ranges indicated above; the final value
    * of tm_mday is not set until tm_mon and tm_year are determined.
    * Returns: the specified calendar time encoded as a value of type time_t.
    *          If the calendar time cannot be represented, the function returns
    *          the value (time_t)-1.
    */
extern __declspec(__nothrow) time_t time(time_t * /*timer*/);
   /*
    * determines the current calendar time. The encoding of the value is
    * unspecified.
    * Returns: the implementations best approximation to the current calendar
    *          time. The value (time_t)-1 is returned if the calendar time is
    *          not available. If timer is not a null pointer, the return value
    *          is also assigned to the object it points to.
    */

extern __declspec(__nothrow) char *asctime(const struct tm * /*timeptr*/) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) char *_asctime_r(const struct tm * /*timeptr*/,
                                char * __restrict /*buf*/) __attribute__((__nonnull__(1,2)));

extern __declspec(__nothrow) char *asctime_r(const struct tm * /*timeptr*/,
                               char * __restrict /*buf*/) __attribute__((__nonnull__(1,2)));

   /*
    * converts the broken-down time in the structure pointed to by timeptr into
    * a string in the form "Sun Sep 16 01:03:52 1973\n\0".
    * Returns: a pointer to the string containing the date and time.
    */
extern __declspec(__nothrow) char *ctime(const time_t * /*timer*/) __attribute__((__nonnull__(1)));
   /*
    * converts the calendar time pointed to by timer to local time in the form
    * of a string. It is equivalent to asctime(localtime(timer));
    * Returns: the pointer returned by the asctime function with that
    *          broken-down time as argument.
    */
extern __declspec(__nothrow) struct tm *gmtime(const time_t * /*timer*/) __attribute__((__nonnull__(1)));
   /*
    * converts the calendar time pointed to by timer into a broken-down time,
    * expressed as Greenwich Mean Time (GMT).
    * Returns: a pointer to that object or a null pointer if GMT not available.
    */
extern __declspec(__nothrow) struct tm *localtime(const time_t * /*timer*/) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) struct tm *_localtime_r(const time_t * __restrict /*timer*/,
                                       struct tm * __restrict /*result*/) __attribute__((__nonnull__(1,2)));

extern __declspec(__nothrow) struct tm *localtime_r(const time_t * __restrict /*timer*/,
                                      struct tm * __restrict /*result*/) __attribute__((__nonnull__(1,2)));

   /*
    * converts the calendar time pointed to by timer into a broken-down time,
    * expressed a local time.
    * Returns: a pointer to that object.
    */
extern __declspec(__nothrow) size_t strftime(char * __restrict /*s*/, size_t /*maxsize*/,
                       const char * __restrict /*format*/,
                       const struct tm * __restrict /*timeptr*/) __attribute__((__nonnull__(1,3,4)));
   /*
    * places characters into the array pointed to by s as controlled by the
    * string pointed to by format. The format string consists of zero or more
    * directives and ordinary characters. A directive consists of a % character
    * followed by a character that determines the directive's behaviour. All
    * ordinary characters (including the terminating null character) are copied
    * unchanged into the array. No more than maxsize characters are placed into
    * the array. Each directive is replaced by appropriate characters  as
    * described in the following list. The appropriate characters are
    * determined by the LC_TIME category of the current locale and by the
    * values contained in the structure pointed to by timeptr.
    * %a is replaced by the locale's abbreviated weekday name.
    * %A is replaced by the locale's full weekday name.
    * %b is replaced by the locale's abbreviated month name.
    * %B is replaced by the locale's full month name.
    * %c is replaced by the locale's appropriate date and time representation.
    * %d is replaced by the day of the month as a decimal number (01-31).
    * %H is replaced by the hour (24-hour clock) as a decimal number (00-23).
    * %I is replaced by the hour (12-hour clock) as a decimal number (01-12).
    * %j is replaced by the day of the year as a decimal number (001-366).
    * %m is replaced by the month as a decimal number (01-12).
    * %M is replaced by the minute as a decimal number (00-59).
    * %p is replaced by the locale's equivalent of either AM or PM designations
    *       associated with a 12-hour clock.
    * %S is replaced by the second as a decimal number (00-61).
    * %U is replaced by the week number of the year (Sunday as the first day of
    *       week 1) as a decimal number (00-53).
    * %w is replaced by the weekday as a decimal number (0(Sunday) - 6).
    * %W is replaced by the week number of the year (Monday as the first day of
    *       week 1) as a decimal number (00-53).
    * %x is replaced by the locale's appropriate date representation.
    * %X is replaced by the locale's appropriate time representation.
    * %y is replaced by the year without century as a decimal number (00-99).
    * %Y is replaced by the year with century as a decimal number.
    * %Z is replaced by the timezone name or abbreviation, or by no characters
    *       if no time zone is determinable.
    * %% is replaced by %.
    * If a directive is not one of the above, the behaviour is undefined.
    * Returns: If the total number of resulting characters including the
    *          terminating null character is not more than maxsize, the
    *          strftime function returns the number of characters placed into
    *          the array pointed to by s not including the terminating null
    *          character. otherwise, zero is returned and the contents of the
    *          array are indeterminate.
    */











# 280 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"



/* end of time.h */

# 12 "L:/PLT/pcac/lwm2m_wakaama/adaptor/lwm2m_config.h"

/************************/
/*platform macro control*/
/************************/


/************************/
/*mode macro control*/
/************************/
//#define LWM2M_SERVER_MODE
//#define LWM2M_BOOTSTRAP_SERVER_MODE




/************************/
/*fucntion macro control*/
/************************/


//#define LWM2M_MEMORY_TRACE




//#undef time_t
//#define time_t unsigned int





# 71 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"

# 82 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"

# 90 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"



/* TLV is mandatory for LWM2M 1.0 client and server. */
/* TLV is mandatory for LWM2M 1.1 server. */








/*
 * Platform abstraction functions to be implemented by the user
 */


// Allocate a block of size bytes of memory, returning a pointer to the beginning of the block.
void * lwm2m_malloc(size_t s);
// Deallocate a block of memory previously allocated by lwm2m_malloc() or lwm2m_strdup()
void lwm2m_free(void * p);
// Allocate a memory block, duplicate the string str in it and return a pointer to this new block.
char * lwm2m_strdup(const char * str);
# 124 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"
// Compare at most the n first bytes of s1 and s2, return 0 if they match
int lwm2m_strncmp(const char * s1, const char * s2, size_t n);
int lwm2m_strcasecmp(const char * str1, const char * str2);
// This function must return the number of seconds elapsed since origin.
// The origin (Epoch, system boot, etc...) does not matter as this
// function is used only to determine the elapsed time since the last
// call to it.
// In case of error, this must return a negative value.
// Per POSIX specifications, time_t is a signed integer.
time_t lwm2m_gettime(void);
int lwm2m_rand(void *output, size_t len);


// Same usage as C89 printf()
void lwm2m_printf(const char * format, ...);


typedef struct _lwm2m_context_ lwm2m_context_t;

// communication layer

// Returns a session handle that MUST uniquely identify a peer.
// contextP: Pointer to the LWM2M context
// secObjInstID: ID of the Securty Object instance to open a connection to
// userData: parameter to lwm2m_init()
void * lwm2m_connect_server(uint16_t secObjInstID, void * userData);
// Close a session created by lwm2m_connect_server()
// sessionH: session handle identifying the peer (opaque to the core)
// userData: parameter to lwm2m_init()
void lwm2m_close_connection(void * sessionH, void * userData);

// Send data to a peer
// Returns COAP_NO_ERROR or a COAP_NNN error code
// sessionH: session handle identifying the peer (opaque to the core)
// buffer, length: data to send
// userData: parameter to lwm2m_init()
uint8_t lwm2m_buffer_send(void * sessionH, uint8_t * buffer, size_t length, void * userData);
// Compare two session handles
// Returns true if the two sessions identify the same peer. false otherwise.
// userData: parameter to lwm2m_init()
_Bool lwm2m_session_is_equal(void * session1, void * session2, void * userData);

/*
 * Error code
 */





# 191 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"

/*
 * Standard Object IDs
 */
# 204 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"

/*
 * Resource IDs for the LWM2M Security Object
 */
# 221 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"

/*
 * Resource IDs for the LWM2M Server Object
 */
# 249 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"







/*
 * Utility functions for sorted linked list
 */

typedef struct _lwm2m_list_t
{
    struct _lwm2m_list_t * next;
    uint16_t    id;
} lwm2m_list_t;

// defined in list.c
// Add 'node' to the list 'head' and return the new list
lwm2m_list_t * lwm2m_list_add(lwm2m_list_t * head, lwm2m_list_t * node);
// Return the node with ID 'id' from the list 'head' or NULL if not found
lwm2m_list_t * lwm2m_list_find(lwm2m_list_t * head, uint16_t id);
// Remove the node with ID 'id' from the list 'head' and return the new list
lwm2m_list_t * lwm2m_list_remove(lwm2m_list_t * head, uint16_t id, lwm2m_list_t ** nodeP);
// Return the lowest unused ID in the list 'head'
uint16_t lwm2m_list_newId(lwm2m_list_t * head);
// Free a list. Do not use if nodes contain allocated pointers as it calls lwm2m_free on nodes only.
// If the nodes of the list need to do more than just "free()" their instances, don't use lwm2m_list_free().
void lwm2m_list_free(lwm2m_list_t * head);






/*
 * Helper functions for CoAP block size settings.
 */
_Bool lwm2m_set_coap_block_size(uint16_t coap_block_size_arg);
uint16_t lwm2m_get_coap_block_size(void);

/*
 * URI
 *
 * objectId is always set
 * instanceId or resourceId are set according to the flag bit-field
 *
 */



# 306 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"

typedef struct
{
    uint16_t    objectId;
    uint16_t    instanceId;
    uint16_t    resourceId;



} lwm2m_uri_t;





// Parse an URI in LWM2M format and fill the lwm2m_uri_t.
// Return the number of characters read from buffer or 0 in case of error.
// Valid URIs: /1, /1/, /1/2, /1/2/, /1/2/3
// Invalid URIs: /, //, //2, /1//, /1//3, /1/2/3/, /1/2/3/4
int lwm2m_stringToUri(const char * buffer, size_t buffer_len, lwm2m_uri_t * uriP);

/*
 * The lwm2m_data_t is used to store LWM2M resource values in a hierarchical way.
 * Depending on the type the value is different:
 * - LWM2M_TYPE_OBJECT, LWM2M_TYPE_OBJECT_INSTANCE, LWM2M_TYPE_MULTIPLE_RESOURCE: value.asChildren
 * - LWM2M_TYPE_STRING, LWM2M_TYPE_OPAQUE, LWM2M_TYPE_CORE_LINK: value.asBuffer
 * - LWM2M_TYPE_INTEGER, LWM2M_TYPE_TIME: value.asInteger
 * - LWM2M_TYPE_UNSIGNED_INTEGER: value.asUnsigned
 * - LWM2M_TYPE_FLOAT: value.asFloat
 * - LWM2M_TYPE_BOOLEAN: value.asBoolean
 *
 * LWM2M_TYPE_STRING is also used when the data is in text format.
 */

typedef enum
{
    LWM2M_TYPE_UNDEFINED = 0,
    LWM2M_TYPE_OBJECT,
    LWM2M_TYPE_OBJECT_INSTANCE,
    LWM2M_TYPE_MULTIPLE_RESOURCE,

    LWM2M_TYPE_STRING,
    LWM2M_TYPE_OPAQUE,
    LWM2M_TYPE_INTEGER,
    LWM2M_TYPE_UNSIGNED_INTEGER,
    LWM2M_TYPE_FLOAT,
    LWM2M_TYPE_BOOLEAN,

    LWM2M_TYPE_OBJECT_LINK,
    LWM2M_TYPE_CORE_LINK
} lwm2m_data_type_t;

typedef struct _lwm2m_data_t lwm2m_data_t;

struct _lwm2m_data_t
{
    lwm2m_data_type_t type;
    uint16_t    id;
    union
    {
        _Bool        asBoolean;
        int64_t     asInteger;
        uint64_t    asUnsigned;
        double      asFloat;
        struct
        {
            size_t    length;
            uint8_t * buffer;
        } asBuffer;
        struct
        {
            size_t         count;
            lwm2m_data_t * array;
        } asChildren;
        struct
        {
            uint16_t objectId;
            uint16_t objectInstanceId;
        } asObjLink;
    } value;
};

typedef enum
{
    LWM2M_CONTENT_TEXT       = 0,        // Also used as undefined
    LWM2M_CONTENT_LINK       = 40,
    LWM2M_CONTENT_OPAQUE     = 42,
    LWM2M_CONTENT_TLV_OLD    = 1542,     // Keep old value for backward-compatibility
    LWM2M_CONTENT_TLV        = 11542,
    LWM2M_CONTENT_JSON_OLD   = 1543,     // Keep old value for backward-compatibility
    LWM2M_CONTENT_JSON       = 11543,
    LWM2M_CONTENT_SENML_JSON = 110
} lwm2m_media_type_t;

lwm2m_data_t * lwm2m_data_new(int size);
int lwm2m_data_parse(lwm2m_uri_t * uriP, const uint8_t * buffer, size_t bufferLen, lwm2m_media_type_t format, lwm2m_data_t ** dataP);
int lwm2m_data_serialize(lwm2m_uri_t * uriP, int size, lwm2m_data_t * dataP, lwm2m_media_type_t * formatP, uint8_t ** bufferP);
void lwm2m_data_free(int size, lwm2m_data_t * dataP);

void lwm2m_data_encode_string(const char * string, lwm2m_data_t * dataP);
void lwm2m_data_encode_nstring(const char * string, size_t length, lwm2m_data_t * dataP);
void lwm2m_data_encode_opaque(const uint8_t * buffer, size_t length, lwm2m_data_t * dataP);
void lwm2m_data_encode_int(int64_t value, lwm2m_data_t * dataP);
int lwm2m_data_decode_int(const lwm2m_data_t * dataP, int64_t * valueP);
void lwm2m_data_encode_uint(uint64_t value, lwm2m_data_t * dataP);
int lwm2m_data_decode_uint(const lwm2m_data_t * dataP, uint64_t * valueP);
void lwm2m_data_encode_float(double value, lwm2m_data_t * dataP);
int lwm2m_data_decode_float(const lwm2m_data_t * dataP, double * valueP);
void lwm2m_data_encode_bool(_Bool value, lwm2m_data_t * dataP);
int lwm2m_data_decode_bool(const lwm2m_data_t * dataP, _Bool * valueP);
void lwm2m_data_encode_objlink(uint16_t objectId, uint16_t objectInstanceId, lwm2m_data_t * dataP);
void lwm2m_data_encode_corelink(const char * corelink, lwm2m_data_t * dataP);
void lwm2m_data_encode_instances(lwm2m_data_t * subDataP, size_t count, lwm2m_data_t * dataP);
void lwm2m_data_include(lwm2m_data_t * subDataP, size_t count, lwm2m_data_t * dataP);


/*
 * Utility function to parse TLV buffers directly
 *
 * Returned value: number of bytes parsed
 * buffer: buffer to parse
 * buffer_len: length in bytes of buffer
 * oType: (OUT) type of the parsed TLV record. can be:
 *          - LWM2M_TYPE_OBJECT
 *          - LWM2M_TYPE_OBJECT_INSTANCE
 *          - LWM2M_TYPE_MULTIPLE_RESOURCE
 *          - LWM2M_TYPE_OPAQUE
 * oID: (OUT) ID of the parsed TLV record
 * oDataIndex: (OUT) index of the data of the parsed TLV record in the buffer
 * oDataLen: (OUT) length of the data of the parsed TLV record
 */



int lwm2m_decode_TLV(const uint8_t * buffer, size_t buffer_len, lwm2m_data_type_t * oType, uint16_t * oID, size_t * oDataIndex, size_t * oDataLen);


/*
 * LWM2M Objects
 *
 * For the read callback, if *numDataP is not zero, *dataArrayP is pre-allocated
 * and contains the list of resources to read.
 *
 */

typedef struct _lwm2m_object_t lwm2m_object_t;

typedef enum
{
    LWM2M_WRITE_PARTIAL_UPDATE,     // Write should add or update resources and resource instances.
    LWM2M_WRITE_REPLACE_RESOURCES,  // Write should replace resources entirely.
    LWM2M_WRITE_REPLACE_INSTANCE,   // Write should replace the entire instance.
} lwm2m_write_type_t;

typedef uint8_t (*lwm2m_read_callback_t) (lwm2m_context_t * contextP, uint16_t instanceId, int * numDataP, lwm2m_data_t ** dataArrayP, lwm2m_object_t * objectP);
typedef uint8_t (*lwm2m_discover_callback_t) (lwm2m_context_t * contextP, uint16_t instanceId, int * numDataP, lwm2m_data_t ** dataArrayP, lwm2m_object_t * objectP);
typedef uint8_t (*lwm2m_write_callback_t) (lwm2m_context_t * contextP, uint16_t instanceId, int numData, lwm2m_data_t * dataArray, lwm2m_object_t * objectP, lwm2m_write_type_t writeType);
typedef uint8_t (*lwm2m_execute_callback_t) (lwm2m_context_t * contextP, uint16_t instanceId, uint16_t resourceId, uint8_t * buffer, int length, lwm2m_object_t * objectP);
typedef uint8_t (*lwm2m_create_callback_t) (lwm2m_context_t * contextP, uint16_t instanceId, int numData, lwm2m_data_t * dataArray, lwm2m_object_t * objectP);





typedef uint8_t (*lwm2m_delete_callback_t) (lwm2m_context_t * contextP, uint16_t instanceId, lwm2m_object_t * objectP);

struct _lwm2m_object_t
{
    struct _lwm2m_object_t * next;           // for internal use only.
    uint16_t       objID;
    uint8_t        versionMajor;
    uint8_t        versionMinor;
    lwm2m_list_t * instanceList;
    lwm2m_read_callback_t     readFunc;
    lwm2m_write_callback_t    writeFunc;
    lwm2m_execute_callback_t  executeFunc;
    lwm2m_create_callback_t   createFunc;





    lwm2m_delete_callback_t   deleteFunc;
    lwm2m_discover_callback_t discoverFunc;
    void * userData;
};

/*
 * LWM2M Servers
 *
 * Since LWM2M Server Object instances are not accessible to LWM2M servers,
 * there is no need to store them as lwm2m_objects_t
 */

typedef enum
{
    STATE_DEREGISTERED = 0,        // not registered or bootstrap not started
    STATE_REG_HOLD_OFF,            // initial registration delay or delay between retries
    STATE_REG_PENDING,             // registration pending
    STATE_REGISTERED,              // successfully registered
    STATE_REG_FAILED,              // last registration failed
    STATE_REG_UPDATE_PENDING,      // registration update pending
    STATE_REG_UPDATE_NEEDED,       // registration update required
    STATE_REG_FULL_UPDATE_NEEDED,  // registration update with objects required
    STATE_DEREG_PENDING,           // deregistration pending
    STATE_BS_HOLD_OFF,             // bootstrap hold off time
    STATE_BS_INITIATED,            // bootstrap request sent
    STATE_BS_PENDING,              // bootstrap ongoing
    STATE_BS_FINISHING,            // bootstrap finish received
    STATE_BS_FINISHED,             // bootstrap done
    STATE_BS_FAILING,              // bootstrap error occurred
    STATE_BS_FAILED,               // bootstrap failed
} lwm2m_status_t;

typedef enum
{
    VERSION_MISSING = 0,  // Version number not in registration.
    VERSION_UNRECOGNIZED, // Version number in registration not recognized.
    VERSION_1_0,          // LWM2M version 1.0
    VERSION_1_1,          // LWM2M version 1.1
} lwm2m_version_t;

# 534 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"
/* Legacy bindings */




typedef uint8_t lwm2m_binding_t;

/*
 * LWM2M block data
 *
 * Temporary data needed to handle block1 request and block2 responses.
 */
typedef enum
{
    BLOCK_1,
    BLOCK_2,
} block_type_t;

typedef union _block_data_identifier_
{
    char * uri;                               // resource string if block1 
    int32_t mid;                                    // mid of the last request if block2 eg the mid for the expected block
} block_data_identifier_t;


typedef struct _lwm2m_block_data_ lwm2m_block_data_t;

struct _lwm2m_block_data_
{
    struct _lwm2m_block_data_ *     next;
    block_type_t                    blockType;
    block_data_identifier_t         identifier;
    uint8_t *                       blockBuffer;        // data buffer
    size_t                          blockBufferSize;    // buffer size
    uint32_t                        blockNum;           // block num of the last message received



};


typedef struct _lwm2m_server_
{
    struct _lwm2m_server_ * next;         // matches lwm2m_list_t::next
    uint16_t                secObjInstID; // matches lwm2m_list_t::id
    uint16_t                shortID;      // servers short ID, may be 0 for bootstrap server
    time_t                  lifetime;     // lifetime of the registration in sec or 0 if default value (86400 sec), also used as hold off time for bootstrap servers
    time_t                  registration; // date of the last registration in sec or end of client hold off time for bootstrap servers or end of hold off time for registration holds.
    lwm2m_binding_t         binding;      // client connection mode with this server
    void *                  sessionH;
    lwm2m_status_t          status;
    char *                  location;
    _Bool                    dirty;
    lwm2m_block_data_t *    blockData;   // list to handle temporary block data.





} lwm2m_server_t;

typedef struct _block_info_t
{
    int block_num;
    int block_size;
    _Bool block_more;
} block_info_t;

/*
 * LWM2M result callback
 *
 * When used with an observe, if 'data' is not nil, 'status' holds the observe counter.
 */
typedef void (*lwm2m_result_callback_t)(lwm2m_context_t *contextP, uint16_t clientID, lwm2m_uri_t *uriP, int status,
                                        block_info_t *block_info, lwm2m_media_type_t format, uint8_t *data,
                                        size_t dataLength, void *userData);

/*
 * LWM2M Observations
 *
 * Used to store latest user operation on the observation of remote clients resources.
 * Any node in the observation list means observation was established with client already.
 * status STATE_REG_PENDING means the observe request was sent to the client but not yet answered.
 * status STATE_REGISTERED means the client acknowledged the observe request.
 * status STATE_DEREG_PENDING means the user canceled the request before the client answered it.
 */

typedef struct _lwm2m_observation_
{
    struct _lwm2m_observation_ * next;  // matches lwm2m_list_t::next
    uint16_t                     id;    // matches lwm2m_list_t::id
    struct _lwm2m_client_ * clientP;
    lwm2m_uri_t             uri;
    lwm2m_status_t          status;     // latest user operation
    lwm2m_result_callback_t callback;
    void *                  userData;
} lwm2m_observation_t;

/*
 * LWM2M Link Attributes
 *
 * Used for observation parameters.
 *
 */







typedef struct
{
    uint8_t     toSet;
    uint8_t     toClear;
    uint32_t    minPeriod;
    uint32_t    maxPeriod;
    double      greaterThan;
    double      lessThan;
    double      step;
} lwm2m_attributes_t;

/*
 * LWM2M Clients
 *
 * Be careful not to mix lwm2m_client_object_t used to store list of objects of remote clients
 * and lwm2m_object_t describing objects exposed to remote servers.
 *
 */

typedef struct _lwm2m_client_object_
{
    struct _lwm2m_client_object_ * next; // matches lwm2m_list_t::next
    uint16_t                 id;         // matches lwm2m_list_t::id
    uint8_t                  versionMajor;
    uint8_t                  versionMinor;
    lwm2m_list_t *           instanceList;
} lwm2m_client_object_t;

typedef struct _lwm2m_client_
{
    struct _lwm2m_client_ * next;       // matches lwm2m_list_t::next
    uint16_t                internalID; // matches lwm2m_list_t::id
    char *                  name;
    lwm2m_version_t         version;
    lwm2m_binding_t         binding;
    char *                  msisdn;
    char *                  altPath;
    lwm2m_media_type_t      format;
    uint32_t                lifetime;
    time_t                  endOfLife;
    void *                  sessionH;
    lwm2m_client_object_t * objectList;
    lwm2m_observation_t *   observationList;
    uint16_t                observationId;
    lwm2m_block_data_t *    blockData;   // list to handle temporary block data.
} lwm2m_client_t;


/*
 * LWM2M transaction
 *
 * Adaptation of Erbium's coap_transaction_t
 */

typedef struct _lwm2m_transaction_ lwm2m_transaction_t;

typedef void (*lwm2m_transaction_callback_t) (lwm2m_context_t * contextP, lwm2m_transaction_t * transacP, void * message);

struct _lwm2m_transaction_
{
    lwm2m_transaction_t * next;  // matches lwm2m_list_t::next
    uint16_t              mID;   // matches lwm2m_list_t::id
    void *                peerH;
    uint8_t               ack_received; // indicates, that the ACK was received
    time_t                response_timeout; // timeout to wait for response, if token is used. When 0, use calculated acknowledge timeout.
    uint8_t  retrans_counter;
    time_t   retrans_time;
    void * message;
    uint16_t buffer_len;
    uint8_t * buffer;
    size_t
        payload_len;  // the length of the entire payload, message payload might be smaller in case of a block1 transfer
    uint8_t *payload; // carries the entire payload across multiple transactions in case of a block 1 transfer
    lwm2m_transaction_callback_t callback;
    void * userData;
};

/*
 * LWM2M observed resources
 */
typedef struct _lwm2m_watcher_
{
    struct _lwm2m_watcher_ * next;

    _Bool active;
    _Bool update;
    lwm2m_server_t * server;
    lwm2m_attributes_t * parameters;
    lwm2m_media_type_t format;
    uint8_t token[8];
    size_t tokenLen;
    time_t lastTime;
    uint32_t counter;
    uint16_t lastMid;
    union
    {
        int64_t asInteger;
        uint64_t asUnsigned;
        double  asFloat;
    } lastValue;
} lwm2m_watcher_t;

typedef struct _lwm2m_observed_
{
    struct _lwm2m_observed_ * next;

    lwm2m_uri_t uri;
    lwm2m_watcher_t * watcherList;
} lwm2m_observed_t;



typedef enum
{
    STATE_INITIAL = 0,
    STATE_BOOTSTRAP_REQUIRED,
    STATE_BOOTSTRAPPING,
    STATE_REGISTER_REQUIRED,
    STATE_REGISTERING,
    STATE_READY
} lwm2m_client_state_t;


/*
 * LWM2M Context
 */

# 785 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"

struct _lwm2m_context_
{

    lwm2m_client_state_t state;
    char *               endpointName;
    char *               msisdn;
    char *               altPath;
    lwm2m_server_t *     bootstrapServerList;
    lwm2m_server_t *     serverList;
    lwm2m_object_t *     objectList;
    lwm2m_observed_t *   observedList;
# 809 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"
    uint16_t                nextMID;
    lwm2m_transaction_t *   transactionList;
    void *                  userData;
};

# 832 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"

# 841 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"

# 850 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"




// initialize a liblwm2m context.
lwm2m_context_t * lwm2m_init(void * userData);
// close a liblwm2m context.
void lwm2m_close(lwm2m_context_t * contextP);

// perform any required pending operation and adjust timeoutP to the maximal time interval to wait in seconds.
int lwm2m_step(lwm2m_context_t * contextP, time_t * timeoutP);
// dispatch received data to liblwm2m
void lwm2m_handle_packet(lwm2m_context_t *contextP, uint8_t *buffer, size_t length, void *fromSessionH);


// configure the client side with the Endpoint Name, binding, MSISDN (can be nil), alternative path
// for objects (can be nil) and a list of objects.
// LWM2M Security Object (ID 0) must be present with either a bootstrap server or a LWM2M server and
// its matching LWM2M Server Object (ID 1) instance
int lwm2m_configure(lwm2m_context_t * contextP, const char * endpointName, const char * msisdn, const char * altPath, uint16_t numObject, lwm2m_object_t * objectList[]);
int lwm2m_add_object(lwm2m_context_t * contextP, lwm2m_object_t * objectP);
int lwm2m_remove_object(lwm2m_context_t * contextP, uint16_t id);

// send a registration update to the server specified by the server short identifier
// or all if the ID is 0.
// If withObjects is true, the registration update contains the object list.
int lwm2m_update_registration(lwm2m_context_t * contextP, uint16_t shortServerID, _Bool withObjects);
// send deregistration to all servers connected to client
void lwm2m_deregister(lwm2m_context_t * context);
void lwm2m_resource_value_changed(lwm2m_context_t * contextP, lwm2m_uri_t * uriP);


# 907 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"

# 923 "L:/PLT/pcac/lwm2m_wakaama/include/liblwm2m.h"





# 49 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"

# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"
/* stdio.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.9 */
/* Copyright (C) Codemist Ltd., 1988-1993                       */
/* Copyright 1991-1998 ARM Limited. All rights reserved.        */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: sdouglas $
 */

/*
 * stdio.h declares two types, several macros, and many functions for
 * performing input and output. For a discussion on Streams and Files
 * refer to sections 4.9.2 and 4.9.3 in the above ANSI draft, or to a
 * modern textbook on C.
 */





/*
 * Depending on compiler version __int64 or __INT64_TYPE__ should be defined.
 */




  /* On some architectures neither of these may be defined - if so, fall
     through and error out if used. */








# 47 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"


 /* always defined in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */









/* ANSI forbids va_list to be defined here */
/* keep in step with <stdarg.h> and <wchar.h> */

/* always defined in C++ and non-strict C for consistency of debug info */



    typedef struct __va_list __va_list;






   /*
    * If the compiler supports signalling nans as per N965 then it
    * will define __SUPPORT_SNAN__, in which case a user may define
    * _WANT_SNAN in order to obtain compliant versions of the printf
    * and scanf families of functions
    */




typedef struct __fpos_t_struct {
    unsigned __int64 __pos;
    /*
     * this structure is equivalent to an mbstate_t, but we're not
     * allowed to actually define the type name `mbstate_t' within
     * stdio.h
     */
    struct {
        unsigned int __state1, __state2;
    } __mbstate;
} fpos_t;
   /*
    * fpos_t is an object capable of recording all information needed to
    * specify uniquely every position within a file.
    */


   /* _SYS_OPEN defines a limit on the number of open files that is imposed
    * by this C library
    */

typedef struct __FILE FILE;
   /*
    * FILE is an object capable of recording all information needed to control
    * a stream, such as its file position indicator, a pointer to its
    * associated buffer, an error indicator that records whether a read/write
    * error has occurred and an end-of-file indicator that records whether the
    * end-of-file has been reached.
    * Its structure is not made known to library clients.
    */

# 136 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"


extern FILE __stdin, __stdout, __stderr;
extern FILE *__aeabi_stdin, *__aeabi_stdout, *__aeabi_stderr;

# 166 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"
   /* pointer to a FILE object associated with standard input stream */

   /* pointer to a FILE object associated with standard output stream */

   /* pointer to a FILE object associated with standard error stream */





    /* Various default file IO buffer sizes */



   /*
    * an integral constant expression that is the minimum number of files that
    * this implementation guarantees can be open simultaneously.
    */


   /*
    * an integral constant expression that is the size of an array of char
    * large enough to hold the longest filename string
    */

   /*
    * an integral constant expression that is the size of an array of char
    * large enough to hold a temporary file name string generated by the
    * tmpnam function.
    */

   /*
    * an integral constant expression that is the minimum number of unique
    * file names that shall be generated by the tmpnam function.
    */




   /*
    * negative integral constant, indicates end-of-file, that is, no more input
    * from a stream.
    */





    /*
     * _IOBIN is the flag passed to _sys_write to denote a binary
     * file.
     */






extern __declspec(__nothrow) int remove(const char * /*filename*/) __attribute__((__nonnull__(1)));
   /*
    * causes the file whose name is the string pointed to by filename to be
    * removed. Subsequent attempts to open the file will fail, unless it is
    * created anew. If the file is open, the behaviour of the remove function
    * is implementation-defined.
    * Returns: zero if the operation succeeds, nonzero if it fails.
    */
extern __declspec(__nothrow) int rename(const char * /*old*/, const char * /*new*/) __attribute__((__nonnull__(1,2)));
   /*
    * causes the file whose name is the string pointed to by old to be
    * henceforth known by the name given by the string pointed to by new. The
    * file named old is effectively removed. If a file named by the string
    * pointed to by new exists prior to the call of the rename function, the
    * behaviour is implementation-defined.
    * Returns: zero if the operation succeeds, nonzero if it fails, in which
    *          case if the file existed previously it is still known by its
    *          original name.
    */
extern __declspec(__nothrow) FILE *tmpfile(void);
   /*
    * creates a temporary binary file that will be automatically removed when
    * it is closed or at program termination. The file is opened for update.
    * Returns: a pointer to the stream of the file that it created. If the file
    *          cannot be created, a null pointer is returned.
    */
extern __declspec(__nothrow) char *tmpnam(char * /*s*/);
   /*
    * generates a string that is not the same as the name of an existing file.
    * The tmpnam function generates a different string each time it is called,
    * up to TMP_MAX times. If it is called more than TMP_MAX times, the
    * behaviour is implementation-defined.
    * Returns: If the argument is a null pointer, the tmpnam function leaves
    *          its result in an internal static object and returns a pointer to
    *          that object. Subsequent calls to the tmpnam function may modify
    *          the same object. if the argument is not a null pointer, it is
    *          assumed to point to an array of at least L_tmpnam characters;
    *          the tmpnam function writes its result in that array and returns
    *          the argument as its value.
    */

extern __declspec(__nothrow) int fclose(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * causes the stream pointed to by stream to be flushed and the associated
    * file to be closed. Any unwritten buffered data for the stream are
    * delivered to the host environment to be written to the file; any unread
    * buffered data are discarded. The stream is disassociated from the file.
    * If the associated buffer was automatically allocated, it is deallocated.
    * Returns: zero if the stream was succesfully closed, or nonzero if any
    *          errors were detected or if the stream was already closed.
    */
extern __declspec(__nothrow) int fflush(FILE * /*stream*/);
   /*
    * If the stream points to an output or update stream in which the most
    * recent operation was output, the fflush function causes any unwritten
    * data for that stream to be delivered to the host environment to be
    * written to the file. If the stream points to an input or update stream,
    * the fflush function undoes the effect of any preceding ungetc operation
    * on the stream.
    * Returns: nonzero if a write error occurs.
    */
extern __declspec(__nothrow) FILE *fopen(const char * __restrict /*filename*/,
                           const char * __restrict /*mode*/) __attribute__((__nonnull__(1,2)));
   /*
    * opens the file whose name is the string pointed to by filename, and
    * associates a stream with it.
    * The argument mode points to a string beginning with one of the following
    * sequences:
    * "r"         open text file for reading
    * "w"         create text file for writing, or truncate to zero length
    * "a"         append; open text file or create for writing at eof
    * "rb"        open binary file for reading
    * "wb"        create binary file for writing, or truncate to zero length
    * "ab"        append; open binary file or create for writing at eof
    * "r+"        open text file for update (reading and writing)
    * "w+"        create text file for update, or truncate to zero length
    * "a+"        append; open text file or create for update, writing at eof
    * "r+b"/"rb+" open binary file for update (reading and writing)
    * "w+b"/"wb+" create binary file for update, or truncate to zero length
    * "a+b"/"ab+" append; open binary file or create for update, writing at eof
    *
    * Opening a file with read mode ('r' as the first character in the mode
    * argument) fails if the file does not exist or cannot be read.
    * Opening a file with append mode ('a' as the first character in the mode
    * argument) causes all subsequent writes to be forced to the current end of
    * file, regardless of intervening calls to the fseek function. In some
    * implementations, opening a binary file with append mode ('b' as the
    * second or third character in the mode argument) may initially position
    * the file position indicator beyond the last data written, because of the
    * NUL padding.
    * When a file is opened with update mode ('+' as the second or third
    * character in the mode argument), both input and output may be performed
    * on the associated stream. However, output may not be directly followed
    * by input without an intervening call to the fflush fuction or to a file
    * positioning function (fseek, fsetpos, or rewind), and input be not be
    * directly followed by output without an intervening call to the fflush
    * fuction or to a file positioning function, unless the input operation
    * encounters end-of-file. Opening a file with update mode may open or
    * create a binary stream in some implementations. When opened, a stream
    * is fully buffered if and only if it does not refer to an interactive
    * device. The error and end-of-file indicators for the stream are
    * cleared.
    * Returns: a pointer to the object controlling the stream. If the open
    *          operation fails, fopen returns a null pointer.
    */
extern __declspec(__nothrow) FILE *freopen(const char * __restrict /*filename*/,
                    const char * __restrict /*mode*/,
                    FILE * __restrict /*stream*/) __attribute__((__nonnull__(2,3)));
   /*
    * opens the file whose name is the string pointed to by filename and
    * associates the stream pointed to by stream with it. The mode argument is
    * used just as in the fopen function.
    * The freopen function first attempts to close any file that is associated
    * with the specified stream. Failure to close the file successfully is
    * ignored. The error and end-of-file indicators for the stream are cleared.
    * Returns: a null pointer if the operation fails. Otherwise, freopen
    *          returns the value of the stream.
    */
extern __declspec(__nothrow) void setbuf(FILE * __restrict /*stream*/,
                    char * __restrict /*buf*/) __attribute__((__nonnull__(1)));
   /*
    * Except that it returns no value, the setbuf function is equivalent to the
    * setvbuf function invoked with the values _IOFBF for mode and BUFSIZ for
    * size, or (if buf is a null pointer), with the value _IONBF for mode.
    * Returns: no value.
    */
extern __declspec(__nothrow) int setvbuf(FILE * __restrict /*stream*/,
                   char * __restrict /*buf*/,
                   int /*mode*/, size_t /*size*/) __attribute__((__nonnull__(1)));
   /*
    * may be used after the stream pointed to by stream has been associated
    * with an open file but before it is read or written. The argument mode
    * determines how stream will be buffered, as follows: _IOFBF causes
    * input/output to be fully buffered; _IOLBF causes output to be line
    * buffered (the buffer will be flushed when a new-line character is
    * written, when the buffer is full, or when input is requested); _IONBF
    * causes input/output to be completely unbuffered. If buf is not the null
    * pointer, the array it points to may be used instead of an automatically
    * allocated buffer (the buffer must have a lifetime at least as great as
    * the open stream, so the stream should be closed before a buffer that has
    * automatic storage duration is deallocated upon block exit). The argument
    * size specifies the size of the array. The contents of the array at any
    * time are indeterminate.
    * Returns: zero on success, or nonzero if an invalid value is given for
    *          mode or size, or if the request cannot be honoured.
    */
#pragma __printf_args
extern __declspec(__nothrow) int fprintf(FILE * __restrict /*stream*/,
                    const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * writes output to the stream pointed to by stream, under control of the
    * string pointed to by format that specifies how subsequent arguments are
    * converted for output. If there are insufficient arguments for the format,
    * the behaviour is undefined. If the format is exhausted while arguments
    * remain, the excess arguments are evaluated but otherwise ignored. The
    * fprintf function returns when the end of the format string is reached.
    * The format shall be a multibyte character sequence, beginning and ending
    * in its initial shift state. The format is composed of zero or more
    * directives: ordinary multibyte characters (not %), which are copied
    * unchanged to the output stream; and conversion specifiers, each of which
    * results in fetching zero or more subsequent arguments. Each conversion
    * specification is introduced by the character %. For a description of the
    * available conversion specifiers refer to section ******* in the ANSI
    * draft mentioned at the start of this file or to any modern textbook on C.
    * The minimum value for the maximum number of characters producable by any
    * single conversion is at least 509.
    * Returns: the number of characters transmitted, or a negative value if an
    *          output error occurred.
    */
#pragma __printf_args
extern __declspec(__nothrow) int _fprintf(FILE * __restrict /*stream*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fprintf, but does not support floating-point formats.
    * You can use instead of fprintf to improve code size.
    * Returns: as fprintf.
    */
#pragma __printf_args
extern __declspec(__nothrow) int printf(const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to fprintf with the argument stdout interposed before the
    * arguments to printf.
    * Returns: the number of characters transmitted, or a negative value if an
    *          output error occurred.
    */
#pragma __printf_args
extern __declspec(__nothrow) int _printf(const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to printf, but does not support floating-point formats.
    * You can use instead of printf to improve code size.
    * Returns: as printf.
    */
#pragma __printf_args
extern __declspec(__nothrow) int sprintf(char * __restrict /*s*/, const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fprintf, except that the argument s specifies an array
    * into which the generated output is to be written, rather than to a
    * stream. A null character is written at the end of the characters written;
    * it is not counted as part of the returned sum.
    * Returns: the number of characters written to the array, not counting the
    *          terminating null character.
    */
#pragma __printf_args
extern __declspec(__nothrow) int _sprintf(char * __restrict /*s*/, const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to sprintf, but does not support floating-point formats.
    * You can use instead of sprintf to improve code size.
    * Returns: as sprintf.
    */

#pragma __printf_args
extern __declspec(__nothrow) int __ARM_snprintf(char * __restrict /*s*/, size_t /*n*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(3)));


#pragma __printf_args
extern __declspec(__nothrow) int snprintf(char * __restrict /*s*/, size_t /*n*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(3)));
   /*
    * is equivalent to fprintf, except that the argument s specifies an array
    * into which the generated output is to be written, rather than to a
    * stream. The argument n specifies the size of the output array, so as to
    * avoid overflowing the buffer.
    * A null character is written at the end of the characters written, even
    * if the formatting was not completed; it is not counted as part of the
    * returned sum. At most n characters of the output buffer are used,
    * _including_ the null character.
    * Returns: the number of characters that would have been written to the
    *          array, not counting the terminating null character, if the
    *          array had been big enough. So if the return is >=0 and <n, then
    *          the entire string was successfully formatted; if the return is
    *          >=n, the string was truncated (but there is still a null char
    *          at the end of what was written); if the return is <0, there was
    *          an error.
    */

#pragma __printf_args
extern __declspec(__nothrow) int _snprintf(char * __restrict /*s*/, size_t /*n*/,
                      const char * __restrict /*format*/, ...) __attribute__((__nonnull__(3)));
   /*
    * is equivalent to snprintf, but does not support floating-point formats.
    * You can use instead of snprintf to improve code size.
    * Returns: as snprintf.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int fscanf(FILE * __restrict /*stream*/,
                    const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * reads input from the stream pointed to by stream, under control of the
    * string pointed to by format that specifies the admissible input sequences
    * and how thay are to be converted for assignment, using subsequent
    * arguments as pointers to the objects to receive the converted input. If
    * there are insufficient arguments for the format, the behaviour is
    * undefined. If the format is exhausted while arguments remain, the excess
    * arguments are evaluated but otherwise ignored.
    * The format is composed of zero or more directives: one or more
    * white-space characters; an ordinary character (not %); or a conversion
    * specification. Each conversion specification is introduced by the
    * character %. For a description of the available conversion specifiers
    * refer to section ******* in the ANSI draft mentioned at the start of this
    * file, or to any modern textbook on C.
    * If end-of-file is encountered during input, conversion is terminated. If
    * end-of-file occurs before any characters matching the current directive
    * have been read (other than leading white space, where permitted),
    * execution of the current directive terminates with an input failure;
    * otherwise, unless execution of the current directive is terminated with a
    * matching failure, execution of the following directive (if any) is
    * terminated with an input failure.
    * If conversions terminates on a conflicting input character, the offending
    * input character is left unread in the input strem. Trailing white space
    * (including new-line characters) is left unread unless matched by a
    * directive. The success of literal matches and suppressed asignments is
    * not directly determinable other than via the %n directive.
    * Returns: the value of the macro EOF if an input failure occurs before any
    *          conversion. Otherwise, the fscanf function returns the number of
    *          input items assigned, which can be fewer than provided for, or
    *          even zero, in the event of an early conflict between an input
    *          character and the format.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int _fscanf(FILE * __restrict /*stream*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fscanf, but does not support floating-point formats.
    * You can use instead of fscanf to improve code size.
    * Returns: as fscanf.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int scanf(const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to fscanf with the argument stdin interposed before the
    * arguments to scanf.
    * Returns: the value of the macro EOF if an input failure occurs before any
    *          conversion. Otherwise, the scanf function returns the number of
    *          input items assigned, which can be fewer than provided for, or
    *          even zero, in the event of an early matching failure.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int _scanf(const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to scanf, but does not support floating-point formats.
    * You can use instead of scanf to improve code size.
    * Returns: as scanf.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int sscanf(const char * __restrict /*s*/,
                    const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fscanf except that the argument s specifies a string
    * from which the input is to be obtained, rather than from a stream.
    * Reaching the end of the string is equivalent to encountering end-of-file
    * for the fscanf function.
    * Returns: the value of the macro EOF if an input failure occurs before any
    *          conversion. Otherwise, the scanf function returns the number of
    *          input items assigned, which can be fewer than provided for, or
    *          even zero, in the event of an early matching failure.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int _sscanf(const char * __restrict /*s*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to sscanf, but does not support floating-point formats.
    * You can use instead of sscanf to improve code size.
    * Returns: as sscanf.
    */

/* C99 additions */
extern __declspec(__nothrow) int vfscanf(FILE * __restrict /*stream*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) int vscanf(const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) int vsscanf(const char * __restrict /*s*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));

extern __declspec(__nothrow) int _vfscanf(FILE * __restrict /*stream*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) int _vscanf(const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) int _vsscanf(const char * __restrict /*s*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) int __ARM_vsscanf(const char * __restrict /*s*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));

extern __declspec(__nothrow) int vprintf(const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to printf, with the variable argument list replaced by arg,
    * which has been initialised by the va_start macro (and possibly subsequent
    * va_arg calls). The vprintf function does not invoke the va_end function.
    * Returns: the number of characters transmitted, or a negative value if an
    *          output error occurred.
    */
extern __declspec(__nothrow) int _vprintf(const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to vprintf, but does not support floating-point formats.
    * You can use instead of vprintf to improve code size.
    * Returns: as vprintf.
    */
extern __declspec(__nothrow) int vfprintf(FILE * __restrict /*stream*/,
                    const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fprintf, with the variable argument list replaced by
    * arg, which has been initialised by the va_start macro (and possibly
    * subsequent va_arg calls). The vfprintf function does not invoke the
    * va_end function.
    * Returns: the number of characters transmitted, or a negative value if an
    *          output error occurred.
    */
extern __declspec(__nothrow) int vsprintf(char * __restrict /*s*/,
                     const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to sprintf, with the variable argument list replaced by
    * arg, which has been initialised by the va_start macro (and possibly
    * subsequent va_arg calls). The vsprintf function does not invoke the
    * va_end function.
    * Returns: the number of characters written in the array, not counting the
    *          terminating null character.
    */
extern __declspec(__nothrow) int __ARM_vsnprintf(char * __restrict /*s*/, size_t /*n*/,
                     const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(3)));

extern __declspec(__nothrow) int vsnprintf(char * __restrict /*s*/, size_t /*n*/,
                     const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(3)));
   /*
    * is equivalent to snprintf, with the variable argument list replaced by
    * arg, which has been initialised by the va_start macro (and possibly
    * subsequent va_arg calls). The vsprintf function does not invoke the
    * va_end function.
    * Returns: the number of characters that would have been written in the
    *          array, not counting the terminating null character. As
    *          snprintf.
    */

extern __declspec(__nothrow) int _vsprintf(char * __restrict /*s*/,
                      const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to vsprintf, but does not support floating-point formats.
    * You can use instead of vsprintf to improve code size.
    * Returns: as vsprintf.
    */
extern __declspec(__nothrow) int _vfprintf(FILE * __restrict /*stream*/,
                     const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to vfprintf, but does not support floating-point formats.
    * You can use instead of vfprintf to improve code size.
    * Returns: as vfprintf.
    */
extern __declspec(__nothrow) int _vsnprintf(char * __restrict /*s*/, size_t /*n*/,
                      const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(3)));
   /*
    * is equivalent to vsnprintf, but does not support floating-point formats.
    * You can use instead of vsnprintf to improve code size.
    * Returns: as vsnprintf.
    */

#pragma __printf_args
extern __declspec(__nothrow) int asprintf(char ** /*strp*/, const char * __restrict /*format*/, ...) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) int vasprintf(char ** /*strp*/, const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(2)));

#pragma __printf_args
extern __declspec(__nothrow) int __ARM_asprintf(char ** /*strp*/, const char * __restrict /*format*/, ...) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) int __ARM_vasprintf(char ** /*strp*/, const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(2)));
   /*
    * dynamically allocates a buffer of the right size for the
    * formatted string, and returns it in (*strp). Formal return value
    * is the same as any other printf variant, except that it returns
    * -1 if the buffer could not be allocated.
    *
    * (The functions with __ARM_ prefixed names are identical to the
    * ones without, but are available in all compilation modes without
    * violating user namespace.)
    */

extern __declspec(__nothrow) int fgetc(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * obtains the next character (if present) as an unsigned char converted to
    * an int, from the input stream pointed to by stream, and advances the
    * associated file position indicator (if defined).
    * Returns: the next character from the input stream pointed to by stream.
    *          If the stream is at end-of-file, the end-of-file indicator is
    *          set and fgetc returns EOF. If a read error occurs, the error
    *          indicator is set and fgetc returns EOF.
    */
extern __declspec(__nothrow) char *fgets(char * __restrict /*s*/, int /*n*/,
                    FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,3)));
   /*
    * reads at most one less than the number of characters specified by n from
    * the stream pointed to by stream into the array pointed to by s. No
    * additional characters are read after a new-line character (which is
    * retained) or after end-of-file. A null character is written immediately
    * after the last character read into the array.
    * Returns: s if successful. If end-of-file is encountered and no characters
    *          have been read into the array, the contents of the array remain
    *          unchanged and a null pointer is returned. If a read error occurs
    *          during the operation, the array contents are indeterminate and a
    *          null pointer is returned.
    */
extern __declspec(__nothrow) int fputc(int /*c*/, FILE * /*stream*/) __attribute__((__nonnull__(2)));
   /*
    * writes the character specified by c (converted to an unsigned char) to
    * the output stream pointed to by stream, at the position indicated by the
    * asociated file position indicator (if defined), and advances the
    * indicator appropriately. If the file position indicator is not defined,
    * the character is appended to the output stream.
    * Returns: the character written. If a write error occurs, the error
    *          indicator is set and fputc returns EOF.
    */
extern __declspec(__nothrow) int fputs(const char * __restrict /*s*/, FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,2)));
   /*
    * writes the string pointed to by s to the stream pointed to by stream.
    * The terminating null character is not written.
    * Returns: EOF if a write error occurs; otherwise it returns a nonnegative
    *          value.
    */
extern __declspec(__nothrow) int getc(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to fgetc except that it may be implemented as an unsafe
    * macro (stream may be evaluated more than once, so the argument should
    * never be an expression with side-effects).
    * Returns: the next character from the input stream pointed to by stream.
    *          If the stream is at end-of-file, the end-of-file indicator is
    *          set and getc returns EOF. If a read error occurs, the error
    *          indicator is set and getc returns EOF.
    */




    extern __declspec(__nothrow) int (getchar)(void);

   /*
    * is equivalent to getc with the argument stdin.
    * Returns: the next character from the input stream pointed to by stdin.
    *          If the stream is at end-of-file, the end-of-file indicator is
    *          set and getchar returns EOF. If a read error occurs, the error
    *          indicator is set and getchar returns EOF.
    */
extern __declspec(__nothrow) char *gets(char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * reads characters from the input stream pointed to by stdin into the array
    * pointed to by s, until end-of-file is encountered or a new-line character
    * is read. Any new-line character is discarded, and a null character is
    * written immediately after the last character read into the array.
    * Returns: s if successful. If end-of-file is encountered and no characters
    *          have been read into the array, the contents of the array remain
    *          unchanged and a null pointer is returned. If a read error occurs
    *          during the operation, the array contents are indeterminate and a
    *          null pointer is returned.
    */
extern __declspec(__nothrow) int putc(int /*c*/, FILE * /*stream*/) __attribute__((__nonnull__(2)));
   /*
    * is equivalent to fputc except that it may be implemented as aan unsafe
    * macro (stream may be evaluated more than once, so the argument should
    * never be an expression with side-effects).
    * Returns: the character written. If a write error occurs, the error
    *          indicator is set and putc returns EOF.
    */




    extern __declspec(__nothrow) int (putchar)(int /*c*/);

   /*
    * is equivalent to putc with the second argument stdout.
    * Returns: the character written. If a write error occurs, the error
    *          indicator is set and putc returns EOF.
    */
extern __declspec(__nothrow) int puts(const char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * writes the string pointed to by s to the stream pointed to by stdout, and
    * appends a new-line character to the output. The terminating null
    * character is not written.
    * Returns: EOF if a write error occurs; otherwise it returns a nonnegative
    *          value.
    */
extern __declspec(__nothrow) int ungetc(int /*c*/, FILE * /*stream*/) __attribute__((__nonnull__(2)));
   /*
    * pushes the character specified by c (converted to an unsigned char) back
    * onto the input stream pointed to by stream. The character will be
    * returned by the next read on that stream. An intervening call to the
    * fflush function or to a file positioning function (fseek, fsetpos,
    * rewind) discards any pushed-back characters. The extern _ARMABIal storage
    * corresponding to the stream is unchanged.
    * One character pushback is guaranteed. If the unget function is called too
    * many times on the same stream without an intervening read or file
    * positioning operation on that stream, the operation may fail.
    * If the value of c equals that of the macro EOF, the operation fails and
    * the input stream is unchanged.
    * A successful call to the ungetc function clears the end-of-file
    * indicator. The value of the file position indicator after reading or
    * discarding all pushed-back characters shall be the same as it was before
    * the characters were pushed back. For a text stream, the value of the file
    * position indicator after a successful call to the ungetc function is
    * unspecified until all pushed-back characters are read or discarded. For a
    * binary stream, the file position indicator is decremented by each
    * successful call to the ungetc function; if its value was zero before a
    * call, it is indeterminate after the call.
    * Returns: the character pushed back after conversion, or EOF if the
    *          operation fails.
    */

extern __declspec(__nothrow) size_t fread(void * __restrict /*ptr*/,
                    size_t /*size*/, size_t /*nmemb*/, FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,4)));
   /*
    * reads into the array pointed to by ptr, up to nmemb members whose size is
    * specified by size, from the stream pointed to by stream. The file
    * position indicator (if defined) is advanced by the number of characters
    * successfully read. If an error occurs, the resulting value of the file
    * position indicator is indeterminate. If a partial member is read, its
    * value is indeterminate. The ferror or feof function shall be used to
    * distinguish between a read error and end-of-file.
    * Returns: the number of members successfully read, which may be less than
    *          nmemb if a read error or end-of-file is encountered. If size or
    *          nmemb is zero, fread returns zero and the contents of the array
    *          and the state of the stream remain unchanged.
    */

extern __declspec(__nothrow) size_t __fread_bytes_avail(void * __restrict /*ptr*/,
                    size_t /*count*/, FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,3)));
   /*
    * reads into the array pointed to by ptr, up to count characters from the
    * stream pointed to by stream. The file position indicator (if defined)
    * is advanced by the number of characters successfully read. If an error
    * occurs, the resulting value of the file position indicator is
    * indeterminate. The ferror or feof function shall be used to
    * distinguish between a read error and end-of-file.  The call will block
    * only if no characters are available.
    * Returns: the number of characters successfully read, which may be less than
    *          count. If count is zero, __fread_bytes_avail returns zero and
    *          the contents of the array and the state of the stream remain
    *          unchanged.
    */

extern __declspec(__nothrow) size_t fwrite(const void * __restrict /*ptr*/,
                    size_t /*size*/, size_t /*nmemb*/, FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,4)));
   /*
    * writes, from the array pointed to by ptr up to nmemb members whose size
    * is specified by size, to the stream pointed to by stream. The file
    * position indicator (if defined) is advanced by the number of characters
    * successfully written. If an error occurs, the resulting value of the file
    * position indicator is indeterminate.
    * Returns: the number of members successfully written, which will be less
    *          than nmemb only if a write error is encountered.
    */

extern __declspec(__nothrow) int fgetpos(FILE * __restrict /*stream*/, fpos_t * __restrict /*pos*/) __attribute__((__nonnull__(1,2)));
   /*
    * stores the current value of the file position indicator for the stream
    * pointed to by stream in the object pointed to by pos. The value stored
    * contains unspecified information usable by the fsetpos function for
    * repositioning the stream to its position at the time  of the call to the
    * fgetpos function.
    * Returns: zero, if successful. Otherwise nonzero is returned and the
    *          integer expression errno is set to an implementation-defined
    *          nonzero value.
    */
extern __declspec(__nothrow) int fseek(FILE * /*stream*/, long int /*offset*/, int /*whence*/) __attribute__((__nonnull__(1)));
   /*
    * sets the file position indicator for the stream pointed to by stream.
    * For a binary stream, the new position is at the signed number of
    * characters specified by offset away from the point specified by whence.
    * The specified point is the beginning of the file for SEEK_SET, the
    * current position in the file for SEEK_CUR, or end-of-file for SEEK_END.
    * A binary stream need not meaningfully support fseek calls with a whence
    * value of SEEK_END.
    * For a text stream, either offset shall be zero, or offset shall be a
    * value returned by an earlier call to the ftell function on the same
    * stream and whence shall be SEEK_SET.
    * The fseek function clears the end-of-file indicator and undoes any
    * effects of the ungetc function on the same stream. After an fseek call,
    * the next operation on an update stream may be either input or output.
    * Returns: nonzero only for a request that cannot be satisfied.
    */
extern __declspec(__nothrow) int fsetpos(FILE * __restrict /*stream*/, const fpos_t * __restrict /*pos*/) __attribute__((__nonnull__(1,2)));
   /*
    * sets  the file position indicator for the stream pointed to by stream
    * according to the value of the object pointed to by pos, which shall be a
    * value returned by an earlier call to the fgetpos function on the same
    * stream.
    * The fsetpos function clears the end-of-file indicator and undoes any
    * effects of the ungetc function on the same stream. After an fsetpos call,
    * the next operation on an update stream may be either input or output.
    * Returns: zero, if successful. Otherwise nonzero is returned and the
    *          integer expression errno is set to an implementation-defined
    *          nonzero value.
    */
extern __declspec(__nothrow) long int ftell(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * obtains the current value of the file position indicator for the stream
    * pointed to by stream. For a binary stream, the value is the number of
    * characters from the beginning of the file. For a text stream, the file
    * position indicator contains unspecified information, usable by the fseek
    * function for returning the file position indicator to its position at the
    * time of the ftell call; the difference between two such return values is
    * not necessarily a meaningful measure of the number of characters written
    * or read.
    * Returns: if successful, the current value of the file position indicator.
    *          On failure, the ftell function returns -1L and sets the integer
    *          expression errno to an implementation-defined nonzero value.
    */
extern __declspec(__nothrow) void rewind(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * sets the file position indicator for the stream pointed to by stream to
    * the beginning of the file. It is equivalent to
    *          (void)fseek(stream, 0L, SEEK_SET)
    * except that the error indicator for the stream is also cleared.
    * Returns: no value.
    */

extern __declspec(__nothrow) void clearerr(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * clears the end-of-file and error indicators for the stream pointed to by
    * stream. These indicators are cleared only when the file is opened or by
    * an explicit call to the clearerr function or to the rewind function.
    * Returns: no value.
    */

extern __declspec(__nothrow) int feof(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * tests the end-of-file indicator for the stream pointed to by stream.
    * Returns: nonzero iff the end-of-file indicator is set for stream.
    */
extern __declspec(__nothrow) int ferror(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * tests the error indicator for the stream pointed to by stream.
    * Returns: nonzero iff the error indicator is set for stream.
    */
extern __declspec(__nothrow) void perror(const char * /*s*/);
   /*
    * maps the error number  in the integer expression errno to an error
    * message. It writes a sequence of characters to the standard error stream
    * thus: first (if s is not a null pointer and the character pointed to by
    * s is not the null character), the string pointed to by s followed by a
    * colon and a space; then an appropriate error message string followed by
    * a new-line character. The contents of the error message strings are the
    * same as those returned by the strerror function with argument errno,
    * which are implementation-defined.
    * Returns: no value.
    */

extern __declspec(__nothrow) int _fisatty(FILE * /*stream*/ ) __attribute__((__nonnull__(1)));
    /* Returns 1 if the stream is tty (stdin), 0 otherwise. Not ANSI compliant.
     */

extern __declspec(__nothrow) void __use_no_semihosting_swi(void);
extern __declspec(__nothrow) void __use_no_semihosting(void);
    /*
     * Referencing either of these symbols will cause a link-time
     * error if any library functions that use semihosting SWI
     * calls are also present in the link, i.e. you define it if
     * you want to make sure you haven't accidentally used any such
     * SWIs.
     */











# 1021 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"



/* end of stdio.h */

# 51 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
/* stdlib.h: ANSI draft (X3J11 May 88) library header, section 4.10 */
/* Copyright (C) Codemist Ltd., 1988-1993.                          */
/* Copyright 1991-1998,2014 ARM Limited. All rights reserved.       */
/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */
 
/*
 * stdlib.h declares four types, several general purpose functions,
 * and defines several macros.
 */






  /* armclang and non-strict armcc allow 'long long' in system headers */














  /*
   * Some of these declarations are new in C99.  To access them in C++
   * you can use -D__USE_C99_STDLIB (or -D__USE_C99ALL).
   */








# 54 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 70 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"






   /* unconditional in non-strict C for consistency of debug info */



    typedef unsigned short wchar_t; /* see <stddef.h> */
# 91 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"

typedef struct div_t { int quot, rem; } div_t;
   /* type of the value returned by the div function. */
typedef struct ldiv_t { long int quot, rem; } ldiv_t;
   /* type of the value returned by the ldiv function. */

typedef struct lldiv_t { long long quot, rem; } lldiv_t;
   /* type of the value returned by the lldiv function. */


# 112 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
   /*
    * an integral expression which may be used as an argument to the exit
    * function to return successful termination status to the host
    * environment.
    */

   /*
    * Defining __USE_ANSI_EXAMPLE_RAND at compile time switches to
    * the example implementation of rand() and srand() provided in
    * the ANSI C standard. This implementation is very poor, but is
    * provided for completeness.
    */
# 131 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
   /*
    * RAND_MAX: an integral constant expression, the value of which
    * is the maximum value returned by the rand function.
    */
extern __declspec(__nothrow) int __aeabi_MB_CUR_MAX(void);

   /*
    * a positive integer expression whose value is the maximum number of bytes
    * in a multibyte character for the extended character set specified by the
    * current locale (category LC_CTYPE), and whose value is never greater
    * than MB_LEN_MAX.
    */

   /*
    * If the compiler supports signalling nans as per N965 then it
    * will define __SUPPORT_SNAN__, in which case a user may define
    * _WANT_SNAN in order to obtain a compliant version of the strtod
    * family of functions.
    */




extern __declspec(__nothrow) double atof(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to double
    * representation.
    * Returns: the converted value.
    */
extern __declspec(__nothrow) int atoi(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to int
    * representation.
    * Returns: the converted value.
    */
extern __declspec(__nothrow) long int atol(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to long int
    * representation.
    * Returns: the converted value.
    */

extern __declspec(__nothrow) long long atoll(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to
    * long long int representation.
    * Returns: the converted value.
    */


extern __declspec(__nothrow) double strtod(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to double
    * representation. First it decomposes the input string into three parts:
    * an initial, possibly empty, sequence of white-space characters (as
    * specified by the isspace function), a subject sequence resembling a
    * floating point constant; and a final string of one or more unrecognised
    * characters, including the terminating null character of the input string.
    * Then it attempts to convert the subject sequence to a floating point
    * number, and returns the result. A pointer to the final string is stored
    * in the object pointed to by endptr, provided that endptr is not a null
    * pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned. If the correct value is outside the range of
    *          representable values, plus or minus HUGE_VAL is returned
    *          (according to the sign of the value), and the value of the macro
    *          ERANGE is stored in errno. If the correct value would cause
    *          underflow, zero is returned and the value of the macro ERANGE is
    *          stored in errno.
    */

extern __declspec(__nothrow) float strtof(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) long double strtold(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
   /*
    * same as strtod, but return float and long double respectively.
    */

extern __declspec(__nothrow) long int strtol(const char * __restrict /*nptr*/,
                        char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to long int
    * representation. First it decomposes the input string into three parts:
    * an initial, possibly empty, sequence of white-space characters (as
    * specified by the isspace function), a subject sequence resembling an
    * integer represented in some radix determined by the value of base, and a
    * final string of one or more unrecognised characters, including the
    * terminating null character of the input string. Then it attempts to
    * convert the subject sequence to an integer, and returns the result.
    * If the value of base is 0, the expected form of the subject sequence is
    * that of an integer constant (described in ANSI Draft, section 3.1.3.2),
    * optionally preceded by a '+' or '-' sign, but not including an integer
    * suffix. If the value of base is between 2 and 36, the expected form of
    * the subject sequence is a sequence of letters and digits representing an
    * integer with the radix specified by base, optionally preceded by a plus
    * or minus sign, but not including an integer suffix. The letters from a
    * (or A) through z (or Z) are ascribed the values 10 to 35; only letters
    * whose ascribed values are less than that of the base are permitted. If
    * the value of base is 16, the characters 0x or 0X may optionally precede
    * the sequence of letters and digits following the sign if present.
    * A pointer to the final string is stored in the object
    * pointed to by endptr, provided that endptr is not a null pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned and nptr is stored in *endptr.
    *          If the correct value is outside the range of
    *          representable values, LONG_MAX or LONG_MIN is returned
    *          (according to the sign of the value), and the value of the
    *          macro ERANGE is stored in errno.
    */
extern __declspec(__nothrow) unsigned long int strtoul(const char * __restrict /*nptr*/,
                                       char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to unsigned
    * long int representation. First it decomposes the input string into three
    * parts: an initial, possibly empty, sequence of white-space characters (as
    * determined by the isspace function), a subject sequence resembling an
    * unsigned integer represented in some radix determined by the value of
    * base, and a final string of one or more unrecognised characters,
    * including the terminating null character of the input string. Then it
    * attempts to convert the subject sequence to an unsigned integer, and
    * returns the result. If the value of base is zero, the expected form of
    * the subject sequence is that of an integer constant (described in ANSI
    * Draft, section 3.1.3.2), optionally preceded by a '+' or '-' sign, but
    * not including an integer suffix. If the value of base is between 2 and
    * 36, the expected form of the subject sequence is a sequence of letters
    * and digits representing an integer with the radix specified by base,
    * optionally preceded by a '+' or '-' sign, but not including an integer
    * suffix. The letters from a (or A) through z (or Z) stand for the values
    * 10 to 35; only letters whose ascribed values are less than that of the
    * base are permitted. If the value of base is 16, the characters 0x or 0X
    * may optionally precede the sequence of letters and digits following the
    * sign, if present. A pointer to the final string is stored in the object
    * pointed to by endptr, provided that endptr is not a null pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned and nptr is stored in *endptr.
    *          If the correct value is outside the range of
    *          representable values, ULONG_MAX is returned, and the value of
    *          the macro ERANGE is stored in errno.
    */

/* C90 reserves all names beginning with 'str' */
extern __declspec(__nothrow) long long strtoll(const char * __restrict /*nptr*/,
                                  char ** __restrict /*endptr*/, int /*base*/)
                          __attribute__((__nonnull__(1)));
   /*
    * as strtol but returns a long long int value.  If the correct value is
    * outside the range of representable values,  LLONG_MAX or LLONG_MIN is
    * returned (according to the sign of the value), and the value of the
    * macro ERANGE is stored in errno.
    */
extern __declspec(__nothrow) unsigned long long strtoull(const char * __restrict /*nptr*/,
                                            char ** __restrict /*endptr*/, int /*base*/)
                                   __attribute__((__nonnull__(1)));
   /*
    * as strtoul but returns an unsigned long long int value.  If the correct
    * value is outside the range of representable values, ULLONG_MAX is returned,
    * and the value of the macro ERANGE is stored in errno.
    */

extern __declspec(__nothrow) int rand(void);
   /*
    * Computes a sequence of pseudo-random integers in the range 0 to RAND_MAX.
    * Uses an additive generator (Mitchell & Moore) of the form:
    *   Xn = (X[n-24] + X[n-55]) MOD 2^31
    * This is described in section 3.2.2 of Knuth, vol 2. It's period is
    * in excess of 2^55 and its randomness properties, though unproven, are
    * conjectured to be good. Empirical testing since 1958 has shown no flaws.
    * Returns: a pseudo-random integer.
    */
extern __declspec(__nothrow) void srand(unsigned int /*seed*/);
   /*
    * uses its argument as a seed for a new sequence of pseudo-random numbers
    * to be returned by subsequent calls to rand. If srand is then called with
    * the same seed value, the sequence of pseudo-random numbers is repeated.
    * If rand is called before any calls to srand have been made, the same
    * sequence is generated as when srand is first called with a seed value
    * of 1.
    */

struct _rand_state { int __x[57]; };
extern __declspec(__nothrow) int _rand_r(struct _rand_state *);
extern __declspec(__nothrow) void _srand_r(struct _rand_state *, unsigned int);
struct _ANSI_rand_state { int __x[1]; };
extern __declspec(__nothrow) int _ANSI_rand_r(struct _ANSI_rand_state *);
extern __declspec(__nothrow) void _ANSI_srand_r(struct _ANSI_rand_state *, unsigned int);
   /*
    * Re-entrant variants of both flavours of rand, which operate on
    * an explicitly supplied state buffer.
    */

extern __declspec(__nothrow) void *calloc(size_t /*nmemb*/, size_t /*size*/);
   /*
    * allocates space for an array of nmemb objects, each of whose size is
    * 'size'. The space is initialised to all bits zero.
    * Returns: either a null pointer or a pointer to the allocated space.
    */
extern __declspec(__nothrow) void free(void * /*ptr*/);
   /*
    * causes the space pointed to by ptr to be deallocated (i.e., made
    * available for further allocation). If ptr is a null pointer, no action
    * occurs. Otherwise, if ptr does not match a pointer earlier returned by
    * calloc, malloc or realloc or if the space has been deallocated by a call
    * to free or realloc, the behaviour is undefined.
    */
extern __declspec(__nothrow) void *malloc(size_t /*size*/);
   /*
    * allocates space for an object whose size is specified by 'size' and whose
    * value is indeterminate.
    * Returns: either a null pointer or a pointer to the allocated space.
    */
extern __declspec(__nothrow) void *realloc(void * /*ptr*/, size_t /*size*/);
   /*
    * changes the size of the object pointed to by ptr to the size specified by
    * size. The contents of the object shall be unchanged up to the lesser of
    * the new and old sizes. If the new size is larger, the value of the newly
    * allocated portion of the object is indeterminate. If ptr is a null
    * pointer, the realloc function behaves like a call to malloc for the
    * specified size. Otherwise, if ptr does not match a pointer earlier
    * returned by calloc, malloc or realloc, or if the space has been
    * deallocated by a call to free or realloc, the behaviour is undefined.
    * If the space cannot be allocated, the object pointed to by ptr is
    * unchanged. If size is zero and ptr is not a null pointer, the object it
    * points to is freed.
    * Returns: either a null pointer or a pointer to the possibly moved
    *          allocated space.
    */

extern __declspec(__nothrow) int posix_memalign(void ** /*ret*/, size_t /*alignment*/, size_t /*size*/);
   /*
    * allocates space for an object of size 'size', aligned to a
    * multiple of 'alignment' (which must be a power of two and at
    * least 4).
    *
    * On success, a pointer to the allocated object is stored in
    * *ret, and zero is returned. On failure, the return value is
    * either ENOMEM (allocation failed because no suitable piece of
    * memory was available) or EINVAL (the 'alignment' parameter was
    * invalid).
    */

typedef int (*__heapprt)(void *, char const *, ...);
extern __declspec(__nothrow) void __heapstats(int (* /*dprint*/)(void * /*param*/,
                                           char const * /*format*/, ...),
                        void * /*param*/) __attribute__((__nonnull__(1)));
   /*
    * reports current heap statistics (eg. number of free blocks in
    * the free-list). Output is as implementation-defined free-form
    * text, provided via the dprint function. `param' gives an
    * extra data word to pass to dprint. You can call
    * __heapstats(fprintf,stdout) by casting fprintf to the above
    * function type; the typedef `__heapprt' is provided for this
    * purpose.
    *
    * `dprint' will not be called while the heap is being examined,
    * so it can allocate memory itself without trouble.
    */
extern __declspec(__nothrow) int __heapvalid(int (* /*dprint*/)(void * /*param*/,
                                           char const * /*format*/, ...),
                       void * /*param*/, int /*verbose*/) __attribute__((__nonnull__(1)));
   /*
    * performs a consistency check on the heap. Errors are reported
    * through dprint, like __heapstats. If `verbose' is nonzero,
    * full diagnostic information on the heap state is printed out.
    *
    * This routine probably won't work if the heap isn't a
    * contiguous chunk (for example, if __user_heap_extend has been
    * overridden).
    *
    * `dprint' may be called while the heap is being examined or
    * even in an invalid state, so it must perform no memory
    * allocation. In particular, if `dprint' calls (or is) a stdio
    * function, the stream it outputs to must already have either
    * been written to or been setvbuf'ed, or else the system will
    * allocate buffer space for it on the first call to dprint.
    */
extern __declspec(__nothrow) __declspec(__noreturn) void abort(void);
   /*
    * causes abnormal program termination to occur, unless the signal SIGABRT
    * is being caught and the signal handler does not return. Whether open
    * output streams are flushed or open streams are closed or temporary
    * files removed is implementation-defined.
    * An implementation-defined form of the status 'unsuccessful termination'
    * is returned to the host environment by means of a call to
    * raise(SIGABRT).
    */

extern __declspec(__nothrow) int atexit(void (* /*func*/)(void)) __attribute__((__nonnull__(1)));
   /*
    * registers the function pointed to by func, to be called without its
    * arguments at normal program termination. It is possible to register at
    * least 32 functions.
    * Returns: zero if the registration succeeds, nonzero if it fails.
    */
# 436 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


extern __declspec(__nothrow) __declspec(__noreturn) void exit(int /*status*/);
   /*
    * causes normal program termination to occur. If more than one call to the
    * exit function is executed by a program, the behaviour is undefined.
    * First, all functions registered by the atexit function are called, in the
    * reverse order of their registration.
    * Next, all open output streams are flushed, all open streams are closed,
    * and all files created by the tmpfile function are removed.
    * Finally, control is returned to the host environment. If the value of
    * status is zero or EXIT_SUCCESS, an implementation-defined form of the
    * status 'successful termination' is returned. If the value of status is
    * EXIT_FAILURE, an implementation-defined form of the status
    * 'unsuccessful termination' is returned. Otherwise the status returned
    * is implementation-defined.
    */

extern __declspec(__nothrow) __declspec(__noreturn) void _Exit(int /*status*/);
   /*
    * causes normal program termination to occur. No functions registered
    * by the atexit function are called.
    * In this implementation, all open output streams are flushed, all
    * open streams are closed, and all files created by the tmpfile function
    * are removed.
    * Control is returned to the host environment. The status returned to
    * the host environment is determined in the same way as for 'exit'.
    */     

extern __declspec(__nothrow) char *getenv(const char * /*name*/) __attribute__((__nonnull__(1)));
   /*
    * searches the environment list, provided by the host environment, for a
    * string that matches the string pointed to by name. The set of environment
    * names and the method for altering the environment list are
    * implementation-defined.
    * Returns: a pointer to a string associated with the matched list member.
    *          The array pointed to shall not be modified by the program, but
    *          may be overwritten by a subsequent call to the getenv function.
    *          If the specified name cannot be found, a null pointer is
    *          returned.
    */

extern __declspec(__nothrow) int  system(const char * /*string*/);
   /*
    * passes the string pointed to by string to the host environment to be
    * executed by a command processor in an implementation-defined manner.
    * A null pointer may be used for string, to inquire whether a command
    * processor exists.
    *
    * Returns: If the argument is a null pointer, the system function returns
    *          non-zero only if a command processor is available. If the
    *          argument is not a null pointer, the system function returns an
    *          implementation-defined value.
    */

extern  void *bsearch(const void * /*key*/, const void * /*base*/,
              size_t /*nmemb*/, size_t /*size*/,
              int (* /*compar*/)(const void *, const void *)) __attribute__((__nonnull__(1,2,5)));
   /*
    * searches an array of nmemb objects, the initial member of which is
    * pointed to by base, for a member that matches the object pointed to by
    * key. The size of each member of the array is specified by size.
    * The contents of the array shall be in ascending sorted order according to
    * a comparison function pointed to by compar, which is called with two
    * arguments that point to the key object and to an array member, in that
    * order. The function shall return an integer less than, equal to, or
    * greater than zero if the key object is considered, respectively, to be
    * less than, to match, or to be greater than the array member.
    * Returns: a pointer to a matching member of the array, or a null pointer
    *          if no match is found. If two members compare as equal, which
    *          member is matched is unspecified.
    */
# 524 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


extern  void qsort(void * /*base*/, size_t /*nmemb*/, size_t /*size*/,
           int (* /*compar*/)(const void *, const void *)) __attribute__((__nonnull__(1,4)));
   /*
    * sorts an array of nmemb objects, the initial member of which is pointed
    * to by base. The size of each object is specified by size.
    * The contents of the array shall be in ascending order according to a
    * comparison function pointed to by compar, which is called with two
    * arguments that point to the objects being compared. The function shall
    * return an integer less than, equal to, or greater than zero if the first
    * argument is considered to be respectively less than, equal to, or greater
    * than the second. If two members compare as equal, their order in the
    * sorted array is unspecified.
    */

# 553 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"

extern __declspec(__nothrow) __attribute__((const)) int abs(int /*j*/);
   /*
    * computes the absolute value of an integer j. If the result cannot be
    * represented, the behaviour is undefined.
    * Returns: the absolute value.
    */

extern __declspec(__nothrow) __attribute__((const)) div_t div(int /*numer*/, int /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the resulting
    * quotient is the integer of lesser magnitude that is the nearest to the
    * algebraic quotient. If the result cannot be represented, the behaviour is
    * undefined; otherwise, quot * denom + rem shall equal numer.
    * Returns: a structure of type div_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          int quot; int rem;
    */
extern __declspec(__nothrow) __attribute__((const)) long int labs(long int /*j*/);
   /*
    * computes the absolute value of an long integer j. If the result cannot be
    * represented, the behaviour is undefined.
    * Returns: the absolute value.
    */




extern __declspec(__nothrow) __attribute__((const)) ldiv_t ldiv(long int /*numer*/, long int /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the sign of
    * the resulting quotient is that of the algebraic quotient, and the
    * magnitude of the resulting quotient is the largest integer less than the
    * magnitude of the algebraic quotient. If the result cannot be represented,
    * the behaviour is undefined; otherwise, quot * denom + rem shall equal
    * numer.
    * Returns: a structure of type ldiv_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          long int quot; long int rem;
    */







extern __declspec(__nothrow) __attribute__((const)) long long llabs(long long /*j*/);
   /*
    * computes the absolute value of a long long integer j. If the
    * result cannot be represented, the behaviour is undefined.
    * Returns: the absolute value.
    */




extern __declspec(__nothrow) __attribute__((const)) lldiv_t lldiv(long long /*numer*/, long long /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the sign of
    * the resulting quotient is that of the algebraic quotient, and the
    * magnitude of the resulting quotient is the largest integer less than the
    * magnitude of the algebraic quotient. If the result cannot be represented,
    * the behaviour is undefined; otherwise, quot * denom + rem shall equal
    * numer.
    * Returns: a structure of type lldiv_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          long long quot; long long rem;
    */
# 634 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


/*
 * ARM real-time divide functions for guaranteed performance
 */
typedef struct __sdiv32by16 { int quot, rem; } __sdiv32by16;
typedef struct __udiv32by16 { unsigned int quot, rem; } __udiv32by16;
   /* used int so that values return in separate regs, although 16-bit */
typedef struct __sdiv64by32 { int rem, quot; } __sdiv64by32;

__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __sdiv32by16 __rt_sdiv32by16(
     int /*numer*/,
     short int /*denom*/);
   /*
    * Signed divide: (16-bit quot), (16-bit rem) = (32-bit) / (16-bit)
    */
__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __udiv32by16 __rt_udiv32by16(
     unsigned int /*numer*/,
     unsigned short /*denom*/);
   /*
    * Unsigned divide: (16-bit quot), (16-bit rem) = (32-bit) / (16-bit)
    */
__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __sdiv64by32 __rt_sdiv64by32(
     int /*numer_h*/, unsigned int /*numer_l*/,
     int /*denom*/);
   /*
    * Signed divide: (32-bit quot), (32-bit rem) = (64-bit) / (32-bit)
    */


/*
 * ARM floating-point mask/status function (for both hardfp and softfp)
 */
extern __declspec(__nothrow) unsigned int __fp_status(unsigned int /*mask*/, unsigned int /*flags*/);
   /*
    * mask and flags are bit-fields which correspond directly to the
    * floating point status register in the FPE/FPA and fplib.  
    * __fp_status returns the current value of the status register,
    * and also sets the writable bits of the word
    * (the exception control and flag bytes) to:
    *
    *     new = (old & ~mask) ^ flags;
    */












/*
 * Multibyte Character Functions.
 * The behaviour of the multibyte character functions is affected by the
 * LC_CTYPE category of the current locale. For a state-dependent encoding,
 * each function is placed into its initial state by a call for which its
 * character pointer argument, s, is a null pointer. Subsequent calls with s
 * as other than a null pointer cause the internal state of the function to be
 * altered as necessary. A call with s as a null pointer causes these functions
 * to return a nonzero value if encodings have state dependency, and a zero
 * otherwise. After the LC_CTYPE category is changed, the shift state of these
 * functions is indeterminate.
 */
extern __declspec(__nothrow) int mblen(const char * /*s*/, size_t /*n*/);
   /*
    * If s is not a null pointer, the mblen function determines the number of
    * bytes compromising the multibyte character pointed to by s. Except that
    * the shift state of the mbtowc function is not affected, it is equivalent
    * to   mbtowc((wchar_t *)0, s, n);
    * Returns: If s is a null pointer, the mblen function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the mblen function either returns a 0 (if s points to a
    *          null character), or returns the number of bytes that compromise
    *          the multibyte character (if the next n of fewer bytes form a
    *          valid multibyte character), or returns -1 (they do not form a
    *          valid multibyte character).
    */
extern __declspec(__nothrow) int mbtowc(wchar_t * __restrict /*pwc*/,
                   const char * __restrict /*s*/, size_t /*n*/);
   /*
    * If s is not a null pointer, the mbtowc function determines the number of
    * bytes that compromise the multibyte character pointed to by s. It then
    * determines the code for value of type wchar_t that corresponds to that
    * multibyte character. (The value of the code corresponding to the null
    * character is zero). If the multibyte character is valid and pwc is not a
    * null pointer, the mbtowc function stores the code in the object pointed
    * to by pwc. At most n bytes of the array pointed to by s will be examined.
    * Returns: If s is a null pointer, the mbtowc function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the mbtowc function either returns a 0 (if s points to
    *          a null character), or returns the number of bytes that
    *          compromise the converted multibyte character (if the next n of
    *          fewer bytes form a valid multibyte character), or returns -1
    *          (they do not form a valid multibyte character).
    */
extern __declspec(__nothrow) int wctomb(char * /*s*/, wchar_t /*wchar*/);
   /*
    * determines the number of bytes need to represent the multibyte character
    * corresponding to the code whose value is wchar (including any change in
    * shift state). It stores the multibyte character representation in the
    * array object pointed to by s (if s is not a null pointer). At most
    * MB_CUR_MAX characters are stored. If the value of wchar is zero, the
    * wctomb function is left in the initial shift state).
    * Returns: If s is a null pointer, the wctomb function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the wctomb function returns a -1 if the value of wchar
    *          does not correspond to a valid multibyte character, or returns
    *          the number of bytes that compromise the multibyte character
    *          corresponding to the value of wchar.
    */

/*
 * Multibyte String Functions.
 * The behaviour of the multibyte string functions is affected by the LC_CTYPE
 * category of the current locale.
 */
extern __declspec(__nothrow) size_t mbstowcs(wchar_t * __restrict /*pwcs*/,
                      const char * __restrict /*s*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * converts a sequence of multibyte character that begins in the initial
    * shift state from the array pointed to by s into a sequence of
    * corresponding codes and stores not more than n codes into the array
    * pointed to by pwcs. No multibyte character that follow a null character
    * (which is converted into a code with value zero) will be examined or
    * converted. Each multibyte character is converted as if by a call to
    * mbtowc function, except that the shift state of the mbtowc function is
    * not affected. No more than n elements will be modified in the array
    * pointed to by pwcs. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: If an invalid multibyte character is encountered, the mbstowcs
    *          function returns (size_t)-1. Otherwise, the mbstowcs function
    *          returns the number of array elements modified, not including
    *          a terminating zero code, if any.
    */
extern __declspec(__nothrow) size_t wcstombs(char * __restrict /*s*/,
                      const wchar_t * __restrict /*pwcs*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * converts a sequence of codes that correspond to multibyte characters
    * from the array pointed to by pwcs into a sequence of multibyte
    * characters that begins in the initial shift state and stores these
    * multibyte characters into the array pointed to by s, stopping if a
    * multibyte character would exceed the limit of n total bytes or if a
    * null character is stored. Each code is converted as if by a call to the
    * wctomb function, except that the shift state of the wctomb function is
    * not affected. No more than n elements will be modified in the array
    * pointed to by s. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: If a code is encountered that does not correspond to a valid
    *          multibyte character, the wcstombs function returns (size_t)-1.
    *          Otherwise, the wcstombs function returns the number of bytes
    *          modified, not including a terminating null character, if any.
    */

extern __declspec(__nothrow) void __use_realtime_heap(void);
extern __declspec(__nothrow) void __use_realtime_division(void);
extern __declspec(__nothrow) void __use_two_region_memory(void);
extern __declspec(__nothrow) void __use_no_heap(void);
extern __declspec(__nothrow) void __use_no_heap_region(void);

extern __declspec(__nothrow) char const *__C_library_version_string(void);
extern __declspec(__nothrow) int __C_library_version_number(void);











# 892 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"





/* end of stdlib.h */
# 52 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
/* string.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.11 */
/* Copyright (C) Codemist Ltd., 1988-1993.                        */
/* Copyright 1991-1993 ARM Limited. All rights reserved.          */
/* version 0.04 */

/*
 * RCS $Revision$
 * Checkin $Date$
 */

/*
 * string.h declares one type and several functions, and defines one macro
 * useful for manipulating character arrays and other objects treated as
 * character arrays. Various methods are used for determining the lengths of
 * the arrays, but in all cases a char * or void * argument points to the
 * initial (lowest addresses) character of the array. If an array is written
 * beyond the end of an object, the behaviour is undefined.
 */












# 38 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 54 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"




extern __declspec(__nothrow) void *memcpy(void * __restrict /*s1*/,
                    const void * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) void *memmove(void * /*s1*/,
                    const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. Copying takes place as if the n characters from the
    * object pointed to by s2 are first copied into a temporary array of n
    * characters that does not overlap the objects pointed to by s1 and s2,
    * and then the n characters from the temporary array are copied into the
    * object pointed to by s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strcpy(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string pointed to by s2 (including the terminating nul
    * character) into the array pointed to by s1. If copying takes place
    * between objects that overlap, the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncpy(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies not more than n characters (characters that follow a null
    * character are not copied) from the array pointed to by s2 into the array
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */

extern __declspec(__nothrow) char *strcat(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends a copy of the string pointed to by s2 (including the terminating
    * null character) to the end of the string pointed to by s1. The initial
    * character of s2 overwrites the null character at the end of s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncat(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends not more than n characters (a null character and characters that
    * follow it are not appended) from the array pointed to by s2 to the end of
    * the string pointed to by s1. The initial character of s2 overwrites the
    * null character at the end of s1. A terminating null character is always
    * appended to the result.
    * Returns: the value of s1.
    */

/*
 * The sign of a nonzero value returned by the comparison functions is
 * determined by the sign of the difference between the values of the first
 * pair of characters (both interpreted as unsigned char) that differ in the
 * objects being compared.
 */

extern __declspec(__nothrow) int memcmp(const void * /*s1*/, const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the first n characters of the object pointed to by s1 to the
    * first n characters of the object pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the object pointed to by s1 is greater than, equal to, or
    *          less than the object pointed to by s2.
    */
extern __declspec(__nothrow) int strcmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcasecmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2,
    * case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncasecmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2, case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcoll(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2, both
    * interpreted as appropriate to the LC_COLLATE category of the current
    * locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2 when both are interpreted
    *          as appropriate to the current locale.
    */

extern __declspec(__nothrow) size_t strxfrm(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * transforms the string pointed to by s2 and places the resulting string
    * into the array pointed to by s1. The transformation function is such that
    * if the strcmp function is applied to two transformed strings, it returns
    * a value greater than, equal to or less than zero, corresponding to the
    * result of the strcoll function applied to the same two original strings.
    * No more than n characters are placed into the resulting array pointed to
    * by s1, including the terminating null character. If n is zero, s1 is
    * permitted to be a null pointer. If copying takes place between objects
    * that overlap, the behaviour is undefined.
    * Returns: The length of the transformed string is returned (not including
    *          the terminating null character). If the value returned is n or
    *          more, the contents of the array pointed to by s1 are
    *          indeterminate.
    */


# 193 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) void *memchr(const void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an unsigned char) in the
    * initial n characters (each interpreted as unsigned char) of the object
    * pointed to by s.
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the object.
    */

# 209 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an char) in the string
    * pointed to by s (including the terminating null character).
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the string.
    */

extern __declspec(__nothrow) size_t strcspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters not from the string pointed to by
    * s2. The terminating null character is not considered part of s2.
    * Returns: the length of the segment.
    */

# 232 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strpbrk(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of any
    * character from the string pointed to by s2.
    * Returns: returns a pointer to the character, or a null pointer if no
    *          character form s2 occurs in s1.
    */

# 247 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strrchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the last occurence of c (converted to a char) in the string
    * pointed to by s. The terminating null character is considered part of
    * the string.
    * Returns: returns a pointer to the character, or a null pointer if c does
    *          not occur in the string.
    */

extern __declspec(__nothrow) size_t strspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters from the string pointed to by S2
    * Returns: the length of the segment.
    */

# 270 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strstr(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of the
    * sequence of characters (excluding the terminating null character) in the
    * string pointed to by s2.
    * Returns: a pointer to the located string, or a null pointer if the string
    *          is not found.
    */

extern __declspec(__nothrow) char *strtok(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) char *_strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

extern __declspec(__nothrow) char *strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

   /*
    * A sequence of calls to the strtok function breaks the string pointed to
    * by s1 into a sequence of tokens, each of which is delimited by a
    * character from the string pointed to by s2. The first call in the
    * sequence has s1 as its first argument, and is followed by calls with a
    * null pointer as their first argument. The separator string pointed to by
    * s2 may be different from call to call.
    * The first call in the sequence searches for the first character that is
    * not contained in the current separator string s2. If no such character
    * is found, then there are no tokens in s1 and the strtok function returns
    * a null pointer. If such a character is found, it is the start of the
    * first token.
    * The strtok function then searches from there for a character that is
    * contained in the current separator string. If no such character is found,
    * the current token extends to the end of the string pointed to by s1, and
    * subsequent searches for a token will fail. If such a character is found,
    * it is overwritten by a null character, which terminates the current
    * token. The strtok function saves a pointer to the following character,
    * from which the next search for a token will start.
    * Each subsequent call, with a null pointer as the value for the first
    * argument, starts searching from the saved pointer and behaves as
    * described above.
    * Returns: pointer to the first character of a token, or a null pointer if
    *          there is no token.
    *
    * strtok_r() is a common extension which works exactly like
    * strtok(), but instead of storing its state in a hidden
    * library variable, requires the user to pass in a pointer to a
    * char * variable which will be used instead. Any sequence of
    * calls to strtok_r() passing the same char ** pointer should
    * behave exactly like the corresponding sequence of calls to
    * strtok(). This means that strtok_r() can safely be used in
    * multi-threaded programs, and also that you can tokenise two
    * strings in parallel.
    */

extern __declspec(__nothrow) void *memset(void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));
   /*
    * copies the value of c (converted to an unsigned char) into each of the
    * first n charactes of the object pointed to by s.
    * Returns: the value of s.
    */
extern __declspec(__nothrow) char *strerror(int /*errnum*/);
   /*
    * maps the error number in errnum to an error message string.
    * Returns: a pointer to the string, the contents of which are
    *          implementation-defined. The array pointed to shall not be
    *          modified by the program, but may be overwritten by a
    *          subsequent call to the strerror function.
    */
extern __declspec(__nothrow) size_t strlen(const char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * computes the length of the string pointed to by s.
    * Returns: the number of characters that precede the terminating null
    *          character.
    */

extern __declspec(__nothrow) size_t strlcpy(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string src into the string dst, using no more than
    * len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src. Thus, the operation
    * succeeded without truncation if and only if ret < len;
    * otherwise, the value in ret tells you how big to make dst if
    * you decide to reallocate it. (That value does _not_ include
    * the NUL.)
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) size_t strlcat(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * concatenates the string src to the string dst, using no more
    * than len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src plus the original length
    * of dst. Thus, the operation succeeded without truncation if
    * and only if ret < len; otherwise, the value in ret tells you
    * how big to make dst if you decide to reallocate it. (That
    * value does _not_ include the NUL.)
    * 
    * If no NUL is encountered within the first len bytes of dst,
    * then the length of dst is considered to have been equal to
    * len for the purposes of the return value (as if there were a
    * NUL at dst[len]). Thus, the return value in this case is len
    * + strlen(src).
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) void _membitcpybl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpybb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
    /*
     * Copies or moves a piece of memory from one place to another,
     * with one-bit granularity. So you can start or finish a copy
     * part way through a byte, and you can copy between regions
     * with different alignment within a byte.
     * 
     * All these functions have the same prototype: two void *
     * pointers for destination and source, then two integers
     * giving the bit offset from those pointers, and finally the
     * number of bits to copy.
     * 
     * Just like memcpy and memmove, the "cpy" functions copy as
     * fast as they can in the assumption that the memory regions
     * do not overlap, while the "move" functions cope correctly
     * with overlap.
     *
     * Treating memory as a stream of individual bits requires
     * defining a convention about what order those bits are
     * considered to be arranged in. The above functions support
     * multiple conventions:
     * 
     *  - the "bl" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in little-endian fashion, so that the LSB comes
     *    first. (For example, membitcpybl(a,b,0,7,1) would copy
     *    the MSB of the byte at b to the LSB of the byte at a.)
     * 
     *  - the "bb" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in big-endian fashion, so that the MSB comes
     *    first.
     * 
     *  - the "hl" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in little-endian fashion.
     * 
     *  - the "hb" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in big-endian fashion.
     * 
     *  - the "wl" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in little-endian fashion.
     * 
     *  - the "wb" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in big-endian fashion.
     */







# 502 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"



/* end of string.h */

# 53 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
/* limits.h: ANSI 'C' (X3J11 Oct 88) library header, section 2.2.4.2 */
/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991-1997 ARM Limited. All rights reserved         */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */






    /* max number of bits for smallest object that is not a bit-field (byte) */

    /* mimimum value for an object of type signed char */

    /* maximum value for an object of type signed char */

    /* maximum value for an object of type unsigned char */
# 30 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
      /* minimum value for an object of type char */

      /* maximum value for an object of type char */






# 45 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
    /* maximum number of bytes in a multibyte character, */
    /* for any supported locale */


    /* minimum value for an object of type short int */

    /* maximum value for an object of type short int */

    /* maximum value for an object of type unsigned short int */

    /* minimum value for an object of type int */

    /* maximum value for an object of type int */

    /* maximum value for an object of type unsigned int */





    /* minimum value for an object of type long int */





    /* maximum value for an object of type long int */





    /* maximum value for an object of type unsigned long int */


      /* minimum value for an object of type long long int */

      /* maximum value for an object of type long long int */

      /* maximum value for an object of type unsigned long int */




/* end of limits.h */

# 54 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"

typedef struct _server_instance_
{
    struct _server_instance_ * next;   // matches lwm2m_list_t::next
    uint16_t    instanceId;            // matches lwm2m_list_t::id
    uint16_t    shortServerId;
    uint32_t    lifetime;
    uint32_t    defaultMinPeriod;
    uint32_t    defaultMaxPeriod;
    uint32_t    disableTimeout;
    _Bool        storing;
    char        binding[4];
# 76 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"
} server_instance_t;

static uint8_t prv_server_delete(lwm2m_context_t *contextP,
                                 uint16_t id,
                                 lwm2m_object_t * objectP);
static uint8_t prv_server_create(lwm2m_context_t *contextP,
                                 uint16_t instanceId,
                                 int numData,
                                 lwm2m_data_t * dataArray,
                                 lwm2m_object_t * objectP);

static uint8_t prv_get_value(lwm2m_data_t * dataP,
                             server_instance_t * targetP)
{
    switch (dataP->id)
    {
    case 0:
        lwm2m_data_encode_int(targetP->shortServerId, dataP);
        return (uint8_t)0x45;

    case 1:
        lwm2m_data_encode_int(targetP->lifetime, dataP);
        return (uint8_t)0x45;

    case 2:
        lwm2m_data_encode_int(targetP->defaultMinPeriod, dataP);
        return (uint8_t)0x45;

    case 3:
        lwm2m_data_encode_int(targetP->defaultMaxPeriod, dataP);
        return (uint8_t)0x45;

    case 4:
        return (uint8_t)0x85;

    case 5:
        lwm2m_data_encode_int(targetP->disableTimeout, dataP);
        return (uint8_t)0x45;

    case 6:
        lwm2m_data_encode_bool(targetP->storing, dataP);
        return (uint8_t)0x45;

    case 7:
        lwm2m_data_encode_string(targetP->binding, dataP);
        return (uint8_t)0x45;

    case 8:
        return (uint8_t)0x85;

# 216 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"

    default:
        return (uint8_t)0x84;
    }
}

static uint8_t prv_server_read(lwm2m_context_t *contextP,
                               uint16_t instanceId,
                               int * numDataP,
                               lwm2m_data_t ** dataArrayP,
                               lwm2m_object_t * objectP)
{
    server_instance_t * targetP;
    uint8_t result;
    int i;

    /* unused parameter */
    (void)contextP;

    targetP = (server_instance_t *)lwm2m_list_find(objectP->instanceList, instanceId);
    if (0 == targetP) return (uint8_t)0x84;

    // is the server asking for the full instance ?
    if (*numDataP == 0)
    {
        uint16_t resList[] = {
            0,
            1,
            2,
            3,
            5,
            6,
            7,
# 259 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"
        };
        int nbRes = sizeof(resList)/sizeof(uint16_t);

# 361 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"

        *dataArrayP = lwm2m_data_new(nbRes);
        if (*dataArrayP == 0) return (uint8_t)0xA0;
        *numDataP = nbRes;
        for (i = 0 ; i < nbRes ; i++)
        {
            (*dataArrayP)[i].id = resList[i];
        }
    }

    i = 0;
    do
    {
        if ((*dataArrayP)[i].type == LWM2M_TYPE_MULTIPLE_RESOURCE)
        {
            result = (uint8_t)0x84;
        }
        else
        {
            result = prv_get_value((*dataArrayP) + i, targetP);
        }
        i++;
    } while (i < *numDataP && result == (uint8_t)0x45);

    return result;
}

static uint8_t prv_server_discover(lwm2m_context_t *contextP,
                                   uint16_t instanceId,
                                   int * numDataP,
                                   lwm2m_data_t ** dataArrayP,
                                   lwm2m_object_t * objectP)
{
    server_instance_t * targetP;
    uint8_t result;
    int i;

    /* unused parameter */
    (void)contextP;

    result = (uint8_t)0x45;

    targetP = (server_instance_t *)lwm2m_list_find(objectP->instanceList, instanceId);
    if (0 == targetP) return (uint8_t)0x84;

    // is the server asking for the full object ?
    if (*numDataP == 0)
    {
        uint16_t resList[] = {
            0,
            1,
            2,
            3,
            4,
            5,
            6,
            7,
            8,
# 429 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"
        };
        int nbRes = sizeof(resList) / sizeof(uint16_t);

# 531 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"

        *dataArrayP = lwm2m_data_new(nbRes);
        if (*dataArrayP == 0) return (uint8_t)0xA0;
        *numDataP = nbRes;
        for (i = 0; i < nbRes; i++)
        {
            (*dataArrayP)[i].id = resList[i];
        }
    }
    else
    {
        for (i = 0; i < *numDataP && result == (uint8_t)0x45; i++)
        {
            switch ((*dataArrayP)[i].id)
            {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
                break;
# 613 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"

            default:
                result = (uint8_t)0x84;
                break;
            }
        }
    }

    return result;
}

static uint8_t prv_set_int_value(lwm2m_data_t * dataArray, uint32_t * data) {
    uint8_t result;
    int64_t value;

    if (1 == lwm2m_data_decode_int(dataArray, &value))
    {
        if (value >= 0 && value <= 0xFFFFFFFF)
        {
            *data = value;
            result = (uint8_t)0x44;
        }
        else
        {
            result = (uint8_t)0x86;
        }
    }
    else
    {
        result = (uint8_t)0x80;
    }
    return result;
}

static uint8_t prv_server_write(lwm2m_context_t *contextP,
                                uint16_t instanceId,
                                int numData,
                                lwm2m_data_t * dataArray,
                                lwm2m_object_t * objectP,
                                lwm2m_write_type_t writeType)
{
    server_instance_t * targetP;
    int i;
    uint8_t result;

    targetP = (server_instance_t *)lwm2m_list_find(objectP->instanceList, instanceId);
    if (0 == targetP)
    {
        return (uint8_t)0x84;
    }

    if (writeType == LWM2M_WRITE_REPLACE_INSTANCE)
    {
        result = prv_server_delete(contextP, instanceId, objectP);
        if (result == (uint8_t)0x42)
        {
            result = prv_server_create(contextP, instanceId, numData, dataArray, objectP);
            if (result == (uint8_t)0x41)
            {
                result = (uint8_t)0x44;
            }
        }
        return result;
    }

    i = 0;
    do
    {
        /* No multiple instance resources */
        if (dataArray[i].type == LWM2M_TYPE_MULTIPLE_RESOURCE)
        {
            result = (uint8_t)0x84;
            continue;
        }

        switch (dataArray[i].id)
        {
        case 0:
            {
                uint32_t value = targetP->shortServerId;
                result = prv_set_int_value(dataArray + i, &value);
                if ((uint8_t)0x44 == result)
                {
                    if (0 < value && 0xFFFF >= value)
                    {
                        targetP->shortServerId = value;
                    }
                    else
                    {
                        result = (uint8_t)0x86;
                    }
                }
            }
            break;

        case 1:
            result = prv_set_int_value(dataArray + i, (uint32_t *)&(targetP->lifetime));
            break;

        case 2:
            result = prv_set_int_value(dataArray + i, &(targetP->defaultMinPeriod));
            break;

        case 3:
            result = prv_set_int_value(dataArray + i, &(targetP->defaultMaxPeriod));
            break;

        case 4:
            result = (uint8_t)0x85;
            break;

        case 5:
            result = prv_set_int_value(dataArray + i, &(targetP->disableTimeout));
            break;

        case 6:
        {
            _Bool value;

            if (1 == lwm2m_data_decode_bool(dataArray + i, &value))
            {
                targetP->storing = value;
                result = (uint8_t)0x44;
            }
            else
            {
                result = (uint8_t)0x80;
            }
        }
        break;

        case 7:
            if ((dataArray[i].type == LWM2M_TYPE_STRING || dataArray[i].type == LWM2M_TYPE_OPAQUE)
             && dataArray[i].value.asBuffer.length > 0 && dataArray[i].value.asBuffer.length <= 3

             && (strncmp((char*)dataArray[i].value.asBuffer.buffer, "U", dataArray[i].value.asBuffer.length) == 0
              || strncmp((char*)dataArray[i].value.asBuffer.buffer, "UQ", dataArray[i].value.asBuffer.length) == 0
              || strncmp((char*)dataArray[i].value.asBuffer.buffer, "S", dataArray[i].value.asBuffer.length) == 0
              || strncmp((char*)dataArray[i].value.asBuffer.buffer, "SQ", dataArray[i].value.asBuffer.length) == 0
              || strncmp((char*)dataArray[i].value.asBuffer.buffer, "US", dataArray[i].value.asBuffer.length) == 0
              || strncmp((char*)dataArray[i].value.asBuffer.buffer, "UQS", dataArray[i].value.asBuffer.length) == 0)

               )
            {
                strncpy(targetP->binding, (char*)dataArray[i].value.asBuffer.buffer, dataArray[i].value.asBuffer.length);
                result = (uint8_t)0x44;
            }
            else
            {
                result = (uint8_t)0x80;
            }
            break;

        case 8:
            result = (uint8_t)0x85;
            break;

# 933 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"

        default:
            return (uint8_t)0x84;
        }
        i++;
    } while (i < numData && result == (uint8_t)0x44);

    return result;
}

static uint8_t prv_server_execute(lwm2m_context_t *contextP,
                                  uint16_t instanceId,
                                  uint16_t resourceId,
                                  uint8_t * buffer,
                                  int length,
                                  lwm2m_object_t * objectP)

{
    server_instance_t * targetP;

    /* unused parameter */
    (void)contextP;

    targetP = (server_instance_t *)lwm2m_list_find(objectP->instanceList, instanceId);
    if (0 == targetP) return (uint8_t)0x84;

    switch (resourceId)
    {
    case 4:
        // executed in core, if COAP_204_CHANGED is returned
        if (0 < targetP->disableTimeout) return (uint8_t)0x44;
        else return (uint8_t)0x85;
    case 8:
        // executed in core, if COAP_204_CHANGED is returned
        return (uint8_t)0x44;
    default:
        return (uint8_t)0x85;
    }
}

static uint8_t prv_server_delete(lwm2m_context_t *contextP,
                                 uint16_t id,
                                 lwm2m_object_t * objectP)
{
    server_instance_t * serverInstance;

    /* unused parameter */
    (void)contextP;

    objectP->instanceList = lwm2m_list_remove(objectP->instanceList, id, (lwm2m_list_t **)&serverInstance);
    if (0 == serverInstance) return (uint8_t)0x84;

    lwm2m_free(serverInstance);

    return (uint8_t)0x42;
}

static uint8_t prv_server_create(lwm2m_context_t *contextP,
                                 uint16_t instanceId,
                                 int numData,
                                 lwm2m_data_t * dataArray,
                                 lwm2m_object_t * objectP)
{
    server_instance_t * serverInstance;
    uint8_t result;

    serverInstance = (server_instance_t *)lwm2m_malloc(sizeof(server_instance_t));
    if (0 == serverInstance) return (uint8_t)0xA0;
    memset(serverInstance, 0, sizeof(server_instance_t));

    serverInstance->instanceId = instanceId;
# 1014 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"
    objectP->instanceList = lwm2m_list_add((lwm2m_list_t *)objectP->instanceList, (lwm2m_list_t *)serverInstance);;

    result = prv_server_write(contextP, instanceId, numData, dataArray, objectP, LWM2M_WRITE_REPLACE_RESOURCES);

    if (result != (uint8_t)0x44)
    {
        (void)prv_server_delete(contextP, instanceId, objectP);
    }
    else
    {
        result = (uint8_t)0x41;
    }

    return result;
}

void copy_server_object(lwm2m_object_t * objectDest, lwm2m_object_t * objectSrc)
{
	if(0 == objectDest)
    {
        return;
    }
    memcpy(objectDest, objectSrc, sizeof(lwm2m_object_t));
    objectDest->instanceList = 0;
    objectDest->userData = 0;
    server_instance_t * instanceSrc = (server_instance_t *)objectSrc->instanceList;
    server_instance_t * previousInstanceDest = 0;
    while (instanceSrc != 0)
    {
        server_instance_t * instanceDest = (server_instance_t *)lwm2m_malloc(sizeof(server_instance_t));
        if (0 == instanceDest)
        {
            return;
        }
        memcpy(instanceDest, instanceSrc, sizeof(server_instance_t));
        // not sure it's necessary:
        strcpy(instanceDest->binding, instanceSrc->binding);
        instanceSrc = (server_instance_t *)instanceSrc->next;
        if (previousInstanceDest == 0)
        {
            objectDest->instanceList = (lwm2m_list_t *)instanceDest;
        }
        else
        {
            previousInstanceDest->next = instanceDest;
        }
        previousInstanceDest = instanceDest;
    }
}

void display_server_object(lwm2m_object_t * object)
{
    lwm2m_printf("/%u: Server object, instances:\r\n", object->objID);
    server_instance_t * serverInstance = (server_instance_t *)object->instanceList;
    while (serverInstance != 0)
    {
        lwm2m_printf("/%u/%u: instanceId: %u, shortServerId: %u, lifetime: %u, storing: %s, binding: %s",
                object->objID, serverInstance->instanceId,
                serverInstance->instanceId, serverInstance->shortServerId, serverInstance->lifetime,
                serverInstance->storing ? "true" : "false", serverInstance->binding);
# 1094 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"
        lwm2m_printf("\r\n");
        serverInstance = (server_instance_t *)serverInstance->next;
    }
}

lwm2m_object_t * get_server_object(int serverId,
                                   const char* binding,
                                   int lifetime,
                                   _Bool storing)
{
    lwm2m_object_t * serverObj;

    serverObj = (lwm2m_object_t *)lwm2m_malloc(sizeof(lwm2m_object_t));

    if (0 != serverObj)
    {
        server_instance_t * serverInstance;

        memset(serverObj, 0, sizeof(lwm2m_object_t));

        serverObj->objID = 1;






        // Manually create an hardcoded server
        serverInstance = (server_instance_t *)lwm2m_malloc(sizeof(server_instance_t));
        if (0 == serverInstance)
        {
            lwm2m_free(serverObj);
            return 0;
        }

        memset(serverInstance, 0, sizeof(server_instance_t));
        serverInstance->instanceId = 0;
        serverInstance->shortServerId = serverId;
        serverInstance->lifetime = lifetime;
        serverInstance->storing = storing;
        memcpy (serverInstance->binding, binding, strlen(binding)+1);
# 1145 "L:/PLT/pcac/lwm2m_wakaama/adaptor/asrclient/object_server.c"
        serverObj->instanceList = lwm2m_list_add((lwm2m_list_t *)serverObj->instanceList, (lwm2m_list_t *)serverInstance);;

        serverObj->readFunc = prv_server_read;
        serverObj->discoverFunc = prv_server_discover;
        serverObj->writeFunc = prv_server_write;
        serverObj->createFunc = prv_server_create;
        serverObj->deleteFunc = prv_server_delete;
        serverObj->executeFunc = prv_server_execute;
    }

    return serverObj;
}

void clean_server_object(lwm2m_object_t * object)
{
	if(0 == object)
    {
        return;
    }
    while (object->instanceList != 0)
    {
        server_instance_t * serverInstance = (server_instance_t *)object->instanceList;
        object->instanceList = object->instanceList->next;
        lwm2m_free(serverInstance);
    }
}
