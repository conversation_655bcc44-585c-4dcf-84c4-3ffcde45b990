# 1 "L:/PLT/pcac/mbedTLS/library/ecp.c"
/*
 *  Elliptic curves over GF(p): generic functions
 *
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */

/*
 * References:
 *
 * SEC1 https://www.secg.org/sec1-v2.pdf
 * GECC = Guide to Elliptic Curve Cryptography - <PERSON>, Men<PERSON><PERSON>, Vanstone
 * FIPS 186-3 http://csrc.nist.gov/publications/fips/fips186-3/fips_186-3.pdf
 * RFC 4492 for the related TLS structures and constants
 * - https://www.rfc-editor.org/rfc/rfc4492
 * RFC 7748 for the Curve448 and Curve25519 curve definitions
 * - https://www.rfc-editor.org/rfc/rfc7748
 *
 * [Curve25519] https://cr.yp.to/ecdh/curve25519-20060209.pdf
 *
 * [2] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>e<PERSON><PERSON>. Resistance against differential power analysis
 *     for elliptic curve cryptosystems. In : Cryptographic Hardware and
 *     Embedded Systems. Springer Berlin Heidelberg, 1999. p. 292-302.
 *     <http://link.springer.com/chapter/10.1007/3-540-48059-5_25>
 *
 * [3] HEDABOU, Mustapha, PINEL, Pierre, et B'EN'ETEAU, Lucien. A comb method to
 *     render ECC resistant against Side Channel Attacks. IACR Cryptology
 *     ePrint Archive, 2004, vol. 2004, p. 342.
 *     <http://eprint.iacr.org/2004/342.pdf>
 */

# 1 "L:/PLT/pcac/mbedTLS/library/common.h"
/**
 * \file common.h
 *
 * \brief Utility macros for internal use in the library
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */




# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"
/**
 * \file mbedtls/build_info.h
 *
 * \brief Build-time configuration info
 *
 *  Include this file if you need to depend on the
 *  configuration options defined in mbedtls_config.h or MBEDTLS_CONFIG_FILE
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */




# 25 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

/*
 * This set of compile-time defines can be used to determine the version number
 * of the Mbed TLS library used. Run-time variables for the same can be found in
 * version.h
 */

/**
 * The version number x.y.z is split into three parts.
 * Major, Minor, Patchlevel
 */




/**
 * The single version number has the following structure:
 *    MMNNPP00
 *    Major version | Minor version | Patch version
 */




/* Macros for build-time platform detection */





























/* This is defined if the architecture is Armv8-A, or higher */
# 94 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

# 102 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"





/* Define `inline` on some non-C99-compliant compilers. */





# 119 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

/* X.509, TLS and non-PSA crypto configuration */
# 1 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"
/**
 * \file asros_mbedtls_config.h
 *
 * \brief Minimal configuration of features that do not require an entropy source
 */
/*
 *  Copyright (C) 2016, ARM Limited, All Rights Reserved
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  This file is part of mbed TLS (https://tls.mbed.org)
 */
/*
 * Minimal configuration of features that do not require an entropy source
 * Distinguishing reatures:
 * - no entropy module
 * - no TLS protocol implementation available due to absence of an entropy
 *   source
 *
 * See README.txt for usage instructions.
 */





typedef unsigned int size_t;

# 50 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"








//#define MBEDTLS_CONFIG_TLS_DEBUG





/*mbedtls macro sync define begin:*/
/*mbedtls macro sync define begin:*/
/*mbedtls macro sync define begin:*/

/* System support */










/* mbed TLS feature support */
# 88 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"
//#define MBEDTLS_ECDSA_DETERMINISTIC // closed for reduce code size



//#define MBEDTLS_SELF_TEST // closed for reduce code size
//#define MBEDTLS_VERSION_FEATURES //closed for reduce code size



/* mbed TLS modules */


//#define MBEDTLS_ASN1_WRITE_C // closed for reduce code size











//#define MBEDTLS_ERROR_C //closed for reduce code size

//#define MBEDTLS_HMAC_DRBG_C // closed for reduce code size
# 127 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"
//#define MBEDTLS_VERSION_C // closed for reduce code size
# 141 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"
//#define MBEDTLS_SSL_SRV_C
//#define MBEDTLS_CERTS_C // need open?
//#define MBEDTLS_SSL_SESSION_TICKETS















/*2022-01-18, add option, for redmine#51878*/
# 166 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"

/**
 * \def MBEDTLS_X509_ALLOW_UNSUPPORTED_CRITICAL_EXTENSION
 *
 * If set, the X509 parser will not break-off when parsing an X509 certificate
 * and encountering an unknown critical extension.
 *
 * \warning Depending on your PKI use, enabling this can be a security risk!
 *
 * Uncomment to prevent an error.
 */




# 204 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"

/*mbedtls macro sync define end:*/
/*mbedtls macro sync define end:*/
/*mbedtls macro sync define end:*/



# 222 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"

/* Miscellaneous options */
//#define MBEDTLS_AES_ROM_TABLES

# 1 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform_alt.h"
/*
  *  platform_alt.h
  *
  *  Copyright (C) 2018, Arm Limited, All Rights Reserved
  *  SPDX-License-Identifier: Apache-2.0
  *
  *  Licensed under the Apache License, Version 2.0 (the "License"); you may
  *  not use this file except in compliance with the License.
  *  You may obtain a copy of the License at
  *
  *  http://www.apache.org/licenses/LICENSE-2.0
  *
  *  Unless required by applicable law or agreed to in writing, software
  *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  *  See the License for the specific language governing permissions and
  *  limitations under the License.
  *
  */




# 1 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.h"
/**
 *  Copyright (C) 2006-2016, ARM Limited, All Rights Reserved
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  This file is part of mbed TLS (https://tls.mbed.org)
 */





# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"
/**
 * \file mbedtls/build_info.h
 *
 * \brief Build-time configuration info
 *
 *  Include this file if you need to depend on the
 *  configuration options defined in mbedtls_config.h or MBEDTLS_CONFIG_FILE
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */

# 25 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.h"
# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_time.h"
/**
 * \file platform_time.h
 *
 * \brief Mbed TLS Platform time abstraction
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */



# 14 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_time.h"





/*
 * The time_t datatype
 */



/* For time_t */
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"
/* time.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.12 */
/* Copyright (C) Codemist Ltd., 1988-1993.                      */
/* Copyright 1991-1993 ARM Limited. All rights reserved.        */
/* version 0.03 */

/*
 * time.h declares two macros, four types and several functions for
 * manipulating time. Many functions deal with a calendar time that
 * represents the current date (according to the Gregorian
 * calendar) and time. Some functions deal with local time, which
 * is the calendar time expressed for some specific time zone, and
 * with Daylight Savings Time, which is a temporary change in the
 * algorithm for determining local time.
 */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */













# 41 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 57 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"




    /* CLOCKS_PER_SEC: the number per second of the value returned by the
     * clock function. */
# 73 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"





typedef unsigned int clock_t;    /* cpu time type */
typedef unsigned int time_t;     /* date/time in unix secs past 1-Jan-70 */

#pragma push
#pragma anon_unions

struct tm {
    int tm_sec;   /* seconds after the minute, 0 to 60
                     (0 - 60 allows for the occasional leap second) */
    int tm_min;   /* minutes after the hour, 0 to 59 */
    int tm_hour;  /* hours since midnight, 0 to 23 */
    int tm_mday;  /* day of the month, 1 to 31 */
    int tm_mon;   /* months since January, 0 to 11 */
    int tm_year;  /* years since 1900 */
    int tm_wday;  /* days since Sunday, 0 to 6 */
    int tm_yday;  /* days since January 1, 0 to 365 */
    int tm_isdst; /* Daylight Savings Time flag */
    union {       /* ABI-required extra fields, in a variety of types */
        struct {
            int __extra_1, __extra_2;
        };
        struct {
            long __extra_1_long, __extra_2_long;
        };
        struct {
            char *__extra_1_cptr, *__extra_2_cptr;
        };
        struct {
            void *__extra_1_vptr, *__extra_2_vptr;
        };
    };
};

#pragma pop

   /* struct tm holds the components of a calendar time, called the broken-down
    * time. The value of tm_isdst is positive if Daylight Savings Time is in
    * effect, zero if Daylight Savings Time is not in effect, and negative if
    * the information is not available.
    */

extern __declspec(__nothrow) clock_t clock(void);
   /* determines the processor time used.
    * Returns: the implementation's best approximation to the processor time
    *          used by the program since program invocation. The time in
    *          seconds is the value returned divided by the value of the macro
    *          CLK_TCK. The value (clock_t)-1 is returned if the processor time
    *          used is not available.
    */
extern __declspec(__nothrow) double difftime(time_t /*time1*/, time_t /*time0*/);
   /*
    * computes the difference between two calendar times: time1 - time0.
    * Returns: the difference expressed in seconds as a double.
    */
extern __declspec(__nothrow) time_t mktime(struct tm * /*timeptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the broken-down time, expressed as local time, in the structure
    * pointed to by timeptr into a calendar time value with the same encoding
    * as that of the values returned by the time function. The original values
    * of the tm_wday and tm_yday components of the structure are ignored, and
    * the original values of the other components are not restricted to the
    * ranges indicated above. On successful completion, the values of the
    * tm_wday and tm_yday structure components are set appropriately, and the
    * other components are set to represent the specified calendar time, but
    * with their values forced to the ranges indicated above; the final value
    * of tm_mday is not set until tm_mon and tm_year are determined.
    * Returns: the specified calendar time encoded as a value of type time_t.
    *          If the calendar time cannot be represented, the function returns
    *          the value (time_t)-1.
    */
extern __declspec(__nothrow) time_t time(time_t * /*timer*/);
   /*
    * determines the current calendar time. The encoding of the value is
    * unspecified.
    * Returns: the implementations best approximation to the current calendar
    *          time. The value (time_t)-1 is returned if the calendar time is
    *          not available. If timer is not a null pointer, the return value
    *          is also assigned to the object it points to.
    */

extern __declspec(__nothrow) char *asctime(const struct tm * /*timeptr*/) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) char *_asctime_r(const struct tm * /*timeptr*/,
                                char * __restrict /*buf*/) __attribute__((__nonnull__(1,2)));

extern __declspec(__nothrow) char *asctime_r(const struct tm * /*timeptr*/,
                               char * __restrict /*buf*/) __attribute__((__nonnull__(1,2)));

   /*
    * converts the broken-down time in the structure pointed to by timeptr into
    * a string in the form "Sun Sep 16 01:03:52 1973\n\0".
    * Returns: a pointer to the string containing the date and time.
    */
extern __declspec(__nothrow) char *ctime(const time_t * /*timer*/) __attribute__((__nonnull__(1)));
   /*
    * converts the calendar time pointed to by timer to local time in the form
    * of a string. It is equivalent to asctime(localtime(timer));
    * Returns: the pointer returned by the asctime function with that
    *          broken-down time as argument.
    */
extern __declspec(__nothrow) struct tm *gmtime(const time_t * /*timer*/) __attribute__((__nonnull__(1)));
   /*
    * converts the calendar time pointed to by timer into a broken-down time,
    * expressed as Greenwich Mean Time (GMT).
    * Returns: a pointer to that object or a null pointer if GMT not available.
    */
extern __declspec(__nothrow) struct tm *localtime(const time_t * /*timer*/) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) struct tm *_localtime_r(const time_t * __restrict /*timer*/,
                                       struct tm * __restrict /*result*/) __attribute__((__nonnull__(1,2)));

extern __declspec(__nothrow) struct tm *localtime_r(const time_t * __restrict /*timer*/,
                                      struct tm * __restrict /*result*/) __attribute__((__nonnull__(1,2)));

   /*
    * converts the calendar time pointed to by timer into a broken-down time,
    * expressed a local time.
    * Returns: a pointer to that object.
    */
extern __declspec(__nothrow) size_t strftime(char * __restrict /*s*/, size_t /*maxsize*/,
                       const char * __restrict /*format*/,
                       const struct tm * __restrict /*timeptr*/) __attribute__((__nonnull__(1,3,4)));
   /*
    * places characters into the array pointed to by s as controlled by the
    * string pointed to by format. The format string consists of zero or more
    * directives and ordinary characters. A directive consists of a % character
    * followed by a character that determines the directive's behaviour. All
    * ordinary characters (including the terminating null character) are copied
    * unchanged into the array. No more than maxsize characters are placed into
    * the array. Each directive is replaced by appropriate characters  as
    * described in the following list. The appropriate characters are
    * determined by the LC_TIME category of the current locale and by the
    * values contained in the structure pointed to by timeptr.
    * %a is replaced by the locale's abbreviated weekday name.
    * %A is replaced by the locale's full weekday name.
    * %b is replaced by the locale's abbreviated month name.
    * %B is replaced by the locale's full month name.
    * %c is replaced by the locale's appropriate date and time representation.
    * %d is replaced by the day of the month as a decimal number (01-31).
    * %H is replaced by the hour (24-hour clock) as a decimal number (00-23).
    * %I is replaced by the hour (12-hour clock) as a decimal number (01-12).
    * %j is replaced by the day of the year as a decimal number (001-366).
    * %m is replaced by the month as a decimal number (01-12).
    * %M is replaced by the minute as a decimal number (00-59).
    * %p is replaced by the locale's equivalent of either AM or PM designations
    *       associated with a 12-hour clock.
    * %S is replaced by the second as a decimal number (00-61).
    * %U is replaced by the week number of the year (Sunday as the first day of
    *       week 1) as a decimal number (00-53).
    * %w is replaced by the weekday as a decimal number (0(Sunday) - 6).
    * %W is replaced by the week number of the year (Monday as the first day of
    *       week 1) as a decimal number (00-53).
    * %x is replaced by the locale's appropriate date representation.
    * %X is replaced by the locale's appropriate time representation.
    * %y is replaced by the year without century as a decimal number (00-99).
    * %Y is replaced by the year with century as a decimal number.
    * %Z is replaced by the timezone name or abbreviation, or by no characters
    *       if no time zone is determinable.
    * %% is replaced by %.
    * If a directive is not one of the above, the behaviour is undefined.
    * Returns: If the total number of resulting characters including the
    *          terminating null character is not more than maxsize, the
    *          strftime function returns the number of characters placed into
    *          the array pointed to by s not including the terminating null
    *          character. otherwise, zero is returned and the contents of the
    *          array are indeterminate.
    */











# 280 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"



/* end of time.h */

# 27 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_time.h"
typedef time_t mbedtls_time_t;


# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
/* Copyright (C) ARM Ltd., 1999,2014 */
/* All rights reserved */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */









    /* armcc has builtin '__int64' which can be used in --strict mode */
# 27 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
    /* armclang and non-strict armcc allow 'long long' in system headers */











# 46 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"


/*
 * 'signed' is redundant below, except for 'signed char' and if
 * the typedef is used to declare a bitfield.
 */

    /* 7.18.1.1 */

    /* exact-width signed integer types */
typedef   signed          char int8_t;
typedef   signed short     int int16_t;
typedef   signed           int int32_t;
typedef   signed       __int64 int64_t;

    /* exact-width unsigned integer types */
typedef unsigned          char uint8_t;
typedef unsigned short     int uint16_t;
typedef unsigned           int uint32_t;
typedef unsigned       __int64 uint64_t;

    /* 7.18.1.2 */

    /* smallest type of at least n bits */
    /* minimum-width signed integer types */
typedef   signed          char int_least8_t;
typedef   signed short     int int_least16_t;
typedef   signed           int int_least32_t;
typedef   signed       __int64 int_least64_t;

    /* minimum-width unsigned integer types */
typedef unsigned          char uint_least8_t;
typedef unsigned short     int uint_least16_t;
typedef unsigned           int uint_least32_t;
typedef unsigned       __int64 uint_least64_t;

    /* 7.18.1.3 */

    /* fastest minimum-width signed integer types */
typedef   signed           int int_fast8_t;
typedef   signed           int int_fast16_t;
typedef   signed           int int_fast32_t;
typedef   signed       __int64 int_fast64_t;

    /* fastest minimum-width unsigned integer types */
typedef unsigned           int uint_fast8_t;
typedef unsigned           int uint_fast16_t;
typedef unsigned           int uint_fast32_t;
typedef unsigned       __int64 uint_fast64_t;

    /* 7.18.1.4 integer types capable of holding object pointers */




typedef   signed           int intptr_t;
typedef unsigned           int uintptr_t;


    /* 7.18.1.5 greatest-width integer types */
typedef   signed     long long intmax_t;
typedef unsigned     long long uintmax_t;




    /* 7.18.2.1 */

    /* minimum values of exact-width signed integer types */





    /* maximum values of exact-width signed integer types */





    /* maximum values of exact-width unsigned integer types */





    /* 7.18.2.2 */

    /* minimum values of minimum-width signed integer types */





    /* maximum values of minimum-width signed integer types */





    /* maximum values of minimum-width unsigned integer types */





    /* 7.18.2.3 */

    /* minimum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width unsigned integer types */





    /* 7.18.2.4 */

    /* minimum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding unsigned integer type */






    /* 7.18.2.5 */

    /* minimum value of greatest-width signed integer type */


    /* maximum value of greatest-width signed integer type */


    /* maximum value of greatest-width unsigned integer type */


    /* 7.18.3 */

    /* limits of ptrdiff_t */
# 216 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of sig_atomic_t */



    /* limit of size_t */






    /* limits of wchar_t */
    /* NB we have to undef and redef because they're defined in both
     * stdint.h and wchar.h */



# 241 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of wint_t */







    /* 7.18.4.1 macros for minimum-width integer constants */










    /* 7.18.4.2 macros for greatest-width integer constants */











# 305 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"






/* end of stdint.h */
# 34 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_time.h"
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"
/* Copyright (C) ARM Ltd., 1999,2014 */
/* All rights reserved */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */

/* Based on WG14/N843 (C9X) Committee Draft August 3, 1998 */








# 20 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"



# 33 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"


    /* 7.8.1 */

# 51 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 66 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 81 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 96 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 111 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 126 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 141 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 156 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 171 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 186 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

# 201 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"





   /* unconditional in non-strict C for consistency of debug info */



      typedef unsigned short wchar_t; /* see <stddef.h> */
# 221 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\inttypes.h"

typedef struct imaxdiv_t { intmax_t quot, rem; } imaxdiv_t;
   /* type of the value returned by the imaxdiv function. */





__declspec(__nothrow) intmax_t strtoimax(const char * __restrict /*nptr*/,
                   char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
    /* as for strtol */
__declspec(__nothrow) uintmax_t strtoumax(const char * __restrict /*nptr*/,
                    char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
    /* as for strtoul */

__declspec(__nothrow) intmax_t wcstoimax(const wchar_t * __restrict /*nptr*/,
                   wchar_t ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
__declspec(__nothrow) uintmax_t wcstoumax(const wchar_t * __restrict /*nptr*/,
                    wchar_t ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));

extern __declspec(__nothrow) __attribute__((const)) intmax_t imaxabs(intmax_t /*j*/);
   /*
    * computes the absolute value of an intmax_t j. If the result cannot be
    * represented, the behaviour is undefined.
    * Returns: the absolute value.
    */
extern __declspec(__nothrow) __attribute__((const)) imaxdiv_t imaxdiv(intmax_t /*numer*/, intmax_t /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the sign of
    * the resulting quotient is that of the algebraic quotient, and the
    * magnitude of the resulting quotient is the largest integer less than the
    * magnitude of the algebraic quotient. If the result cannot be represented,
    * the behaviour is undefined; otherwise, quot * denom + rem shall equal
    * numer.
    * Returns: a structure of type imaxdiv_t, comprising both the quotient and
    *          the remainder. the structure shall contain the following
    *          members, in either order.
    *          intmax_t quot; intmax_t rem;
    */







/* end of inttypes.h */

# 35 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_time.h"
typedef int64_t mbedtls_ms_time_t;


/**
 * \brief   Get time in milliseconds.
 *
 * \return Monotonically-increasing current time in milliseconds.
 *
 * \note Define MBEDTLS_PLATFORM_MS_TIME_ALT to be able to provide an
 *       alternative implementation
 *
 * \warning This function returns a monotonically-increasing time value from a
 *          start time that will differ from platform to platform, and possibly
 *          from run to run of the process.
 *
 */
mbedtls_ms_time_t mbedtls_ms_time(void);

/*
 * The function pointers for time
 */
# 74 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_time.h"





# 27 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.h"


# 46 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.h"









# 63 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.h"

/*
 * MBEDTLS_ERR_PLATFORM_HW_FAILED is deprecated and should not be used.
 */




typedef struct mbedtls_timing_delay_context
{
    unsigned int timer; // ticks
    unsigned int int_ms;
    unsigned int fin_ms;
} mbedtls_timing_delay_context;

int mbedtls_timing_get_delay(void *data);
void mbedtls_timing_set_delay(void *data, unsigned int int_ms, unsigned int fin_ms);


# 92 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform.h"


mbedtls_ms_time_t mbedtls_ms_time(void);


# 25 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform_alt.h"







# 74 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_platform_alt.h"

# 227 "L:/PLT/pcac/mbedTLS/asros/asros_mbedtls_config.h"

# 125 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"








/* Target and application specific configurations
 *
 * Allow user to override any previous default.
 *
 */




/* PSA crypto configuration */
# 153 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

/* Indicate that all configuration files have been read.
 * It is now time to adjust the configuration (follow through on dependencies,
 * make PSA and legacy crypto consistent, etc.).
 */


/* Auto-enable MBEDTLS_CTR_DRBG_USE_128_BIT_KEY if
 * MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH and MBEDTLS_CTR_DRBG_C defined
 * to ensure a 128-bit key size in CTR_DRBG.
 */




/* Auto-enable MBEDTLS_MD_C if needed by a module that didn't require it
 * in a previous release, to ensure backwards compatibility.
 */




/* PSA crypto specific configuration options
 * - If config_psa.h reads a configuration option in preprocessor directive,
 *   this symbol should be set before its inclusion. (e.g. MBEDTLS_MD_C)
 * - If config_psa.h writes a configuration option in conditional directive,
 *   this symbol should be consulted after its inclusion.
 *   (e.g. MBEDTLS_MD_LIGHT)
 */






# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"
/**
 * \file mbedtls/config_adjust_legacy_crypto.h
 * \brief Adjust legacy configuration configuration
 *
 * This is an internal header. Do not include it directly.
 *
 * Automatically enable certain dependencies. Generally, MBEDTLS_xxx
 * configurations need to be explicitly enabled by the user: enabling
 * MBEDTLS_xxx_A but not MBEDTLS_xxx_B when A requires B results in a
 * compilation error. However, we do automatically enable certain options
 * in some circumstances. One case is if MBEDTLS_xxx_B is an internal option
 * used to identify parts of a module that are used by other module, and we
 * don't want to make the symbol MBEDTLS_xxx_B part of the public API.
 * Another case is if A didn't depend on B in earlier versions, and we
 * want to use B in A but we need to preserve backward compatibility with
 * configurations that explicitly activate MBEDTLS_xxx_A but not
 * MBEDTLS_xxx_B.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */




# 34 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Ideally, we'd set those as defaults in mbedtls_config.h, but
 * putting an #ifdef _WIN32 in mbedtls_config.h would confuse config.py.
 *
 * So, adjust it here.
 * Not related to crypto, but this is the bottom of the stack. */
# 50 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Auto-enable CIPHER_C when any of the unauthenticated ciphers is builtin
 * in PSA. */
# 65 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Auto-enable MBEDTLS_MD_LIGHT based on MBEDTLS_MD_C.
 * This allows checking for MD_LIGHT rather than MD_LIGHT || MD_C.
 */




/* Auto-enable MBEDTLS_MD_LIGHT if needed by a module that didn't require it
 * in a previous release, to ensure backwards compatibility.
 */
# 87 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"


/*
 * - MBEDTLS_MD_CAN_xxx is defined if the md module can perform xxx.
 * - MBEDTLS_MD_xxx_VIA_PSA is defined if the md module may perform xxx via PSA
 *   (see below).
 * - MBEDTLS_MD_SOME_PSA is defined if at least one algorithm may be performed
 *   via PSA (see below).
 * - MBEDTLS_MD_SOME_LEGACY is defined if at least one algorithm may be performed
 *   via a direct legacy call (see below).
 *
 * The md module performs an algorithm via PSA if there is a PSA hash
 * accelerator and the PSA driver subsytem is initialized at the time the
 * operation is started, and makes a direct legacy call otherwise.
 */

/* PSA accelerated implementations */
# 162 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Built-in implementations */
# 199 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"



/* BLOCK_CIPHER module can dispatch to PSA when:
 * - PSA is enabled and drivers have been initialized
 * - desired key type is supported on the PSA side
 * If the above conditions are not met, but the legacy support is enabled, then
 * BLOCK_CIPHER will dynamically fallback to it.
 *
 * In case BLOCK_CIPHER is defined (see below) the following symbols/helpers
 * can be used to define its capabilities:
 * - MBEDTLS_BLOCK_CIPHER_SOME_PSA: there is at least 1 key type between AES,
 *   ARIA and Camellia which is supported through a driver;
 * - MBEDTLS_BLOCK_CIPHER_xxx_VIA_PSA: xxx key type is supported through a
 *   driver;
 * - MBEDTLS_BLOCK_CIPHER_xxx_VIA_LEGACY: xxx key type is supported through
 *   a legacy module (i.e. MBEDTLS_xxx_C)
 */
# 231 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

# 241 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Helpers to state that BLOCK_CIPHER module supports AES, ARIA and/or Camellia
 * block ciphers via either PSA or legacy. */
# 256 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* GCM_C and CCM_C can either depend on (in order of preference) BLOCK_CIPHER_C
 * or CIPHER_C. The former is auto-enabled when:
 * - CIPHER_C is not defined, which is also the legacy solution;
 * - BLOCK_CIPHER_SOME_PSA because in this case BLOCK_CIPHER can take advantage
 *   of the driver's acceleration.
 */





/* Helpers for GCM/CCM capabilities */















/* MBEDTLS_ECP_LIGHT is auto-enabled by the following symbols:
 * - MBEDTLS_ECP_C because now it consists of MBEDTLS_ECP_LIGHT plus functions
 *   for curve arithmetic. As a consequence if MBEDTLS_ECP_C is required for
 *   some reason, then MBEDTLS_ECP_LIGHT should be enabled as well.
 * - MBEDTLS_PK_PARSE_EC_EXTENDED and MBEDTLS_PK_PARSE_EC_COMPRESSED because
 *   these features are not supported in PSA so the only way to have them is
 *   to enable the built-in solution.
 *   Both of them are temporary dependencies:
 *   - PK_PARSE_EC_EXTENDED will be removed after #7779 and #7789
 *   - support for compressed points should also be added to PSA, but in this
 *     case there is no associated issue to track it yet.
 * - PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_DERIVE because Weierstrass key derivation
 *   still depends on ECP_LIGHT.
 * - PK_C + USE_PSA + PSA_WANT_ALG_ECDSA is a temporary dependency which will
 *   be fixed by #7453.
 */
# 306 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Backward compatibility: after #8740 the RSA module offers functions to parse
 * and write RSA private/public keys without relying on the PK one. Of course
 * this needs ASN1 support to do so, so we enable it here. */





/* MBEDTLS_PK_PARSE_EC_COMPRESSED is introduced in Mbed TLS version 3.5, while
 * in previous version compressed points were automatically supported as long
 * as PK_PARSE_C and ECP_C were enabled. As a consequence, for backward
 * compatibility, we auto-enable PK_PARSE_EC_COMPRESSED when these conditions
 * are met. */




/* Helper symbol to state that there is support for ECDH, either through
 * library implementation (ECDH_C) or through PSA. */





/* PK module can achieve ECDSA functionalities by means of either software
 * implementations (ECDSA_C) or through a PSA driver. The following defines
 * are meant to list these capabilities in a general way which abstracts how
 * they are implemented under the hood. */
# 350 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"





/* If MBEDTLS_PSA_CRYPTO_C is defined, make sure MBEDTLS_PSA_CRYPTO_CLIENT
 * is defined as well to include all PSA code.
 */




/* Helpers to state that each key is supported either on the builtin or PSA side. */
# 402 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Helper symbol to state that the PK module has support for EC keys. This
 * can either be provided through the legacy ECP solution or through the
 * PSA friendly MBEDTLS_PK_USE_PSA_EC_DATA (see pk.h for its description). */





/* Historically pkparse did not check the CBC padding when decrypting
 * a key. This was a bug, which is now fixed. As a consequence, pkparse
 * now needs PKCS7 padding support, but existing configurations might not
 * enable it, so we enable it here. */




/* Backwards compatibility for some macros which were renamed to reflect that
 * they are related to Armv8, not aarch64. */
# 428 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* psa_util file features some ECDSA conversion functions, to convert between
 * legacy's ASN.1 DER format and PSA's raw one. */





/* Some internal helpers to determine which keys are available. */
# 449 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_legacy_crypto.h"

/* Some internal helpers to determine which operation modes are available. */

























# 189 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_x509.h"
/**
 * \file mbedtls/config_adjust_x509.h
 * \brief Adjust X.509 configuration
 *
 * This is an internal header. Do not include it directly.
 *
 * Automatically enable certain dependencies. Generally, MBEDTLS_xxx
 * configurations need to be explicitly enabled by the user: enabling
 * MBEDTLS_xxx_A but not MBEDTLS_xxx_B when A requires B results in a
 * compilation error. However, we do automatically enable certain options
 * in some circumstances. One case is if MBEDTLS_xxx_B is an internal option
 * used to identify parts of a module that are used by other module, and we
 * don't want to make the symbol MBEDTLS_xxx_B part of the public API.
 * Another case is if A didn't depend on B in earlier versions, and we
 * want to use B in A but we need to preserve backward compatibility with
 * configurations that explicitly activate MBEDTLS_xxx_A but not
 * MBEDTLS_xxx_B.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */




# 34 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_x509.h"

# 191 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_ssl.h"
/**
 * \file mbedtls/config_adjust_ssl.h
 * \brief Adjust TLS configuration
 *
 * This is an internal header. Do not include it directly.
 *
 * Automatically enable certain dependencies. Generally, MBEDTLS_xxx
 * configurations need to be explicitly enabled by the user: enabling
 * MBEDTLS_xxx_A but not MBEDTLS_xxx_B when A requires B results in a
 * compilation error. However, we do automatically enable certain options
 * in some circumstances. One case is if MBEDTLS_xxx_B is an internal option
 * used to identify parts of a module that are used by other module, and we
 * don't want to make the symbol MBEDTLS_xxx_B part of the public API.
 * Another case is if A didn't depend on B in earlier versions, and we
 * want to use B in A but we need to preserve backward compatibility with
 * configurations that explicitly activate MBEDTLS_xxx_A but not
 * MBEDTLS_xxx_B.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */




# 34 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_ssl.h"

/* The following blocks make it easier to disable all of TLS,
 * or of TLS 1.2 or 1.3 or DTLS, without having to manually disable all
 * key exchanges, options and extensions related to them. */

# 46 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_ssl.h"





# 59 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_ssl.h"

# 76 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_ssl.h"

# 84 "L:/PLT/pcac/mbedTLS/include/mbedtls/config_adjust_ssl.h"







# 193 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

/* Indicate that all configuration symbols are set,
 * even the ones that are calculated programmatically.
 * It is now safe to query the configuration (to check it, to size buffers,
 * etc.).
 */


# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"
/**
 * \file check_config.h
 *
 * \brief Consistency checks for configuration options
 *
 * This is an internal header. Do not include it directly.
 *
 * This header is included automatically by all public Mbed TLS headers
 * (via mbedtls/build_info.h). Do not include it directly in a configuration
 * file such as mbedtls/mbedtls_config.h or #MBEDTLS_USER_CONFIG_FILE!
 * It would run at the wrong time due to missing derived symbols.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */




/* *INDENT-OFF* */







/*
 * We assume CHAR_BIT is 8 in many places. In practice, this is true on our
 * target platforms, so not an issue, but let's just be extra sure.
 */
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
/* limits.h: ANSI 'C' (X3J11 Oct 88) library header, section 2.2.4.2 */
/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991-1997 ARM Limited. All rights reserved         */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */






    /* max number of bits for smallest object that is not a bit-field (byte) */

    /* mimimum value for an object of type signed char */

    /* maximum value for an object of type signed char */

    /* maximum value for an object of type unsigned char */
# 30 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
      /* minimum value for an object of type char */

      /* maximum value for an object of type char */






# 45 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
    /* maximum number of bytes in a multibyte character, */
    /* for any supported locale */


    /* minimum value for an object of type short int */

    /* maximum value for an object of type short int */

    /* maximum value for an object of type unsigned short int */

    /* minimum value for an object of type int */

    /* maximum value for an object of type int */

    /* maximum value for an object of type unsigned int */





    /* minimum value for an object of type long int */





    /* maximum value for an object of type long int */





    /* maximum value for an object of type unsigned long int */


      /* minimum value for an object of type long long int */

      /* maximum value for an object of type long long int */

      /* maximum value for an object of type unsigned long int */




/* end of limits.h */

# 34 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"




# 39 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 47 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"














/* Limitations on ECC key types acceleration: if we have any of `PUBLIC_KEY`,
 * `KEY_PAIR_BASIC`, `KEY_PAIR_IMPORT`, `KEY_PAIR_EXPORT` then we must have
 * all 4 of them.
 */
# 76 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

/* Limitations on ECC curves acceleration: partial curve acceleration is only
 * supported with crypto excluding PK, X.509 or TLS.
 * Note: no need to check X.509 as it depends on PK. */
# 100 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"





















# 135 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 150 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"





# 172 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 181 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"






# 197 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"










# 223 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 241 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 251 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

































































# 322 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"




























# 356 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"













# 383 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

/* Use of EC J-PAKE in TLS requires SHA-256. */





# 397 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 412 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"





































# 455 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 462 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"




































































































































































































































# 696 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"
















# 719 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"















































# 776 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"










# 796 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"





/* TLS 1.3 requires separate HKDF parts from PSA,
 * and at least one ciphersuite, so at least SHA-256 or SHA-384
 * from PSA to use with HKDF.
 *
 * Note: for dependencies common with TLS 1.2 (running handshake hash),
 * see MBEDTLS_SSL_TLS_C. */
# 814 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 822 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"







/*
 * The current implementation of TLS 1.3 requires MBEDTLS_SSL_KEEP_PEER_CERTIFICATE.
 */




# 851 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 858 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 865 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"



















/* TLS 1.2 and 1.3 require SHA-256 or SHA-384 (running handshake hash) */
# 897 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"














































# 950 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"





































# 1003 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"









# 1018 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

# 1025 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"





















































/* Reject attempts to enable options that have been removed and that could
 * cause a build to succeed but with features removed. */













































# 1132 "L:/PLT/pcac/mbedTLS/include/mbedtls/check_config.h"

/*
 * Avoid warning from -pedantic. This is a convenient place for this
 * workaround since this is included by every single file before the
 * #if defined(MBEDTLS_xxx_C) that results in empty translation units.
 */
typedef int mbedtls_iso_c_forbids_empty_translation_units;

/* *INDENT-ON* */
# 202 "L:/PLT/pcac/mbedTLS/include/mbedtls/build_info.h"

# 15 "L:/PLT/pcac/mbedTLS/library/common.h"
# 1 "L:/PLT/pcac/mbedTLS/library/alignment.h"
/**
 * \file alignment.h
 *
 * \brief Utility code for dealing with unaligned memory accesses
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */




# 15 "L:/PLT/pcac/mbedTLS/library/alignment.h"
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
/* string.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.11 */
/* Copyright (C) Codemist Ltd., 1988-1993.                        */
/* Copyright 1991-1993 ARM Limited. All rights reserved.          */
/* version 0.04 */

/*
 * RCS $Revision$
 * Checkin $Date$
 */

/*
 * string.h declares one type and several functions, and defines one macro
 * useful for manipulating character arrays and other objects treated as
 * character arrays. Various methods are used for determining the lengths of
 * the arrays, but in all cases a char * or void * argument points to the
 * initial (lowest addresses) character of the array. If an array is written
 * beyond the end of an object, the behaviour is undefined.
 */












# 38 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 54 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"




extern __declspec(__nothrow) void *memcpy(void * __restrict /*s1*/,
                    const void * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) void *memmove(void * /*s1*/,
                    const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. Copying takes place as if the n characters from the
    * object pointed to by s2 are first copied into a temporary array of n
    * characters that does not overlap the objects pointed to by s1 and s2,
    * and then the n characters from the temporary array are copied into the
    * object pointed to by s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strcpy(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string pointed to by s2 (including the terminating nul
    * character) into the array pointed to by s1. If copying takes place
    * between objects that overlap, the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncpy(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies not more than n characters (characters that follow a null
    * character are not copied) from the array pointed to by s2 into the array
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */

extern __declspec(__nothrow) char *strcat(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends a copy of the string pointed to by s2 (including the terminating
    * null character) to the end of the string pointed to by s1. The initial
    * character of s2 overwrites the null character at the end of s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncat(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends not more than n characters (a null character and characters that
    * follow it are not appended) from the array pointed to by s2 to the end of
    * the string pointed to by s1. The initial character of s2 overwrites the
    * null character at the end of s1. A terminating null character is always
    * appended to the result.
    * Returns: the value of s1.
    */

/*
 * The sign of a nonzero value returned by the comparison functions is
 * determined by the sign of the difference between the values of the first
 * pair of characters (both interpreted as unsigned char) that differ in the
 * objects being compared.
 */

extern __declspec(__nothrow) int memcmp(const void * /*s1*/, const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the first n characters of the object pointed to by s1 to the
    * first n characters of the object pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the object pointed to by s1 is greater than, equal to, or
    *          less than the object pointed to by s2.
    */
extern __declspec(__nothrow) int strcmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcasecmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2,
    * case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncasecmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2, case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcoll(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2, both
    * interpreted as appropriate to the LC_COLLATE category of the current
    * locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2 when both are interpreted
    *          as appropriate to the current locale.
    */

extern __declspec(__nothrow) size_t strxfrm(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * transforms the string pointed to by s2 and places the resulting string
    * into the array pointed to by s1. The transformation function is such that
    * if the strcmp function is applied to two transformed strings, it returns
    * a value greater than, equal to or less than zero, corresponding to the
    * result of the strcoll function applied to the same two original strings.
    * No more than n characters are placed into the resulting array pointed to
    * by s1, including the terminating null character. If n is zero, s1 is
    * permitted to be a null pointer. If copying takes place between objects
    * that overlap, the behaviour is undefined.
    * Returns: The length of the transformed string is returned (not including
    *          the terminating null character). If the value returned is n or
    *          more, the contents of the array pointed to by s1 are
    *          indeterminate.
    */


# 193 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) void *memchr(const void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an unsigned char) in the
    * initial n characters (each interpreted as unsigned char) of the object
    * pointed to by s.
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the object.
    */

# 209 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an char) in the string
    * pointed to by s (including the terminating null character).
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the string.
    */

extern __declspec(__nothrow) size_t strcspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters not from the string pointed to by
    * s2. The terminating null character is not considered part of s2.
    * Returns: the length of the segment.
    */

# 232 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strpbrk(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of any
    * character from the string pointed to by s2.
    * Returns: returns a pointer to the character, or a null pointer if no
    *          character form s2 occurs in s1.
    */

# 247 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strrchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the last occurence of c (converted to a char) in the string
    * pointed to by s. The terminating null character is considered part of
    * the string.
    * Returns: returns a pointer to the character, or a null pointer if c does
    *          not occur in the string.
    */

extern __declspec(__nothrow) size_t strspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters from the string pointed to by S2
    * Returns: the length of the segment.
    */

# 270 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strstr(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of the
    * sequence of characters (excluding the terminating null character) in the
    * string pointed to by s2.
    * Returns: a pointer to the located string, or a null pointer if the string
    *          is not found.
    */

extern __declspec(__nothrow) char *strtok(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) char *_strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

extern __declspec(__nothrow) char *strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

   /*
    * A sequence of calls to the strtok function breaks the string pointed to
    * by s1 into a sequence of tokens, each of which is delimited by a
    * character from the string pointed to by s2. The first call in the
    * sequence has s1 as its first argument, and is followed by calls with a
    * null pointer as their first argument. The separator string pointed to by
    * s2 may be different from call to call.
    * The first call in the sequence searches for the first character that is
    * not contained in the current separator string s2. If no such character
    * is found, then there are no tokens in s1 and the strtok function returns
    * a null pointer. If such a character is found, it is the start of the
    * first token.
    * The strtok function then searches from there for a character that is
    * contained in the current separator string. If no such character is found,
    * the current token extends to the end of the string pointed to by s1, and
    * subsequent searches for a token will fail. If such a character is found,
    * it is overwritten by a null character, which terminates the current
    * token. The strtok function saves a pointer to the following character,
    * from which the next search for a token will start.
    * Each subsequent call, with a null pointer as the value for the first
    * argument, starts searching from the saved pointer and behaves as
    * described above.
    * Returns: pointer to the first character of a token, or a null pointer if
    *          there is no token.
    *
    * strtok_r() is a common extension which works exactly like
    * strtok(), but instead of storing its state in a hidden
    * library variable, requires the user to pass in a pointer to a
    * char * variable which will be used instead. Any sequence of
    * calls to strtok_r() passing the same char ** pointer should
    * behave exactly like the corresponding sequence of calls to
    * strtok(). This means that strtok_r() can safely be used in
    * multi-threaded programs, and also that you can tokenise two
    * strings in parallel.
    */

extern __declspec(__nothrow) void *memset(void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));
   /*
    * copies the value of c (converted to an unsigned char) into each of the
    * first n charactes of the object pointed to by s.
    * Returns: the value of s.
    */
extern __declspec(__nothrow) char *strerror(int /*errnum*/);
   /*
    * maps the error number in errnum to an error message string.
    * Returns: a pointer to the string, the contents of which are
    *          implementation-defined. The array pointed to shall not be
    *          modified by the program, but may be overwritten by a
    *          subsequent call to the strerror function.
    */
extern __declspec(__nothrow) size_t strlen(const char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * computes the length of the string pointed to by s.
    * Returns: the number of characters that precede the terminating null
    *          character.
    */

extern __declspec(__nothrow) size_t strlcpy(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string src into the string dst, using no more than
    * len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src. Thus, the operation
    * succeeded without truncation if and only if ret < len;
    * otherwise, the value in ret tells you how big to make dst if
    * you decide to reallocate it. (That value does _not_ include
    * the NUL.)
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) size_t strlcat(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * concatenates the string src to the string dst, using no more
    * than len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src plus the original length
    * of dst. Thus, the operation succeeded without truncation if
    * and only if ret < len; otherwise, the value in ret tells you
    * how big to make dst if you decide to reallocate it. (That
    * value does _not_ include the NUL.)
    * 
    * If no NUL is encountered within the first len bytes of dst,
    * then the length of dst is considered to have been equal to
    * len for the purposes of the return value (as if there were a
    * NUL at dst[len]). Thus, the return value in this case is len
    * + strlen(src).
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) void _membitcpybl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpybb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
    /*
     * Copies or moves a piece of memory from one place to another,
     * with one-bit granularity. So you can start or finish a copy
     * part way through a byte, and you can copy between regions
     * with different alignment within a byte.
     * 
     * All these functions have the same prototype: two void *
     * pointers for destination and source, then two integers
     * giving the bit offset from those pointers, and finally the
     * number of bits to copy.
     * 
     * Just like memcpy and memmove, the "cpy" functions copy as
     * fast as they can in the assumption that the memory regions
     * do not overlap, while the "move" functions cope correctly
     * with overlap.
     *
     * Treating memory as a stream of individual bits requires
     * defining a convention about what order those bits are
     * considered to be arranged in. The above functions support
     * multiple conventions:
     * 
     *  - the "bl" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in little-endian fashion, so that the LSB comes
     *    first. (For example, membitcpybl(a,b,0,7,1) would copy
     *    the MSB of the byte at b to the LSB of the byte at a.)
     * 
     *  - the "bb" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in big-endian fashion, so that the MSB comes
     *    first.
     * 
     *  - the "hl" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in little-endian fashion.
     * 
     *  - the "hb" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in big-endian fashion.
     * 
     *  - the "wl" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in little-endian fashion.
     * 
     *  - the "wb" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in big-endian fashion.
     */







# 502 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"



/* end of string.h */

# 16 "L:/PLT/pcac/mbedTLS/library/alignment.h"
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
/* stdlib.h: ANSI draft (X3J11 May 88) library header, section 4.10 */
/* Copyright (C) Codemist Ltd., 1988-1993.                          */
/* Copyright 1991-1998,2014 ARM Limited. All rights reserved.       */
/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */
 
/*
 * stdlib.h declares four types, several general purpose functions,
 * and defines several macros.
 */






  /* armclang and non-strict armcc allow 'long long' in system headers */














  /*
   * Some of these declarations are new in C99.  To access them in C++
   * you can use -D__USE_C99_STDLIB (or -D__USE_C99ALL).
   */








# 54 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 70 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"






   /* unconditional in non-strict C for consistency of debug info */



    typedef unsigned short wchar_t; /* see <stddef.h> */
# 91 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"

typedef struct div_t { int quot, rem; } div_t;
   /* type of the value returned by the div function. */
typedef struct ldiv_t { long int quot, rem; } ldiv_t;
   /* type of the value returned by the ldiv function. */

typedef struct lldiv_t { long long quot, rem; } lldiv_t;
   /* type of the value returned by the lldiv function. */


# 112 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
   /*
    * an integral expression which may be used as an argument to the exit
    * function to return successful termination status to the host
    * environment.
    */

   /*
    * Defining __USE_ANSI_EXAMPLE_RAND at compile time switches to
    * the example implementation of rand() and srand() provided in
    * the ANSI C standard. This implementation is very poor, but is
    * provided for completeness.
    */
# 131 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
   /*
    * RAND_MAX: an integral constant expression, the value of which
    * is the maximum value returned by the rand function.
    */
extern __declspec(__nothrow) int __aeabi_MB_CUR_MAX(void);

   /*
    * a positive integer expression whose value is the maximum number of bytes
    * in a multibyte character for the extended character set specified by the
    * current locale (category LC_CTYPE), and whose value is never greater
    * than MB_LEN_MAX.
    */

   /*
    * If the compiler supports signalling nans as per N965 then it
    * will define __SUPPORT_SNAN__, in which case a user may define
    * _WANT_SNAN in order to obtain a compliant version of the strtod
    * family of functions.
    */




extern __declspec(__nothrow) double atof(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to double
    * representation.
    * Returns: the converted value.
    */
extern __declspec(__nothrow) int atoi(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to int
    * representation.
    * Returns: the converted value.
    */
extern __declspec(__nothrow) long int atol(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to long int
    * representation.
    * Returns: the converted value.
    */

extern __declspec(__nothrow) long long atoll(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to
    * long long int representation.
    * Returns: the converted value.
    */


extern __declspec(__nothrow) double strtod(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to double
    * representation. First it decomposes the input string into three parts:
    * an initial, possibly empty, sequence of white-space characters (as
    * specified by the isspace function), a subject sequence resembling a
    * floating point constant; and a final string of one or more unrecognised
    * characters, including the terminating null character of the input string.
    * Then it attempts to convert the subject sequence to a floating point
    * number, and returns the result. A pointer to the final string is stored
    * in the object pointed to by endptr, provided that endptr is not a null
    * pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned. If the correct value is outside the range of
    *          representable values, plus or minus HUGE_VAL is returned
    *          (according to the sign of the value), and the value of the macro
    *          ERANGE is stored in errno. If the correct value would cause
    *          underflow, zero is returned and the value of the macro ERANGE is
    *          stored in errno.
    */

extern __declspec(__nothrow) float strtof(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) long double strtold(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
   /*
    * same as strtod, but return float and long double respectively.
    */

extern __declspec(__nothrow) long int strtol(const char * __restrict /*nptr*/,
                        char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to long int
    * representation. First it decomposes the input string into three parts:
    * an initial, possibly empty, sequence of white-space characters (as
    * specified by the isspace function), a subject sequence resembling an
    * integer represented in some radix determined by the value of base, and a
    * final string of one or more unrecognised characters, including the
    * terminating null character of the input string. Then it attempts to
    * convert the subject sequence to an integer, and returns the result.
    * If the value of base is 0, the expected form of the subject sequence is
    * that of an integer constant (described in ANSI Draft, section 3.1.3.2),
    * optionally preceded by a '+' or '-' sign, but not including an integer
    * suffix. If the value of base is between 2 and 36, the expected form of
    * the subject sequence is a sequence of letters and digits representing an
    * integer with the radix specified by base, optionally preceded by a plus
    * or minus sign, but not including an integer suffix. The letters from a
    * (or A) through z (or Z) are ascribed the values 10 to 35; only letters
    * whose ascribed values are less than that of the base are permitted. If
    * the value of base is 16, the characters 0x or 0X may optionally precede
    * the sequence of letters and digits following the sign if present.
    * A pointer to the final string is stored in the object
    * pointed to by endptr, provided that endptr is not a null pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned and nptr is stored in *endptr.
    *          If the correct value is outside the range of
    *          representable values, LONG_MAX or LONG_MIN is returned
    *          (according to the sign of the value), and the value of the
    *          macro ERANGE is stored in errno.
    */
extern __declspec(__nothrow) unsigned long int strtoul(const char * __restrict /*nptr*/,
                                       char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to unsigned
    * long int representation. First it decomposes the input string into three
    * parts: an initial, possibly empty, sequence of white-space characters (as
    * determined by the isspace function), a subject sequence resembling an
    * unsigned integer represented in some radix determined by the value of
    * base, and a final string of one or more unrecognised characters,
    * including the terminating null character of the input string. Then it
    * attempts to convert the subject sequence to an unsigned integer, and
    * returns the result. If the value of base is zero, the expected form of
    * the subject sequence is that of an integer constant (described in ANSI
    * Draft, section 3.1.3.2), optionally preceded by a '+' or '-' sign, but
    * not including an integer suffix. If the value of base is between 2 and
    * 36, the expected form of the subject sequence is a sequence of letters
    * and digits representing an integer with the radix specified by base,
    * optionally preceded by a '+' or '-' sign, but not including an integer
    * suffix. The letters from a (or A) through z (or Z) stand for the values
    * 10 to 35; only letters whose ascribed values are less than that of the
    * base are permitted. If the value of base is 16, the characters 0x or 0X
    * may optionally precede the sequence of letters and digits following the
    * sign, if present. A pointer to the final string is stored in the object
    * pointed to by endptr, provided that endptr is not a null pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned and nptr is stored in *endptr.
    *          If the correct value is outside the range of
    *          representable values, ULONG_MAX is returned, and the value of
    *          the macro ERANGE is stored in errno.
    */

/* C90 reserves all names beginning with 'str' */
extern __declspec(__nothrow) long long strtoll(const char * __restrict /*nptr*/,
                                  char ** __restrict /*endptr*/, int /*base*/)
                          __attribute__((__nonnull__(1)));
   /*
    * as strtol but returns a long long int value.  If the correct value is
    * outside the range of representable values,  LLONG_MAX or LLONG_MIN is
    * returned (according to the sign of the value), and the value of the
    * macro ERANGE is stored in errno.
    */
extern __declspec(__nothrow) unsigned long long strtoull(const char * __restrict /*nptr*/,
                                            char ** __restrict /*endptr*/, int /*base*/)
                                   __attribute__((__nonnull__(1)));
   /*
    * as strtoul but returns an unsigned long long int value.  If the correct
    * value is outside the range of representable values, ULLONG_MAX is returned,
    * and the value of the macro ERANGE is stored in errno.
    */

extern __declspec(__nothrow) int rand(void);
   /*
    * Computes a sequence of pseudo-random integers in the range 0 to RAND_MAX.
    * Uses an additive generator (Mitchell & Moore) of the form:
    *   Xn = (X[n-24] + X[n-55]) MOD 2^31
    * This is described in section 3.2.2 of Knuth, vol 2. It's period is
    * in excess of 2^55 and its randomness properties, though unproven, are
    * conjectured to be good. Empirical testing since 1958 has shown no flaws.
    * Returns: a pseudo-random integer.
    */
extern __declspec(__nothrow) void srand(unsigned int /*seed*/);
   /*
    * uses its argument as a seed for a new sequence of pseudo-random numbers
    * to be returned by subsequent calls to rand. If srand is then called with
    * the same seed value, the sequence of pseudo-random numbers is repeated.
    * If rand is called before any calls to srand have been made, the same
    * sequence is generated as when srand is first called with a seed value
    * of 1.
    */

struct _rand_state { int __x[57]; };
extern __declspec(__nothrow) int _rand_r(struct _rand_state *);
extern __declspec(__nothrow) void _srand_r(struct _rand_state *, unsigned int);
struct _ANSI_rand_state { int __x[1]; };
extern __declspec(__nothrow) int _ANSI_rand_r(struct _ANSI_rand_state *);
extern __declspec(__nothrow) void _ANSI_srand_r(struct _ANSI_rand_state *, unsigned int);
   /*
    * Re-entrant variants of both flavours of rand, which operate on
    * an explicitly supplied state buffer.
    */

extern __declspec(__nothrow) void *calloc(size_t /*nmemb*/, size_t /*size*/);
   /*
    * allocates space for an array of nmemb objects, each of whose size is
    * 'size'. The space is initialised to all bits zero.
    * Returns: either a null pointer or a pointer to the allocated space.
    */
extern __declspec(__nothrow) void free(void * /*ptr*/);
   /*
    * causes the space pointed to by ptr to be deallocated (i.e., made
    * available for further allocation). If ptr is a null pointer, no action
    * occurs. Otherwise, if ptr does not match a pointer earlier returned by
    * calloc, malloc or realloc or if the space has been deallocated by a call
    * to free or realloc, the behaviour is undefined.
    */
extern __declspec(__nothrow) void *malloc(size_t /*size*/);
   /*
    * allocates space for an object whose size is specified by 'size' and whose
    * value is indeterminate.
    * Returns: either a null pointer or a pointer to the allocated space.
    */
extern __declspec(__nothrow) void *realloc(void * /*ptr*/, size_t /*size*/);
   /*
    * changes the size of the object pointed to by ptr to the size specified by
    * size. The contents of the object shall be unchanged up to the lesser of
    * the new and old sizes. If the new size is larger, the value of the newly
    * allocated portion of the object is indeterminate. If ptr is a null
    * pointer, the realloc function behaves like a call to malloc for the
    * specified size. Otherwise, if ptr does not match a pointer earlier
    * returned by calloc, malloc or realloc, or if the space has been
    * deallocated by a call to free or realloc, the behaviour is undefined.
    * If the space cannot be allocated, the object pointed to by ptr is
    * unchanged. If size is zero and ptr is not a null pointer, the object it
    * points to is freed.
    * Returns: either a null pointer or a pointer to the possibly moved
    *          allocated space.
    */

extern __declspec(__nothrow) int posix_memalign(void ** /*ret*/, size_t /*alignment*/, size_t /*size*/);
   /*
    * allocates space for an object of size 'size', aligned to a
    * multiple of 'alignment' (which must be a power of two and at
    * least 4).
    *
    * On success, a pointer to the allocated object is stored in
    * *ret, and zero is returned. On failure, the return value is
    * either ENOMEM (allocation failed because no suitable piece of
    * memory was available) or EINVAL (the 'alignment' parameter was
    * invalid).
    */

typedef int (*__heapprt)(void *, char const *, ...);
extern __declspec(__nothrow) void __heapstats(int (* /*dprint*/)(void * /*param*/,
                                           char const * /*format*/, ...),
                        void * /*param*/) __attribute__((__nonnull__(1)));
   /*
    * reports current heap statistics (eg. number of free blocks in
    * the free-list). Output is as implementation-defined free-form
    * text, provided via the dprint function. `param' gives an
    * extra data word to pass to dprint. You can call
    * __heapstats(fprintf,stdout) by casting fprintf to the above
    * function type; the typedef `__heapprt' is provided for this
    * purpose.
    *
    * `dprint' will not be called while the heap is being examined,
    * so it can allocate memory itself without trouble.
    */
extern __declspec(__nothrow) int __heapvalid(int (* /*dprint*/)(void * /*param*/,
                                           char const * /*format*/, ...),
                       void * /*param*/, int /*verbose*/) __attribute__((__nonnull__(1)));
   /*
    * performs a consistency check on the heap. Errors are reported
    * through dprint, like __heapstats. If `verbose' is nonzero,
    * full diagnostic information on the heap state is printed out.
    *
    * This routine probably won't work if the heap isn't a
    * contiguous chunk (for example, if __user_heap_extend has been
    * overridden).
    *
    * `dprint' may be called while the heap is being examined or
    * even in an invalid state, so it must perform no memory
    * allocation. In particular, if `dprint' calls (or is) a stdio
    * function, the stream it outputs to must already have either
    * been written to or been setvbuf'ed, or else the system will
    * allocate buffer space for it on the first call to dprint.
    */
extern __declspec(__nothrow) __declspec(__noreturn) void abort(void);
   /*
    * causes abnormal program termination to occur, unless the signal SIGABRT
    * is being caught and the signal handler does not return. Whether open
    * output streams are flushed or open streams are closed or temporary
    * files removed is implementation-defined.
    * An implementation-defined form of the status 'unsuccessful termination'
    * is returned to the host environment by means of a call to
    * raise(SIGABRT).
    */

extern __declspec(__nothrow) int atexit(void (* /*func*/)(void)) __attribute__((__nonnull__(1)));
   /*
    * registers the function pointed to by func, to be called without its
    * arguments at normal program termination. It is possible to register at
    * least 32 functions.
    * Returns: zero if the registration succeeds, nonzero if it fails.
    */
# 436 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


extern __declspec(__nothrow) __declspec(__noreturn) void exit(int /*status*/);
   /*
    * causes normal program termination to occur. If more than one call to the
    * exit function is executed by a program, the behaviour is undefined.
    * First, all functions registered by the atexit function are called, in the
    * reverse order of their registration.
    * Next, all open output streams are flushed, all open streams are closed,
    * and all files created by the tmpfile function are removed.
    * Finally, control is returned to the host environment. If the value of
    * status is zero or EXIT_SUCCESS, an implementation-defined form of the
    * status 'successful termination' is returned. If the value of status is
    * EXIT_FAILURE, an implementation-defined form of the status
    * 'unsuccessful termination' is returned. Otherwise the status returned
    * is implementation-defined.
    */

extern __declspec(__nothrow) __declspec(__noreturn) void _Exit(int /*status*/);
   /*
    * causes normal program termination to occur. No functions registered
    * by the atexit function are called.
    * In this implementation, all open output streams are flushed, all
    * open streams are closed, and all files created by the tmpfile function
    * are removed.
    * Control is returned to the host environment. The status returned to
    * the host environment is determined in the same way as for 'exit'.
    */     

extern __declspec(__nothrow) char *getenv(const char * /*name*/) __attribute__((__nonnull__(1)));
   /*
    * searches the environment list, provided by the host environment, for a
    * string that matches the string pointed to by name. The set of environment
    * names and the method for altering the environment list are
    * implementation-defined.
    * Returns: a pointer to a string associated with the matched list member.
    *          The array pointed to shall not be modified by the program, but
    *          may be overwritten by a subsequent call to the getenv function.
    *          If the specified name cannot be found, a null pointer is
    *          returned.
    */

extern __declspec(__nothrow) int  system(const char * /*string*/);
   /*
    * passes the string pointed to by string to the host environment to be
    * executed by a command processor in an implementation-defined manner.
    * A null pointer may be used for string, to inquire whether a command
    * processor exists.
    *
    * Returns: If the argument is a null pointer, the system function returns
    *          non-zero only if a command processor is available. If the
    *          argument is not a null pointer, the system function returns an
    *          implementation-defined value.
    */

extern  void *bsearch(const void * /*key*/, const void * /*base*/,
              size_t /*nmemb*/, size_t /*size*/,
              int (* /*compar*/)(const void *, const void *)) __attribute__((__nonnull__(1,2,5)));
   /*
    * searches an array of nmemb objects, the initial member of which is
    * pointed to by base, for a member that matches the object pointed to by
    * key. The size of each member of the array is specified by size.
    * The contents of the array shall be in ascending sorted order according to
    * a comparison function pointed to by compar, which is called with two
    * arguments that point to the key object and to an array member, in that
    * order. The function shall return an integer less than, equal to, or
    * greater than zero if the key object is considered, respectively, to be
    * less than, to match, or to be greater than the array member.
    * Returns: a pointer to a matching member of the array, or a null pointer
    *          if no match is found. If two members compare as equal, which
    *          member is matched is unspecified.
    */
# 524 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


extern  void qsort(void * /*base*/, size_t /*nmemb*/, size_t /*size*/,
           int (* /*compar*/)(const void *, const void *)) __attribute__((__nonnull__(1,4)));
   /*
    * sorts an array of nmemb objects, the initial member of which is pointed
    * to by base. The size of each object is specified by size.
    * The contents of the array shall be in ascending order according to a
    * comparison function pointed to by compar, which is called with two
    * arguments that point to the objects being compared. The function shall
    * return an integer less than, equal to, or greater than zero if the first
    * argument is considered to be respectively less than, equal to, or greater
    * than the second. If two members compare as equal, their order in the
    * sorted array is unspecified.
    */

# 553 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"

extern __declspec(__nothrow) __attribute__((const)) int abs(int /*j*/);
   /*
    * computes the absolute value of an integer j. If the result cannot be
    * represented, the behaviour is undefined.
    * Returns: the absolute value.
    */

extern __declspec(__nothrow) __attribute__((const)) div_t div(int /*numer*/, int /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the resulting
    * quotient is the integer of lesser magnitude that is the nearest to the
    * algebraic quotient. If the result cannot be represented, the behaviour is
    * undefined; otherwise, quot * denom + rem shall equal numer.
    * Returns: a structure of type div_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          int quot; int rem;
    */
extern __declspec(__nothrow) __attribute__((const)) long int labs(long int /*j*/);
   /*
    * computes the absolute value of an long integer j. If the result cannot be
    * represented, the behaviour is undefined.
    * Returns: the absolute value.
    */




extern __declspec(__nothrow) __attribute__((const)) ldiv_t ldiv(long int /*numer*/, long int /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the sign of
    * the resulting quotient is that of the algebraic quotient, and the
    * magnitude of the resulting quotient is the largest integer less than the
    * magnitude of the algebraic quotient. If the result cannot be represented,
    * the behaviour is undefined; otherwise, quot * denom + rem shall equal
    * numer.
    * Returns: a structure of type ldiv_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          long int quot; long int rem;
    */







extern __declspec(__nothrow) __attribute__((const)) long long llabs(long long /*j*/);
   /*
    * computes the absolute value of a long long integer j. If the
    * result cannot be represented, the behaviour is undefined.
    * Returns: the absolute value.
    */




extern __declspec(__nothrow) __attribute__((const)) lldiv_t lldiv(long long /*numer*/, long long /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the sign of
    * the resulting quotient is that of the algebraic quotient, and the
    * magnitude of the resulting quotient is the largest integer less than the
    * magnitude of the algebraic quotient. If the result cannot be represented,
    * the behaviour is undefined; otherwise, quot * denom + rem shall equal
    * numer.
    * Returns: a structure of type lldiv_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          long long quot; long long rem;
    */
# 634 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


/*
 * ARM real-time divide functions for guaranteed performance
 */
typedef struct __sdiv32by16 { int quot, rem; } __sdiv32by16;
typedef struct __udiv32by16 { unsigned int quot, rem; } __udiv32by16;
   /* used int so that values return in separate regs, although 16-bit */
typedef struct __sdiv64by32 { int rem, quot; } __sdiv64by32;

__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __sdiv32by16 __rt_sdiv32by16(
     int /*numer*/,
     short int /*denom*/);
   /*
    * Signed divide: (16-bit quot), (16-bit rem) = (32-bit) / (16-bit)
    */
__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __udiv32by16 __rt_udiv32by16(
     unsigned int /*numer*/,
     unsigned short /*denom*/);
   /*
    * Unsigned divide: (16-bit quot), (16-bit rem) = (32-bit) / (16-bit)
    */
__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __sdiv64by32 __rt_sdiv64by32(
     int /*numer_h*/, unsigned int /*numer_l*/,
     int /*denom*/);
   /*
    * Signed divide: (32-bit quot), (32-bit rem) = (64-bit) / (32-bit)
    */


/*
 * ARM floating-point mask/status function (for both hardfp and softfp)
 */
extern __declspec(__nothrow) unsigned int __fp_status(unsigned int /*mask*/, unsigned int /*flags*/);
   /*
    * mask and flags are bit-fields which correspond directly to the
    * floating point status register in the FPE/FPA and fplib.  
    * __fp_status returns the current value of the status register,
    * and also sets the writable bits of the word
    * (the exception control and flag bytes) to:
    *
    *     new = (old & ~mask) ^ flags;
    */












/*
 * Multibyte Character Functions.
 * The behaviour of the multibyte character functions is affected by the
 * LC_CTYPE category of the current locale. For a state-dependent encoding,
 * each function is placed into its initial state by a call for which its
 * character pointer argument, s, is a null pointer. Subsequent calls with s
 * as other than a null pointer cause the internal state of the function to be
 * altered as necessary. A call with s as a null pointer causes these functions
 * to return a nonzero value if encodings have state dependency, and a zero
 * otherwise. After the LC_CTYPE category is changed, the shift state of these
 * functions is indeterminate.
 */
extern __declspec(__nothrow) int mblen(const char * /*s*/, size_t /*n*/);
   /*
    * If s is not a null pointer, the mblen function determines the number of
    * bytes compromising the multibyte character pointed to by s. Except that
    * the shift state of the mbtowc function is not affected, it is equivalent
    * to   mbtowc((wchar_t *)0, s, n);
    * Returns: If s is a null pointer, the mblen function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the mblen function either returns a 0 (if s points to a
    *          null character), or returns the number of bytes that compromise
    *          the multibyte character (if the next n of fewer bytes form a
    *          valid multibyte character), or returns -1 (they do not form a
    *          valid multibyte character).
    */
extern __declspec(__nothrow) int mbtowc(wchar_t * __restrict /*pwc*/,
                   const char * __restrict /*s*/, size_t /*n*/);
   /*
    * If s is not a null pointer, the mbtowc function determines the number of
    * bytes that compromise the multibyte character pointed to by s. It then
    * determines the code for value of type wchar_t that corresponds to that
    * multibyte character. (The value of the code corresponding to the null
    * character is zero). If the multibyte character is valid and pwc is not a
    * null pointer, the mbtowc function stores the code in the object pointed
    * to by pwc. At most n bytes of the array pointed to by s will be examined.
    * Returns: If s is a null pointer, the mbtowc function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the mbtowc function either returns a 0 (if s points to
    *          a null character), or returns the number of bytes that
    *          compromise the converted multibyte character (if the next n of
    *          fewer bytes form a valid multibyte character), or returns -1
    *          (they do not form a valid multibyte character).
    */
extern __declspec(__nothrow) int wctomb(char * /*s*/, wchar_t /*wchar*/);
   /*
    * determines the number of bytes need to represent the multibyte character
    * corresponding to the code whose value is wchar (including any change in
    * shift state). It stores the multibyte character representation in the
    * array object pointed to by s (if s is not a null pointer). At most
    * MB_CUR_MAX characters are stored. If the value of wchar is zero, the
    * wctomb function is left in the initial shift state).
    * Returns: If s is a null pointer, the wctomb function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the wctomb function returns a -1 if the value of wchar
    *          does not correspond to a valid multibyte character, or returns
    *          the number of bytes that compromise the multibyte character
    *          corresponding to the value of wchar.
    */

/*
 * Multibyte String Functions.
 * The behaviour of the multibyte string functions is affected by the LC_CTYPE
 * category of the current locale.
 */
extern __declspec(__nothrow) size_t mbstowcs(wchar_t * __restrict /*pwcs*/,
                      const char * __restrict /*s*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * converts a sequence of multibyte character that begins in the initial
    * shift state from the array pointed to by s into a sequence of
    * corresponding codes and stores not more than n codes into the array
    * pointed to by pwcs. No multibyte character that follow a null character
    * (which is converted into a code with value zero) will be examined or
    * converted. Each multibyte character is converted as if by a call to
    * mbtowc function, except that the shift state of the mbtowc function is
    * not affected. No more than n elements will be modified in the array
    * pointed to by pwcs. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: If an invalid multibyte character is encountered, the mbstowcs
    *          function returns (size_t)-1. Otherwise, the mbstowcs function
    *          returns the number of array elements modified, not including
    *          a terminating zero code, if any.
    */
extern __declspec(__nothrow) size_t wcstombs(char * __restrict /*s*/,
                      const wchar_t * __restrict /*pwcs*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * converts a sequence of codes that correspond to multibyte characters
    * from the array pointed to by pwcs into a sequence of multibyte
    * characters that begins in the initial shift state and stores these
    * multibyte characters into the array pointed to by s, stopping if a
    * multibyte character would exceed the limit of n total bytes or if a
    * null character is stored. Each code is converted as if by a call to the
    * wctomb function, except that the shift state of the wctomb function is
    * not affected. No more than n elements will be modified in the array
    * pointed to by s. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: If a code is encountered that does not correspond to a valid
    *          multibyte character, the wcstombs function returns (size_t)-1.
    *          Otherwise, the wcstombs function returns the number of bytes
    *          modified, not including a terminating null character, if any.
    */

extern __declspec(__nothrow) void __use_realtime_heap(void);
extern __declspec(__nothrow) void __use_realtime_division(void);
extern __declspec(__nothrow) void __use_two_region_memory(void);
extern __declspec(__nothrow) void __use_no_heap(void);
extern __declspec(__nothrow) void __use_no_heap_region(void);

extern __declspec(__nothrow) char const *__C_library_version_string(void);
extern __declspec(__nothrow) int __C_library_version_number(void);











# 892 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"





/* end of stdlib.h */
# 17 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/*
 * Define MBEDTLS_EFFICIENT_UNALIGNED_ACCESS for architectures where unaligned memory
 * accesses are known to be efficient.
 *
 * All functions defined here will behave correctly regardless, but might be less
 * efficient when this is not defined.
 */
# 39 "L:/PLT/pcac/mbedTLS/library/alignment.h"

# 96 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/*
 * We try to force mbedtls_(get|put)_unaligned_uintXX to be always inline, because this results
 * in code that is both smaller and faster. IAR and gcc both benefit from this when optimising
 * for size.
 */

/**
 * Read the unsigned 16 bits integer from the given address, which need not
 * be aligned.
 *
 * \param   p pointer to 2 bytes of data
 * \return  Data at the given address
 */



__attribute__((always_inline))

static __inline uint16_t mbedtls_get_unaligned_uint16(const void *p)
{
    uint16_t r;
# 125 "L:/PLT/pcac/mbedTLS/library/alignment.h"
    memcpy(&r, p, sizeof(r));

    return r;
}

/**
 * Write the unsigned 16 bits integer to the given address, which need not
 * be aligned.
 *
 * \param   p pointer to 2 bytes of data
 * \param   x data to write
 */



__attribute__((always_inline))

static __inline void mbedtls_put_unaligned_uint16(void *p, uint16_t x)
{
# 151 "L:/PLT/pcac/mbedTLS/library/alignment.h"
    memcpy(p, &x, sizeof(x));

}

/**
 * Read the unsigned 32 bits integer from the given address, which need not
 * be aligned.
 *
 * \param   p pointer to 4 bytes of data
 * \return  Data at the given address
 */



__attribute__((always_inline))

static __inline uint32_t mbedtls_get_unaligned_uint32(const void *p)
{
    uint32_t r;
# 177 "L:/PLT/pcac/mbedTLS/library/alignment.h"
    memcpy(&r, p, sizeof(r));

    return r;
}

/**
 * Write the unsigned 32 bits integer to the given address, which need not
 * be aligned.
 *
 * \param   p pointer to 4 bytes of data
 * \param   x data to write
 */



__attribute__((always_inline))

static __inline void mbedtls_put_unaligned_uint32(void *p, uint32_t x)
{
# 203 "L:/PLT/pcac/mbedTLS/library/alignment.h"
    memcpy(p, &x, sizeof(x));

}

/**
 * Read the unsigned 64 bits integer from the given address, which need not
 * be aligned.
 *
 * \param   p pointer to 8 bytes of data
 * \return  Data at the given address
 */



__attribute__((always_inline))

static __inline uint64_t mbedtls_get_unaligned_uint64(const void *p)
{
    uint64_t r;
# 229 "L:/PLT/pcac/mbedTLS/library/alignment.h"
    memcpy(&r, p, sizeof(r));

    return r;
}

/**
 * Write the unsigned 64 bits integer to the given address, which need not
 * be aligned.
 *
 * \param   p pointer to 8 bytes of data
 * \param   x data to write
 */



__attribute__((always_inline))

static __inline void mbedtls_put_unaligned_uint64(void *p, uint64_t x)
{
# 255 "L:/PLT/pcac/mbedTLS/library/alignment.h"
    memcpy(p, &x, sizeof(x));

}





/** Byte Reading Macros
 *
 * Given a multi-byte integer \p x, MBEDTLS_BYTE_n retrieves the n-th
 * byte from x, where byte 0 is the least significant byte.
 */
# 276 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/*
 * Detect GCC built-in byteswap routines
 */
# 289 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/*
 * Detect Clang built-in byteswap routines
 */
# 304 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/*
 * Detect MSVC built-in byteswap routines
 */
# 319 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/* Detect armcc built-in byteswap routine */
# 327 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/* Detect IAR built-in byteswap routine */
# 337 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/*
 * Where compiler built-ins are not present, fall back to C code that the
 * compiler may be able to detect and transform into the relevant bswap or
 * similar instruction.
 */

static __inline uint16_t mbedtls_bswap16(uint16_t x)
{
    return
        (x & 0x00ff) << 8 |
        (x & 0xff00) >> 8;
}



# 364 "L:/PLT/pcac/mbedTLS/library/alignment.h"


static __inline uint64_t mbedtls_bswap64(uint64_t x)
{
    return
        (x & 0x00000000000000ffULL) << 56 |
        (x & 0x000000000000ff00ULL) << 40 |
        (x & 0x0000000000ff0000ULL) << 24 |
        (x & 0x00000000ff000000ULL) <<  8 |
        (x & 0x000000ff00000000ULL) >>  8 |
        (x & 0x0000ff0000000000ULL) >> 24 |
        (x & 0x00ff000000000000ULL) >> 40 |
        (x & 0xff00000000000000ULL) >> 56;
}





# 389 "L:/PLT/pcac/mbedTLS/library/alignment.h"
static const uint16_t mbedtls_byte_order_detector = { 0x100 };



# 402 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/**
 * Get the unsigned 32 bits integer corresponding to four bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the four bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the four bytes to build the 32 bits unsigned
 *                  integer from.
 */






/**
 * Put in memory a 32 bits unsigned integer in big-endian order.
 *
 * \param   n       32 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 32
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 32 bits unsigned integer \p n.
 */
# 438 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/**
 * Get the unsigned 32 bits integer corresponding to four bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the four bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the four bytes to build the 32 bits unsigned
 *                  integer from.
 */







/**
 * Put in memory a 32 bits unsigned integer in little-endian order.
 *
 * \param   n       32 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 32
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 32 bits unsigned integer \p n.
 */
# 475 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/**
 * Get the unsigned 16 bits integer corresponding to two bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the two bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the two bytes to build the 16 bits unsigned
 *                  integer from.
 */






/**
 * Put in memory a 16 bits unsigned integer in little-endian order.
 *
 * \param   n       16 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 16
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 16 bits unsigned integer \p n.
 */
# 511 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/**
 * Get the unsigned 16 bits integer corresponding to two bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the two bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the two bytes to build the 16 bits unsigned
 *                  integer from.
 */






/**
 * Put in memory a 16 bits unsigned integer in big-endian order.
 *
 * \param   n       16 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 16
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 16 bits unsigned integer \p n.
 */
# 547 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/**
 * Get the unsigned 24 bits integer corresponding to three bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the three bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the three bytes to build the 24 bits unsigned
 *                  integer from.
 */
# 563 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/**
 * Put in memory a 24 bits unsigned integer in big-endian order.
 *
 * \param   n       24 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 24
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 24 bits unsigned integer \p n.
 */
# 579 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/**
 * Get the unsigned 24 bits integer corresponding to three bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the three bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the three bytes to build the 24 bits unsigned
 *                  integer from.
 */
# 595 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/**
 * Put in memory a 24 bits unsigned integer in little-endian order.
 *
 * \param   n       24 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 24
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 24 bits unsigned integer \p n.
 */
# 611 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/**
 * Get the unsigned 64 bits integer corresponding to eight bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the eight bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the eight bytes to build the 64 bits unsigned
 *                  integer from.
 */






/**
 * Put in memory a 64 bits unsigned integer in big-endian order.
 *
 * \param   n       64 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 64
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 64 bits unsigned integer \p n.
 */
# 647 "L:/PLT/pcac/mbedTLS/library/alignment.h"

/**
 * Get the unsigned 64 bits integer corresponding to eight bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the eight bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the eight bytes to build the 64 bits unsigned
 *                  integer from.
 */






/**
 * Put in memory a 64 bits unsigned integer in little-endian order.
 *
 * \param   n       64 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 64
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 64 bits unsigned integer \p n.
 */
# 683 "L:/PLT/pcac/mbedTLS/library/alignment.h"

# 16 "L:/PLT/pcac/mbedTLS/library/common.h"

# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\assert.h"
/* assert.h: ANSI 'C' (X3J11 Oct 88) library header section 4.2 */
/* Copyright (C) Codemist Ltd., 1988-1993                       */
/* Copyright 1991-1993 ARM Limited. All rights reserved.        */
/* version 0.04 */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */

/*
 * The assert macro puts diagnostics into programs. When it is executed,
 * if its argument expression is false, it writes information about the
 * call that failed (including the text of the argument, the name of the
 * source file, and the source line number - the latter are respectively
 * the values of the preprocessing macros __FILE__ and __LINE__) on the
 * standard error stream. It then calls the abort function.
 * If its argument expression is true, the assert macro returns no value.
 */

/*
 * Note that <assert.h> may be included more that once in a program with
 * different setting of NDEBUG. Hence the slightly unusual first-time
 * only flag.
 */

# 43 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\assert.h"
    extern __declspec(__nothrow) __declspec(__noreturn) void abort(void);
    extern __declspec(__nothrow) __declspec(__noreturn) void __aeabi_assert(const char *, const char *, int) __attribute__((__nonnull__(1,2)));
# 53 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\assert.h"

# 77 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\assert.h"





/* end of assert.h */

# 18 "L:/PLT/pcac/mbedTLS/library/common.h"
# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"
/* stddef.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.1.4 */

/* Copyright (C) ARM Ltd., 1999
 * All rights reserved
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */

/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991 ARM Limited. All rights reserved.             */
/* version 0.05 */

/*
 * The following types and macros are defined in several headers referred to in
 * the descriptions of the functions declared in that header. They are also
 * defined in this header file.
 */





# 34 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"




  typedef signed int ptrdiff_t;



 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 57 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



  /* unconditional in non-strict C for consistency of debug info */



      typedef unsigned short wchar_t; /* also in <stdlib.h> and <inttypes.h> */
# 82 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



   /* null pointer constant. */




  /* EDG uses __INTADDR__ to avoid errors when strict */




  typedef long double max_align_t;









# 114 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



/* end of stddef.h */

# 19 "L:/PLT/pcac/mbedTLS/library/common.h"
# 20 "L:/PLT/pcac/mbedTLS/library/common.h"
# 21 "L:/PLT/pcac/mbedTLS/library/common.h"

# 29 "L:/PLT/pcac/mbedTLS/library/common.h"

/** Helper to define a function as static except when building invasive tests.
 *
 * If a function is only used inside its own source file and should be
 * declared `static` to allow the compiler to optimize for code size,
 * but that function has unit tests, define it with
 * ```
 * MBEDTLS_STATIC_TESTABLE int mbedtls_foo(...) { ... }
 * ```
 * and declare it in a header in the `library/` directory with
 * ```
 * #if defined(MBEDTLS_TEST_HOOKS)
 * int mbedtls_foo(...);
 * #endif
 * ```
 */






# 63 "L:/PLT/pcac/mbedTLS/library/common.h"

/** \def ARRAY_LENGTH
 * Return the number of elements of a static or stack array.
 *
 * \param array         A value of array (not pointer) type.
 *
 * \return The number of elements of the array.
 */
/* A correct implementation of ARRAY_LENGTH, but which silently gives
 * a nonsensical result if called with a pointer rather than an array. */




/* Test if arg and &(arg)[0] have the same type. This is true if arg is
 * an array but not if it's a pointer. */



/* A compile-time constant with the value 0. If `const_expr` is not a
 * compile-time constant with a nonzero value, cause a compile-time error. */



/* Return the scalar value `value` (possibly promoted). This is a compile-time
 * constant if `value` is. `condition` must be a compile-time constant.
 * If `condition` is false, arrange to cause a compile-time error. */












/** Allow library to access its structs' private members.
 *
 * Although structs defined in header files are publicly available,
 * their members are private and should not be accessed by the user.
 */


/**
 * \brief       Securely zeroize a buffer then free it.
 *
 *              Similar to making consecutive calls to
 *              \c mbedtls_platform_zeroize() and \c mbedtls_free(), but has
 *              code size savings, and potential for optimisation in the future.
 *
 *              Guaranteed to be a no-op if \p buf is \c NULL and \p len is 0.
 *
 * \param buf   Buffer to be zeroized then freed.
 * \param len   Length of the buffer in bytes
 */
void mbedtls_zeroize_and_free(void *buf, size_t len);

/** Return an offset into a buffer.
 *
 * This is just the addition of an offset to a pointer, except that this
 * function also accepts an offset of 0 into a buffer whose pointer is null.
 * (`p + n` has undefined behavior when `p` is null, even when `n == 0`.
 * A null pointer is a valid buffer pointer when the size is 0, for example
 * as the result of `malloc(0)` on some platforms.)
 *
 * \param p     Pointer to a buffer of at least n bytes.
 *              This may be \p NULL if \p n is zero.
 * \param n     An offset in bytes.
 * \return      Pointer to offset \p n in the buffer \p p.
 *              Note that this is only a valid pointer if the size of the
 *              buffer is at least \p n + 1.
 */
static __inline unsigned char *mbedtls_buffer_offset(
    unsigned char *p, size_t n)
{
    return p == 0 ? 0 : p + n;
}

/** Return an offset into a read-only buffer.
 *
 * Similar to mbedtls_buffer_offset(), but for const pointers.
 *
 * \param p     Pointer to a buffer of at least n bytes.
 *              This may be \p NULL if \p n is zero.
 * \param n     An offset in bytes.
 * \return      Pointer to offset \p n in the buffer \p p.
 *              Note that this is only a valid pointer if the size of the
 *              buffer is at least \p n + 1.
 */
static __inline const unsigned char *mbedtls_buffer_offset_const(
    const unsigned char *p, size_t n)
{
    return p == 0 ? 0 : p + n;
}

/* Always inline mbedtls_xor() for similar reasons as mbedtls_xor_no_simd(). */



__attribute__((always_inline))

/**
 * Perform a fast block XOR operation, such that
 * r[i] = a[i] ^ b[i] where 0 <= i < n
 *
 * \param   r Pointer to result (buffer of at least \p n bytes). \p r
 *            may be equal to either \p a or \p b, but behaviour when
 *            it overlaps in other ways is undefined.
 * \param   a Pointer to input (buffer of at least \p n bytes)
 * \param   b Pointer to input (buffer of at least \p n bytes)
 * \param   n Number of bytes to process.
 *
 * \note      Depending on the situation, it may be faster to use either mbedtls_xor() or
 *            mbedtls_xor_no_simd() (these are functionally equivalent).
 *            If the result is used immediately after the xor operation in non-SIMD code (e.g, in
 *            AES-CBC), there may be additional latency to transfer the data from SIMD to scalar
 *            registers, and in this case, mbedtls_xor_no_simd() may be faster. In other cases where
 *            the result is not used immediately (e.g., in AES-CTR), mbedtls_xor() may be faster.
 *            For targets without SIMD support, they will behave the same.
 */
static __inline void mbedtls_xor(unsigned char *r,
                               const unsigned char *a,
                               const unsigned char *b,
                               size_t n)
{
    size_t i = 0;
# 234 "L:/PLT/pcac/mbedTLS/library/common.h"
    for (; i < n; i++) {
        r[i] = a[i] ^ b[i];
    }
}

/* Always inline mbedtls_xor_no_simd() as we see significant perf regressions when it does not get
 * inlined (e.g., observed about 3x perf difference in gcm_mult_largetable with gcc 7 - 12) */



__attribute__((always_inline))

/**
 * Perform a fast block XOR operation, such that
 * r[i] = a[i] ^ b[i] where 0 <= i < n
 *
 * In some situations, this can perform better than mbedtls_xor() (e.g., it's about 5%
 * better in AES-CBC).
 *
 * \param   r Pointer to result (buffer of at least \p n bytes). \p r
 *            may be equal to either \p a or \p b, but behaviour when
 *            it overlaps in other ways is undefined.
 * \param   a Pointer to input (buffer of at least \p n bytes)
 * \param   b Pointer to input (buffer of at least \p n bytes)
 * \param   n Number of bytes to process.
 *
 * \note      Depending on the situation, it may be faster to use either mbedtls_xor() or
 *            mbedtls_xor_no_simd() (these are functionally equivalent).
 *            If the result is used immediately after the xor operation in non-SIMD code (e.g, in
 *            AES-CBC), there may be additional latency to transfer the data from SIMD to scalar
 *            registers, and in this case, mbedtls_xor_no_simd() may be faster. In other cases where
 *            the result is not used immediately (e.g., in AES-CTR), mbedtls_xor() may be faster.
 *            For targets without SIMD support, they will behave the same.
 */
static __inline void mbedtls_xor_no_simd(unsigned char *r,
                                       const unsigned char *a,
                                       const unsigned char *b,
                                       size_t n)
{
    size_t i = 0;
# 302 "L:/PLT/pcac/mbedTLS/library/common.h"
    for (; i < n; i++) {
        r[i] = a[i] ^ b[i];
    }
}

/* Fix MSVC C99 compatible issue
 *      MSVC support __func__ from visual studio 2015( 1900 )
 *      Use MSVC predefine macro to avoid name check fail.
 */




/* Define `asm` for compilers which don't define it. */
/* *INDENT-OFF* */
# 324 "L:/PLT/pcac/mbedTLS/library/common.h"
/* *INDENT-ON* */

/*
 * Define the constraint used for read-only pointer operands to aarch64 asm.
 *
 * This is normally the usual "r", but for aarch64_32 (aka ILP32,
 * as found in watchos), "p" is required to avoid warnings from clang.
 *
 * Note that clang does not recognise '+p' or '=p', and armclang
 * does not recognise 'p' at all. Therefore, to update a pointer from
 * aarch64 assembly, it is necessary to use something like:
 *
 * uintptr_t uptr = (uintptr_t) ptr;
 * asm( "ldr x4, [%x0], #8" ... : "+r" (uptr) : : )
 * ptr = (void*) uptr;
 *
 * Note that the "x" in "%x0" is neccessary; writing "%0" will cause warnings.
 */
# 353 "L:/PLT/pcac/mbedTLS/library/common.h"

/* Always provide a static assert macro, so it can be used unconditionally.
 * It does nothing on systems where we don't know how to define a static assert.
 */
/* Can't use the C11-style `defined(static_assert)` on FreeBSD, since it
 * defines static_assert even with -std=c99, but then complains about it.
 */



/* Make sure `MBEDTLS_STATIC_ASSERT(expr, msg);` is valid both inside and
 * outside a function. We choose a struct declaration, which can be repeated
 * any number of times and does not need a matching definition. */










/* Define compiler branch hints */
# 384 "L:/PLT/pcac/mbedTLS/library/common.h"

/* MBEDTLS_ASSUME may be used to provide additional information to the compiler
 * which can result in smaller code-size. */
# 399 "L:/PLT/pcac/mbedTLS/library/common.h"

/* For gcc -Os, override with -O2 for a given function.
 *
 * This will not affect behaviour for other optimisation settings, e.g. -O0.
 */






/* Suppress compiler warnings for unused functions and variables. */
# 436 "L:/PLT/pcac/mbedTLS/library/common.h"

# 33 "L:/PLT/pcac/mbedTLS/library/ecp.c"

/**
 * \brief Function level alternative implementation.
 *
 * The MBEDTLS_ECP_INTERNAL_ALT macro enables alternative implementations to
 * replace certain functions in this module. The alternative implementations are
 * typically hardware accelerators and need to activate the hardware before the
 * computation starts and deactivate it after it finishes. The
 * mbedtls_internal_ecp_init() and mbedtls_internal_ecp_free() functions serve
 * this purpose.
 *
 * To preserve the correct functionality the following conditions must hold:
 *
 * - The alternative implementation must be activated by
 *   mbedtls_internal_ecp_init() before any of the replaceable functions is
 *   called.
 * - mbedtls_internal_ecp_free() must \b only be called when the alternative
 *   implementation is activated.
 * - mbedtls_internal_ecp_init() must \b not be called when the alternative
 *   implementation is activated.
 * - Public functions must not return while the alternative implementation is
 *   activated.
 * - Replaceable functions are guarded by \c MBEDTLS_ECP_XXX_ALT macros and
 *   before calling them an \code if( mbedtls_internal_ecp_grp_capable( grp ) )
 *   \endcode ensures that the alternative implementation supports the current
 *   group.
 */





# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/ecp.h"
/**
 * \file ecp.h
 *
 * \brief This file provides an API for Elliptic Curves over GF(P) (ECP).
 *
 * The use of ECP in cryptography and TLS is defined in
 * <em>Standards for Efficient Cryptography Group (SECG): SEC1
 * Elliptic Curve Cryptography</em> and
 * <em>RFC-4492: Elliptic Curve Cryptography (ECC) Cipher Suites
 * for Transport Layer Security (TLS)</em>.
 *
 * <em>RFC-2409: The Internet Key Exchange (IKE)</em> defines ECP
 * group types.
 *
 */

/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */

# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/private_access.h"
/**
 * \file private_access.h
 *
 * \brief Macro wrapper for struct's members.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */




# 20 "L:/PLT/pcac/mbedTLS/include/mbedtls/private_access.h"

# 25 "L:/PLT/pcac/mbedTLS/include/mbedtls/ecp.h"

# 27 "L:/PLT/pcac/mbedTLS/include/mbedtls/ecp.h"
# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_util.h"
/**
 * \file platform_util.h
 *
 * \brief Common and shared functions used by multiple modules in the Mbed TLS
 *        library.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */



# 15 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_util.h"

# 17 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_util.h"









/* Internal helper macros for deprecating API constants. */
# 42 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_util.h"

/* Implementation of the check-return facility.
 * See the user documentation in mbedtls_config.h.
 *
 * Do not use this macro directly to annotate function: instead,
 * use one of MBEDTLS_CHECK_RETURN_CRITICAL or MBEDTLS_CHECK_RETURN_TYPICAL
 * depending on how important it is to check the return value.
 */
# 60 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_util.h"

/** Critical-failure function
 *
 * This macro appearing at the beginning of the declaration of a function
 * indicates that its return value should be checked in all applications.
 * Omitting the check is very likely to indicate a bug in the application
 * and will result in a compile-time warning if #MBEDTLS_CHECK_RETURN
 * is implemented for the compiler in use.
 *
 * \note  The use of this macro is a work in progress.
 *        This macro may be added to more functions in the future.
 *        Such an extension is not considered an API break, provided that
 *        there are near-unavoidable circumstances under which the function
 *        can fail. For example, signature/MAC/AEAD verification functions,
 *        and functions that require a random generator, are considered
 *        return-check-critical.
 */


/** Ordinary-failure function
 *
 * This macro appearing at the beginning of the declaration of a function
 * indicates that its return value should be generally be checked in portable
 * applications. Omitting the check will result in a compile-time warning if
 * #MBEDTLS_CHECK_RETURN is implemented for the compiler in use and
 * #MBEDTLS_CHECK_RETURN_WARNING is enabled in the compile-time configuration.
 *
 * You can use #MBEDTLS_IGNORE_RETURN to explicitly ignore the return value
 * of a function that is annotated with #MBEDTLS_CHECK_RETURN.
 *
 * \note  The use of this macro is a work in progress.
 *        This macro will be added to more functions in the future.
 *        Eventually this should appear before most functions returning
 *        an error code (as \c int in the \c mbedtls_xxx API or
 *        as ::psa_status_t in the \c psa_xxx API).
 */






/** Benign-failure function
 *
 * This macro appearing at the beginning of the declaration of a function
 * indicates that it is rarely useful to check its return value.
 *
 * This macro has an empty expansion. It exists for documentation purposes:
 * a #MBEDTLS_CHECK_RETURN_OPTIONAL annotation indicates that the function
 * has been analyzed for return-check usefulness, whereas the lack of
 * an annotation indicates that the function has not been analyzed and its
 * return-check usefulness is unknown.
 */


/** \def MBEDTLS_IGNORE_RETURN
 *
 * Call this macro with one argument, a function call, to suppress a warning
 * from #MBEDTLS_CHECK_RETURN due to that function call.
 */

/* GCC doesn't silence the warning with just (void)(result).
 * (void)!(result) is known to work up at least up to GCC 10, as well
 * as with Clang and MSVC.
 *
 * https://gcc.gnu.org/onlinedocs/gcc-3.4.6/gcc/Non_002dbugs.html
 * https://stackoverflow.com/questions/40576003/ignoring-warning-wunused-result
 * https://gcc.gnu.org/bugzilla/show_bug.cgi?id=66425#c34
 */



/* If the following macro is defined, the library is being built by the test
 * framework, and the framework is going to provide a replacement
 * mbedtls_platform_zeroize() using a preprocessor macro, so the function
 * declaration should be omitted.  */

/**
 * \brief       Securely zeroize a buffer
 *
 *              The function is meant to wipe the data contained in a buffer so
 *              that it can no longer be recovered even if the program memory
 *              is later compromised. Call this function on sensitive data
 *              stored on the stack before returning from a function, and on
 *              sensitive data stored on the heap before freeing the heap
 *              object.
 *
 *              It is extremely difficult to guarantee that calls to
 *              mbedtls_platform_zeroize() are not removed by aggressive
 *              compiler optimizations in a portable way. For this reason, Mbed
 *              TLS provides the configuration option
 *              MBEDTLS_PLATFORM_ZEROIZE_ALT, which allows users to configure
 *              mbedtls_platform_zeroize() to use a suitable implementation for
 *              their platform and needs
 *
 * \param buf   Buffer to be zeroized
 * \param len   Length of the buffer in bytes
 *
 */
void mbedtls_platform_zeroize(void *buf, size_t len);


# 192 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform_util.h"





# 28 "L:/PLT/pcac/mbedTLS/include/mbedtls/ecp.h"

# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/bignum.h"
/**
 * \file bignum.h
 *
 * \brief Multi-precision integer library
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */
# 13 "L:/PLT/pcac/mbedTLS/include/mbedtls/bignum.h"

# 15 "L:/PLT/pcac/mbedTLS/include/mbedtls/bignum.h"

# 17 "L:/PLT/pcac/mbedTLS/include/mbedtls/bignum.h"
# 18 "L:/PLT/pcac/mbedTLS/include/mbedtls/bignum.h"





/** An error occurred while reading from or writing to a file. */

/** Bad input parameters to function. */

/** There is an invalid character in the digit string. */

/** The buffer is too small to write to. */

/** The input arguments are negative or result in illegal output. */

/** The input argument for division is zero, which is not allowed. */

/** The input arguments are not acceptable. */

/** Memory allocation failed. */


# 46 "L:/PLT/pcac/mbedTLS/include/mbedtls/bignum.h"

/*
 * Maximum size MPIs are allowed to grow to in number of limbs.
 */



/*
 * Maximum window size used for modular exponentiation. Default: 3
 * Minimum value: 1. Maximum value: 6.
 *
 * Result is an array of ( 2 ** MBEDTLS_MPI_WINDOW_SIZE ) MPIs used
 * for the sliding window calculation. (So 8 by default)
 *
 * Reduction in size, reduces speed.
 */




/*
 * Maximum size of MPIs allowed in bits and bytes for user-MPIs.
 * ( Default: 512 bytes => 4096 bits, Maximum tested: 2048 bytes => 16384 bits )
 *
 * Note: Calculations can temporarily result in larger MPIs. So the number
 * of limbs required (MBEDTLS_MPI_MAX_LIMBS) is higher.
 */





/*
 * When reading from files with mbedtls_mpi_read_file() and writing to files with
 * mbedtls_mpi_write_file() the buffer should have space
 * for a (short) label, the MPI (in the provided radix), the newline
 * characters and the '\0'.
 *
 * By default we assume at least a 10 char label, a minimum radix of 10
 * (decimal) and a maximum of 4096 bit numbers (1234 decimal chars).
 * Autosized at compile time for at least a 10 char label, a minimum radix
 * of 10 (decimal) for a number of MBEDTLS_MPI_MAX_BITS size.
 *
 * This used to be statically sized to 1250 for a maximum of 4096 bit
 * numbers (1234 decimal chars).
 *
 * Calculate using the formula:
 *  MBEDTLS_MPI_RW_BUFFER_SIZE = ceil(MBEDTLS_MPI_MAX_BITS / ln(10) * ln(2)) +
 *                                LabelSize + 6
 */






/*
 * Define the base integer type, architecture-wise.
 *
 * 32 or 64-bit integer types can be forced regardless of the underlying
 * architecture by defining MBEDTLS_HAVE_INT32 or MBEDTLS_HAVE_INT64
 * respectively and undefining MBEDTLS_HAVE_ASM.
 *
 * Double-width integers (e.g. 128-bit in 64-bit architectures) can be
 * disabled by defining MBEDTLS_NO_UDBL_DIVISION.
 */
# 162 "L:/PLT/pcac/mbedTLS/include/mbedtls/bignum.h"


/* Default to 32-bit compilation */



typedef  int32_t mbedtls_mpi_sint;
typedef uint32_t mbedtls_mpi_uint;


typedef uint64_t mbedtls_t_udbl;




/*
 * Sanity check that exactly one of MBEDTLS_HAVE_INT32 or MBEDTLS_HAVE_INT64 is defined,
 * so that code elsewhere doesn't have to check.
 */





/** \typedef mbedtls_mpi_uint
 * \brief The type of machine digits in a bignum, called _limbs_.
 *
 * This is always an unsigned integer type with no padding bits. The size
 * is platform-dependent.
 */

/** \typedef mbedtls_mpi_sint
 * \brief The signed type corresponding to #mbedtls_mpi_uint.
 *
 * This is always an signed integer type with no padding bits. The size
 * is platform-dependent.
 */





/**
 * \brief          MPI structure
 */
typedef struct mbedtls_mpi {
    /** Pointer to limbs.
     *
     * This may be \c NULL if \c n is 0.
     */
    mbedtls_mpi_uint *p;

    /** Sign: -1 if the mpi is negative, 1 otherwise.
     *
     * The number 0 must be represented with `s = +1`. Although many library
     * functions treat all-limbs-zero as equivalent to a valid representation
     * of 0 regardless of the sign bit, there are exceptions, so bignum
     * functions and external callers must always set \c s to +1 for the
     * number zero.
     *
     * Note that this implies that calloc() or `... = {0}` does not create
     * a valid MPI representation. You must call mbedtls_mpi_init().
     */
    signed short s;

    /** Total number of limbs in \c p.  */
    unsigned short n;
    /* Make sure that MBEDTLS_MPI_MAX_LIMBS fits in n.
     * Use the same limit value on all platforms so that we don't have to
     * think about different behavior on the rare platforms where
     * unsigned short can store values larger than the minimum required by
     * the C language, which is 65535.
     */



}
mbedtls_mpi;

/**
 * \brief           Initialize an MPI context.
 *
 *                  This makes the MPI ready to be set or freed,
 *                  but does not define a value for the MPI.
 *
 * \param X         The MPI context to initialize. This must not be \c NULL.
 */
void mbedtls_mpi_init(mbedtls_mpi *X);

/**
 * \brief          This function frees the components of an MPI context.
 *
 * \param X        The MPI context to be cleared. This may be \c NULL,
 *                 in which case this function is a no-op. If it is
 *                 not \c NULL, it must point to an initialized MPI.
 */
void mbedtls_mpi_free(mbedtls_mpi *X);

/**
 * \brief          Enlarge an MPI to the specified number of limbs.
 *
 * \note           This function does nothing if the MPI is
 *                 already large enough.
 *
 * \param X        The MPI to grow. It must be initialized.
 * \param nblimbs  The target number of limbs.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_grow(mbedtls_mpi *X, size_t nblimbs);

/**
 * \brief          This function resizes an MPI downwards, keeping at least the
 *                 specified number of limbs.
 *
 *                 If \c X is smaller than \c nblimbs, it is resized up
 *                 instead.
 *
 * \param X        The MPI to shrink. This must point to an initialized MPI.
 * \param nblimbs  The minimum number of limbs to keep.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed
 *                 (this can only happen when resizing up).
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_shrink(mbedtls_mpi *X, size_t nblimbs);

/**
 * \brief          Make a copy of an MPI.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param Y        The source MPI. This must point to an initialized MPI.
 *
 * \note           The limb-buffer in the destination MPI is enlarged
 *                 if necessary to hold the value in the source MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_copy(mbedtls_mpi *X, const mbedtls_mpi *Y);

/**
 * \brief          Swap the contents of two MPIs.
 *
 * \param X        The first MPI. It must be initialized.
 * \param Y        The second MPI. It must be initialized.
 */
void mbedtls_mpi_swap(mbedtls_mpi *X, mbedtls_mpi *Y);

/**
 * \brief          Perform a safe conditional copy of MPI which doesn't
 *                 reveal whether the condition was true or not.
 *
 * \param X        The MPI to conditionally assign to. This must point
 *                 to an initialized MPI.
 * \param Y        The MPI to be assigned from. This must point to an
 *                 initialized MPI.
 * \param assign   The condition deciding whether to perform the
 *                 assignment or not. Must be either 0 or 1:
 *                 * \c 1: Perform the assignment `X = Y`.
 *                 * \c 0: Keep the original value of \p X.
 *
 * \note           This function is equivalent to
 *                      `if( assign ) mbedtls_mpi_copy( X, Y );`
 *                 except that it avoids leaking any information about whether
 *                 the assignment was done or not (the above code may leak
 *                 information through branch prediction and/or memory access
 *                 patterns analysis).
 *
 * \warning        If \p assign is neither 0 nor 1, the result of this function
 *                 is indeterminate, and the resulting value in \p X might be
 *                 neither its original value nor the value in \p Y.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_safe_cond_assign(mbedtls_mpi *X, const mbedtls_mpi *Y, unsigned char assign);

/**
 * \brief          Perform a safe conditional swap which doesn't
 *                 reveal whether the condition was true or not.
 *
 * \param X        The first MPI. This must be initialized.
 * \param Y        The second MPI. This must be initialized.
 * \param swap     The condition deciding whether to perform
 *                 the swap or not. Must be either 0 or 1:
 *                 * \c 1: Swap the values of \p X and \p Y.
 *                 * \c 0: Keep the original values of \p X and \p Y.
 *
 * \note           This function is equivalent to
 *                      if( swap ) mbedtls_mpi_swap( X, Y );
 *                 except that it avoids leaking any information about whether
 *                 the swap was done or not (the above code may leak
 *                 information through branch prediction and/or memory access
 *                 patterns analysis).
 *
 * \warning        If \p swap is neither 0 nor 1, the result of this function
 *                 is indeterminate, and both \p X and \p Y might end up with
 *                 values different to either of the original ones.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 *
 */
int mbedtls_mpi_safe_cond_swap(mbedtls_mpi *X, mbedtls_mpi *Y, unsigned char swap);

/**
 * \brief          Store integer value in MPI.
 *
 * \param X        The MPI to set. This must be initialized.
 * \param z        The value to use.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_lset(mbedtls_mpi *X, mbedtls_mpi_sint z);

/**
 * \brief          Get a specific bit from an MPI.
 *
 * \param X        The MPI to query. This must be initialized.
 * \param pos      Zero-based index of the bit to query.
 *
 * \return         \c 0 or \c 1 on success, depending on whether bit \c pos
 *                 of \c X is unset or set.
 * \return         A negative error code on failure.
 */
int mbedtls_mpi_get_bit(const mbedtls_mpi *X, size_t pos);

/**
 * \brief          Modify a specific bit in an MPI.
 *
 * \note           This function will grow the target MPI if necessary to set a
 *                 bit to \c 1 in a not yet existing limb. It will not grow if
 *                 the bit should be set to \c 0.
 *
 * \param X        The MPI to modify. This must be initialized.
 * \param pos      Zero-based index of the bit to modify.
 * \param val      The desired value of bit \c pos: \c 0 or \c 1.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_set_bit(mbedtls_mpi *X, size_t pos, unsigned char val);

/**
 * \brief          Return the number of bits of value \c 0 before the
 *                 least significant bit of value \c 1.
 *
 * \note           This is the same as the zero-based index of
 *                 the least significant bit of value \c 1.
 *
 * \param X        The MPI to query.
 *
 * \return         The number of bits of value \c 0 before the least significant
 *                 bit of value \c 1 in \p X.
 */
size_t mbedtls_mpi_lsb(const mbedtls_mpi *X);

/**
 * \brief          Return the number of bits up to and including the most
 *                 significant bit of value \c 1.
 *
 * * \note         This is same as the one-based index of the most
 *                 significant bit of value \c 1.
 *
 * \param X        The MPI to query. This must point to an initialized MPI.
 *
 * \return         The number of bits up to and including the most
 *                 significant bit of value \c 1.
 */
size_t mbedtls_mpi_bitlen(const mbedtls_mpi *X);

/**
 * \brief          Return the total size of an MPI value in bytes.
 *
 * \param X        The MPI to use. This must point to an initialized MPI.
 *
 * \note           The value returned by this function may be less than
 *                 the number of bytes used to store \p X internally.
 *                 This happens if and only if there are trailing bytes
 *                 of value zero.
 *
 * \return         The least number of bytes capable of storing
 *                 the absolute value of \p X.
 */
size_t mbedtls_mpi_size(const mbedtls_mpi *X);

/**
 * \brief          Import an MPI from an ASCII string.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param radix    The numeric base of the input string.
 * \param s        Null-terminated string buffer.
 *
 * \return         \c 0 if successful.
 * \return         A negative error code on failure.
 */
int mbedtls_mpi_read_string(mbedtls_mpi *X, int radix, const char *s);

/**
 * \brief          Export an MPI to an ASCII string.
 *
 * \param X        The source MPI. This must point to an initialized MPI.
 * \param radix    The numeric base of the output string.
 * \param buf      The buffer to write the string to. This must be writable
 *                 buffer of length \p buflen Bytes.
 * \param buflen   The available size in Bytes of \p buf.
 * \param olen     The address at which to store the length of the string
 *                 written, including the  final \c NULL byte. This must
 *                 not be \c NULL.
 *
 * \note           You can call this function with `buflen == 0` to obtain the
 *                 minimum required buffer size in `*olen`.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL if the target buffer \p buf
 *                 is too small to hold the value of \p X in the desired base.
 *                 In this case, `*olen` is nonetheless updated to contain the
 *                 size of \p buf required for a successful call.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_write_string(const mbedtls_mpi *X, int radix,
                             char *buf, size_t buflen, size_t *olen);

# 537 "L:/PLT/pcac/mbedTLS/include/mbedtls/bignum.h"

/**
 * \brief          Import an MPI from unsigned big endian binary data.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param buf      The input buffer. This must be a readable buffer of length
 *                 \p buflen Bytes.
 * \param buflen   The length of the input buffer \p buf in Bytes.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_read_binary(mbedtls_mpi *X, const unsigned char *buf,
                            size_t buflen);

/**
 * \brief          Import X from unsigned binary data, little endian
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param buf      The input buffer. This must be a readable buffer of length
 *                 \p buflen Bytes.
 * \param buflen   The length of the input buffer \p buf in Bytes.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_read_binary_le(mbedtls_mpi *X,
                               const unsigned char *buf, size_t buflen);

/**
 * \brief          Export X into unsigned binary data, big endian.
 *                 Always fills the whole buffer, which will start with zeros
 *                 if the number is smaller.
 *
 * \param X        The source MPI. This must point to an initialized MPI.
 * \param buf      The output buffer. This must be a writable buffer of length
 *                 \p buflen Bytes.
 * \param buflen   The size of the output buffer \p buf in Bytes.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL if \p buf isn't
 *                 large enough to hold the value of \p X.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_write_binary(const mbedtls_mpi *X, unsigned char *buf,
                             size_t buflen);

/**
 * \brief          Export X into unsigned binary data, little endian.
 *                 Always fills the whole buffer, which will end with zeros
 *                 if the number is smaller.
 *
 * \param X        The source MPI. This must point to an initialized MPI.
 * \param buf      The output buffer. This must be a writable buffer of length
 *                 \p buflen Bytes.
 * \param buflen   The size of the output buffer \p buf in Bytes.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL if \p buf isn't
 *                 large enough to hold the value of \p X.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_write_binary_le(const mbedtls_mpi *X,
                                unsigned char *buf, size_t buflen);

/**
 * \brief          Perform a left-shift on an MPI: X <<= count
 *
 * \param X        The MPI to shift. This must point to an initialized MPI.
 *                 The MPI pointed by \p X may be resized to fit
 *                 the resulting number.
 * \param count    The number of bits to shift by.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_shift_l(mbedtls_mpi *X, size_t count);

/**
 * \brief          Perform a right-shift on an MPI: X >>= count
 *
 * \param X        The MPI to shift. This must point to an initialized MPI.
 * \param count    The number of bits to shift by.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_shift_r(mbedtls_mpi *X, size_t count);

/**
 * \brief          Compare the absolute values of two MPIs.
 *
 * \param X        The left-hand MPI. This must point to an initialized MPI.
 * \param Y        The right-hand MPI. This must point to an initialized MPI.
 *
 * \return         \c 1 if `|X|` is greater than `|Y|`.
 * \return         \c -1 if `|X|` is lesser than `|Y|`.
 * \return         \c 0 if `|X|` is equal to `|Y|`.
 */
int mbedtls_mpi_cmp_abs(const mbedtls_mpi *X, const mbedtls_mpi *Y);

/**
 * \brief          Compare two MPIs.
 *
 * \param X        The left-hand MPI. This must point to an initialized MPI.
 * \param Y        The right-hand MPI. This must point to an initialized MPI.
 *
 * \return         \c 1 if \p X is greater than \p Y.
 * \return         \c -1 if \p X is lesser than \p Y.
 * \return         \c 0 if \p X is equal to \p Y.
 */
int mbedtls_mpi_cmp_mpi(const mbedtls_mpi *X, const mbedtls_mpi *Y);

/**
 * \brief          Check if an MPI is less than the other in constant time.
 *
 * \param X        The left-hand MPI. This must point to an initialized MPI
 *                 with the same allocated length as Y.
 * \param Y        The right-hand MPI. This must point to an initialized MPI
 *                 with the same allocated length as X.
 * \param ret      The result of the comparison:
 *                 \c 1 if \p X is less than \p Y.
 *                 \c 0 if \p X is greater than or equal to \p Y.
 *
 * \return         0 on success.
 * \return         MBEDTLS_ERR_MPI_BAD_INPUT_DATA if the allocated length of
 *                 the two input MPIs is not the same.
 */
int mbedtls_mpi_lt_mpi_ct(const mbedtls_mpi *X, const mbedtls_mpi *Y,
                          unsigned *ret);

/**
 * \brief          Compare an MPI with an integer.
 *
 * \param X        The left-hand MPI. This must point to an initialized MPI.
 * \param z        The integer value to compare \p X to.
 *
 * \return         \c 1 if \p X is greater than \p z.
 * \return         \c -1 if \p X is lesser than \p z.
 * \return         \c 0 if \p X is equal to \p z.
 */
int mbedtls_mpi_cmp_int(const mbedtls_mpi *X, mbedtls_mpi_sint z);

/**
 * \brief          Perform an unsigned addition of MPIs: X = |A| + |B|
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first summand. This must point to an initialized MPI.
 * \param B        The second summand. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_add_abs(mbedtls_mpi *X, const mbedtls_mpi *A,
                        const mbedtls_mpi *B);

/**
 * \brief          Perform an unsigned subtraction of MPIs: X = |A| - |B|
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The minuend. This must point to an initialized MPI.
 * \param B        The subtrahend. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_NEGATIVE_VALUE if \p B is greater than \p A.
 * \return         Another negative error code on different kinds of failure.
 *
 */
int mbedtls_mpi_sub_abs(mbedtls_mpi *X, const mbedtls_mpi *A,
                        const mbedtls_mpi *B);

/**
 * \brief          Perform a signed addition of MPIs: X = A + B
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first summand. This must point to an initialized MPI.
 * \param B        The second summand. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_add_mpi(mbedtls_mpi *X, const mbedtls_mpi *A,
                        const mbedtls_mpi *B);

/**
 * \brief          Perform a signed subtraction of MPIs: X = A - B
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The minuend. This must point to an initialized MPI.
 * \param B        The subtrahend. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_sub_mpi(mbedtls_mpi *X, const mbedtls_mpi *A,
                        const mbedtls_mpi *B);

/**
 * \brief          Perform a signed addition of an MPI and an integer: X = A + b
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first summand. This must point to an initialized MPI.
 * \param b        The second summand.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_add_int(mbedtls_mpi *X, const mbedtls_mpi *A,
                        mbedtls_mpi_sint b);

/**
 * \brief          Perform a signed subtraction of an MPI and an integer:
 *                 X = A - b
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The minuend. This must point to an initialized MPI.
 * \param b        The subtrahend.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_sub_int(mbedtls_mpi *X, const mbedtls_mpi *A,
                        mbedtls_mpi_sint b);

/**
 * \brief          Perform a multiplication of two MPIs: X = A * B
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first factor. This must point to an initialized MPI.
 * \param B        The second factor. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 *
 */
int mbedtls_mpi_mul_mpi(mbedtls_mpi *X, const mbedtls_mpi *A,
                        const mbedtls_mpi *B);

/**
 * \brief          Perform a multiplication of an MPI with an unsigned integer:
 *                 X = A * b
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first factor. This must point to an initialized MPI.
 * \param b        The second factor.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 *
 */
int mbedtls_mpi_mul_int(mbedtls_mpi *X, const mbedtls_mpi *A,
                        mbedtls_mpi_uint b);

/**
 * \brief          Perform a division with remainder of two MPIs:
 *                 A = Q * B + R
 *
 * \param Q        The destination MPI for the quotient.
 *                 This may be \c NULL if the value of the
 *                 quotient is not needed. This must not alias A or B.
 * \param R        The destination MPI for the remainder value.
 *                 This may be \c NULL if the value of the
 *                 remainder is not needed. This must not alias A or B.
 * \param A        The dividend. This must point to an initialized MPI.
 * \param B        The divisor. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_DIVISION_BY_ZERO if \p B equals zero.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_div_mpi(mbedtls_mpi *Q, mbedtls_mpi *R, const mbedtls_mpi *A,
                        const mbedtls_mpi *B);

/**
 * \brief          Perform a division with remainder of an MPI by an integer:
 *                 A = Q * b + R
 *
 * \param Q        The destination MPI for the quotient.
 *                 This may be \c NULL if the value of the
 *                 quotient is not needed.  This must not alias A.
 * \param R        The destination MPI for the remainder value.
 *                 This may be \c NULL if the value of the
 *                 remainder is not needed.  This must not alias A.
 * \param A        The dividend. This must point to an initialized MPi.
 * \param b        The divisor.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_DIVISION_BY_ZERO if \p b equals zero.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_div_int(mbedtls_mpi *Q, mbedtls_mpi *R, const mbedtls_mpi *A,
                        mbedtls_mpi_sint b);

/**
 * \brief          Perform a modular reduction. R = A mod B
 *
 * \param R        The destination MPI for the residue value.
 *                 This must point to an initialized MPI.
 * \param A        The MPI to compute the residue of.
 *                 This must point to an initialized MPI.
 * \param B        The base of the modular reduction.
 *                 This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_DIVISION_BY_ZERO if \p B equals zero.
 * \return         #MBEDTLS_ERR_MPI_NEGATIVE_VALUE if \p B is negative.
 * \return         Another negative error code on different kinds of failure.
 *
 */
int mbedtls_mpi_mod_mpi(mbedtls_mpi *R, const mbedtls_mpi *A,
                        const mbedtls_mpi *B);

/**
 * \brief          Perform a modular reduction with respect to an integer.
 *                 r = A mod b
 *
 * \param r        The address at which to store the residue.
 *                 This must not be \c NULL.
 * \param A        The MPI to compute the residue of.
 *                 This must point to an initialized MPi.
 * \param b        The integer base of the modular reduction.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_DIVISION_BY_ZERO if \p b equals zero.
 * \return         #MBEDTLS_ERR_MPI_NEGATIVE_VALUE if \p b is negative.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_mod_int(mbedtls_mpi_uint *r, const mbedtls_mpi *A,
                        mbedtls_mpi_sint b);

/**
 * \brief          Perform a modular exponentiation: X = A^E mod N
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 *                 This must not alias E or N.
 * \param A        The base of the exponentiation.
 *                 This must point to an initialized MPI.
 * \param E        The exponent MPI. This must point to an initialized MPI.
 * \param N        The base for the modular reduction. This must point to an
 *                 initialized MPI.
 * \param prec_RR  A helper MPI depending solely on \p N which can be used to
 *                 speed-up multiple modular exponentiations for the same value
 *                 of \p N. This may be \c NULL. If it is not \c NULL, it must
 *                 point to an initialized MPI. If it hasn't been used after
 *                 the call to mbedtls_mpi_init(), this function will compute
 *                 the helper value and store it in \p prec_RR for reuse on
 *                 subsequent calls to this function. Otherwise, the function
 *                 will assume that \p prec_RR holds the helper value set by a
 *                 previous call to mbedtls_mpi_exp_mod(), and reuse it.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if \c N is negative or
 *                 even, or if \c E is negative.
 * \return         Another negative error code on different kinds of failures.
 *
 */
int mbedtls_mpi_exp_mod(mbedtls_mpi *X, const mbedtls_mpi *A,
                        const mbedtls_mpi *E, const mbedtls_mpi *N,
                        mbedtls_mpi *prec_RR);

/**
 * \brief          Fill an MPI with a number of random bytes.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param size     The number of random bytes to generate.
 * \param f_rng    The RNG function to use. This must not be \c NULL.
 * \param p_rng    The RNG parameter to be passed to \p f_rng. This may be
 *                 \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on failure.
 *
 * \note           The bytes obtained from the RNG are interpreted
 *                 as a big-endian representation of an MPI; this can
 *                 be relevant in applications like deterministic ECDSA.
 */
int mbedtls_mpi_fill_random(mbedtls_mpi *X, size_t size,
                            int (*f_rng)(void *, unsigned char *, size_t),
                            void *p_rng);

/** Generate a random number uniformly in a range.
 *
 * This function generates a random number between \p min inclusive and
 * \p N exclusive.
 *
 * The procedure complies with RFC 6979 §3.3 (deterministic ECDSA)
 * when the RNG is a suitably parametrized instance of HMAC_DRBG
 * and \p min is \c 1.
 *
 * \note           There are `N - min` possible outputs. The lower bound
 *                 \p min can be reached, but the upper bound \p N cannot.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param min      The minimum value to return.
 *                 It must be nonnegative.
 * \param N        The upper bound of the range, exclusive.
 *                 In other words, this is one plus the maximum value to return.
 *                 \p N must be strictly larger than \p min.
 * \param f_rng    The RNG function to use. This must not be \c NULL.
 * \param p_rng    The RNG parameter to be passed to \p f_rng.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if \p min or \p N is invalid
 *                 or if they are incompatible.
 * \return         #MBEDTLS_ERR_MPI_NOT_ACCEPTABLE if the implementation was
 *                 unable to find a suitable value within a limited number
 *                 of attempts. This has a negligible probability if \p N
 *                 is significantly larger than \p min, which is the case
 *                 for all usual cryptographic applications.
 * \return         Another negative error code on failure.
 */
int mbedtls_mpi_random(mbedtls_mpi *X,
                       mbedtls_mpi_sint min,
                       const mbedtls_mpi *N,
                       int (*f_rng)(void *, unsigned char *, size_t),
                       void *p_rng);

/**
 * \brief          Compute the greatest common divisor: G = gcd(A, B)
 *
 * \param G        The destination MPI. This must point to an initialized MPI.
 * \param A        The first operand. This must point to an initialized MPI.
 * \param B        The second operand. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_gcd(mbedtls_mpi *G, const mbedtls_mpi *A,
                    const mbedtls_mpi *B);

/**
 * \brief          Compute the modular inverse: X = A^-1 mod N
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The MPI to calculate the modular inverse of. This must point
 *                 to an initialized MPI.
 * \param N        The base of the modular inversion. This must point to an
 *                 initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if \p N is less than
 *                 or equal to one.
 * \return         #MBEDTLS_ERR_MPI_NOT_ACCEPTABLE if \p A has no modular
 *                 inverse with respect to \p N.
 */
int mbedtls_mpi_inv_mod(mbedtls_mpi *X, const mbedtls_mpi *A,
                        const mbedtls_mpi *N);

/**
 * \brief          Miller-Rabin primality test.
 *
 * \warning        If \p X is potentially generated by an adversary, for example
 *                 when validating cryptographic parameters that you didn't
 *                 generate yourself and that are supposed to be prime, then
 *                 \p rounds should be at least the half of the security
 *                 strength of the cryptographic algorithm. On the other hand,
 *                 if \p X is chosen uniformly or non-adversarially (as is the
 *                 case when mbedtls_mpi_gen_prime calls this function), then
 *                 \p rounds can be much lower.
 *
 * \param X        The MPI to check for primality.
 *                 This must point to an initialized MPI.
 * \param rounds   The number of bases to perform the Miller-Rabin primality
 *                 test for. The probability of returning 0 on a composite is
 *                 at most 2<sup>-2*\p rounds </sup>.
 * \param f_rng    The RNG function to use. This must not be \c NULL.
 * \param p_rng    The RNG parameter to be passed to \p f_rng.
 *                 This may be \c NULL if \p f_rng doesn't use
 *                 a context parameter.
 *
 * \return         \c 0 if successful, i.e. \p X is probably prime.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_NOT_ACCEPTABLE if \p X is not prime.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_is_prime_ext(const mbedtls_mpi *X, int rounds,
                             int (*f_rng)(void *, unsigned char *, size_t),
                             void *p_rng);
/**
 * \brief Flags for mbedtls_mpi_gen_prime()
 *
 * Each of these flags is a constraint on the result X returned by
 * mbedtls_mpi_gen_prime().
 */
typedef enum {
    MBEDTLS_MPI_GEN_PRIME_FLAG_DH =      0x0001, /**< (X-1)/2 is prime too */
    MBEDTLS_MPI_GEN_PRIME_FLAG_LOW_ERR = 0x0002, /**< lower error rate from 2<sup>-80</sup> to 2<sup>-128</sup> */
} mbedtls_mpi_gen_prime_flag_t;

/**
 * \brief          Generate a prime number.
 *
 * \param X        The destination MPI to store the generated prime in.
 *                 This must point to an initialized MPi.
 * \param nbits    The required size of the destination MPI in bits.
 *                 This must be between \c 3 and #MBEDTLS_MPI_MAX_BITS.
 * \param flags    A mask of flags of type #mbedtls_mpi_gen_prime_flag_t.
 * \param f_rng    The RNG function to use. This must not be \c NULL.
 * \param p_rng    The RNG parameter to be passed to \p f_rng.
 *                 This may be \c NULL if \p f_rng doesn't use
 *                 a context parameter.
 *
 * \return         \c 0 if successful, in which case \p X holds a
 *                 probably prime number.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if `nbits` is not between
 *                 \c 3 and #MBEDTLS_MPI_MAX_BITS.
 */
int mbedtls_mpi_gen_prime(mbedtls_mpi *X, size_t nbits, int flags,
                          int (*f_rng)(void *, unsigned char *, size_t),
                          void *p_rng);

# 1079 "L:/PLT/pcac/mbedTLS/include/mbedtls/bignum.h"





# 30 "L:/PLT/pcac/mbedTLS/include/mbedtls/ecp.h"

/*
 * ECP error codes
 */
/** Bad input parameters to function. */

/** The buffer is too small to write to. */

/** The requested feature is not available, for example, the requested curve is not supported. */

/** The signature is not valid. */

/** Memory allocation failed. */

/** Generation of random value, such as ephemeral key, failed. */

/** Invalid private or public key. */

/** The buffer contains a valid signature followed by more data. */

/** Operation in progress, call again with the same parameters to continue. */


/* Flags indicating whether to include code that is specific to certain
 * types of curves. These flags are for internal library use only. */
# 72 "L:/PLT/pcac/mbedTLS/include/mbedtls/ecp.h"





/**
 * Domain-parameter identifiers: curve, subgroup, and generator.
 *
 * \note Only curves over prime fields are supported.
 *
 * \warning This library does not support validation of arbitrary domain
 * parameters. Therefore, only standardized domain parameters from trusted
 * sources should be used. See mbedtls_ecp_group_load().
 */
/* Note: when adding a new curve:
 * - Add it at the end of this enum, otherwise you'll break the ABI by
 *   changing the numerical value for existing curves.
 * - Increment MBEDTLS_ECP_DP_MAX below if needed.
 * - Update the calculation of MBEDTLS_ECP_MAX_BITS below.
 * - Add the corresponding MBEDTLS_ECP_DP_xxx_ENABLED macro definition to
 *   mbedtls_config.h.
 * - List the curve as a dependency of MBEDTLS_ECP_C and
 *   MBEDTLS_ECDSA_C if supported in check_config.h.
 * - Add the curve to the appropriate curve type macro
 *   MBEDTLS_ECP_yyy_ENABLED above.
 * - Add the necessary definitions to ecp_curves.c.
 * - Add the curve to the ecp_supported_curves array in ecp.c.
 * - Add the curve to applicable profiles in x509_crt.c.
 * - Add the curve to applicable presets in ssl_tls.c.
 */
typedef enum {
    MBEDTLS_ECP_DP_NONE = 0,       /*!< Curve not defined. */
    MBEDTLS_ECP_DP_SECP192R1,      /*!< Domain parameters for the 192-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_SECP224R1,      /*!< Domain parameters for the 224-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_SECP256R1,      /*!< Domain parameters for the 256-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_SECP384R1,      /*!< Domain parameters for the 384-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_SECP521R1,      /*!< Domain parameters for the 521-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_BP256R1,        /*!< Domain parameters for 256-bit Brainpool curve. */
    MBEDTLS_ECP_DP_BP384R1,        /*!< Domain parameters for 384-bit Brainpool curve. */
    MBEDTLS_ECP_DP_BP512R1,        /*!< Domain parameters for 512-bit Brainpool curve. */
    MBEDTLS_ECP_DP_CURVE25519,     /*!< Domain parameters for Curve25519. */
    MBEDTLS_ECP_DP_SECP192K1,      /*!< Domain parameters for 192-bit "Koblitz" curve. */
    MBEDTLS_ECP_DP_SECP224K1,      /*!< Domain parameters for 224-bit "Koblitz" curve. */
    MBEDTLS_ECP_DP_SECP256K1,      /*!< Domain parameters for 256-bit "Koblitz" curve. */
    MBEDTLS_ECP_DP_CURVE448,       /*!< Domain parameters for Curve448. */
} mbedtls_ecp_group_id;

/**
 * The number of supported curves, plus one for #MBEDTLS_ECP_DP_NONE.
 */


/*
 * Curve types
 */
typedef enum {
    MBEDTLS_ECP_TYPE_NONE = 0,
    MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS,    /* y^2 = x^3 + a x + b      */
    MBEDTLS_ECP_TYPE_MONTGOMERY,           /* y^2 = x^3 + a x^2 + x    */
} mbedtls_ecp_curve_type;

/**
 * Curve information, for use by other modules.
 *
 * The fields of this structure are part of the public API and can be
 * accessed directly by applications. Future versions of the library may
 * add extra fields or reorder existing fields.
 */
typedef struct mbedtls_ecp_curve_info {
    mbedtls_ecp_group_id grp_id;    /*!< An internal identifier. */
    uint16_t tls_id;                /*!< The TLS NamedCurve identifier. */
    uint16_t bit_size;              /*!< The curve size in bits. */
    const char *name;               /*!< A human-friendly name. */
} mbedtls_ecp_curve_info;

/**
 * \brief           The ECP point structure, in Jacobian coordinates.
 *
 * \note            All functions expect and return points satisfying
 *                  the following condition: <code>Z == 0</code> or
 *                  <code>Z == 1</code>. Other values of \p Z are
 *                  used only by internal functions.
 *                  The point is zero, or "at infinity", if <code>Z == 0</code>.
 *                  Otherwise, \p X and \p Y are its standard (affine)
 *                  coordinates.
 */
typedef struct mbedtls_ecp_point {
    mbedtls_mpi X;          /*!< The X coordinate of the ECP point. */
    mbedtls_mpi Y;          /*!< The Y coordinate of the ECP point. */
    mbedtls_mpi Z;          /*!< The Z coordinate of the ECP point. */
}
mbedtls_ecp_point;


/*
 * default Mbed TLS elliptic curve arithmetic implementation
 *
 * (in case MBEDTLS_ECP_ALT is defined then the developer has to provide an
 * alternative implementation for the whole module and it will replace this
 * one.)
 */

/**
 * \brief           The ECP group structure.
 *
 * We consider two types of curve equations:
 * <ul><li>Short Weierstrass: <code>y^2 = x^3 + A x + B mod P</code>
 * (SEC1 + RFC-4492)</li>
 * <li>Montgomery: <code>y^2 = x^3 + A x^2 + x mod P</code> (Curve25519,
 * Curve448)</li></ul>
 * In both cases, the generator (\p G) for a prime-order subgroup is fixed.
 *
 * For Short Weierstrass, this subgroup is the whole curve, and its
 * cardinality is denoted by \p N. Our code requires that \p N is an
 * odd prime as mbedtls_ecp_mul() requires an odd number, and
 * mbedtls_ecdsa_sign() requires that it is prime for blinding purposes.
 *
 * The default implementation only initializes \p A without setting it to the
 * authentic value for curves with <code>A = -3</code>(SECP256R1, etc), in which
 * case you need to load \p A by yourself when using domain parameters directly,
 * for example:
 * \code
 * mbedtls_mpi_init(&A);
 * mbedtls_ecp_group_init(&grp);
 * CHECK_RETURN(mbedtls_ecp_group_load(&grp, grp_id));
 * if (mbedtls_ecp_group_a_is_minus_3(&grp)) {
 *     CHECK_RETURN(mbedtls_mpi_sub_int(&A, &grp.P, 3));
 * } else {
 *     CHECK_RETURN(mbedtls_mpi_copy(&A, &grp.A));
 * }
 *
 * do_something_with_a(&A);
 *
 * cleanup:
 * mbedtls_mpi_free(&A);
 * mbedtls_ecp_group_free(&grp);
 * \endcode
 *
 * For Montgomery curves, we do not store \p A, but <code>(A + 2) / 4</code>,
 * which is the quantity used in the formulas. Additionally, \p nbits is
 * not the size of \p N but the required size for private keys.
 *
 * If \p modp is NULL, reduction modulo \p P is done using a generic algorithm.
 * Otherwise, \p modp must point to a function that takes an \p mbedtls_mpi in the
 * range of <code>0..2^(2*pbits)-1</code>, and transforms it in-place to an integer
 * which is congruent mod \p P to the given MPI, and is close enough to \p pbits
 * in size, so that it may be efficiently brought in the 0..P-1 range by a few
 * additions or subtractions. Therefore, it is only an approximate modular
 * reduction. It must return 0 on success and non-zero on failure.
 *
 * \note        Alternative implementations of the ECP module must obey the
 *              following constraints.
 *              * Group IDs must be distinct: if two group structures have
 *                the same ID, then they must be identical.
 *              * The fields \c id, \c P, \c A, \c B, \c G, \c N,
 *                \c pbits and \c nbits must have the same type and semantics
 *                as in the built-in implementation.
 *                They must be available for reading, but direct modification
 *                of these fields does not need to be supported.
 *                They do not need to be at the same offset in the structure.
 */
typedef struct mbedtls_ecp_group {
    mbedtls_ecp_group_id id;    /*!< An internal group identifier. */
    mbedtls_mpi P;              /*!< The prime modulus of the base field. */
    mbedtls_mpi A;              /*!< For Short Weierstrass: \p A in the equation. Note that
                                     \p A is not set to the authentic value in some cases.
                                     Refer to detailed description of ::mbedtls_ecp_group if
                                     using domain parameters in the structure.
                                     For Montgomery curves: <code>(A + 2) / 4</code>. */
    mbedtls_mpi B;              /*!< For Short Weierstrass: \p B in the equation.
                                     For Montgomery curves: unused. */
    mbedtls_ecp_point G;        /*!< The generator of the subgroup used. */
    mbedtls_mpi N;              /*!< The order of \p G. */
    size_t pbits;               /*!< The number of bits in \p P.*/
    size_t nbits;               /*!< For Short Weierstrass: The number of bits in \p P.
                                     For Montgomery curves: the number of bits in the
                                     private keys. */
    /* End of public fields */

    unsigned int h;             /*!< \internal 1 if the constants are static. */
    int(*modp)(mbedtls_mpi *);  /*!< The function for fast pseudo-reduction
                                                    mod \p P (see above).*/
    int(*t_pre)(mbedtls_ecp_point *, void *);   /*!< Unused. */
    int(*t_post)(mbedtls_ecp_point *, void *);  /*!< Unused. */
    void *t_data;               /*!< Unused. */
    mbedtls_ecp_point *T;       /*!< Pre-computed points for ecp_mul_comb(). */
    size_t T_size;              /*!< The number of dynamic allocated pre-computed points. */
}
mbedtls_ecp_group;

/**
 * \name SECTION: Module settings
 *
 * The configuration options you can set for this module are in this section.
 * Either change them in mbedtls_config.h, or define them using the compiler command line.
 * \{
 */


/*
 * Maximum "window" size used for point multiplication.
 * Default: a point where higher memory usage yields diminishing performance
 *          returns.
 * Minimum value: 2. Maximum value: 7.
 *
 * Result is an array of at most ( 1 << ( MBEDTLS_ECP_WINDOW_SIZE - 1 ) )
 * points used for point multiplication. This value is directly tied to EC
 * peak memory usage, so decreasing it by one should roughly cut memory usage
 * by two (if large curves are in use).
 *
 * Reduction in size may reduce speed, but larger curves are impacted first.
 * Sample performances (in ECDHE handshakes/s, with FIXED_POINT_OPTIM = 1):
 *      w-size:     6       5       4       3       2
 *      521       145     141     135     120      97
 *      384       214     209     198     177     146
 *      256       320     320     303     262     226
 *      224       475     475     453     398     342
 *      192       640     640     633     587     476
 */




/*
 * Trade code size for speed on fixed-point multiplication.
 *
 * This speeds up repeated multiplication of the generator (that is, the
 * multiplication in ECDSA signatures, and half of the multiplications in
 * ECDSA verification and ECDHE) by a factor roughly 3 to 4.
 *
 * For each n-bit Short Weierstrass curve that is enabled, this adds 4n bytes
 * of code size if n < 384 and 8n otherwise.
 *
 * Change this value to 0 to reduce code size.
 */



/** \} name SECTION: Module settings */





/**
 * The maximum size of the groups, that is, of \c N and \c P.
 */
# 353 "L:/PLT/pcac/mbedTLS/include/mbedtls/ecp.h"




# 412 "L:/PLT/pcac/mbedTLS/include/mbedtls/ecp.h"



/* We want to declare restartable versions of existing functions anyway */
typedef void mbedtls_ecp_restart_ctx;



/**
 * \brief    The ECP key-pair structure.
 *
 * A generic key-pair that may be used for ECDSA and fixed ECDH, for example.
 *
 * \note    Members are deliberately in the same order as in the
 *          ::mbedtls_ecdsa_context structure.
 */
typedef struct mbedtls_ecp_keypair {
    mbedtls_ecp_group grp;      /*!<  Elliptic curve and base point     */
    mbedtls_mpi d;              /*!<  our secret value                  */
    mbedtls_ecp_point Q;        /*!<  our public value                  */
}
mbedtls_ecp_keypair;

/**
 * The uncompressed point format for Short Weierstrass curves
 * (MBEDTLS_ECP_DP_SECP_XXX and MBEDTLS_ECP_DP_BP_XXX).
 */

/**
 * The compressed point format for Short Weierstrass curves
 * (MBEDTLS_ECP_DP_SECP_XXX and MBEDTLS_ECP_DP_BP_XXX).
 *
 * \warning     While this format is supported for all concerned curves for
 *              writing, when it comes to parsing, it is not supported for all
 *              curves. Specifically, parsing compressed points on
 *              MBEDTLS_ECP_DP_SECP224R1 and MBEDTLS_ECP_DP_SECP224K1 is not
 *              supported.
 */


int mbedtls_ecp_decompress(
    const mbedtls_ecp_group *grp,
    const unsigned char *input, size_t ilen,
    unsigned char *output, size_t *olen, size_t osize
);

int mbedtls_ecp_compress(
    const mbedtls_ecp_group *grp,
    const unsigned char *input, size_t ilen,
    unsigned char *output, size_t *olen, size_t osize
);

/*
 * Some other constants from RFC 4492
 */


# 543 "L:/PLT/pcac/mbedTLS/include/mbedtls/ecp.h"

/*
 * Get the type of a curve
 */
mbedtls_ecp_curve_type mbedtls_ecp_get_type(const mbedtls_ecp_group *grp);

/**
 * \brief           This function retrieves the information defined in
 *                  mbedtls_ecp_curve_info() for all supported curves.
 *
 * \note            This function returns information about all curves
 *                  supported by the library. Some curves may not be
 *                  supported for all algorithms. Call mbedtls_ecdh_can_do()
 *                  or mbedtls_ecdsa_can_do() to check if a curve is
 *                  supported for ECDH or ECDSA.
 *
 * \return          A statically allocated array. The last entry is 0.
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_list(void);

/**
 * \brief           This function retrieves the list of internal group
 *                  identifiers of all supported curves in the order of
 *                  preference.
 *
 * \note            This function returns information about all curves
 *                  supported by the library. Some curves may not be
 *                  supported for all algorithms. Call mbedtls_ecdh_can_do()
 *                  or mbedtls_ecdsa_can_do() to check if a curve is
 *                  supported for ECDH or ECDSA.
 *
 * \return          A statically allocated array,
 *                  terminated with MBEDTLS_ECP_DP_NONE.
 */
const mbedtls_ecp_group_id *mbedtls_ecp_grp_id_list(void);

/**
 * \brief           This function retrieves curve information from an internal
 *                  group identifier.
 *
 * \param grp_id    An \c MBEDTLS_ECP_DP_XXX value.
 *
 * \return          The associated curve information on success.
 * \return          NULL on failure.
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_info_from_grp_id(mbedtls_ecp_group_id grp_id);

/**
 * \brief           This function retrieves curve information from a TLS
 *                  NamedCurve value.
 *
 * \param tls_id    An \c MBEDTLS_ECP_DP_XXX value.
 *
 * \return          The associated curve information on success.
 * \return          NULL on failure.
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_info_from_tls_id(uint16_t tls_id);

/**
 * \brief           This function retrieves curve information from a
 *                  human-readable name.
 *
 * \param name      The human-readable name.
 *
 * \return          The associated curve information on success.
 * \return          NULL on failure.
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_info_from_name(const char *name);

/**
 * \brief           This function initializes a point as zero.
 *
 * \param pt        The point to initialize.
 */
void mbedtls_ecp_point_init(mbedtls_ecp_point *pt);

/**
 * \brief           This function initializes an ECP group context
 *                  without loading any domain parameters.
 *
 * \note            After this function is called, domain parameters
 *                  for various ECP groups can be loaded through the
 *                  mbedtls_ecp_group_load() or mbedtls_ecp_tls_read_group()
 *                  functions.
 */
void mbedtls_ecp_group_init(mbedtls_ecp_group *grp);

/**
 * \brief           This function initializes a key pair as an invalid one.
 *
 * \param key       The key pair to initialize.
 */
void mbedtls_ecp_keypair_init(mbedtls_ecp_keypair *key);

/**
 * \brief           This function frees the components of a point.
 *
 * \param pt        The point to free.
 */
void mbedtls_ecp_point_free(mbedtls_ecp_point *pt);

/**
 * \brief           This function frees the components of an ECP group.
 *
 * \param grp       The group to free. This may be \c NULL, in which
 *                  case this function returns immediately. If it is not
 *                  \c NULL, it must point to an initialized ECP group.
 */
void mbedtls_ecp_group_free(mbedtls_ecp_group *grp);

/**
 * \brief           This function frees the components of a key pair.
 *
 * \param key       The key pair to free. This may be \c NULL, in which
 *                  case this function returns immediately. If it is not
 *                  \c NULL, it must point to an initialized ECP key pair.
 */
void mbedtls_ecp_keypair_free(mbedtls_ecp_keypair *key);

# 680 "L:/PLT/pcac/mbedTLS/include/mbedtls/ecp.h"

/**
 * \brief           This function copies the contents of point \p Q into
 *                  point \p P.
 *
 * \param P         The destination point. This must be initialized.
 * \param Q         The source point. This must be initialized.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          Another negative error code for other kinds of failure.
 */
int mbedtls_ecp_copy(mbedtls_ecp_point *P, const mbedtls_ecp_point *Q);

/**
 * \brief           This function copies the contents of group \p src into
 *                  group \p dst.
 *
 * \param dst       The destination group. This must be initialized.
 * \param src       The source group. This must be initialized.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_group_copy(mbedtls_ecp_group *dst,
                           const mbedtls_ecp_group *src);

/**
 * \brief           This function sets a point to the point at infinity.
 *
 * \param pt        The point to set. This must be initialized.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_set_zero(mbedtls_ecp_point *pt);

/**
 * \brief           This function checks if a point is the point at infinity.
 *
 * \param pt        The point to test. This must be initialized.
 *
 * \return          \c 1 if the point is zero.
 * \return          \c 0 if the point is non-zero.
 * \return          A negative error code on failure.
 */
int mbedtls_ecp_is_zero(mbedtls_ecp_point *pt);

/**
 * \brief           This function compares two points.
 *
 * \note            This assumes that the points are normalized. Otherwise,
 *                  they may compare as "not equal" even if they are.
 *
 * \param P         The first point to compare. This must be initialized.
 * \param Q         The second point to compare. This must be initialized.
 *
 * \return          \c 0 if the points are equal.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if the points are not equal.
 */
int mbedtls_ecp_point_cmp(const mbedtls_ecp_point *P,
                          const mbedtls_ecp_point *Q);

/**
 * \brief           This function imports a non-zero point from two ASCII
 *                  strings.
 *
 * \param P         The destination point. This must be initialized.
 * \param radix     The numeric base of the input.
 * \param x         The first affine coordinate, as a null-terminated string.
 * \param y         The second affine coordinate, as a null-terminated string.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_MPI_XXX error code on failure.
 */
int mbedtls_ecp_point_read_string(mbedtls_ecp_point *P, int radix,
                                  const char *x, const char *y);

/**
 * \brief           This function exports a point into unsigned binary data.
 *
 * \param grp       The group to which the point should belong.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param P         The point to export. This must be initialized.
 * \param format    The point format. This must be either
 *                  #MBEDTLS_ECP_PF_COMPRESSED or #MBEDTLS_ECP_PF_UNCOMPRESSED.
 *                  (For groups without these formats, this parameter is
 *                  ignored. But it still has to be either of the above
 *                  values.)
 * \param olen      The address at which to store the length of
 *                  the output in Bytes. This must not be \c NULL.
 * \param buf       The output buffer. This must be a writable buffer
 *                  of length \p buflen Bytes.
 * \param buflen    The length of the output buffer \p buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL if the output buffer
 *                  is too small to hold the point.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the point format
 *                  or the export for the given group is not implemented.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_point_write_binary(const mbedtls_ecp_group *grp,
                                   const mbedtls_ecp_point *P,
                                   int format, size_t *olen,
                                   unsigned char *buf, size_t buflen);

/**
 * \brief           This function imports a point from unsigned binary data.
 *
 * \note            This function does not check that the point actually
 *                  belongs to the given group, see mbedtls_ecp_check_pubkey()
 *                  for that.
 *
 * \note            For compressed points, see #MBEDTLS_ECP_PF_COMPRESSED for
 *                  limitations.
 *
 * \param grp       The group to which the point should belong.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param P         The destination context to import the point to.
 *                  This must be initialized.
 * \param buf       The input buffer. This must be a readable buffer
 *                  of length \p ilen Bytes.
 * \param ilen      The length of the input buffer \p buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if the input is invalid.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the import for the
 *                  given group is not implemented.
 */
int mbedtls_ecp_point_read_binary(const mbedtls_ecp_group *grp,
                                  mbedtls_ecp_point *P,
                                  const unsigned char *buf, size_t ilen);

/**
 * \brief           This function imports a point from a TLS ECPoint record.
 *
 * \note            On function return, \p *buf is updated to point immediately
 *                  after the ECPoint record.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param pt        The destination point.
 * \param buf       The address of the pointer to the start of the input buffer.
 * \param len       The length of the buffer.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_MPI_XXX error code on initialization
 *                  failure.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if input is invalid.
 */
int mbedtls_ecp_tls_read_point(const mbedtls_ecp_group *grp,
                               mbedtls_ecp_point *pt,
                               const unsigned char **buf, size_t len);

/**
 * \brief           This function exports a point as a TLS ECPoint record
 *                  defined in RFC 4492, Section 5.4.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param pt        The point to be exported. This must be initialized.
 * \param format    The point format to use. This must be either
 *                  #MBEDTLS_ECP_PF_COMPRESSED or #MBEDTLS_ECP_PF_UNCOMPRESSED.
 * \param olen      The address at which to store the length in Bytes
 *                  of the data written.
 * \param buf       The target buffer. This must be a writable buffer of
 *                  length \p blen Bytes.
 * \param blen      The length of the target buffer \p buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if the input is invalid.
 * \return          #MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL if the target buffer
 *                  is too small to hold the exported point.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_tls_write_point(const mbedtls_ecp_group *grp,
                                const mbedtls_ecp_point *pt,
                                int format, size_t *olen,
                                unsigned char *buf, size_t blen);

/**
 * \brief           This function sets up an ECP group context
 *                  from a standardized set of domain parameters.
 *
 * \note            The index should be a value of the NamedCurve enum,
 *                  as defined in <em>RFC-4492: Elliptic Curve Cryptography
 *                  (ECC) Cipher Suites for Transport Layer Security (TLS)</em>,
 *                  usually in the form of an \c MBEDTLS_ECP_DP_XXX macro.
 *
 * \param grp       The group context to setup. This must be initialized.
 * \param id        The identifier of the domain parameter set to load.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if \p id doesn't
 *                  correspond to a known group.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_group_load(mbedtls_ecp_group *grp, mbedtls_ecp_group_id id);

/**
 * \brief           This function sets up an ECP group context from a TLS
 *                  ECParameters record as defined in RFC 4492, Section 5.4.
 *
 * \note            The read pointer \p buf is updated to point right after
 *                  the ECParameters record on exit.
 *
 * \param grp       The group context to setup. This must be initialized.
 * \param buf       The address of the pointer to the start of the input buffer.
 * \param len       The length of the input buffer \c *buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if input is invalid.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the group is not
 *                  recognized.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_tls_read_group(mbedtls_ecp_group *grp,
                               const unsigned char **buf, size_t len);

/**
 * \brief           This function extracts an elliptic curve group ID from a
 *                  TLS ECParameters record as defined in RFC 4492, Section 5.4.
 *
 * \note            The read pointer \p buf is updated to point right after
 *                  the ECParameters record on exit.
 *
 * \param grp       The address at which to store the group id.
 *                  This must not be \c NULL.
 * \param buf       The address of the pointer to the start of the input buffer.
 * \param len       The length of the input buffer \c *buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if input is invalid.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the group is not
 *                  recognized.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_tls_read_group_id(mbedtls_ecp_group_id *grp,
                                  const unsigned char **buf,
                                  size_t len);
/**
 * \brief           This function exports an elliptic curve as a TLS
 *                  ECParameters record as defined in RFC 4492, Section 5.4.
 *
 * \param grp       The ECP group to be exported.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param olen      The address at which to store the number of Bytes written.
 *                  This must not be \c NULL.
 * \param buf       The buffer to write to. This must be a writable buffer
 *                  of length \p blen Bytes.
 * \param blen      The length of the output buffer \p buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL if the output
 *                  buffer is too small to hold the exported group.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_tls_write_group(const mbedtls_ecp_group *grp,
                                size_t *olen,
                                unsigned char *buf, size_t blen);

/**
 * \brief           This function performs a scalar multiplication of a point
 *                  by an integer: \p R = \p m * \p P.
 *
 *                  It is not thread-safe to use same group in multiple threads.
 *
 * \note            To prevent timing attacks, this function
 *                  executes the exact same sequence of base-field
 *                  operations for any valid \p m. It avoids any if-branch or
 *                  array index depending on the value of \p m. It also uses
 *                  \p f_rng to randomize some intermediate results.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param R         The point in which to store the result of the calculation.
 *                  This must be initialized.
 * \param m         The integer by which to multiply. This must be initialized.
 * \param P         The point to multiply. This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may be \c
 *                  NULL if \p f_rng doesn't need a context.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if \p m is not a valid private
 *                  key, or \p P is not a valid public key.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_mul(mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
                    const mbedtls_mpi *m, const mbedtls_ecp_point *P,
                    int (*f_rng)(void *, unsigned char *, size_t), void *p_rng);

/**
 * \brief           This function performs multiplication of a point by
 *                  an integer: \p R = \p m * \p P in a restartable way.
 *
 * \see             mbedtls_ecp_mul()
 *
 * \note            This function does the same as \c mbedtls_ecp_mul(), but
 *                  it can return early and restart according to the limit set
 *                  with \c mbedtls_ecp_set_max_ops() to reduce blocking.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param R         The point in which to store the result of the calculation.
 *                  This must be initialized.
 * \param m         The integer by which to multiply. This must be initialized.
 * \param P         The point to multiply. This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may be \c
 *                  NULL if \p f_rng doesn't need a context.
 * \param rs_ctx    The restart context (NULL disables restart).
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if \p m is not a valid private
 *                  key, or \p P is not a valid public key.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_IN_PROGRESS if maximum number of
 *                  operations was reached: see \c mbedtls_ecp_set_max_ops().
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_mul_restartable(mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
                                const mbedtls_mpi *m, const mbedtls_ecp_point *P,
                                int (*f_rng)(void *, unsigned char *, size_t), void *p_rng,
                                mbedtls_ecp_restart_ctx *rs_ctx);


/**
 * \brief           This function checks if domain parameter A of the curve is
 *                  \c -3.
 *
 * \note            This function is only defined for short Weierstrass curves.
 *                  It may not be included in builds without any short
 *                  Weierstrass curve.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 *
 * \return          \c 1 if <code>A = -3</code>.
 * \return          \c 0 Otherwise.
 */
static __inline int mbedtls_ecp_group_a_is_minus_3(const mbedtls_ecp_group *grp)
{
    return grp->A. p == 0;
}

/**
 * \brief           This function performs multiplication and addition of two
 *                  points by integers: \p R = \p m * \p P + \p n * \p Q
 *
 *                  It is not thread-safe to use same group in multiple threads.
 *
 * \note            In contrast to mbedtls_ecp_mul(), this function does not
 *                  guarantee a constant execution flow and timing.
 *
 * \note            This function is only defined for short Weierstrass curves.
 *                  It may not be included in builds without any short
 *                  Weierstrass curve.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param R         The point in which to store the result of the calculation.
 *                  This must be initialized.
 * \param m         The integer by which to multiply \p P.
 *                  This must be initialized.
 * \param P         The point to multiply by \p m. This must be initialized.
 * \param n         The integer by which to multiply \p Q.
 *                  This must be initialized.
 * \param Q         The point to be multiplied by \p n.
 *                  This must be initialized.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if \p m or \p n are not
 *                  valid private keys, or \p P or \p Q are not valid public
 *                  keys.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if \p grp does not
 *                  designate a short Weierstrass curve.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_muladd(mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
                       const mbedtls_mpi *m, const mbedtls_ecp_point *P,
                       const mbedtls_mpi *n, const mbedtls_ecp_point *Q);

/**
 * \brief           This function performs multiplication and addition of two
 *                  points by integers: \p R = \p m * \p P + \p n * \p Q in a
 *                  restartable way.
 *
 * \see             \c mbedtls_ecp_muladd()
 *
 * \note            This function works the same as \c mbedtls_ecp_muladd(),
 *                  but it can return early and restart according to the limit
 *                  set with \c mbedtls_ecp_set_max_ops() to reduce blocking.
 *
 * \note            This function is only defined for short Weierstrass curves.
 *                  It may not be included in builds without any short
 *                  Weierstrass curve.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param R         The point in which to store the result of the calculation.
 *                  This must be initialized.
 * \param m         The integer by which to multiply \p P.
 *                  This must be initialized.
 * \param P         The point to multiply by \p m. This must be initialized.
 * \param n         The integer by which to multiply \p Q.
 *                  This must be initialized.
 * \param Q         The point to be multiplied by \p n.
 *                  This must be initialized.
 * \param rs_ctx    The restart context (NULL disables restart).
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if \p m or \p n are not
 *                  valid private keys, or \p P or \p Q are not valid public
 *                  keys.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if \p grp does not
 *                  designate a short Weierstrass curve.
 * \return          #MBEDTLS_ERR_ECP_IN_PROGRESS if maximum number of
 *                  operations was reached: see \c mbedtls_ecp_set_max_ops().
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_muladd_restartable(
    mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
    const mbedtls_mpi *m, const mbedtls_ecp_point *P,
    const mbedtls_mpi *n, const mbedtls_ecp_point *Q,
    mbedtls_ecp_restart_ctx *rs_ctx);


/**
 * \brief           This function checks that a point is a valid public key
 *                  on this curve.
 *
 *                  It only checks that the point is non-zero, has
 *                  valid coordinates and lies on the curve. It does not verify
 *                  that it is indeed a multiple of \c G. This additional
 *                  check is computationally more expensive, is not required
 *                  by standards, and should not be necessary if the group
 *                  used has a small cofactor. In particular, it is useless for
 *                  the NIST groups which all have a cofactor of 1.
 *
 * \note            This function uses bare components rather than an
 *                  ::mbedtls_ecp_keypair structure, to ease use with other
 *                  structures, such as ::mbedtls_ecdh_context or
 *                  ::mbedtls_ecdsa_context.
 *
 * \param grp       The ECP group the point should belong to.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param pt        The point to check. This must be initialized.
 *
 * \return          \c 0 if the point is a valid public key.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if the point is not
 *                  a valid public key for the given curve.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_check_pubkey(const mbedtls_ecp_group *grp,
                             const mbedtls_ecp_point *pt);

/**
 * \brief           This function checks that an \c mbedtls_mpi is a
 *                  valid private key for this curve.
 *
 * \note            This function uses bare components rather than an
 *                  ::mbedtls_ecp_keypair structure to ease use with other
 *                  structures, such as ::mbedtls_ecdh_context or
 *                  ::mbedtls_ecdsa_context.
 *
 * \param grp       The ECP group the private key should belong to.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param d         The integer to check. This must be initialized.
 *
 * \return          \c 0 if the point is a valid private key.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if the point is not a valid
 *                  private key for the given curve.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_check_privkey(const mbedtls_ecp_group *grp,
                              const mbedtls_mpi *d);

/**
 * \brief           This function generates a private key.
 *
 * \param grp       The ECP group to generate a private key for.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param d         The destination MPI (secret part). This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG parameter to be passed to \p f_rng. This may be
 *                  \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_MPI_XXX error code
 *                  on failure.
 */
int mbedtls_ecp_gen_privkey(const mbedtls_ecp_group *grp,
                            mbedtls_mpi *d,
                            int (*f_rng)(void *, unsigned char *, size_t),
                            void *p_rng);

/**
 * \brief           This function generates a keypair with a configurable base
 *                  point.
 *
 * \note            This function uses bare components rather than an
 *                  ::mbedtls_ecp_keypair structure to ease use with other
 *                  structures, such as ::mbedtls_ecdh_context or
 *                  ::mbedtls_ecdsa_context.
 *
 * \param grp       The ECP group to generate a key pair for.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param G         The base point to use. This must be initialized
 *                  and belong to \p grp. It replaces the default base
 *                  point \c grp->G used by mbedtls_ecp_gen_keypair().
 * \param d         The destination MPI (secret part).
 *                  This must be initialized.
 * \param Q         The destination point (public part).
 *                  This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may
 *                  be \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_MPI_XXX error code
 *                  on failure.
 */
int mbedtls_ecp_gen_keypair_base(mbedtls_ecp_group *grp,
                                 const mbedtls_ecp_point *G,
                                 mbedtls_mpi *d, mbedtls_ecp_point *Q,
                                 int (*f_rng)(void *, unsigned char *, size_t),
                                 void *p_rng);

/**
 * \brief           This function generates an ECP keypair.
 *
 * \note            This function uses bare components rather than an
 *                  ::mbedtls_ecp_keypair structure to ease use with other
 *                  structures, such as ::mbedtls_ecdh_context or
 *                  ::mbedtls_ecdsa_context.
 *
 * \param grp       The ECP group to generate a key pair for.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param d         The destination MPI (secret part).
 *                  This must be initialized.
 * \param Q         The destination point (public part).
 *                  This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may
 *                  be \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_MPI_XXX error code
 *                  on failure.
 */
int mbedtls_ecp_gen_keypair(mbedtls_ecp_group *grp, mbedtls_mpi *d,
                            mbedtls_ecp_point *Q,
                            int (*f_rng)(void *, unsigned char *, size_t),
                            void *p_rng);

/**
 * \brief           This function generates an ECP key.
 *
 * \param grp_id    The ECP group identifier.
 * \param key       The destination key. This must be initialized.
 * \param f_rng     The RNG function to use. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may
 *                  be \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_MPI_XXX error code
 *                  on failure.
 */
int mbedtls_ecp_gen_key(mbedtls_ecp_group_id grp_id, mbedtls_ecp_keypair *key,
                        int (*f_rng)(void *, unsigned char *, size_t),
                        void *p_rng);

/** \brief          Set the public key in a key pair object.
 *
 * \note            This function does not check that the point actually
 *                  belongs to the given group. Call mbedtls_ecp_check_pubkey()
 *                  on \p Q before calling this function to check that.
 *
 * \note            This function does not check that the public key matches
 *                  the private key that is already in \p key, if any.
 *                  To check the consistency of the resulting key pair object,
 *                  call mbedtls_ecp_check_pub_priv() after setting both
 *                  the public key and the private key.
 *
 * \param grp_id    The ECP group identifier.
 * \param key       The key pair object. It must be initialized.
 *                  If its group has already been set, it must match \p grp_id.
 *                  If its group has not been set, it will be set to \p grp_id.
 *                  If the public key has already been set, it is overwritten.
 * \param Q         The public key to copy. This must be a point on the
 *                  curve indicated by \p grp_id.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if \p key does not
 *                  match \p grp_id.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the operation for
 *                  the group is not implemented.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_set_public_key(mbedtls_ecp_group_id grp_id,
                               mbedtls_ecp_keypair *key,
                               const mbedtls_ecp_point *Q);

/**
 * \brief           This function reads an elliptic curve private key.
 *
 * \note            This function does not set the public key in the
 *                  key pair object. Without a public key, the key pair object
 *                  cannot be used with operations that require the public key.
 *                  Call mbedtls_ecp_keypair_calc_public() to set the public
 *                  key from the private key. Alternatively, you can call
 *                  mbedtls_ecp_set_public_key() to set the public key part,
 *                  and then optionally mbedtls_ecp_check_pub_priv() to check
 *                  that the private and public parts are consistent.
 *
 * \note            If a public key has already been set in the key pair
 *                  object, this function does not check that it is consistent
 *                  with the private key. Call mbedtls_ecp_check_pub_priv()
 *                  after setting both the public key and the private key
 *                  to make that check.
 *
 * \param grp_id    The ECP group identifier.
 * \param key       The destination key.
 * \param buf       The buffer containing the binary representation of the
 *                  key. (Big endian integer for Weierstrass curves, byte
 *                  string for Montgomery curves.)
 * \param buflen    The length of the buffer in bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY error if the key is
 *                  invalid.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the operation for
 *                  the group is not implemented.
 * \return          Another negative error code on different kinds of failure.
 */
int mbedtls_ecp_read_key(mbedtls_ecp_group_id grp_id, mbedtls_ecp_keypair *key,
                         const unsigned char *buf, size_t buflen);


/**
 * \brief           This function exports an elliptic curve private key.
 *
 * \deprecated      Note that although this function accepts an output
 *                  buffer that is smaller or larger than the key, most key
 *                  import interfaces require the output to have exactly
 *                  key's nominal length. It is generally simplest to
 *                  pass the key's nominal length as \c buflen, after
 *                  checking that the output buffer is large enough.
 *                  See the description of the \p buflen parameter for
 *                  how to calculate the nominal length.
 *                  To avoid this difficulty, use mbedtls_ecp_write_key_ext()
 *                  instead.
 *                  mbedtls_ecp_write_key() is deprecated and will be
 *                  removed in a future version of the library.
 *
 * \note            If the private key was not set in \p key,
 *                  the output is unspecified. Future versions
 *                  may return an error in that case.
 *
 * \param key       The private key.
 * \param buf       The output buffer for containing the binary representation
 *                  of the key.
 *                  For Weierstrass curves, this is the big-endian
 *                  representation, padded with null bytes at the beginning
 *                  to reach \p buflen bytes.
 *                  For Montgomery curves, this is the standard byte string
 *                  representation (which is little-endian), padded with
 *                  null bytes at the end to reach \p buflen bytes.
 * \param buflen    The total length of the buffer in bytes.
 *                  The length of the output is
 *                  (`grp->nbits` + 7) / 8 bytes
 *                  where `grp->nbits` is the private key size in bits.
 *                  For Weierstrass keys, if the output buffer is smaller,
 *                  leading zeros are trimmed to fit if possible. For
 *                  Montgomery keys, the output buffer must always be large
 *                  enough for the nominal length.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL or
 *                  #MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL if the \p key
 *                  representation is larger than the available space in \p buf.
 * \return          Another negative error code on different kinds of failure.
 */
int  mbedtls_ecp_write_key(mbedtls_ecp_keypair *key,
                                             unsigned char *buf, size_t buflen);


/**
 * \brief           This function exports an elliptic curve private key.
 *
 * \param key       The private key.
 * \param olen      On success, the length of the private key.
 *                  This is always (`grp->nbits` + 7) / 8 bytes
 *                  where `grp->nbits` is the private key size in bits.
 * \param buf       The output buffer for containing the binary representation
 *                  of the key.
 * \param buflen    The total length of the buffer in bytes.
 *                  #MBEDTLS_ECP_MAX_BYTES is always sufficient.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL if the \p key
 *                  representation is larger than the available space in \p buf.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if no private key is
 *                  set in \p key.
 * \return          Another negative error code on different kinds of failure.
 */
int mbedtls_ecp_write_key_ext(const mbedtls_ecp_keypair *key,
                              size_t *olen, unsigned char *buf, size_t buflen);

/**
 * \brief           This function exports an elliptic curve public key.
 *
 * \note            If the public key was not set in \p key,
 *                  the output is unspecified. Future versions
 *                  may return an error in that case.
 *
 * \param key       The public key.
 * \param format    The point format. This must be either
 *                  #MBEDTLS_ECP_PF_COMPRESSED or #MBEDTLS_ECP_PF_UNCOMPRESSED.
 *                  (For groups without these formats, this parameter is
 *                  ignored. But it still has to be either of the above
 *                  values.)
 * \param olen      The address at which to store the length of
 *                  the output in Bytes. This must not be \c NULL.
 * \param buf       The output buffer. This must be a writable buffer
 *                  of length \p buflen Bytes.
 * \param buflen    The length of the output buffer \p buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL if the output buffer
 *                  is too small to hold the point.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the point format
 *                  or the export for the given group is not implemented.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_write_public_key(const mbedtls_ecp_keypair *key,
                                 int format, size_t *olen,
                                 unsigned char *buf, size_t buflen);

/**
 * \brief           This function checks that the keypair objects
 *                  \p pub and \p prv have the same group and the
 *                  same public point, and that the private key in
 *                  \p prv is consistent with the public key.
 *
 * \param pub       The keypair structure holding the public key. This
 *                  must be initialized. If it contains a private key, that
 *                  part is ignored.
 * \param prv       The keypair structure holding the full keypair.
 *                  This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may be \c
 *                  NULL if \p f_rng doesn't need a context.
 *
 * \return          \c 0 on success, meaning that the keys are valid and match.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if the keys are invalid or do not match.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or an \c MBEDTLS_ERR_MPI_XXX
 *                  error code on calculation failure.
 */
int mbedtls_ecp_check_pub_priv(
    const mbedtls_ecp_keypair *pub, const mbedtls_ecp_keypair *prv,
    int (*f_rng)(void *, unsigned char *, size_t), void *p_rng);

/** \brief          Calculate the public key from a private key in a key pair.
 *
 * \param key       A keypair structure. It must have a private key set.
 *                  If the public key is set, it will be overwritten.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may be \c
 *                  NULL if \p f_rng doesn't need a context.
 *
 * \return          \c 0 on success. The key pair object can be used for
 *                  operations that require the public key.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or an \c MBEDTLS_ERR_MPI_XXX
 *                  error code on calculation failure.
 */
int mbedtls_ecp_keypair_calc_public(
    mbedtls_ecp_keypair *key,
    int (*f_rng)(void *, unsigned char *, size_t), void *p_rng);

/** \brief          Query the group that a key pair belongs to.
 *
 * \param key       The key pair to query.
 *
 * \return          The group ID for the group registered in the key pair
 *                  object.
 *                  This is \c MBEDTLS_ECP_DP_NONE if no group has been set
 *                  in the key pair object.
 */
mbedtls_ecp_group_id mbedtls_ecp_keypair_get_group_id(
    const mbedtls_ecp_keypair *key);

/**
 * \brief           This function exports generic key-pair parameters.
 *
 *                  Each of the output parameters can be a null pointer
 *                  if you do not need that parameter.
 *
 * \note            If the private key or the public key was not set in \p key,
 *                  the corresponding output is unspecified. Future versions
 *                  may return an error in that case.
 *
 * \param key       The key pair to export from.
 * \param grp       Slot for exported ECP group.
 *                  It must either be null or point to an initialized ECP group.
 * \param d         Slot for the exported secret value.
 *                  It must either be null or point to an initialized mpi.
 * \param Q         Slot for the exported public value.
 *                  It must either be null or point to an initialized ECP point.
 *
 * \return          \c 0 on success,
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if key id doesn't
 *                  correspond to a known group.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_export(const mbedtls_ecp_keypair *key, mbedtls_ecp_group *grp,
                       mbedtls_mpi *d, mbedtls_ecp_point *Q);

# 1535 "L:/PLT/pcac/mbedTLS/include/mbedtls/ecp.h"





# 66 "L:/PLT/pcac/mbedTLS/library/ecp.c"
# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/threading.h"
/**
 * \file threading.h
 *
 * \brief Threading abstraction layer
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */
# 13 "L:/PLT/pcac/mbedTLS/include/mbedtls/threading.h"

# 15 "L:/PLT/pcac/mbedTLS/include/mbedtls/threading.h"

# 17 "L:/PLT/pcac/mbedTLS/include/mbedtls/threading.h"





/** Bad input parameters to function. */

/** Locking / unlocking / free failed with error code. */


# 41 "L:/PLT/pcac/mbedTLS/include/mbedtls/threading.h"

# 74 "L:/PLT/pcac/mbedTLS/include/mbedtls/threading.h"

# 132 "L:/PLT/pcac/mbedTLS/include/mbedtls/threading.h"





# 67 "L:/PLT/pcac/mbedTLS/library/ecp.c"
# 68 "L:/PLT/pcac/mbedTLS/library/ecp.c"
# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/error.h"
/**
 * \file error.h
 *
 * \brief Error to string translation
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */



# 14 "L:/PLT/pcac/mbedTLS/include/mbedtls/error.h"

# 16 "L:/PLT/pcac/mbedTLS/include/mbedtls/error.h"

/**
 * Error code layout.
 *
 * Currently we try to keep all error codes within the negative space of 16
 * bits signed integers to support all platforms (-0x0001 - -0x7FFF). In
 * addition we'd like to give two layers of information on the error if
 * possible.
 *
 * For that purpose the error codes are segmented in the following manner:
 *
 * 16 bit error code bit-segmentation
 *
 * 1 bit  - Unused (sign bit)
 * 3 bits - High level module ID
 * 5 bits - Module-dependent error code
 * 7 bits - Low level module errors
 *
 * For historical reasons, low-level error codes are divided in even and odd,
 * even codes were assigned first, and -1 is reserved for other errors.
 *
 * Low-level module errors (0x0002-0x007E, 0x0001-0x007F)
 *
 * Module   Nr  Codes assigned
 * ERROR     2  0x006E          0x0001
 * MPI       7  0x0002-0x0010
 * GCM       3  0x0012-0x0016   0x0013-0x0013
 * THREADING 3  0x001A-0x001E
 * AES       5  0x0020-0x0022   0x0021-0x0025
 * CAMELLIA  3  0x0024-0x0026   0x0027-0x0027
 * BASE64    2  0x002A-0x002C
 * OID       1  0x002E-0x002E   0x000B-0x000B
 * PADLOCK   1  0x0030-0x0030
 * DES       2  0x0032-0x0032   0x0033-0x0033
 * CTR_DBRG  4  0x0034-0x003A
 * ENTROPY   3  0x003C-0x0040   0x003D-0x003F
 * NET      13  0x0042-0x0052   0x0043-0x0049
 * ARIA      4  0x0058-0x005E
 * ASN1      7  0x0060-0x006C
 * CMAC      1  0x007A-0x007A
 * PBKDF2    1  0x007C-0x007C
 * HMAC_DRBG 4                  0x0003-0x0009
 * CCM       3                  0x000D-0x0011
 * MD5       1                  0x002F-0x002F
 * RIPEMD160 1                  0x0031-0x0031
 * SHA1      1                  0x0035-0x0035 0x0073-0x0073
 * SHA256    1                  0x0037-0x0037 0x0074-0x0074
 * SHA512    1                  0x0039-0x0039 0x0075-0x0075
 * SHA-3     1                  0x0076-0x0076
 * CHACHA20  3                  0x0051-0x0055
 * POLY1305  3                  0x0057-0x005B
 * CHACHAPOLY 2 0x0054-0x0056
 * PLATFORM  2  0x0070-0x0072
 * LMS       5  0x0011-0x0019
 *
 * High-level module nr (3 bits - 0x0...-0x7...)
 * Name      ID  Nr of Errors
 * PEM       1   9
 * PKCS#12   1   4 (Started from top)
 * X509      2   20
 * PKCS5     2   4 (Started from top)
 * DHM       3   11
 * PK        3   15 (Started from top)
 * RSA       4   11
 * ECP       4   10 (Started from top)
 * MD        5   5
 * HKDF      5   1 (Started from top)
 * PKCS7     5   12 (Started from 0x5300)
 * SSL       5   2 (Started from 0x5F00)
 * CIPHER    6   8 (Started from 0x6080)
 * SSL       6   22 (Started from top, plus 0x6000)
 * SSL       7   20 (Started from 0x7000, gaps at
 *                   0x7380, 0x7900-0x7980, 0x7A80-0x7E80)
 *
 * Module dependent error code (5 bits 0x.00.-0x.F8.)
 */





/** Generic error */

/** This is a bug in the library */


/** Hardware accelerator failed */

/** The requested feature is not supported by the platform */


/**
 * \brief Combines a high-level and low-level error code together.
 *
 *        Wrapper macro for mbedtls_error_add(). See that function for
 *        more details.
 */



# 123 "L:/PLT/pcac/mbedTLS/include/mbedtls/error.h"

/**
 * \brief Combines a high-level and low-level error code together.
 *
 *        This function can be called directly however it is usually
 *        called via the #MBEDTLS_ERROR_ADD macro.
 *
 *        While a value of zero is not a negative error code, it is still an
 *        error code (that denotes success) and can be combined with both a
 *        negative error code or another value of zero.
 *
 * \note  When invasive testing is enabled via #MBEDTLS_TEST_HOOKS, also try to
 *        call \link mbedtls_test_hook_error_add \endlink.
 *
 * \param high      high-level error code. See error.h for more details.
 * \param low       low-level error code. See error.h for more details.
 * \param file      file where this error code addition occurred.
 * \param line      line where this error code addition occurred.
 */
static __inline int mbedtls_error_add(int high, int low,
                                    const char *file, int line)
{





    (void) file;
    (void) line;

    return high + low;
}

/**
 * \brief Translate an Mbed TLS error code into a string representation.
 *        The result is truncated if necessary and always includes a
 *        terminating null byte.
 *
 * \param errnum    error code
 * \param buffer    buffer to place representation in
 * \param buflen    length of the buffer
 */
void mbedtls_strerror(int errnum, char *buffer, size_t buflen);

/**
 * \brief Translate the high-level part of an Mbed TLS error code into a string
 *        representation.
 *
 * This function returns a const pointer to an un-modifiable string. The caller
 * must not try to modify the string. It is intended to be used mostly for
 * logging purposes.
 *
 * \param error_code    error code
 *
 * \return The string representation of the error code, or \c NULL if the error
 *         code is unknown.
 */
const char *mbedtls_high_level_strerr(int error_code);

/**
 * \brief Translate the low-level part of an Mbed TLS error code into a string
 *        representation.
 *
 * This function returns a const pointer to an un-modifiable string. The caller
 * must not try to modify the string. It is intended to be used mostly for
 * logging purposes.
 *
 * \param error_code    error code
 *
 * \return The string representation of the error code, or \c NULL if the error
 *         code is unknown.
 */
const char *mbedtls_low_level_strerr(int error_code);





# 69 "L:/PLT/pcac/mbedTLS/library/ecp.c"

# 1 "L:/PLT/pcac/mbedTLS/library/bn_mul.h"
/**
 * \file bn_mul.h
 *
 * \brief Multi-precision integer library
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */
/*
 *      Multiply source vector [s] with b, add result
 *       to destination vector [d] and set carry c.
 *
 *      Currently supports:
 *
 *         . IA-32 (386+)         . AMD64 / EM64T
 *         . IA-32 (SSE2)         . Motorola 68000
 *         . PowerPC, 32-bit      . MicroBlaze
 *         . PowerPC, 64-bit      . TriCore
 *         . SPARC v8             . ARM v3+
 *         . Alpha                . MIPS32
 *         . C, longlong          . C, generic
 */



# 28 "L:/PLT/pcac/mbedTLS/library/bn_mul.h"

# 30 "L:/PLT/pcac/mbedTLS/library/bn_mul.h"


/*
 * Conversion macros for embedded constants:
 * build lists of mbedtls_mpi_uint's from lists of unsigned char's grouped by 8, 4 or 2
 */















# 70 "L:/PLT/pcac/mbedTLS/library/bn_mul.h"

/* *INDENT-OFF* */


/* armcc5 --gnu defines __GNUC__ but doesn't support GNU's extended asm */
# 923 "L:/PLT/pcac/mbedTLS/library/bn_mul.h"

# 1024 "L:/PLT/pcac/mbedTLS/library/bn_mul.h"











# 1042 "L:/PLT/pcac/mbedTLS/library/bn_mul.h"




# 1074 "L:/PLT/pcac/mbedTLS/library/bn_mul.h"



















/* *INDENT-ON* */
# 71 "L:/PLT/pcac/mbedTLS/library/ecp.c"
# 1 "L:/PLT/pcac/mbedTLS/library/ecp_invasive.h"
/**
 * \file ecp_invasive.h
 *
 * \brief ECP module: interfaces for invasive testing only.
 *
 * The interfaces in this file are intended for testing purposes only.
 * They SHOULD NOT be made available in library integrations except when
 * building the library for testing.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */



# 18 "L:/PLT/pcac/mbedTLS/library/ecp_invasive.h"
# 19 "L:/PLT/pcac/mbedTLS/library/ecp_invasive.h"
# 1 "L:/PLT/pcac/mbedTLS/library/bignum_mod.h"
/**
 *  Modular bignum functions
 *
 * This module implements operations on integers modulo some fixed modulus.
 *
 * The functions in this module obey the following conventions unless
 * explicitly indicated otherwise:
 *
 * - **Modulus parameters**: the modulus is passed as a pointer to a structure
 *   of type #mbedtls_mpi_mod_modulus. The structure must be set up with an
 *   array of limbs storing the bignum value of the modulus. The modulus must
 *   be odd and is assumed to have no leading zeroes. The modulus is usually
 *   named \c N and is usually input-only. Functions which take a parameter
 *   of type \c const #mbedtls_mpi_mod_modulus* must not modify its value.
 * - **Bignum parameters**: Bignums are passed as pointers to an array of
 *   limbs or to a #mbedtls_mpi_mod_residue structure. A limb has the type
 *   #mbedtls_mpi_uint. Residues must be initialized before use, and must be
 *   associated with the modulus \c N. Unless otherwise specified:
 *     - Bignum parameters called \c A, \c B, ... are inputs and are not
 *       modified by the function. Functions which take a parameter of
 *       type \c const #mbedtls_mpi_mod_residue* must not modify its value.
 *     - Bignum parameters called \c X, \c Y, ... are outputs or input-output.
 *       The initial bignum value of output-only parameters is ignored, but
 *       they must be set up and associated with the modulus \c N. Some
 *       functions (typically constant-flow) require that the limbs in an
 *       output residue are initialized.
 *     - Bignum parameters called \c p are inputs used to set up a modulus or
 *       residue. These must be pointers to an array of limbs.
 *     - \c T is a temporary storage area. The initial content of such a
 *       parameter is ignored and the final content is unspecified.
 *     - Some functions use different names, such as \c r for the residue.
 * - **Bignum sizes**: bignum sizes are always expressed in limbs. Both
 *   #mbedtls_mpi_mod_modulus and #mbedtls_mpi_mod_residue have a \c limbs
 *   member storing its size. All bignum parameters must have the same
 *   number of limbs as the modulus. All bignum sizes must be at least 1 and
 *   must be significantly less than #SIZE_MAX. The behavior if a size is 0 is
 *   undefined.
 * - **Bignum representation**: the representation of inputs and outputs is
 *   specified by the \c int_rep field of the modulus.
 * - **Parameter ordering**: for bignum parameters, outputs come before inputs.
 *   The modulus is passed after residues. Temporaries come last.
 * - **Aliasing**: in general, output bignums may be aliased to one or more
 *   inputs. Modulus values may not be aliased to any other parameter. Outputs
 *   may not be aliased to one another. Temporaries may not be aliased to any
 *   other parameter.
 * - **Overlap**: apart from aliasing of residue pointers (where two residue
 *   arguments are equal pointers), overlap is not supported and may result
 *   in undefined behavior.
 * - **Error handling**: functions generally check compatibility of input
 *   sizes. Most functions will not check that input values are in canonical
 *   form (i.e. that \c A < \c N), this is only checked during setup of a
 *   residue structure.
 * - **Modular representatives**: all functions expect inputs to be in the
 *   range [0, \c N - 1] and guarantee outputs in the range [0, \c N - 1].
 *   Residues are set up with an associated modulus, and operations are only
 *   guaranteed to work if the modulus is associated with all residue
 *   parameters. If a residue is passed with a modulus other than the one it
 *   is associated with, then it may be out of range. If an input is out of
 *   range, outputs are fully unspecified, though bignum values out of range
 *   should not cause buffer overflows (beware that this is not extensively
 *   tested).
 */

/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */




# 73 "L:/PLT/pcac/mbedTLS/library/bignum_mod.h"

# 76 "L:/PLT/pcac/mbedTLS/library/bignum_mod.h"


/** How residues associated with a modulus are represented.
 *
 * This also determines which fields of the modulus structure are valid and
 * what their contents are (see #mbedtls_mpi_mod_modulus).
 */
typedef enum {
    /** Representation not chosen (makes the modulus structure invalid). */
    MBEDTLS_MPI_MOD_REP_INVALID    = 0,
    /* Skip 1 as it is slightly easier to accidentally pass to functions. */
    /** Montgomery representation. */
    MBEDTLS_MPI_MOD_REP_MONTGOMERY = 2,
    /* Optimised reduction available. This indicates a coordinate modulus (P)
     * and one or more of the following have been configured:
     * - A nist curve (MBEDTLS_ECP_DP_SECPXXXR1_ENABLED) & MBEDTLS_ECP_NIST_OPTIM.
     * - A Kobliz Curve.
     * - A Fast Reduction Curve CURVE25519 or CURVE448. */
    MBEDTLS_MPI_MOD_REP_OPT_RED,
} mbedtls_mpi_mod_rep_selector;

/* Make mbedtls_mpi_mod_rep_selector and mbedtls_mpi_mod_ext_rep disjoint to
 * make it easier to catch when they are accidentally swapped. */
typedef enum {
    MBEDTLS_MPI_MOD_EXT_REP_INVALID = 0,
    MBEDTLS_MPI_MOD_EXT_REP_LE      = 8,
    MBEDTLS_MPI_MOD_EXT_REP_BE
} mbedtls_mpi_mod_ext_rep;

typedef struct {
    mbedtls_mpi_uint *p;
    size_t limbs;
} mbedtls_mpi_mod_residue;

typedef struct {
    mbedtls_mpi_uint const *rr;  /* The residue for 2^{2*n*biL} mod N */
    mbedtls_mpi_uint mm;         /* Montgomery const for -N^{-1} mod 2^{ciL} */
} mbedtls_mpi_mont_struct;

typedef int (*mbedtls_mpi_modp_fn)(mbedtls_mpi_uint *X, size_t X_limbs);

typedef struct {
    mbedtls_mpi_modp_fn modp;    /* The optimised reduction function pointer */
} mbedtls_mpi_opt_red_struct;

typedef struct {
    const mbedtls_mpi_uint *p;
    size_t limbs;                            // number of limbs
    size_t bits;                             // bitlen of p
    mbedtls_mpi_mod_rep_selector int_rep;    // selector to signal the active member of the union
    union rep {
        /* if int_rep == #MBEDTLS_MPI_MOD_REP_MONTGOMERY */
        mbedtls_mpi_mont_struct mont;
        /* if int_rep == #MBEDTLS_MPI_MOD_REP_OPT_RED */
        mbedtls_mpi_opt_red_struct ored;
    } rep;
} mbedtls_mpi_mod_modulus;

/** Setup a residue structure.
 *
 * The residue will be set up with the buffer \p p and modulus \p N.
 *
 * The memory pointed to by \p p will be used by the resulting residue structure.
 * The value at the pointed-to memory will be the initial value of \p r and must
 * hold a value that is less than the modulus. This value will be used as-is
 * and interpreted according to the value of the `N->int_rep` field.
 *
 * The modulus \p N will be the modulus associated with \p r. The residue \p r
 * should only be used in operations where the modulus is \p N.
 *
 * \param[out] r    The address of the residue to setup.
 * \param[in] N     The address of the modulus related to \p r.
 * \param[in] p     The address of the limb array containing the value of \p r.
 *                  The memory pointed to by \p p will be used by \p r and must
 *                  not be modified in any way until after
 *                  mbedtls_mpi_mod_residue_release() is called. The data
 *                  pointed to by \p p must be less than the modulus (the value
 *                  pointed to by `N->p`) and already in the representation
 *                  indicated by `N->int_rep`.
 * \param p_limbs   The number of limbs of \p p. Must be the same as the number
 *                  of limbs in the modulus \p N.
 *
 * \return      \c 0 if successful.
 * \return      #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if \p p_limbs is less than the
 *              limbs in \p N or if \p p is not less than \p N.
 */
int mbedtls_mpi_mod_residue_setup(mbedtls_mpi_mod_residue *r,
                                  const mbedtls_mpi_mod_modulus *N,
                                  mbedtls_mpi_uint *p,
                                  size_t p_limbs);

/** Unbind elements of a residue structure.
 *
 * This function removes the reference to the limb array that was passed to
 * mbedtls_mpi_mod_residue_setup() to make it safe to free or use again.
 *
 * This function invalidates \p r and it must not be used until after
 * mbedtls_mpi_mod_residue_setup() is called on it again.
 *
 * \param[out] r     The address of residue to release.
 */
void mbedtls_mpi_mod_residue_release(mbedtls_mpi_mod_residue *r);

/** Initialize a modulus structure.
 *
 * \param[out] N     The address of the modulus structure to initialize.
 */
void mbedtls_mpi_mod_modulus_init(mbedtls_mpi_mod_modulus *N);

/** Setup a modulus structure.
 *
 * \param[out] N    The address of the modulus structure to populate.
 * \param[in] p     The address of the limb array storing the value of \p N.
 *                  The memory pointed to by \p p will be used by \p N and must
 *                  not be modified in any way until after
 *                  mbedtls_mpi_mod_modulus_free() is called.
 * \param p_limbs   The number of limbs of \p p.
 *
 * \return      \c 0 if successful.
 */
int mbedtls_mpi_mod_modulus_setup(mbedtls_mpi_mod_modulus *N,
                                  const mbedtls_mpi_uint *p,
                                  size_t p_limbs);

/** Setup an optimised-reduction compatible modulus structure.
 *
 * \param[out] N    The address of the modulus structure to populate.
 * \param[in] p     The address of the limb array storing the value of \p N.
 *                  The memory pointed to by \p p will be used by \p N and must
 *                  not be modified in any way until after
 *                  mbedtls_mpi_mod_modulus_free() is called.
 * \param p_limbs   The number of limbs of \p p.
 * \param modp      A pointer to the optimised reduction function to use. \p p.
 *
 * \return      \c 0 if successful.
 */
int mbedtls_mpi_mod_optred_modulus_setup(mbedtls_mpi_mod_modulus *N,
                                         const mbedtls_mpi_uint *p,
                                         size_t p_limbs,
                                         mbedtls_mpi_modp_fn modp);

/** Free elements of a modulus structure.
 *
 * This function frees any memory allocated by mbedtls_mpi_mod_modulus_setup().
 *
 * \warning This function does not free the limb array passed to
 *          mbedtls_mpi_mod_modulus_setup() only removes the reference to it,
 *          making it safe to free or to use it again.
 *
 * \param[in,out] N     The address of the modulus structure to free.
 */
void mbedtls_mpi_mod_modulus_free(mbedtls_mpi_mod_modulus *N);

/** \brief  Multiply two residues, returning the residue modulo the specified
 *          modulus.
 *
 * \note Currently handles the case when `N->int_rep` is
 * MBEDTLS_MPI_MOD_REP_MONTGOMERY.
 *
 * The size of the operation is determined by \p N. \p A, \p B and \p X must
 * all be associated with the modulus \p N and must all have the same number
 * of limbs as \p N.
 *
 * \p X may be aliased to \p A or \p B, or even both, but may not overlap
 * either otherwise. They may not alias \p N (since they must be in canonical
 * form, they cannot == \p N).
 *
 * \param[out] X        The address of the result MPI. Must have the same
 *                      number of limbs as \p N.
 *                      On successful completion, \p X contains the result of
 *                      the multiplication `A * B * R^-1` mod N where
 *                      `R = 2^(biL * N->limbs)`.
 * \param[in]  A        The address of the first MPI.
 * \param[in]  B        The address of the second MPI.
 * \param[in]  N        The address of the modulus. Used to perform a modulo
 *                      operation on the result of the multiplication.
 *
 * \return      \c 0 if successful.
 * \return      #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if all the parameters do not
 *              have the same number of limbs or \p N is invalid.
 * \return      #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 */
int mbedtls_mpi_mod_mul(mbedtls_mpi_mod_residue *X,
                        const mbedtls_mpi_mod_residue *A,
                        const mbedtls_mpi_mod_residue *B,
                        const mbedtls_mpi_mod_modulus *N);

/**
 * \brief Perform a fixed-size modular subtraction.
 *
 * Calculate `A - B modulo N`.
 *
 * \p A, \p B and \p X must all have the same number of limbs as \p N.
 *
 * \p X may be aliased to \p A or \p B, or even both, but may not overlap
 * either otherwise.
 *
 * \note This function does not check that \p A or \p B are in canonical
 *       form (that is, are < \p N) - that will have been done by
 *       mbedtls_mpi_mod_residue_setup().
 *
 * \param[out] X    The address of the result MPI. Must be initialized.
 *                  Must have the same number of limbs as the modulus \p N.
 * \param[in]  A    The address of the first MPI.
 * \param[in]  B    The address of the second MPI.
 * \param[in]  N    The address of the modulus. Used to perform a modulo
 *                  operation on the result of the subtraction.
 *
 * \return          \c 0 if successful.
 * \return          #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if the given MPIs do not
 *                  have the correct number of limbs.
 */
int mbedtls_mpi_mod_sub(mbedtls_mpi_mod_residue *X,
                        const mbedtls_mpi_mod_residue *A,
                        const mbedtls_mpi_mod_residue *B,
                        const mbedtls_mpi_mod_modulus *N);

/**
 * \brief Perform modular inversion of an MPI with respect to a modulus \p N.
 *
 * \p A and \p X must be associated with the modulus \p N and will therefore
 * have the same number of limbs as \p N.
 *
 * \p X may be aliased to \p A.
 *
 * \warning  Currently only supports prime moduli, but does not check for them.
 *
 * \param[out] X   The modular inverse of \p A with respect to \p N.
 * \param[in] A    The number to calculate the modular inverse of.
 *                 Must not be 0.
 * \param[in] N    The modulus to use.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if \p A and \p N do not
 *                 have the same number of limbs.
 * \return         #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if \p A is zero.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if couldn't allocate enough
 *                 memory (needed for conversion to and from Mongtomery form
 *                 when not in Montgomery form already, and for temporary use
 *                 by the inversion calculation itself).
 */

int mbedtls_mpi_mod_inv(mbedtls_mpi_mod_residue *X,
                        const mbedtls_mpi_mod_residue *A,
                        const mbedtls_mpi_mod_modulus *N);
/**
 * \brief Perform a fixed-size modular addition.
 *
 * Calculate `A + B modulo N`.
 *
 * \p A, \p B and \p X must all be associated with the modulus \p N and must
 * all have the same number of limbs as \p N.
 *
 * \p X may be aliased to \p A or \p B, or even both, but may not overlap
 * either otherwise.
 *
 * \note This function does not check that \p A or \p B are in canonical
 *       form (that is, are < \p N) - that will have been done by
 *       mbedtls_mpi_mod_residue_setup().
 *
 * \param[out] X    The address of the result residue. Must be initialized.
 *                  Must have the same number of limbs as the modulus \p N.
 * \param[in]  A    The address of the first input residue.
 * \param[in]  B    The address of the second input residue.
 * \param[in]  N    The address of the modulus. Used to perform a modulo
 *                  operation on the result of the addition.
 *
 * \return          \c 0 if successful.
 * \return          #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if the given MPIs do not
 *                  have the correct number of limbs.
 */
int mbedtls_mpi_mod_add(mbedtls_mpi_mod_residue *X,
                        const mbedtls_mpi_mod_residue *A,
                        const mbedtls_mpi_mod_residue *B,
                        const mbedtls_mpi_mod_modulus *N);

/** Generate a random number uniformly in a range.
 *
 * This function generates a random number between \p min inclusive and
 * \p N exclusive.
 *
 * The procedure complies with RFC 6979 §3.3 (deterministic ECDSA)
 * when the RNG is a suitably parametrized instance of HMAC_DRBG
 * and \p min is \c 1.
 *
 * \note           There are `N - min` possible outputs. The lower bound
 *                 \p min can be reached, but the upper bound \p N cannot.
 *
 * \param X        The destination residue.
 * \param min      The minimum value to return. It must be strictly smaller
 *                 than \b N.
 * \param N        The modulus.
 *                 This is the upper bound of the output range, exclusive.
 * \param f_rng    The RNG function to use. This must not be \c NULL.
 * \param p_rng    The RNG parameter to be passed to \p f_rng.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_NOT_ACCEPTABLE if the implementation was
 *                 unable to find a suitable value within a limited number
 *                 of attempts. This has a negligible probability if \p N
 *                 is significantly larger than \p min, which is the case
 *                 for all usual cryptographic applications.
 */
int mbedtls_mpi_mod_random(mbedtls_mpi_mod_residue *X,
                           mbedtls_mpi_uint min,
                           const mbedtls_mpi_mod_modulus *N,
                           int (*f_rng)(void *, unsigned char *, size_t),
                           void *p_rng);

/** Read a residue from a byte buffer.
 *
 * The residue will be automatically converted to the internal representation
 * based on the value of the `N->int_rep` field.
 *
 * The modulus \p N will be the modulus associated with \p r. The residue \p r
 * should only be used in operations where the modulus is \p N or a modulus
 * equivalent to \p N (in the sense that all their fields or memory pointed by
 * their fields hold the same value).
 *
 * \param[out] r    The address of the residue. It must have exactly the same
 *                  number of limbs as the modulus \p N.
 * \param[in] N     The address of the modulus.
 * \param[in] buf   The input buffer to import from.
 * \param buflen    The length in bytes of \p buf.
 * \param ext_rep   The endianness of the number in the input buffer.
 *
 * \return       \c 0 if successful.
 * \return       #MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL if \p r isn't
 *               large enough to hold the value in \p buf.
 * \return       #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if \p ext_rep
 *               is invalid or the value in the buffer is not less than \p N.
 */
int mbedtls_mpi_mod_read(mbedtls_mpi_mod_residue *r,
                         const mbedtls_mpi_mod_modulus *N,
                         const unsigned char *buf,
                         size_t buflen,
                         mbedtls_mpi_mod_ext_rep ext_rep);

/** Write a residue into a byte buffer.
 *
 * The modulus \p N must be the modulus associated with \p r (see
 * mbedtls_mpi_mod_residue_setup() and mbedtls_mpi_mod_read()).
 *
 * The residue will be automatically converted from the internal representation
 * based on the value of `N->int_rep` field.
 *
 * \warning     If the buffer is smaller than `N->bits`, the number of
 *              leading zeroes is leaked through timing. If \p r is
 *              secret, the caller must ensure that \p buflen is at least
 *              (`N->bits`+7)/8.
 *
 * \param[in] r     The address of the residue. It must have the same number of
 *                  limbs as the modulus \p N. (\p r is an input parameter, but
 *                  its value will be modified during execution and restored
 *                  before the function returns.)
 * \param[in] N     The address of the modulus associated with \p r.
 * \param[out] buf  The output buffer to export to.
 * \param buflen    The length in bytes of \p buf.
 * \param ext_rep   The endianness in which the number should be written into
 *                  the output buffer.
 *
 * \return       \c 0 if successful.
 * \return       #MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL if \p buf isn't
 *               large enough to hold the value of \p r (without leading
 *               zeroes).
 * \return       #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if \p ext_rep is invalid.
 * \return       #MBEDTLS_ERR_MPI_ALLOC_FAILED if couldn't allocate enough
 *               memory for conversion. Can occur only for moduli with
 *               MBEDTLS_MPI_MOD_REP_MONTGOMERY.
 */
int mbedtls_mpi_mod_write(const mbedtls_mpi_mod_residue *r,
                          const mbedtls_mpi_mod_modulus *N,
                          unsigned char *buf,
                          size_t buflen,
                          mbedtls_mpi_mod_ext_rep ext_rep);

# 20 "L:/PLT/pcac/mbedTLS/library/ecp_invasive.h"
# 21 "L:/PLT/pcac/mbedTLS/library/ecp_invasive.h"

/*
 * Curve modulus types
 */
typedef enum {
    MBEDTLS_ECP_MOD_NONE = 0,
    MBEDTLS_ECP_MOD_COORDINATE,
    MBEDTLS_ECP_MOD_SCALAR
} mbedtls_ecp_modulus_type;

typedef enum {
    MBEDTLS_ECP_VARIANT_NONE = 0,
    MBEDTLS_ECP_VARIANT_WITH_MPI_STRUCT,
    MBEDTLS_ECP_VARIANT_WITH_MPI_UINT
} mbedtls_ecp_variant;

# 324 "L:/PLT/pcac/mbedTLS/library/ecp_invasive.h"

# 72 "L:/PLT/pcac/mbedTLS/library/ecp.c"

# 74 "L:/PLT/pcac/mbedTLS/library/ecp.c"



# 1 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"
/**
 * \file platform.h
 *
 * \brief This file contains the definitions and functions of the
 *        Mbed TLS platform abstraction layer.
 *
 *        The platform abstraction layer removes the need for the library
 *        to directly link to standard C library functions or operating
 *        system services, making the library easier to port and embed.
 *        Application developers and users of the library can provide their own
 *        implementations of these functions, or implementations specific to
 *        their platform, which can be statically linked to the library or
 *        dynamically configured at runtime.
 *
 *        When all compilation options related to platform abstraction are
 *        disabled, this header just defines `mbedtls_xxx` function names
 *        as aliases to the standard `xxx` function.
 *
 *        Most modules in the library and example programs are expected to
 *        include this header.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */
# 29 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"

# 31 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"

# 34 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"






/**
 * \name SECTION: Module settings
 *
 * The configuration options you can set for this module are in this section.
 * Either change them in mbedtls_config.h or define them on the compiler command line.
 * \{
 */

/* The older Microsoft Windows common runtime provides non-conforming
 * implementations of some standard library functions, including snprintf
 * and vsnprintf. This affects MSVC and MinGW builds.
 */





# 1 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"
/* stdio.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.9 */
/* Copyright (C) Codemist Ltd., 1988-1993                       */
/* Copyright 1991-1998 ARM Limited. All rights reserved.        */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: sdouglas $
 */

/*
 * stdio.h declares two types, several macros, and many functions for
 * performing input and output. For a discussion on Streams and Files
 * refer to sections 4.9.2 and 4.9.3 in the above ANSI draft, or to a
 * modern textbook on C.
 */





/*
 * Depending on compiler version __int64 or __INT64_TYPE__ should be defined.
 */




  /* On some architectures neither of these may be defined - if so, fall
     through and error out if used. */








# 47 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"


 /* always defined in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */









/* ANSI forbids va_list to be defined here */
/* keep in step with <stdarg.h> and <wchar.h> */

/* always defined in C++ and non-strict C for consistency of debug info */



    typedef struct __va_list __va_list;






   /*
    * If the compiler supports signalling nans as per N965 then it
    * will define __SUPPORT_SNAN__, in which case a user may define
    * _WANT_SNAN in order to obtain compliant versions of the printf
    * and scanf families of functions
    */




typedef struct __fpos_t_struct {
    unsigned __int64 __pos;
    /*
     * this structure is equivalent to an mbstate_t, but we're not
     * allowed to actually define the type name `mbstate_t' within
     * stdio.h
     */
    struct {
        unsigned int __state1, __state2;
    } __mbstate;
} fpos_t;
   /*
    * fpos_t is an object capable of recording all information needed to
    * specify uniquely every position within a file.
    */


   /* _SYS_OPEN defines a limit on the number of open files that is imposed
    * by this C library
    */

typedef struct __FILE FILE;
   /*
    * FILE is an object capable of recording all information needed to control
    * a stream, such as its file position indicator, a pointer to its
    * associated buffer, an error indicator that records whether a read/write
    * error has occurred and an end-of-file indicator that records whether the
    * end-of-file has been reached.
    * Its structure is not made known to library clients.
    */

# 136 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"


extern FILE __stdin, __stdout, __stderr;
extern FILE *__aeabi_stdin, *__aeabi_stdout, *__aeabi_stderr;

# 166 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"
   /* pointer to a FILE object associated with standard input stream */

   /* pointer to a FILE object associated with standard output stream */

   /* pointer to a FILE object associated with standard error stream */





    /* Various default file IO buffer sizes */



   /*
    * an integral constant expression that is the minimum number of files that
    * this implementation guarantees can be open simultaneously.
    */


   /*
    * an integral constant expression that is the size of an array of char
    * large enough to hold the longest filename string
    */

   /*
    * an integral constant expression that is the size of an array of char
    * large enough to hold a temporary file name string generated by the
    * tmpnam function.
    */

   /*
    * an integral constant expression that is the minimum number of unique
    * file names that shall be generated by the tmpnam function.
    */




   /*
    * negative integral constant, indicates end-of-file, that is, no more input
    * from a stream.
    */





    /*
     * _IOBIN is the flag passed to _sys_write to denote a binary
     * file.
     */






extern __declspec(__nothrow) int remove(const char * /*filename*/) __attribute__((__nonnull__(1)));
   /*
    * causes the file whose name is the string pointed to by filename to be
    * removed. Subsequent attempts to open the file will fail, unless it is
    * created anew. If the file is open, the behaviour of the remove function
    * is implementation-defined.
    * Returns: zero if the operation succeeds, nonzero if it fails.
    */
extern __declspec(__nothrow) int rename(const char * /*old*/, const char * /*new*/) __attribute__((__nonnull__(1,2)));
   /*
    * causes the file whose name is the string pointed to by old to be
    * henceforth known by the name given by the string pointed to by new. The
    * file named old is effectively removed. If a file named by the string
    * pointed to by new exists prior to the call of the rename function, the
    * behaviour is implementation-defined.
    * Returns: zero if the operation succeeds, nonzero if it fails, in which
    *          case if the file existed previously it is still known by its
    *          original name.
    */
extern __declspec(__nothrow) FILE *tmpfile(void);
   /*
    * creates a temporary binary file that will be automatically removed when
    * it is closed or at program termination. The file is opened for update.
    * Returns: a pointer to the stream of the file that it created. If the file
    *          cannot be created, a null pointer is returned.
    */
extern __declspec(__nothrow) char *tmpnam(char * /*s*/);
   /*
    * generates a string that is not the same as the name of an existing file.
    * The tmpnam function generates a different string each time it is called,
    * up to TMP_MAX times. If it is called more than TMP_MAX times, the
    * behaviour is implementation-defined.
    * Returns: If the argument is a null pointer, the tmpnam function leaves
    *          its result in an internal static object and returns a pointer to
    *          that object. Subsequent calls to the tmpnam function may modify
    *          the same object. if the argument is not a null pointer, it is
    *          assumed to point to an array of at least L_tmpnam characters;
    *          the tmpnam function writes its result in that array and returns
    *          the argument as its value.
    */

extern __declspec(__nothrow) int fclose(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * causes the stream pointed to by stream to be flushed and the associated
    * file to be closed. Any unwritten buffered data for the stream are
    * delivered to the host environment to be written to the file; any unread
    * buffered data are discarded. The stream is disassociated from the file.
    * If the associated buffer was automatically allocated, it is deallocated.
    * Returns: zero if the stream was succesfully closed, or nonzero if any
    *          errors were detected or if the stream was already closed.
    */
extern __declspec(__nothrow) int fflush(FILE * /*stream*/);
   /*
    * If the stream points to an output or update stream in which the most
    * recent operation was output, the fflush function causes any unwritten
    * data for that stream to be delivered to the host environment to be
    * written to the file. If the stream points to an input or update stream,
    * the fflush function undoes the effect of any preceding ungetc operation
    * on the stream.
    * Returns: nonzero if a write error occurs.
    */
extern __declspec(__nothrow) FILE *fopen(const char * __restrict /*filename*/,
                           const char * __restrict /*mode*/) __attribute__((__nonnull__(1,2)));
   /*
    * opens the file whose name is the string pointed to by filename, and
    * associates a stream with it.
    * The argument mode points to a string beginning with one of the following
    * sequences:
    * "r"         open text file for reading
    * "w"         create text file for writing, or truncate to zero length
    * "a"         append; open text file or create for writing at eof
    * "rb"        open binary file for reading
    * "wb"        create binary file for writing, or truncate to zero length
    * "ab"        append; open binary file or create for writing at eof
    * "r+"        open text file for update (reading and writing)
    * "w+"        create text file for update, or truncate to zero length
    * "a+"        append; open text file or create for update, writing at eof
    * "r+b"/"rb+" open binary file for update (reading and writing)
    * "w+b"/"wb+" create binary file for update, or truncate to zero length
    * "a+b"/"ab+" append; open binary file or create for update, writing at eof
    *
    * Opening a file with read mode ('r' as the first character in the mode
    * argument) fails if the file does not exist or cannot be read.
    * Opening a file with append mode ('a' as the first character in the mode
    * argument) causes all subsequent writes to be forced to the current end of
    * file, regardless of intervening calls to the fseek function. In some
    * implementations, opening a binary file with append mode ('b' as the
    * second or third character in the mode argument) may initially position
    * the file position indicator beyond the last data written, because of the
    * NUL padding.
    * When a file is opened with update mode ('+' as the second or third
    * character in the mode argument), both input and output may be performed
    * on the associated stream. However, output may not be directly followed
    * by input without an intervening call to the fflush fuction or to a file
    * positioning function (fseek, fsetpos, or rewind), and input be not be
    * directly followed by output without an intervening call to the fflush
    * fuction or to a file positioning function, unless the input operation
    * encounters end-of-file. Opening a file with update mode may open or
    * create a binary stream in some implementations. When opened, a stream
    * is fully buffered if and only if it does not refer to an interactive
    * device. The error and end-of-file indicators for the stream are
    * cleared.
    * Returns: a pointer to the object controlling the stream. If the open
    *          operation fails, fopen returns a null pointer.
    */
extern __declspec(__nothrow) FILE *freopen(const char * __restrict /*filename*/,
                    const char * __restrict /*mode*/,
                    FILE * __restrict /*stream*/) __attribute__((__nonnull__(2,3)));
   /*
    * opens the file whose name is the string pointed to by filename and
    * associates the stream pointed to by stream with it. The mode argument is
    * used just as in the fopen function.
    * The freopen function first attempts to close any file that is associated
    * with the specified stream. Failure to close the file successfully is
    * ignored. The error and end-of-file indicators for the stream are cleared.
    * Returns: a null pointer if the operation fails. Otherwise, freopen
    *          returns the value of the stream.
    */
extern __declspec(__nothrow) void setbuf(FILE * __restrict /*stream*/,
                    char * __restrict /*buf*/) __attribute__((__nonnull__(1)));
   /*
    * Except that it returns no value, the setbuf function is equivalent to the
    * setvbuf function invoked with the values _IOFBF for mode and BUFSIZ for
    * size, or (if buf is a null pointer), with the value _IONBF for mode.
    * Returns: no value.
    */
extern __declspec(__nothrow) int setvbuf(FILE * __restrict /*stream*/,
                   char * __restrict /*buf*/,
                   int /*mode*/, size_t /*size*/) __attribute__((__nonnull__(1)));
   /*
    * may be used after the stream pointed to by stream has been associated
    * with an open file but before it is read or written. The argument mode
    * determines how stream will be buffered, as follows: _IOFBF causes
    * input/output to be fully buffered; _IOLBF causes output to be line
    * buffered (the buffer will be flushed when a new-line character is
    * written, when the buffer is full, or when input is requested); _IONBF
    * causes input/output to be completely unbuffered. If buf is not the null
    * pointer, the array it points to may be used instead of an automatically
    * allocated buffer (the buffer must have a lifetime at least as great as
    * the open stream, so the stream should be closed before a buffer that has
    * automatic storage duration is deallocated upon block exit). The argument
    * size specifies the size of the array. The contents of the array at any
    * time are indeterminate.
    * Returns: zero on success, or nonzero if an invalid value is given for
    *          mode or size, or if the request cannot be honoured.
    */
#pragma __printf_args
extern __declspec(__nothrow) int fprintf(FILE * __restrict /*stream*/,
                    const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * writes output to the stream pointed to by stream, under control of the
    * string pointed to by format that specifies how subsequent arguments are
    * converted for output. If there are insufficient arguments for the format,
    * the behaviour is undefined. If the format is exhausted while arguments
    * remain, the excess arguments are evaluated but otherwise ignored. The
    * fprintf function returns when the end of the format string is reached.
    * The format shall be a multibyte character sequence, beginning and ending
    * in its initial shift state. The format is composed of zero or more
    * directives: ordinary multibyte characters (not %), which are copied
    * unchanged to the output stream; and conversion specifiers, each of which
    * results in fetching zero or more subsequent arguments. Each conversion
    * specification is introduced by the character %. For a description of the
    * available conversion specifiers refer to section ******* in the ANSI
    * draft mentioned at the start of this file or to any modern textbook on C.
    * The minimum value for the maximum number of characters producable by any
    * single conversion is at least 509.
    * Returns: the number of characters transmitted, or a negative value if an
    *          output error occurred.
    */
#pragma __printf_args
extern __declspec(__nothrow) int _fprintf(FILE * __restrict /*stream*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fprintf, but does not support floating-point formats.
    * You can use instead of fprintf to improve code size.
    * Returns: as fprintf.
    */
#pragma __printf_args
extern __declspec(__nothrow) int printf(const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to fprintf with the argument stdout interposed before the
    * arguments to printf.
    * Returns: the number of characters transmitted, or a negative value if an
    *          output error occurred.
    */
#pragma __printf_args
extern __declspec(__nothrow) int _printf(const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to printf, but does not support floating-point formats.
    * You can use instead of printf to improve code size.
    * Returns: as printf.
    */
#pragma __printf_args
extern __declspec(__nothrow) int sprintf(char * __restrict /*s*/, const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fprintf, except that the argument s specifies an array
    * into which the generated output is to be written, rather than to a
    * stream. A null character is written at the end of the characters written;
    * it is not counted as part of the returned sum.
    * Returns: the number of characters written to the array, not counting the
    *          terminating null character.
    */
#pragma __printf_args
extern __declspec(__nothrow) int _sprintf(char * __restrict /*s*/, const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to sprintf, but does not support floating-point formats.
    * You can use instead of sprintf to improve code size.
    * Returns: as sprintf.
    */

#pragma __printf_args
extern __declspec(__nothrow) int __ARM_snprintf(char * __restrict /*s*/, size_t /*n*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(3)));


#pragma __printf_args
extern __declspec(__nothrow) int snprintf(char * __restrict /*s*/, size_t /*n*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(3)));
   /*
    * is equivalent to fprintf, except that the argument s specifies an array
    * into which the generated output is to be written, rather than to a
    * stream. The argument n specifies the size of the output array, so as to
    * avoid overflowing the buffer.
    * A null character is written at the end of the characters written, even
    * if the formatting was not completed; it is not counted as part of the
    * returned sum. At most n characters of the output buffer are used,
    * _including_ the null character.
    * Returns: the number of characters that would have been written to the
    *          array, not counting the terminating null character, if the
    *          array had been big enough. So if the return is >=0 and <n, then
    *          the entire string was successfully formatted; if the return is
    *          >=n, the string was truncated (but there is still a null char
    *          at the end of what was written); if the return is <0, there was
    *          an error.
    */

#pragma __printf_args
extern __declspec(__nothrow) int _snprintf(char * __restrict /*s*/, size_t /*n*/,
                      const char * __restrict /*format*/, ...) __attribute__((__nonnull__(3)));
   /*
    * is equivalent to snprintf, but does not support floating-point formats.
    * You can use instead of snprintf to improve code size.
    * Returns: as snprintf.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int fscanf(FILE * __restrict /*stream*/,
                    const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * reads input from the stream pointed to by stream, under control of the
    * string pointed to by format that specifies the admissible input sequences
    * and how thay are to be converted for assignment, using subsequent
    * arguments as pointers to the objects to receive the converted input. If
    * there are insufficient arguments for the format, the behaviour is
    * undefined. If the format is exhausted while arguments remain, the excess
    * arguments are evaluated but otherwise ignored.
    * The format is composed of zero or more directives: one or more
    * white-space characters; an ordinary character (not %); or a conversion
    * specification. Each conversion specification is introduced by the
    * character %. For a description of the available conversion specifiers
    * refer to section ******* in the ANSI draft mentioned at the start of this
    * file, or to any modern textbook on C.
    * If end-of-file is encountered during input, conversion is terminated. If
    * end-of-file occurs before any characters matching the current directive
    * have been read (other than leading white space, where permitted),
    * execution of the current directive terminates with an input failure;
    * otherwise, unless execution of the current directive is terminated with a
    * matching failure, execution of the following directive (if any) is
    * terminated with an input failure.
    * If conversions terminates on a conflicting input character, the offending
    * input character is left unread in the input strem. Trailing white space
    * (including new-line characters) is left unread unless matched by a
    * directive. The success of literal matches and suppressed asignments is
    * not directly determinable other than via the %n directive.
    * Returns: the value of the macro EOF if an input failure occurs before any
    *          conversion. Otherwise, the fscanf function returns the number of
    *          input items assigned, which can be fewer than provided for, or
    *          even zero, in the event of an early conflict between an input
    *          character and the format.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int _fscanf(FILE * __restrict /*stream*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fscanf, but does not support floating-point formats.
    * You can use instead of fscanf to improve code size.
    * Returns: as fscanf.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int scanf(const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to fscanf with the argument stdin interposed before the
    * arguments to scanf.
    * Returns: the value of the macro EOF if an input failure occurs before any
    *          conversion. Otherwise, the scanf function returns the number of
    *          input items assigned, which can be fewer than provided for, or
    *          even zero, in the event of an early matching failure.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int _scanf(const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to scanf, but does not support floating-point formats.
    * You can use instead of scanf to improve code size.
    * Returns: as scanf.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int sscanf(const char * __restrict /*s*/,
                    const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fscanf except that the argument s specifies a string
    * from which the input is to be obtained, rather than from a stream.
    * Reaching the end of the string is equivalent to encountering end-of-file
    * for the fscanf function.
    * Returns: the value of the macro EOF if an input failure occurs before any
    *          conversion. Otherwise, the scanf function returns the number of
    *          input items assigned, which can be fewer than provided for, or
    *          even zero, in the event of an early matching failure.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int _sscanf(const char * __restrict /*s*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to sscanf, but does not support floating-point formats.
    * You can use instead of sscanf to improve code size.
    * Returns: as sscanf.
    */

/* C99 additions */
extern __declspec(__nothrow) int vfscanf(FILE * __restrict /*stream*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) int vscanf(const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) int vsscanf(const char * __restrict /*s*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));

extern __declspec(__nothrow) int _vfscanf(FILE * __restrict /*stream*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) int _vscanf(const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) int _vsscanf(const char * __restrict /*s*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) int __ARM_vsscanf(const char * __restrict /*s*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));

extern __declspec(__nothrow) int vprintf(const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to printf, with the variable argument list replaced by arg,
    * which has been initialised by the va_start macro (and possibly subsequent
    * va_arg calls). The vprintf function does not invoke the va_end function.
    * Returns: the number of characters transmitted, or a negative value if an
    *          output error occurred.
    */
extern __declspec(__nothrow) int _vprintf(const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to vprintf, but does not support floating-point formats.
    * You can use instead of vprintf to improve code size.
    * Returns: as vprintf.
    */
extern __declspec(__nothrow) int vfprintf(FILE * __restrict /*stream*/,
                    const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fprintf, with the variable argument list replaced by
    * arg, which has been initialised by the va_start macro (and possibly
    * subsequent va_arg calls). The vfprintf function does not invoke the
    * va_end function.
    * Returns: the number of characters transmitted, or a negative value if an
    *          output error occurred.
    */
extern __declspec(__nothrow) int vsprintf(char * __restrict /*s*/,
                     const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to sprintf, with the variable argument list replaced by
    * arg, which has been initialised by the va_start macro (and possibly
    * subsequent va_arg calls). The vsprintf function does not invoke the
    * va_end function.
    * Returns: the number of characters written in the array, not counting the
    *          terminating null character.
    */
extern __declspec(__nothrow) int __ARM_vsnprintf(char * __restrict /*s*/, size_t /*n*/,
                     const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(3)));

extern __declspec(__nothrow) int vsnprintf(char * __restrict /*s*/, size_t /*n*/,
                     const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(3)));
   /*
    * is equivalent to snprintf, with the variable argument list replaced by
    * arg, which has been initialised by the va_start macro (and possibly
    * subsequent va_arg calls). The vsprintf function does not invoke the
    * va_end function.
    * Returns: the number of characters that would have been written in the
    *          array, not counting the terminating null character. As
    *          snprintf.
    */

extern __declspec(__nothrow) int _vsprintf(char * __restrict /*s*/,
                      const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to vsprintf, but does not support floating-point formats.
    * You can use instead of vsprintf to improve code size.
    * Returns: as vsprintf.
    */
extern __declspec(__nothrow) int _vfprintf(FILE * __restrict /*stream*/,
                     const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to vfprintf, but does not support floating-point formats.
    * You can use instead of vfprintf to improve code size.
    * Returns: as vfprintf.
    */
extern __declspec(__nothrow) int _vsnprintf(char * __restrict /*s*/, size_t /*n*/,
                      const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(3)));
   /*
    * is equivalent to vsnprintf, but does not support floating-point formats.
    * You can use instead of vsnprintf to improve code size.
    * Returns: as vsnprintf.
    */

#pragma __printf_args
extern __declspec(__nothrow) int asprintf(char ** /*strp*/, const char * __restrict /*format*/, ...) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) int vasprintf(char ** /*strp*/, const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(2)));

#pragma __printf_args
extern __declspec(__nothrow) int __ARM_asprintf(char ** /*strp*/, const char * __restrict /*format*/, ...) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) int __ARM_vasprintf(char ** /*strp*/, const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(2)));
   /*
    * dynamically allocates a buffer of the right size for the
    * formatted string, and returns it in (*strp). Formal return value
    * is the same as any other printf variant, except that it returns
    * -1 if the buffer could not be allocated.
    *
    * (The functions with __ARM_ prefixed names are identical to the
    * ones without, but are available in all compilation modes without
    * violating user namespace.)
    */

extern __declspec(__nothrow) int fgetc(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * obtains the next character (if present) as an unsigned char converted to
    * an int, from the input stream pointed to by stream, and advances the
    * associated file position indicator (if defined).
    * Returns: the next character from the input stream pointed to by stream.
    *          If the stream is at end-of-file, the end-of-file indicator is
    *          set and fgetc returns EOF. If a read error occurs, the error
    *          indicator is set and fgetc returns EOF.
    */
extern __declspec(__nothrow) char *fgets(char * __restrict /*s*/, int /*n*/,
                    FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,3)));
   /*
    * reads at most one less than the number of characters specified by n from
    * the stream pointed to by stream into the array pointed to by s. No
    * additional characters are read after a new-line character (which is
    * retained) or after end-of-file. A null character is written immediately
    * after the last character read into the array.
    * Returns: s if successful. If end-of-file is encountered and no characters
    *          have been read into the array, the contents of the array remain
    *          unchanged and a null pointer is returned. If a read error occurs
    *          during the operation, the array contents are indeterminate and a
    *          null pointer is returned.
    */
extern __declspec(__nothrow) int fputc(int /*c*/, FILE * /*stream*/) __attribute__((__nonnull__(2)));
   /*
    * writes the character specified by c (converted to an unsigned char) to
    * the output stream pointed to by stream, at the position indicated by the
    * asociated file position indicator (if defined), and advances the
    * indicator appropriately. If the file position indicator is not defined,
    * the character is appended to the output stream.
    * Returns: the character written. If a write error occurs, the error
    *          indicator is set and fputc returns EOF.
    */
extern __declspec(__nothrow) int fputs(const char * __restrict /*s*/, FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,2)));
   /*
    * writes the string pointed to by s to the stream pointed to by stream.
    * The terminating null character is not written.
    * Returns: EOF if a write error occurs; otherwise it returns a nonnegative
    *          value.
    */
extern __declspec(__nothrow) int getc(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to fgetc except that it may be implemented as an unsafe
    * macro (stream may be evaluated more than once, so the argument should
    * never be an expression with side-effects).
    * Returns: the next character from the input stream pointed to by stream.
    *          If the stream is at end-of-file, the end-of-file indicator is
    *          set and getc returns EOF. If a read error occurs, the error
    *          indicator is set and getc returns EOF.
    */




    extern __declspec(__nothrow) int (getchar)(void);

   /*
    * is equivalent to getc with the argument stdin.
    * Returns: the next character from the input stream pointed to by stdin.
    *          If the stream is at end-of-file, the end-of-file indicator is
    *          set and getchar returns EOF. If a read error occurs, the error
    *          indicator is set and getchar returns EOF.
    */
extern __declspec(__nothrow) char *gets(char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * reads characters from the input stream pointed to by stdin into the array
    * pointed to by s, until end-of-file is encountered or a new-line character
    * is read. Any new-line character is discarded, and a null character is
    * written immediately after the last character read into the array.
    * Returns: s if successful. If end-of-file is encountered and no characters
    *          have been read into the array, the contents of the array remain
    *          unchanged and a null pointer is returned. If a read error occurs
    *          during the operation, the array contents are indeterminate and a
    *          null pointer is returned.
    */
extern __declspec(__nothrow) int putc(int /*c*/, FILE * /*stream*/) __attribute__((__nonnull__(2)));
   /*
    * is equivalent to fputc except that it may be implemented as aan unsafe
    * macro (stream may be evaluated more than once, so the argument should
    * never be an expression with side-effects).
    * Returns: the character written. If a write error occurs, the error
    *          indicator is set and putc returns EOF.
    */




    extern __declspec(__nothrow) int (putchar)(int /*c*/);

   /*
    * is equivalent to putc with the second argument stdout.
    * Returns: the character written. If a write error occurs, the error
    *          indicator is set and putc returns EOF.
    */
extern __declspec(__nothrow) int puts(const char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * writes the string pointed to by s to the stream pointed to by stdout, and
    * appends a new-line character to the output. The terminating null
    * character is not written.
    * Returns: EOF if a write error occurs; otherwise it returns a nonnegative
    *          value.
    */
extern __declspec(__nothrow) int ungetc(int /*c*/, FILE * /*stream*/) __attribute__((__nonnull__(2)));
   /*
    * pushes the character specified by c (converted to an unsigned char) back
    * onto the input stream pointed to by stream. The character will be
    * returned by the next read on that stream. An intervening call to the
    * fflush function or to a file positioning function (fseek, fsetpos,
    * rewind) discards any pushed-back characters. The extern _ARMABIal storage
    * corresponding to the stream is unchanged.
    * One character pushback is guaranteed. If the unget function is called too
    * many times on the same stream without an intervening read or file
    * positioning operation on that stream, the operation may fail.
    * If the value of c equals that of the macro EOF, the operation fails and
    * the input stream is unchanged.
    * A successful call to the ungetc function clears the end-of-file
    * indicator. The value of the file position indicator after reading or
    * discarding all pushed-back characters shall be the same as it was before
    * the characters were pushed back. For a text stream, the value of the file
    * position indicator after a successful call to the ungetc function is
    * unspecified until all pushed-back characters are read or discarded. For a
    * binary stream, the file position indicator is decremented by each
    * successful call to the ungetc function; if its value was zero before a
    * call, it is indeterminate after the call.
    * Returns: the character pushed back after conversion, or EOF if the
    *          operation fails.
    */

extern __declspec(__nothrow) size_t fread(void * __restrict /*ptr*/,
                    size_t /*size*/, size_t /*nmemb*/, FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,4)));
   /*
    * reads into the array pointed to by ptr, up to nmemb members whose size is
    * specified by size, from the stream pointed to by stream. The file
    * position indicator (if defined) is advanced by the number of characters
    * successfully read. If an error occurs, the resulting value of the file
    * position indicator is indeterminate. If a partial member is read, its
    * value is indeterminate. The ferror or feof function shall be used to
    * distinguish between a read error and end-of-file.
    * Returns: the number of members successfully read, which may be less than
    *          nmemb if a read error or end-of-file is encountered. If size or
    *          nmemb is zero, fread returns zero and the contents of the array
    *          and the state of the stream remain unchanged.
    */

extern __declspec(__nothrow) size_t __fread_bytes_avail(void * __restrict /*ptr*/,
                    size_t /*count*/, FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,3)));
   /*
    * reads into the array pointed to by ptr, up to count characters from the
    * stream pointed to by stream. The file position indicator (if defined)
    * is advanced by the number of characters successfully read. If an error
    * occurs, the resulting value of the file position indicator is
    * indeterminate. The ferror or feof function shall be used to
    * distinguish between a read error and end-of-file.  The call will block
    * only if no characters are available.
    * Returns: the number of characters successfully read, which may be less than
    *          count. If count is zero, __fread_bytes_avail returns zero and
    *          the contents of the array and the state of the stream remain
    *          unchanged.
    */

extern __declspec(__nothrow) size_t fwrite(const void * __restrict /*ptr*/,
                    size_t /*size*/, size_t /*nmemb*/, FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,4)));
   /*
    * writes, from the array pointed to by ptr up to nmemb members whose size
    * is specified by size, to the stream pointed to by stream. The file
    * position indicator (if defined) is advanced by the number of characters
    * successfully written. If an error occurs, the resulting value of the file
    * position indicator is indeterminate.
    * Returns: the number of members successfully written, which will be less
    *          than nmemb only if a write error is encountered.
    */

extern __declspec(__nothrow) int fgetpos(FILE * __restrict /*stream*/, fpos_t * __restrict /*pos*/) __attribute__((__nonnull__(1,2)));
   /*
    * stores the current value of the file position indicator for the stream
    * pointed to by stream in the object pointed to by pos. The value stored
    * contains unspecified information usable by the fsetpos function for
    * repositioning the stream to its position at the time  of the call to the
    * fgetpos function.
    * Returns: zero, if successful. Otherwise nonzero is returned and the
    *          integer expression errno is set to an implementation-defined
    *          nonzero value.
    */
extern __declspec(__nothrow) int fseek(FILE * /*stream*/, long int /*offset*/, int /*whence*/) __attribute__((__nonnull__(1)));
   /*
    * sets the file position indicator for the stream pointed to by stream.
    * For a binary stream, the new position is at the signed number of
    * characters specified by offset away from the point specified by whence.
    * The specified point is the beginning of the file for SEEK_SET, the
    * current position in the file for SEEK_CUR, or end-of-file for SEEK_END.
    * A binary stream need not meaningfully support fseek calls with a whence
    * value of SEEK_END.
    * For a text stream, either offset shall be zero, or offset shall be a
    * value returned by an earlier call to the ftell function on the same
    * stream and whence shall be SEEK_SET.
    * The fseek function clears the end-of-file indicator and undoes any
    * effects of the ungetc function on the same stream. After an fseek call,
    * the next operation on an update stream may be either input or output.
    * Returns: nonzero only for a request that cannot be satisfied.
    */
extern __declspec(__nothrow) int fsetpos(FILE * __restrict /*stream*/, const fpos_t * __restrict /*pos*/) __attribute__((__nonnull__(1,2)));
   /*
    * sets  the file position indicator for the stream pointed to by stream
    * according to the value of the object pointed to by pos, which shall be a
    * value returned by an earlier call to the fgetpos function on the same
    * stream.
    * The fsetpos function clears the end-of-file indicator and undoes any
    * effects of the ungetc function on the same stream. After an fsetpos call,
    * the next operation on an update stream may be either input or output.
    * Returns: zero, if successful. Otherwise nonzero is returned and the
    *          integer expression errno is set to an implementation-defined
    *          nonzero value.
    */
extern __declspec(__nothrow) long int ftell(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * obtains the current value of the file position indicator for the stream
    * pointed to by stream. For a binary stream, the value is the number of
    * characters from the beginning of the file. For a text stream, the file
    * position indicator contains unspecified information, usable by the fseek
    * function for returning the file position indicator to its position at the
    * time of the ftell call; the difference between two such return values is
    * not necessarily a meaningful measure of the number of characters written
    * or read.
    * Returns: if successful, the current value of the file position indicator.
    *          On failure, the ftell function returns -1L and sets the integer
    *          expression errno to an implementation-defined nonzero value.
    */
extern __declspec(__nothrow) void rewind(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * sets the file position indicator for the stream pointed to by stream to
    * the beginning of the file. It is equivalent to
    *          (void)fseek(stream, 0L, SEEK_SET)
    * except that the error indicator for the stream is also cleared.
    * Returns: no value.
    */

extern __declspec(__nothrow) void clearerr(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * clears the end-of-file and error indicators for the stream pointed to by
    * stream. These indicators are cleared only when the file is opened or by
    * an explicit call to the clearerr function or to the rewind function.
    * Returns: no value.
    */

extern __declspec(__nothrow) int feof(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * tests the end-of-file indicator for the stream pointed to by stream.
    * Returns: nonzero iff the end-of-file indicator is set for stream.
    */
extern __declspec(__nothrow) int ferror(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * tests the error indicator for the stream pointed to by stream.
    * Returns: nonzero iff the error indicator is set for stream.
    */
extern __declspec(__nothrow) void perror(const char * /*s*/);
   /*
    * maps the error number  in the integer expression errno to an error
    * message. It writes a sequence of characters to the standard error stream
    * thus: first (if s is not a null pointer and the character pointed to by
    * s is not the null character), the string pointed to by s followed by a
    * colon and a space; then an appropriate error message string followed by
    * a new-line character. The contents of the error message strings are the
    * same as those returned by the strerror function with argument errno,
    * which are implementation-defined.
    * Returns: no value.
    */

extern __declspec(__nothrow) int _fisatty(FILE * /*stream*/ ) __attribute__((__nonnull__(1)));
    /* Returns 1 if the stream is tty (stdin), 0 otherwise. Not ANSI compliant.
     */

extern __declspec(__nothrow) void __use_no_semihosting_swi(void);
extern __declspec(__nothrow) void __use_no_semihosting(void);
    /*
     * Referencing either of these symbols will cause a link-time
     * error if any library functions that use semihosting SWI
     * calls are also present in the link, i.e. you define it if
     * you want to make sure you haven't accidentally used any such
     * SWIs.
     */











# 1021 "F:\\DS-5 v5.26.0\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"



/* end of stdio.h */

# 59 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"
# 60 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"
# 62 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"
# 120 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"

/* Enable certain documented defines only when generating doxygen to avoid
 * an "unrecognized define" error. */








/** \} name SECTION: Module settings */

/*
 * The function pointers for calloc and free.
 * Please see MBEDTLS_PLATFORM_STD_CALLOC and MBEDTLS_PLATFORM_STD_FREE
 * in mbedtls_config.h for more information about behaviour and requirements.
 */
# 146 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"
/* For size_t */
# 148 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"
extern void *mbedtls_calloc(size_t n, size_t size);
extern void mbedtls_free(void *ptr);

/**
 * \brief               This function dynamically sets the memory-management
 *                      functions used by the library, during runtime.
 *
 * \param calloc_func   The \c calloc function implementation.
 * \param free_func     The \c free function implementation.
 *
 * \return              \c 0.
 */
int mbedtls_platform_set_calloc_free(void *(*calloc_func)(size_t, size_t),
                                     void (*free_func)(void *));
# 169 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"

/*
 * The function pointers for fprintf
 */
# 197 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"

/*
 * The function pointers for printf
 */
# 222 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"

/*
 * The function pointers for snprintf
 *
 * The snprintf implementation should conform to C99:
 * - it *must* always correctly zero-terminate the buffer
 *   (except when n == 0, then it must leave the buffer untouched)
 * - however it is acceptable to return -1 instead of the required length when
 *   the destination buffer is too short.
 */





# 258 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"

/*
 * The function pointers for vsnprintf
 *
 * The vsnprintf implementation should conform to C99:
 * - it *must* always correctly zero-terminate the buffer
 *   (except when n == 0, then it must leave the buffer untouched)
 * - however it is acceptable to return -1 instead of the required length when
 *   the destination buffer is too short.
 */






# 295 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"

/*
 * The function pointers for setbuf
 */
# 348 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"

/*
 * The function pointers for exit
 */
# 373 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"

/*
 * The default exit values
 */
# 387 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"

/*
 * The function pointers for reading from and writing a seed file to
 * Non-Volatile storage (NV) in a platform-independent way
 *
 * Only enabled when the NV seed entropy source is enabled
 */
# 431 "L:/PLT/pcac/mbedTLS/include/mbedtls/platform.h"



/**
 * \brief   The platform context structure.
 *
 * \note    This structure may be used to assist platform-specific
 *          setup or teardown operations.
 */
typedef struct mbedtls_platform_context {
    char dummy; /**< A placeholder member, as empty structs are not portable. */
}
mbedtls_platform_context;





/**
 * \brief   This function performs any platform-specific initialization
 *          operations.
 *
 * \note    This function should be called before any other library functions.
 *
 *          Its implementation is platform-specific, and unless
 *          platform-specific code is provided, it does nothing.
 *
 * \note    The usage and necessity of this function is dependent on the platform.
 *
 * \param   ctx     The platform context.
 *
 * \return  \c 0 on success.
 */
int mbedtls_platform_setup(mbedtls_platform_context *ctx);
/**
 * \brief   This function performs any platform teardown operations.
 *
 * \note    This function should be called after every other Mbed TLS module
 *          has been correctly freed using the appropriate free function.
 *
 *          Its implementation is platform-specific, and unless
 *          platform-specific code is provided, it does nothing.
 *
 * \note    The usage and necessity of this function is dependent on the platform.
 *
 * \param   ctx     The platform context.
 *
 */
void mbedtls_platform_teardown(mbedtls_platform_context *ctx);





# 78 "L:/PLT/pcac/mbedTLS/library/ecp.c"

# 1 "L:/PLT/pcac/mbedTLS/library/ecp_internal_alt.h"
/**
 * \file ecp_internal_alt.h
 *
 * \brief Function declarations for alternative implementation of elliptic curve
 * point arithmetic.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later
 */

/*
 * References:
 *
 * [1] BERNSTEIN, Daniel J. Curve25519: new Diffie-Hellman speed records.
 *     <http://cr.yp.to/ecdh/curve25519-20060209.pdf>
 *
 * [2] CORON, Jean-S'ebastien. Resistance against differential power analysis
 *     for elliptic curve cryptosystems. In : Cryptographic Hardware and
 *     Embedded Systems. Springer Berlin Heidelberg, 1999. p. 292-302.
 *     <http://link.springer.com/chapter/10.1007/3-540-48059-5_25>
 *
 * [3] HEDABOU, Mustapha, PINEL, Pierre, et B'EN'ETEAU, Lucien. A comb method to
 *     render ECC resistant against Side Channel Attacks. IACR Cryptology
 *     ePrint Archive, 2004, vol. 2004, p. 342.
 *     <http://eprint.iacr.org/2004/342.pdf>
 *
 * [4] Certicom Research. SEC 2: Recommended Elliptic Curve Domain Parameters.
 *     <http://www.secg.org/sec2-v2.pdf>
 *
 * [5] HANKERSON, Darrel, MENEZES, Alfred J., VANSTONE, Scott. Guide to Elliptic
 *     Curve Cryptography.
 *
 * [6] Digital Signature Standard (DSS), FIPS 186-4.
 *     <http://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.186-4.pdf>
 *
 * [7] Elliptic Curve Cryptography (ECC) Cipher Suites for Transport Layer
 *     Security (TLS), RFC 4492.
 *     <https://tools.ietf.org/search/rfc4492>
 *
 * [8] <http://www.hyperelliptic.org/EFD/g1p/auto-shortw-jacobian.html>
 *
 * [9] COHEN, Henri. A Course in Computational Algebraic Number Theory.
 *     Springer Science & Business Media, 1 Aug 2000
 */




# 51 "L:/PLT/pcac/mbedTLS/library/ecp_internal_alt.h"

# 286 "L:/PLT/pcac/mbedTLS/library/ecp_internal_alt.h"

# 80 "L:/PLT/pcac/mbedTLS/library/ecp.c"

# 91 "L:/PLT/pcac/mbedTLS/library/ecp.c"

# 310 "L:/PLT/pcac/mbedTLS/library/ecp.c"







static void mpi_init_many(mbedtls_mpi *arr, size_t size)
{
    while (size--) {
        mbedtls_mpi_init(arr++);
    }
}

static void mpi_free_many(mbedtls_mpi *arr, size_t size)
{
    while (size--) {
        mbedtls_mpi_free(arr++);
    }
}


/*
 * List of supported curves:
 *  - internal ID
 *  - TLS NamedCurve ID (RFC 4492 sec. 5.1.1, RFC 7071 sec. 2, RFC 8446 sec. 4.2.7)
 *  - size in bits
 *  - readable name
 *
 * Curves are listed in order: largest curves first, and for a given size,
 * fastest curves first.
 *
 * Reminder: update profiles in x509_crt.c and ssl_tls.c when adding a new curve!
 */
static const mbedtls_ecp_curve_info ecp_supported_curves[] =
{
# 353 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    { MBEDTLS_ECP_DP_SECP384R1,    24,     384,    "secp384r1"         },





    { MBEDTLS_ECP_DP_SECP256R1,    23,     256,    "secp256r1"         },
# 380 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    { MBEDTLS_ECP_DP_CURVE25519,   29,     256,    "x25519"            },




    { MBEDTLS_ECP_DP_NONE,          0,     0,      0                },
};




static mbedtls_ecp_group_id ecp_supported_grp_id[sizeof(ecp_supported_curves) / sizeof(ecp_supported_curves[0])];

/*
 * List of supported curves and associated info
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_list(void)
{
    return ecp_supported_curves;
}

/*
 * List of supported curves, group ID only
 */
const mbedtls_ecp_group_id *mbedtls_ecp_grp_id_list(void)
{
    static int init_done = 0;

    if (!init_done) {
        size_t i = 0;
        const mbedtls_ecp_curve_info *curve_info;

        for (curve_info = mbedtls_ecp_curve_list();
             curve_info->grp_id != MBEDTLS_ECP_DP_NONE;
             curve_info++) {
            ecp_supported_grp_id[i++] = curve_info->grp_id;
        }
        ecp_supported_grp_id[i] = MBEDTLS_ECP_DP_NONE;

        init_done = 1;
    }

    return ecp_supported_grp_id;
}

/*
 * Get the curve info for the internal identifier
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_info_from_grp_id(mbedtls_ecp_group_id grp_id)
{
    const mbedtls_ecp_curve_info *curve_info;

    for (curve_info = mbedtls_ecp_curve_list();
         curve_info->grp_id != MBEDTLS_ECP_DP_NONE;
         curve_info++) {
        if (curve_info->grp_id == grp_id) {
            return curve_info;
        }
    }

    return 0;
}

/*
 * Get the curve info from the TLS identifier
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_info_from_tls_id(uint16_t tls_id)
{
    const mbedtls_ecp_curve_info *curve_info;

    for (curve_info = mbedtls_ecp_curve_list();
         curve_info->grp_id != MBEDTLS_ECP_DP_NONE;
         curve_info++) {
        if (curve_info->tls_id == tls_id) {
            return curve_info;
        }
    }

    return 0;
}

/*
 * Get the curve info from the name
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_info_from_name(const char *name)
{
    const mbedtls_ecp_curve_info *curve_info;

    if (name == 0) {
        return 0;
    }

    for (curve_info = mbedtls_ecp_curve_list();
         curve_info->grp_id != MBEDTLS_ECP_DP_NONE;
         curve_info++) {
        if (strcmp(curve_info->name, name) == 0) {
            return curve_info;
        }
    }

    return 0;
}

/*
 * Get the type of a curve
 */
mbedtls_ecp_curve_type mbedtls_ecp_get_type(const mbedtls_ecp_group *grp)
{
    if (grp->G.X.p == 0) {
        return MBEDTLS_ECP_TYPE_NONE;
    }

    if (grp->G.Y.p == 0) {
        return MBEDTLS_ECP_TYPE_MONTGOMERY;
    } else {
        return MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS;
    }
}

/*
 * Initialize (the components of) a point
 */
void mbedtls_ecp_point_init(mbedtls_ecp_point *pt)
{
    mbedtls_mpi_init(&pt->X);
    mbedtls_mpi_init(&pt->Y);
    mbedtls_mpi_init(&pt->Z);
}

/*
 * Initialize (the components of) a group
 */
void mbedtls_ecp_group_init(mbedtls_ecp_group *grp)
{
    grp->id = MBEDTLS_ECP_DP_NONE;
    mbedtls_mpi_init(&grp->P);
    mbedtls_mpi_init(&grp->A);
    mbedtls_mpi_init(&grp->B);
    mbedtls_ecp_point_init(&grp->G);
    mbedtls_mpi_init(&grp->N);
    grp->pbits = 0;
    grp->nbits = 0;
    grp->h = 0;
    grp->modp = 0;
    grp->t_pre = 0;
    grp->t_post = 0;
    grp->t_data = 0;
    grp->T = 0;
    grp->T_size = 0;
}

/*
 * Initialize (the components of) a key pair
 */
void mbedtls_ecp_keypair_init(mbedtls_ecp_keypair *key)
{
    mbedtls_ecp_group_init(&key->grp);
    mbedtls_mpi_init(&key->d);
    mbedtls_ecp_point_init(&key->Q);
}

/*
 * Unallocate (the components of) a point
 */
void mbedtls_ecp_point_free(mbedtls_ecp_point *pt)
{
    if (pt == 0) {
        return;
    }

    mbedtls_mpi_free(&(pt->X));
    mbedtls_mpi_free(&(pt->Y));
    mbedtls_mpi_free(&(pt->Z));
}

/*
 * Check that the comb table (grp->T) is static initialized.
 */
static int ecp_group_is_static_comb_table(const mbedtls_ecp_group *grp)
{

    return grp->T != 0 && grp->T_size == 0;




}

/*
 * Unallocate (the components of) a group
 */
void mbedtls_ecp_group_free(mbedtls_ecp_group *grp)
{
    size_t i;

    if (grp == 0) {
        return;
    }

    if (grp->h != 1) {
        mbedtls_mpi_free(&grp->A);
        mbedtls_mpi_free(&grp->B);
        mbedtls_ecp_point_free(&grp->G);


        mbedtls_mpi_free(&grp->N);
        mbedtls_mpi_free(&grp->P);

    }

    if (!ecp_group_is_static_comb_table(grp) && grp->T != 0) {
        for (i = 0; i < grp->T_size; i++) {
            mbedtls_ecp_point_free(&grp->T[i]);
        }
        mbedtls_free(grp->T);
    }

    mbedtls_platform_zeroize(grp, sizeof(mbedtls_ecp_group));
}

/*
 * Unallocate (the components of) a key pair
 */
void mbedtls_ecp_keypair_free(mbedtls_ecp_keypair *key)
{
    if (key == 0) {
        return;
    }

    mbedtls_ecp_group_free(&key->grp);
    mbedtls_mpi_free(&key->d);
    mbedtls_ecp_point_free(&key->Q);
}

/*
 * Copy the contents of a point
 */
int mbedtls_ecp_copy(mbedtls_ecp_point *P, const mbedtls_ecp_point *Q)
{
    int ret = -0x006E;
    do { if ((ret = (mbedtls_mpi_copy(&P->X, &Q->X))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_copy(&P->Y, &Q->Y))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_copy(&P->Z, &Q->Z))) != 0) goto cleanup; } while (0);

cleanup:
    return ret;
}

/*
 * Copy the contents of a group object
 */
int mbedtls_ecp_group_copy(mbedtls_ecp_group *dst, const mbedtls_ecp_group *src)
{
    return mbedtls_ecp_group_load(dst, src->id);
}

/*
 * Set point to zero
 */
int mbedtls_ecp_set_zero(mbedtls_ecp_point *pt)
{
    int ret = -0x006E;
    do { if ((ret = (mbedtls_mpi_lset(&pt->X, 1))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_lset(&pt->Y, 1))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_lset(&pt->Z, 0))) != 0) goto cleanup; } while (0);

cleanup:
    return ret;
}

/*
 * Tell if a point is zero
 */
int mbedtls_ecp_is_zero(mbedtls_ecp_point *pt)
{
    return mbedtls_mpi_cmp_int(&pt->Z, 0) == 0;
}

/*
 * Compare two points lazily
 */
int mbedtls_ecp_point_cmp(const mbedtls_ecp_point *P,
                          const mbedtls_ecp_point *Q)
{
    if (mbedtls_mpi_cmp_mpi(&P->X, &Q->X) == 0 &&
        mbedtls_mpi_cmp_mpi(&P->Y, &Q->Y) == 0 &&
        mbedtls_mpi_cmp_mpi(&P->Z, &Q->Z) == 0) {
        return 0;
    }

    return -0x4F80;
}

/*
 * Import a non-zero point from ASCII strings
 */
int mbedtls_ecp_point_read_string(mbedtls_ecp_point *P, int radix,
                                  const char *x, const char *y)
{
    int ret = -0x006E;
    do { if ((ret = (mbedtls_mpi_read_string(&P->X, radix, x))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_read_string(&P->Y, radix, y))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_lset(&P->Z, 1))) != 0) goto cleanup; } while (0);

cleanup:
    return ret;
}

/*
 * Export a point into unsigned binary data (SEC1 2.3.3 and RFC7748)
 */
int mbedtls_ecp_point_write_binary(const mbedtls_ecp_group *grp,
                                   const mbedtls_ecp_point *P,
                                   int format, size_t *olen,
                                   unsigned char *buf, size_t buflen)
{
    int ret = -0x4E80;
    size_t plen;
    if (format != 0 &&
        format != 1) {
        return -0x4F80;
    }

    plen = mbedtls_mpi_size(&grp->P);


    (void) format; /* Montgomery curves always use the same point format */
    if (mbedtls_ecp_get_type(grp) == MBEDTLS_ECP_TYPE_MONTGOMERY) {
        *olen = plen;
        if (buflen < *olen) {
            return -0x4F00;
        }

        do { if ((ret = (mbedtls_mpi_write_binary_le(&P->X, buf, plen))) != 0) goto cleanup; } while (0);
    }


    if (mbedtls_ecp_get_type(grp) == MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS) {
        /*
         * Common case: P == 0
         */
        if (mbedtls_mpi_cmp_int(&P->Z, 0) == 0) {
            if (buflen < 1) {
                return -0x4F00;
            }

            buf[0] = 0x00;
            *olen = 1;

            return 0;
        }

        if (format == 0) {
            *olen = 2 * plen + 1;

            if (buflen < *olen) {
                return -0x4F00;
            }

            buf[0] = 0x04;
            do { if ((ret = (mbedtls_mpi_write_binary(&P->X, buf + 1, plen))) != 0) goto cleanup; } while (0);
            do { if ((ret = (mbedtls_mpi_write_binary(&P->Y, buf + 1 + plen, plen))) != 0) goto cleanup; } while (0);
        } else if (format == 1) {
            *olen = plen + 1;

            if (buflen < *olen) {
                return -0x4F00;
            }

            buf[0] = 0x02 + mbedtls_mpi_get_bit(&P->Y, 0);
            do { if ((ret = (mbedtls_mpi_write_binary(&P->X, buf + 1, plen))) != 0) goto cleanup; } while (0);
        }
    }


cleanup:
    return ret;
}


static int mbedtls_ecp_sw_derive_y(const mbedtls_ecp_group *grp,
                                   const mbedtls_mpi *X,
                                   mbedtls_mpi *Y,
                                   int parity_bit);


/*
 * Import a point from unsigned binary data (SEC1 2.3.4 and RFC7748)
 */
int mbedtls_ecp_point_read_binary(const mbedtls_ecp_group *grp,
                                  mbedtls_ecp_point *pt,
                                  const unsigned char *buf, size_t ilen)
{
    int ret = -0x4E80;
    size_t plen;
    if (ilen < 1) {
        return -0x4F80;
    }

    plen = mbedtls_mpi_size(&grp->P);


    if (mbedtls_ecp_get_type(grp) == MBEDTLS_ECP_TYPE_MONTGOMERY) {
        if (plen != ilen) {
            return -0x4F80;
        }

        do { if ((ret = (mbedtls_mpi_read_binary_le(&pt->X, buf, plen))) != 0) goto cleanup; } while (0);
        mbedtls_mpi_free(&pt->Y);

        if (grp->id == MBEDTLS_ECP_DP_CURVE25519) {
            /* Set most significant bit to 0 as prescribed in RFC7748 §5 */
            do { if ((ret = (mbedtls_mpi_set_bit(&pt->X, plen * 8 - 1, 0))) != 0) goto cleanup; } while (0);
        }

        do { if ((ret = (mbedtls_mpi_lset(&pt->Z, 1))) != 0) goto cleanup; } while (0);
    }


    if (mbedtls_ecp_get_type(grp) == MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS) {
        if (buf[0] == 0x00) {
            if (ilen == 1) {
                return mbedtls_ecp_set_zero(pt);
            } else {
                return -0x4F80;
            }
        }

        if (ilen < 1 + plen) {
            return -0x4F80;
        }

        do { if ((ret = (mbedtls_mpi_read_binary(&pt->X, buf + 1, plen))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_mpi_lset(&pt->Z, 1))) != 0) goto cleanup; } while (0);

        if (buf[0] == 0x04) {
            /* format == MBEDTLS_ECP_PF_UNCOMPRESSED */
            if (ilen != 1 + plen * 2) {
                return -0x4F80;
            }
            return mbedtls_mpi_read_binary(&pt->Y, buf + 1 + plen, plen);
        } else if (buf[0] == 0x02 || buf[0] == 0x03) {
            /* format == MBEDTLS_ECP_PF_COMPRESSED */
            if (ilen != 1 + plen) {
                return -0x4F80;
            }
            return mbedtls_ecp_sw_derive_y(grp, &pt->X, &pt->Y,
                                           (buf[0] & 1));
        } else {
            return -0x4F80;
        }
    }


cleanup:
    return ret;
}

/*
 * Import a point from a TLS ECPoint record (RFC 4492)
 *      struct {
 *          opaque point <1..2^8-1>;
 *      } ECPoint;
 */
int mbedtls_ecp_tls_read_point(const mbedtls_ecp_group *grp,
                               mbedtls_ecp_point *pt,
                               const unsigned char **buf, size_t buf_len)
{
    unsigned char data_len;
    const unsigned char *buf_start;
    /*
     * We must have at least two bytes (1 for length, at least one for data)
     */
    if (buf_len < 2) {
        return -0x4F80;
    }

    data_len = *(*buf)++;
    if (data_len < 1 || data_len > buf_len - 1) {
        return -0x4F80;
    }

    /*
     * Save buffer start for read_binary and update buf
     */
    buf_start = *buf;
    *buf += data_len;

    return mbedtls_ecp_point_read_binary(grp, pt, buf_start, data_len);
}

/*
 * Export a point as a TLS ECPoint record (RFC 4492)
 *      struct {
 *          opaque point <1..2^8-1>;
 *      } ECPoint;
 */
int mbedtls_ecp_tls_write_point(const mbedtls_ecp_group *grp, const mbedtls_ecp_point *pt,
                                int format, size_t *olen,
                                unsigned char *buf, size_t blen)
{
    int ret = -0x006E;
    if (format != 0 &&
        format != 1) {
        return -0x4F80;
    }

    /*
     * buffer length must be at least one, for our length byte
     */
    if (blen < 1) {
        return -0x4F80;
    }

    if ((ret = mbedtls_ecp_point_write_binary(grp, pt, format,
                                              olen, buf + 1, blen - 1)) != 0) {
        return ret;
    }

    /*
     * write length to the first byte and update total length
     */
    buf[0] = (unsigned char) *olen;
    ++*olen;

    return 0;
}

/*
 * Set a group from an ECParameters record (RFC 4492)
 */
int mbedtls_ecp_tls_read_group(mbedtls_ecp_group *grp,
                               const unsigned char **buf, size_t len)
{
    int ret = -0x006E;
    mbedtls_ecp_group_id grp_id;
    if ((ret = mbedtls_ecp_tls_read_group_id(&grp_id, buf, len)) != 0) {
        return ret;
    }

    return mbedtls_ecp_group_load(grp, grp_id);
}

/*
 * Read a group id from an ECParameters record (RFC 4492) and convert it to
 * mbedtls_ecp_group_id.
 */
int mbedtls_ecp_tls_read_group_id(mbedtls_ecp_group_id *grp,
                                  const unsigned char **buf, size_t len)
{
    uint16_t tls_id;
    const mbedtls_ecp_curve_info *curve_info;
    /*
     * We expect at least three bytes (see below)
     */
    if (len < 3) {
        return -0x4F80;
    }

    /*
     * First byte is curve_type; only named_curve is handled
     */
    if (*(*buf)++ != 3) {
        return -0x4F80;
    }

    /*
     * Next two bytes are the namedcurve value
     */
    tls_id = (((*((unsigned char *) (&mbedtls_byte_order_detector)) == 0x01)) ? mbedtls_get_unaligned_uint16((*buf) + (0)) : mbedtls_bswap16(mbedtls_get_unaligned_uint16((*buf) + (0))) );
    *buf += 2;

    if ((curve_info = mbedtls_ecp_curve_info_from_tls_id(tls_id)) == 0) {
        return -0x4E80;
    }

    *grp = curve_info->grp_id;

    return 0;
}

/*
 * Write the ECParameters record corresponding to a group (RFC 4492)
 */
int mbedtls_ecp_tls_write_group(const mbedtls_ecp_group *grp, size_t *olen,
                                unsigned char *buf, size_t blen)
{
    const mbedtls_ecp_curve_info *curve_info;
    if ((curve_info = mbedtls_ecp_curve_info_from_grp_id(grp->id)) == 0) {
        return -0x4F80;
    }

    /*
     * We are going to write 3 bytes (see below)
     */
    *olen = 3;
    if (blen < *olen) {
        return -0x4F00;
    }

    /*
     * First byte is curve_type, always named_curve
     */
    *buf++ = 3;

    /*
     * Next two bytes are the namedcurve value
     */
    { if ((*((unsigned char *) (&mbedtls_byte_order_detector)) == 0x01)) { mbedtls_put_unaligned_uint16((buf) + (0), (uint16_t) (curve_info->tls_id)); } else { mbedtls_put_unaligned_uint16((buf) + (0), mbedtls_bswap16((uint16_t) (curve_info->tls_id))); } };

    return 0;
}

/*
 * Wrapper around fast quasi-modp functions, with fall-back to mbedtls_mpi_mod_mpi.
 * See the documentation of struct mbedtls_ecp_group.
 *
 * This function is in the critial loop for mbedtls_ecp_mul, so pay attention to perf.
 */
static int ecp_modp(mbedtls_mpi *N, const mbedtls_ecp_group *grp)
{
    int ret = -0x006E;

    if (grp->modp == 0) {
        return mbedtls_mpi_mod_mpi(N, N, &grp->P);
    }

    /* N->s < 0 is a much faster test, which fails only if N is 0 */
    if ((N->s < 0 && mbedtls_mpi_cmp_int(N, 0) != 0) ||
        mbedtls_mpi_bitlen(N) > 2 * grp->pbits) {
        return -0x4F80;
    }

    do { if ((ret = (grp->modp(N))) != 0) goto cleanup; } while (0);

    /* N->s < 0 is a much faster test, which fails only if N is 0 */
    while (N->s < 0 && mbedtls_mpi_cmp_int(N, 0) != 0) {
        do { if ((ret = (mbedtls_mpi_add_mpi(N, N, &grp->P))) != 0) goto cleanup; } while (0);
    }

    while (mbedtls_mpi_cmp_mpi(N, &grp->P) >= 0) {
        /* we known P, N and the result are positive */
        do { if ((ret = (mbedtls_mpi_sub_abs(N, N, &grp->P))) != 0) goto cleanup; } while (0);
    }

cleanup:
    return ret;
}

/*
 * Fast mod-p functions expect their argument to be in the 0..p^2 range.
 *
 * In order to guarantee that, we need to ensure that operands of
 * mbedtls_mpi_mul_mpi are in the 0..p range. So, after each operation we will
 * bring the result back to this range.
 *
 * The following macros are shortcuts for doing that.
 */

/*
 * Reduce a mbedtls_mpi mod p in-place, general case, to use after mbedtls_mpi_mul_mpi
 */






# 1054 "L:/PLT/pcac/mbedTLS/library/ecp.c"

static __inline int mbedtls_mpi_mul_mod(const mbedtls_ecp_group *grp,
                                      mbedtls_mpi *X,
                                      const mbedtls_mpi *A,
                                      const mbedtls_mpi *B)
{
    int ret = -0x006E;
    do { if ((ret = (mbedtls_mpi_mul_mpi(X, A, B))) != 0) goto cleanup; } while (0);
    do { do { if ((ret = (ecp_modp(&(*X), grp))) != 0) goto cleanup; } while (0);  } while (0);
cleanup:
    return ret;
}

/*
 * Reduce a mbedtls_mpi mod p in-place, to use after mbedtls_mpi_sub_mpi
 * N->s < 0 is a very fast test, which fails only if N is 0
 */






__attribute__((unused))
static __inline int mbedtls_mpi_sub_mod(const mbedtls_ecp_group *grp,
                                      mbedtls_mpi *X,
                                      const mbedtls_mpi *A,
                                      const mbedtls_mpi *B)
{
    int ret = -0x006E;
    do { if ((ret = (mbedtls_mpi_sub_mpi(X, A, B))) != 0) goto cleanup; } while (0);
    do { while ((X)->s < 0 && mbedtls_mpi_cmp_int((X), 0) != 0) do { if ((ret = (mbedtls_mpi_add_mpi((X), (X), &grp->P))) != 0) goto cleanup; } while (0); } while (0);
cleanup:
    return ret;
}

/*
 * Reduce a mbedtls_mpi mod p in-place, to use after mbedtls_mpi_add_mpi and mbedtls_mpi_mul_int.
 * We known P, N and the result are positive, so sub_abs is correct, and
 * a bit faster.
 */




static __inline int mbedtls_mpi_add_mod(const mbedtls_ecp_group *grp,
                                      mbedtls_mpi *X,
                                      const mbedtls_mpi *A,
                                      const mbedtls_mpi *B)
{
    int ret = -0x006E;
    do { if ((ret = (mbedtls_mpi_add_mpi(X, A, B))) != 0) goto cleanup; } while (0);
    while (mbedtls_mpi_cmp_mpi((X), &grp->P) >= 0) do { if ((ret = (mbedtls_mpi_sub_abs((X), (X), &grp->P))) != 0) goto cleanup; } while (0);
cleanup:
    return ret;
}

__attribute__((unused))
static __inline int mbedtls_mpi_mul_int_mod(const mbedtls_ecp_group *grp,
                                          mbedtls_mpi *X,
                                          const mbedtls_mpi *A,
                                          mbedtls_mpi_uint c)
{
    int ret = -0x006E;

    do { if ((ret = (mbedtls_mpi_mul_int(X, A, c))) != 0) goto cleanup; } while (0);
    while (mbedtls_mpi_cmp_mpi((X), &grp->P) >= 0) do { if ((ret = (mbedtls_mpi_sub_abs((X), (X), &grp->P))) != 0) goto cleanup; } while (0);
cleanup:
    return ret;
}

__attribute__((unused))
static __inline int mbedtls_mpi_sub_int_mod(const mbedtls_ecp_group *grp,
                                          mbedtls_mpi *X,
                                          const mbedtls_mpi *A,
                                          mbedtls_mpi_uint c)
{
    int ret = -0x006E;

    do { if ((ret = (mbedtls_mpi_sub_int(X, A, c))) != 0) goto cleanup; } while (0);
    do { while ((X)->s < 0 && mbedtls_mpi_cmp_int((X), 0) != 0) do { if ((ret = (mbedtls_mpi_add_mpi((X), (X), &grp->P))) != 0) goto cleanup; } while (0); } while (0);
cleanup:
    return ret;
}




__attribute__((unused))
static __inline int mbedtls_mpi_shift_l_mod(const mbedtls_ecp_group *grp,
                                          mbedtls_mpi *X,
                                          size_t count)
{
    int ret = -0x006E;
    do { if ((ret = (mbedtls_mpi_shift_l(X, count))) != 0) goto cleanup; } while (0);
    while (mbedtls_mpi_cmp_mpi((X), &grp->P) >= 0) do { if ((ret = (mbedtls_mpi_sub_abs((X), (X), &grp->P))) != 0) goto cleanup; } while (0);
cleanup:
    return ret;
}

/*
 * Macro wrappers around ECP modular arithmetic
 *
 * Currently, these wrappers are defined via the bignum module.
 */


































/* Needs f_rng, p_rng to be defined. */



/* Conditional negation
 * Needs grp and a temporary MPI tmp to be defined. */
# 1207 "L:/PLT/pcac/mbedTLS/library/ecp.c"














/*
 * Computes the right-hand side of the Short Weierstrass equation
 * RHS = X^3 + A X + B
 */
static int ecp_sw_rhs(const mbedtls_ecp_group *grp,
                      mbedtls_mpi *rhs,
                      const mbedtls_mpi *X)
{
    int ret;

    /* Compute X^3 + A X + B as X (X^2 + A) + B */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, rhs, X, X))) != 0) goto cleanup; } while (0);

    /* Special case for A = -3 */
    if (mbedtls_ecp_group_a_is_minus_3(grp)) {
        do { if ((ret = (mbedtls_mpi_sub_int_mod(grp, rhs, rhs, 3))) != 0) goto cleanup; } while (0);
    } else {
        do { if ((ret = (mbedtls_mpi_add_mod(grp, rhs, rhs, &grp->A))) != 0) goto cleanup; } while (0);
    }

    do { if ((ret = (mbedtls_mpi_mul_mod(grp, rhs, rhs, X))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_add_mod(grp, rhs, rhs, &grp->B))) != 0) goto cleanup; } while (0);

cleanup:
    return ret;
}

/*
 * Derive Y from X and a parity bit
 */
static int mbedtls_ecp_sw_derive_y(const mbedtls_ecp_group *grp,
                                   const mbedtls_mpi *X,
                                   mbedtls_mpi *Y,
                                   int parity_bit)
{
    /* w = y^2 = x^3 + ax + b
     * y = sqrt(w) = w^((p+1)/4) mod p   (for prime p where p = 3 mod 4)
     *
     * Note: this method for extracting square root does not validate that w
     * was indeed a square so this function will return garbage in Y if X
     * does not correspond to a point on the curve.
     */

    /* Check prerequisite p = 3 mod 4 */
    if (mbedtls_mpi_get_bit(&grp->P, 0) != 1 ||
        mbedtls_mpi_get_bit(&grp->P, 1) != 1) {
        return -0x4E80;
    }

    int ret;
    mbedtls_mpi exp;
    mbedtls_mpi_init(&exp);

    /* use Y to store intermediate result, actually w above */
    do { if ((ret = (ecp_sw_rhs(grp, Y, X))) != 0) goto cleanup; } while (0);

    /* w = y^2 */ /* Y contains y^2 intermediate result */
    /* exp = ((p+1)/4) */
    do { if ((ret = (mbedtls_mpi_add_int(&exp, &grp->P, 1))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_shift_r(&exp, 2))) != 0) goto cleanup; } while (0);
    /* sqrt(w) = w^((p+1)/4) mod p   (for prime p where p = 3 mod 4) */
    do { if ((ret = (mbedtls_mpi_exp_mod(Y, Y , &exp, &grp->P, 0))) != 0) goto cleanup; } while (0);

    /* check parity bit match or else invert Y */
    /* This quick inversion implementation is valid because Y != 0 for all
     * Short Weierstrass curves supported by mbedtls, as each supported curve
     * has an order that is a large prime, so each supported curve does not
     * have any point of order 2, and a point with Y == 0 would be of order 2 */
    if (mbedtls_mpi_get_bit(Y, 0) != parity_bit) {
        do { if ((ret = (mbedtls_mpi_sub_mpi(Y, &grp->P, Y))) != 0) goto cleanup; } while (0);
    }

cleanup:

    mbedtls_mpi_free(&exp);
    return ret;
}




/*
 * For curves in short Weierstrass form, we do all the internal operations in
 * Jacobian coordinates.
 *
 * For multiplication, we'll use a comb method with countermeasures against
 * SPA, hence timing attacks.
 */

/*
 * Normalize jacobian coordinates so that Z == 0 || Z == 1  (GECC 3.2.1)
 * Cost: 1N := 1I + 3M + 1S
 */
static int ecp_normalize_jac(const mbedtls_ecp_group *grp, mbedtls_ecp_point *pt)
{
    if (mbedtls_mpi_cmp_int(&pt->Z, 0) == 0) {
        return 0;
    }










    int ret = -0x006E;
    mbedtls_mpi T;
    mbedtls_mpi_init(&T);

    do { if ((ret = (mbedtls_mpi_inv_mod((&T), (&pt->Z), &grp->P))) != 0) goto cleanup; } while (0);            /* T   <-          1 / Z   */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &pt->Y, &pt->Y, &T))) != 0) goto cleanup; } while (0);    /* Y'  <- Y*T    = Y / Z   */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &T, &T, &T))) != 0) goto cleanup; } while (0);                /* T   <- T^2    = 1 / Z^2 */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &pt->X, &pt->X, &T))) != 0) goto cleanup; } while (0);    /* X   <- X  * T = X / Z^2 */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &pt->Y, &pt->Y, &T))) != 0) goto cleanup; } while (0);    /* Y'' <- Y' * T = Y / Z^3 */

    do { if ((ret = (mbedtls_mpi_lset(&pt->Z, 1))) != 0) goto cleanup; } while (0);

cleanup:

    mbedtls_mpi_free(&T);

    return ret;

}

/*
 * Normalize jacobian coordinates of an array of (pointers to) points,
 * using Montgomery's trick to perform only one inversion mod P.
 * (See for example Cohen's "A Course in Computational Algebraic Number
 * Theory", Algorithm 10.3.4.)
 *
 * Warning: fails (returning an error) if one of the points is zero!
 * This should never happen, see choice of w in ecp_mul_comb().
 *
 * Cost: 1N(t) := 1I + (6t - 3)M + 1S
 */
static int ecp_normalize_jac_many(const mbedtls_ecp_group *grp,
                                  mbedtls_ecp_point *T[], size_t T_size)
{
    if (T_size < 2) {
        return ecp_normalize_jac(grp, *T);
    }










    int ret = -0x006E;
    size_t i;
    mbedtls_mpi *c, t;

    if ((c = mbedtls_calloc(T_size, sizeof(mbedtls_mpi))) == 0) {
        return -0x4D80;
    }

    mbedtls_mpi_init(&t);

    mpi_init_many(c, T_size);
    /*
     * c[i] = Z_0 * ... * Z_i,   i = 0,..,n := T_size-1
     */
    do { if ((ret = (mbedtls_mpi_copy(&c[0], &T[0]->Z))) != 0) goto cleanup; } while (0);
    for (i = 1; i < T_size; i++) {
        do { if ((ret = (mbedtls_mpi_mul_mod(grp, &c[i], &c[i-1], &T[i]->Z))) != 0) goto cleanup; } while (0);
    }

    /*
     * c[n] = 1 / (Z_0 * ... * Z_n) mod P
     */
    do { if ((ret = (mbedtls_mpi_inv_mod((&c[T_size - 1]), (&c[T_size - 1]), &grp->P))) != 0) goto cleanup; } while (0);

    for (i = T_size - 1;; i--) {
        /* At the start of iteration i (note that i decrements), we have
         * - c[j] = Z_0 * .... * Z_j        for j  < i,
         * - c[j] = 1 / (Z_0 * .... * Z_j)  for j == i,
         *
         * This is maintained via
         * - c[i-1] <- c[i] * Z_i
         *
         * We also derive 1/Z_i = c[i] * c[i-1] for i>0 and use that
         * to do the actual normalization. For i==0, we already have
         * c[0] = 1 / Z_0.
         */

        if (i > 0) {
            /* Compute 1/Z_i and establish invariant for the next iteration. */
            do { if ((ret = (mbedtls_mpi_mul_mod(grp, &t, &c[i], &c[i-1]))) != 0) goto cleanup; } while (0);
            do { if ((ret = (mbedtls_mpi_mul_mod(grp, &c[i-1], &c[i], &T[i]->Z))) != 0) goto cleanup; } while (0);
        } else {
            do { if ((ret = (mbedtls_mpi_copy(&t, &c[0]))) != 0) goto cleanup; } while (0);
        }

        /* Now t holds 1 / Z_i; normalize as in ecp_normalize_jac() */
        do { if ((ret = (mbedtls_mpi_mul_mod(grp, &T[i]->Y, &T[i]->Y, &t))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_mpi_mul_mod(grp, &t, &t, &t))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_mpi_mul_mod(grp, &T[i]->X, &T[i]->X, &t))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_mpi_mul_mod(grp, &T[i]->Y, &T[i]->Y, &t))) != 0) goto cleanup; } while (0);

        /*
         * Post-precessing: reclaim some memory by shrinking coordinates
         * - not storing Z (always 1)
         * - shrinking other coordinates, but still keeping the same number of
         *   limbs as P, as otherwise it will too likely be regrown too fast.
         */
        do { if ((ret = (mbedtls_mpi_shrink(&T[i]->X, grp->P . n))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_mpi_shrink(&T[i]->Y, grp->P . n))) != 0) goto cleanup; } while (0);

        do { if ((ret = (mbedtls_mpi_lset(&T[i]->Z, 1))) != 0) goto cleanup; } while (0);

        if (i == 0) {
            break;
        }
    }

cleanup:

    mbedtls_mpi_free(&t);
    mpi_free_many(c, T_size);
    mbedtls_free(c);

    return ret;

}

/*
 * Conditional point inversion: Q -> -Q = (Q.X, -Q.Y, Q.Z) without leak.
 * "inv" must be 0 (don't invert) or 1 (invert) or the result will be invalid
 */
static int ecp_safe_invert_jac(const mbedtls_ecp_group *grp,
                               mbedtls_ecp_point *Q,
                               unsigned char inv)
{
    int ret = -0x006E;
    mbedtls_mpi tmp;
    mbedtls_mpi_init(&tmp);

    do { unsigned char nonzero = mbedtls_mpi_cmp_int((&Q->Y), 0) != 0; do { if ((ret = (mbedtls_mpi_sub_mpi(&tmp, &grp->P, (&Q->Y)))) != 0) goto cleanup; } while (0); do { if ((ret = (mbedtls_mpi_safe_cond_assign((&Q->Y), &tmp, nonzero & inv))) != 0) goto cleanup; } while (0); } while (0);

cleanup:
    mbedtls_mpi_free(&tmp);
    return ret;
}

/*
 * Point doubling R = 2 P, Jacobian coordinates
 *
 * Based on http://www.hyperelliptic.org/EFD/g1p/auto-shortw-jacobian.html#doubling-dbl-1998-cmo-2 .
 *
 * We follow the variable naming fairly closely. The formula variations that trade a MUL for a SQR
 * (plus a few ADDs) aren't useful as our bignum implementation doesn't distinguish squaring.
 *
 * Standard optimizations are applied when curve parameter A is one of { 0, -3 }.
 *
 * Cost: 1D := 3M + 4S          (A ==  0)
 *             4M + 4S          (A == -3)
 *             3M + 6S + 1a     otherwise
 */
static int ecp_double_jac(const mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
                          const mbedtls_ecp_point *P,
                          mbedtls_mpi tmp[4])
{













    int ret = -0x006E;

    /* Special case for A = -3 */
    if (mbedtls_ecp_group_a_is_minus_3(grp)) {
        /* tmp[0] <- M = 3(X + Z^2)(X - Z^2) */
        do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[1], &P->Z, &P->Z))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_mpi_add_mod(grp, &tmp[2], &P->X, &tmp[1]))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_mpi_sub_mod(grp, &tmp[3], &P->X, &tmp[1]))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[1], &tmp[2], &tmp[3]))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_mpi_mul_int_mod(grp, &tmp[0], &tmp[1], 3))) != 0) goto cleanup; } while (0);
    } else {
        /* tmp[0] <- M = 3.X^2 + A.Z^4 */
        do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[1], &P->X, &P->X))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_mpi_mul_int_mod(grp, &tmp[0], &tmp[1], 3))) != 0) goto cleanup; } while (0);

        /* Optimize away for "koblitz" curves with A = 0 */
        if (mbedtls_mpi_cmp_int(&grp->A, 0) != 0) {
            /* M += A.Z^4 */
            do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[1], &P->Z, &P->Z))) != 0) goto cleanup; } while (0);
            do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[2], &tmp[1], &tmp[1]))) != 0) goto cleanup; } while (0);
            do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[1], &tmp[2], &grp->A))) != 0) goto cleanup; } while (0);
            do { if ((ret = (mbedtls_mpi_add_mod(grp, &tmp[0], &tmp[0], &tmp[1]))) != 0) goto cleanup; } while (0);
        }
    }

    /* tmp[1] <- S = 4.X.Y^2 */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[2], &P->Y, &P->Y))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_shift_l_mod(grp, &tmp[2], 1))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[1], &P->X, &tmp[2]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_shift_l_mod(grp, &tmp[1], 1))) != 0) goto cleanup; } while (0);

    /* tmp[3] <- U = 8.Y^4 */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[3], &tmp[2], &tmp[2]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_shift_l_mod(grp, &tmp[3], 1))) != 0) goto cleanup; } while (0);

    /* tmp[2] <- T = M^2 - 2.S */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[2], &tmp[0], &tmp[0]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_sub_mod(grp, &tmp[2], &tmp[2], &tmp[1]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_sub_mod(grp, &tmp[2], &tmp[2], &tmp[1]))) != 0) goto cleanup; } while (0);

    /* tmp[1] <- S = M(S - T) - U */
    do { if ((ret = (mbedtls_mpi_sub_mod(grp, &tmp[1], &tmp[1], &tmp[2]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[1], &tmp[1], &tmp[0]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_sub_mod(grp, &tmp[1], &tmp[1], &tmp[3]))) != 0) goto cleanup; } while (0);

    /* tmp[3] <- U = 2.Y.Z */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[3], &P->Y, &P->Z))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_shift_l_mod(grp, &tmp[3], 1))) != 0) goto cleanup; } while (0);

    /* Store results */
    do { if ((ret = (mbedtls_mpi_copy(&R->X, &tmp[2]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_copy(&R->Y, &tmp[1]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_copy(&R->Z, &tmp[3]))) != 0) goto cleanup; } while (0);

cleanup:

    return ret;

}

/*
 * Addition: R = P + Q, mixed affine-Jacobian coordinates (GECC 3.22)
 *
 * The coordinates of Q must be normalized (= affine),
 * but those of P don't need to. R is not normalized.
 *
 * P,Q,R may alias, but only at the level of EC points: they must be either
 * equal as pointers, or disjoint (including the coordinate data buffers).
 * Fine-grained aliasing at the level of coordinates is not supported.
 *
 * Special cases: (1) P or Q is zero, (2) R is zero, (3) P == Q.
 * None of these cases can happen as intermediate step in ecp_mul_comb():
 * - at each step, P, Q and R are multiples of the base point, the factor
 *   being less than its order, so none of them is zero;
 * - Q is an odd multiple of the base point, P an even multiple,
 *   due to the choice of precomputed points in the modified comb method.
 * So branches for these cases do not leak secret information.
 *
 * Cost: 1A := 8M + 3S
 */
static int ecp_add_mixed(const mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
                         const mbedtls_ecp_point *P, const mbedtls_ecp_point *Q,
                         mbedtls_mpi tmp[4])
{













    int ret = -0x006E;

    /* NOTE: Aliasing between input and output is allowed, so one has to make
     *       sure that at the point X,Y,Z are written, {P,Q}->{X,Y,Z} are no
     *       longer read from. */
    mbedtls_mpi * const X = &R->X;
    mbedtls_mpi * const Y = &R->Y;
    mbedtls_mpi * const Z = &R->Z;

    if (!((&Q->Z)->p != 0)) {
        return -0x4F80;
    }

    /*
     * Trivial cases: P == 0 or Q == 0 (case 1)
     */
    if (mbedtls_mpi_cmp_int(&P->Z, 0) == 0) {
        return mbedtls_ecp_copy(R, Q);
    }

    if (mbedtls_mpi_cmp_int(&Q->Z, 0) == 0) {
        return mbedtls_ecp_copy(R, P);
    }

    /*
     * Make sure Q coordinates are normalized
     */
    if (mbedtls_mpi_cmp_int(&Q->Z, 1) != 0) {
        return -0x4F80;
    }

    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[0], &P->Z, &P->Z))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[1], &tmp[0], &P->Z))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[0], &tmp[0], &Q->X))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[1], &tmp[1], &Q->Y))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_sub_mod(grp, &tmp[0], &tmp[0], &P->X))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_sub_mod(grp, &tmp[1], &tmp[1], &P->Y))) != 0) goto cleanup; } while (0);

    /* Special cases (2) and (3) */
    if (mbedtls_mpi_cmp_int(&tmp[0], 0) == 0) {
        if (mbedtls_mpi_cmp_int(&tmp[1], 0) == 0) {
            ret = ecp_double_jac(grp, R, P, tmp);
            goto cleanup;
        } else {
            ret = mbedtls_ecp_set_zero(R);
            goto cleanup;
        }
    }

    /* {P,Q}->Z no longer used, so OK to write to Z even if there's aliasing. */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, Z, &P->Z, &tmp[0]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[2], &tmp[0], &tmp[0]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[3], &tmp[2], &tmp[0]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[2], &tmp[2], &P->X))) != 0) goto cleanup; } while (0);

    do { if ((ret = (mbedtls_mpi_copy(&tmp[0], &tmp[2]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_shift_l_mod(grp, &tmp[0], 1))) != 0) goto cleanup; } while (0);

    /* {P,Q}->X no longer used, so OK to write to X even if there's aliasing. */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, X, &tmp[1], &tmp[1]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_sub_mod(grp, X, X, &tmp[0]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_sub_mod(grp, X, X, &tmp[3]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_sub_mod(grp, &tmp[2], &tmp[2], X))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[2], &tmp[2], &tmp[1]))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &tmp[3], &tmp[3], &P->Y))) != 0) goto cleanup; } while (0);
    /* {P,Q}->Y no longer used, so OK to write to Y even if there's aliasing. */
    do { if ((ret = (mbedtls_mpi_sub_mod(grp, Y, &tmp[2], &tmp[3]))) != 0) goto cleanup; } while (0);

cleanup:

    return ret;

}

/*
 * Randomize jacobian coordinates:
 * (X, Y, Z) -> (l^2 X, l^3 Y, l Z) for random l
 * This is sort of the reverse operation of ecp_normalize_jac().
 *
 * This countermeasure was first suggested in [2].
 */
static int ecp_randomize_jac(const mbedtls_ecp_group *grp, mbedtls_ecp_point *pt,
                             int (*f_rng)(void *, unsigned char *, size_t), void *p_rng)
{









    int ret = -0x006E;
    mbedtls_mpi l;

    mbedtls_mpi_init(&l);

    /* Generate l such that 1 < l < p */
    do { if ((ret = (mbedtls_mpi_random((&l), 2, &grp->P, f_rng, p_rng))) != 0) goto cleanup; } while (0);

    /* Z' = l * Z */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &pt->Z, &pt->Z, &l))) != 0) goto cleanup; } while (0);

    /* Y' = l * Y */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &pt->Y, &pt->Y, &l))) != 0) goto cleanup; } while (0);

    /* X' = l^2 * X */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &l, &l, &l))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &pt->X, &pt->X, &l))) != 0) goto cleanup; } while (0);

    /* Y'' = l^2 * Y' = l^3 * Y */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &pt->Y, &pt->Y, &l))) != 0) goto cleanup; } while (0);

cleanup:
    mbedtls_mpi_free(&l);

    if (ret == -0x000E) {
        ret = -0x4D00;
    }
    return ret;

}

/*
 * Check and define parameters used by the comb method (see below for details)
 */




/* d = ceil( n / w ) */


/* number of precomputed points */


/*
 * Compute the representation of m that will be used with our comb method.
 *
 * The basic comb method is described in GECC 3.44 for example. We use a
 * modified version that provides resistance to SPA by avoiding zero
 * digits in the representation as in [3]. We modify the method further by
 * requiring that all K_i be odd, which has the small cost that our
 * representation uses one more K_i, due to carries, but saves on the size of
 * the precomputed table.
 *
 * Summary of the comb method and its modifications:
 *
 * - The goal is to compute m*P for some w*d-bit integer m.
 *
 * - The basic comb method splits m into the w-bit integers
 *   x[0] .. x[d-1] where x[i] consists of the bits in m whose
 *   index has residue i modulo d, and computes m * P as
 *   S[x[0]] + 2 * S[x[1]] + .. + 2^(d-1) S[x[d-1]], where
 *   S[i_{w-1} .. i_0] := i_{w-1} 2^{(w-1)d} P + ... + i_1 2^d P + i_0 P.
 *
 * - If it happens that, say, x[i+1]=0 (=> S[x[i+1]]=0), one can replace the sum by
 *    .. + 2^{i-1} S[x[i-1]] - 2^i S[x[i]] + 2^{i+1} S[x[i]] + 2^{i+2} S[x[i+2]] ..,
 *   thereby successively converting it into a form where all summands
 *   are nonzero, at the cost of negative summands. This is the basic idea of [3].
 *
 * - More generally, even if x[i+1] != 0, we can first transform the sum as
 *   .. - 2^i S[x[i]] + 2^{i+1} ( S[x[i]] + S[x[i+1]] ) + 2^{i+2} S[x[i+2]] ..,
 *   and then replace S[x[i]] + S[x[i+1]] = S[x[i] ^ x[i+1]] + 2 S[x[i] & x[i+1]].
 *   Performing and iterating this procedure for those x[i] that are even
 *   (keeping track of carry), we can transform the original sum into one of the form
 *   S[x'[0]] +- 2 S[x'[1]] +- .. +- 2^{d-1} S[x'[d-1]] + 2^d S[x'[d]]
 *   with all x'[i] odd. It is therefore only necessary to know S at odd indices,
 *   which is why we are only computing half of it in the first place in
 *   ecp_precompute_comb and accessing it with index abs(i) / 2 in ecp_select_comb.
 *
 * - For the sake of compactness, only the seven low-order bits of x[i]
 *   are used to represent its absolute value (K_i in the paper), and the msb
 *   of x[i] encodes the sign (s_i in the paper): it is set if and only if
 *   if s_i == -1;
 *
 * Calling conventions:
 * - x is an array of size d + 1
 * - w is the size, ie number of teeth, of the comb, and must be between
 *   2 and 7 (in practice, between 2 and MBEDTLS_ECP_WINDOW_SIZE)
 * - m is the MPI, expected to be odd and such that bitlength(m) <= w * d
 *   (the result will be incorrect if these assumptions are not satisfied)
 */
static void ecp_comb_recode_core(unsigned char x[], size_t d,
                                 unsigned char w, const mbedtls_mpi *m)
{
    size_t i, j;
    unsigned char c, cc, adjust;

    memset(x, 0, d+1);

    /* First get the classical comb values (except for x_d = 0) */
    for (i = 0; i < d; i++) {
        for (j = 0; j < w; j++) {
            x[i] |= mbedtls_mpi_get_bit(m, i + d * j) << j;
        }
    }

    /* Now make sure x_1 .. x_d are odd */
    c = 0;
    for (i = 1; i <= d; i++) {
        /* Add carry and update it */
        cc   = x[i] & c;
        x[i] = x[i] ^ c;
        c = cc;

        /* Adjust if needed, avoiding branches */
        adjust = 1 - (x[i] & 0x01);
        c   |= x[i] & (x[i-1] * adjust);
        x[i] = x[i] ^ (x[i-1] * adjust);
        x[i-1] |= adjust << 7;
    }
}

/*
 * Precompute points for the adapted comb method
 *
 * Assumption: T must be able to hold 2^{w - 1} elements.
 *
 * Operation: If i = i_{w-1} ... i_1 is the binary representation of i,
 *            sets T[i] = i_{w-1} 2^{(w-1)d} P + ... + i_1 2^d P + P.
 *
 * Cost: d(w-1) D + (2^{w-1} - 1) A + 1 N(w-1) + 1 N(2^{w-1} - 1)
 *
 * Note: Even comb values (those where P would be omitted from the
 *       sum defining T[i] above) are not needed in our adaption
 *       the comb method. See ecp_comb_recode_core().
 *
 * This function currently works in four steps:
 * (1) [dbl]      Computation of intermediate T[i] for 2-power values of i
 * (2) [norm_dbl] Normalization of coordinates of these T[i]
 * (3) [add]      Computation of all T[i]
 * (4) [norm_add] Normalization of all T[i]
 *
 * Step 1 can be interrupted but not the others; together with the final
 * coordinate normalization they are the largest steps done at once, depending
 * on the window size. Here are operation counts for P-256:
 *
 * step     (2)     (3)     (4)
 * w = 5    142     165     208
 * w = 4    136      77     160
 * w = 3    130      33     136
 * w = 2    124      11     124
 *
 * So if ECC operations are blocking for too long even with a low max_ops
 * value, it's useful to set MBEDTLS_ECP_WINDOW_SIZE to a lower value in order
 * to minimize maximum blocking time.
 */
static int ecp_precompute_comb(const mbedtls_ecp_group *grp,
                               mbedtls_ecp_point T[], const mbedtls_ecp_point *P,
                               unsigned char w, size_t d,
                               mbedtls_ecp_restart_ctx *rs_ctx)
{
    int ret = -0x006E;
    unsigned char i;
    size_t j = 0;
    const unsigned char T_size = 1U << (w - 1);
    mbedtls_ecp_point *cur, *TT[(1 << (4 - 1)) - 1] = { 0 };

    mbedtls_mpi tmp[4];

    mpi_init_many(tmp, sizeof(tmp) / sizeof(mbedtls_mpi));

# 1880 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    (void) rs_ctx;


# 1893 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    /*
     * Set T[0] = P and
     * T[2^{l-1}] = 2^{dl} P for l = 1 .. w-1 (this is not the final value)
     */
    do { if ((ret = (mbedtls_ecp_copy(&T[0], P))) != 0) goto cleanup; } while (0);






    j = 0;

    for (; j < d * (w - 1); j++) {
        ;

        i = 1U << (j / d);
        cur = T + i;

        if (j % d == 0) {
            do { if ((ret = (mbedtls_ecp_copy(cur, T + (i >> 1)))) != 0) goto cleanup; } while (0);
        }

        do { if ((ret = (ecp_double_jac(grp, cur, cur, tmp))) != 0) goto cleanup; } while (0);
    }

# 1926 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    /*
     * Normalize current elements in T to allow them to be used in
     * ecp_add_mixed() below, which requires one normalized input.
     *
     * As T has holes, use an auxiliary array of pointers to elements in T.
     *
     */
    j = 0;
    for (i = 1; i < T_size; i <<= 1) {
        TT[j++] = T + i;
    }

    ;

    do { if ((ret = (ecp_normalize_jac_many(grp, TT, j))) != 0) goto cleanup; } while (0);

# 1949 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    /*
     * Compute the remaining ones using the minimal number of additions
     * Be careful to update T[2^l] only after using it!
     */
    ;

    for (i = 1; i < T_size; i <<= 1) {
        j = i;
        while (j--) {
            do { if ((ret = (ecp_add_mixed(grp, &T[i + j], &T[j], &T[i], tmp))) != 0) goto cleanup; } while (0);
        }
    }

# 1969 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    /*
     * Normalize final elements in T. Even though there are no holes now, we
     * still need the auxiliary array for homogeneity with the previous
     * call. Also, skip T[0] which is already normalised, being a copy of P.
     */
    for (j = 0; j + 1 < T_size; j++) {
        TT[j] = T + j + 1;
    }

    ;

    do { if ((ret = (ecp_normalize_jac_many(grp, TT, j))) != 0) goto cleanup; } while (0);

    /* Free Z coordinate (=1 after normalization) to save RAM.
     * This makes T[i] invalid as mbedtls_ecp_points, but this is OK
     * since from this point onwards, they are only accessed indirectly
     * via the getter function ecp_select_comb() which does set the
     * target's Z coordinate to 1. */
    for (i = 0; i < T_size; i++) {
        mbedtls_mpi_free(&T[i].Z);
    }

cleanup:

    mpi_free_many(tmp, sizeof(tmp) / sizeof(mbedtls_mpi));

# 2003 "L:/PLT/pcac/mbedTLS/library/ecp.c"

    return ret;
}

/*
 * Select precomputed point: R = sign(i) * T[ abs(i) / 2 ]
 *
 * See ecp_comb_recode_core() for background
 */
static int ecp_select_comb(const mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
                           const mbedtls_ecp_point T[], unsigned char T_size,
                           unsigned char i)
{
    int ret = -0x006E;
    unsigned char ii, j;

    /* Ignore the "sign" bit and scale down */
    ii =  (i & 0x7Fu) >> 1;

    /* Read the whole table to thwart cache-based timing attacks */
    for (j = 0; j < T_size; j++) {
        do { if ((ret = (mbedtls_mpi_safe_cond_assign((&R->X), (&T[j]. X), (j == ii)))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_mpi_safe_cond_assign((&R->Y), (&T[j]. Y), (j == ii)))) != 0) goto cleanup; } while (0);
    }

    /* Safely invert result if i is "negative" */
    do { if ((ret = (ecp_safe_invert_jac(grp, R, i >> 7))) != 0) goto cleanup; } while (0);

    do { if ((ret = (mbedtls_mpi_lset(&R->Z, 1))) != 0) goto cleanup; } while (0);

cleanup:
    return ret;
}

/*
 * Core multiplication algorithm for the (modified) comb method.
 * This part is actually common with the basic comb method (GECC 3.44)
 *
 * Cost: d A + d D + 1 R
 */
static int ecp_mul_comb_core(const mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
                             const mbedtls_ecp_point T[], unsigned char T_size,
                             const unsigned char x[], size_t d,
                             int (*f_rng)(void *, unsigned char *, size_t),
                             void *p_rng,
                             mbedtls_ecp_restart_ctx *rs_ctx)
{
    int ret = -0x006E;
    mbedtls_ecp_point Txi;
    mbedtls_mpi tmp[4];
    size_t i;

    mbedtls_ecp_point_init(&Txi);
    mpi_init_many(tmp, sizeof(tmp) / sizeof(mbedtls_mpi));


    (void) rs_ctx;


# 2075 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    {
        /* Start with a non-zero point and randomize its coordinates */
        i = d;
        do { if ((ret = (ecp_select_comb(grp, R, T, T_size, x[i]))) != 0) goto cleanup; } while (0);
        if (f_rng != 0) {
            do { if ((ret = (ecp_randomize_jac(grp, R, f_rng, p_rng))) != 0) goto cleanup; } while (0);
        }
    }

    while (i != 0) {
        ;
        --i;

        do { if ((ret = (ecp_double_jac(grp, R, R, tmp))) != 0) goto cleanup; } while (0);
        do { if ((ret = (ecp_select_comb(grp, &Txi, T, T_size, x[i]))) != 0) goto cleanup; } while (0);
        do { if ((ret = (ecp_add_mixed(grp, R, R, &Txi, tmp))) != 0) goto cleanup; } while (0);
    }

cleanup:

    mbedtls_ecp_point_free(&Txi);
    mpi_free_many(tmp, sizeof(tmp) / sizeof(mbedtls_mpi));

# 2105 "L:/PLT/pcac/mbedTLS/library/ecp.c"

    return ret;
}

/*
 * Recode the scalar to get constant-time comb multiplication
 *
 * As the actual scalar recoding needs an odd scalar as a starting point,
 * this wrapper ensures that by replacing m by N - m if necessary, and
 * informs the caller that the result of multiplication will be negated.
 *
 * This works because we only support large prime order for Short Weierstrass
 * curves, so N is always odd hence either m or N - m is.
 *
 * See ecp_comb_recode_core() for background.
 */
static int ecp_comb_recode_scalar(const mbedtls_ecp_group *grp,
                                  const mbedtls_mpi *m,
                                  unsigned char k[(384 + 1) / 2 + 1],
                                  size_t d,
                                  unsigned char w,
                                  unsigned char *parity_trick)
{
    int ret = -0x006E;
    mbedtls_mpi M, mm;

    mbedtls_mpi_init(&M);
    mbedtls_mpi_init(&mm);

    /* N is always odd (see above), just make extra sure */
    if (mbedtls_mpi_get_bit(&grp->N, 0) != 1) {
        return -0x4F80;
    }

    /* do we need the parity trick? */
    *parity_trick = (mbedtls_mpi_get_bit(m, 0) == 0);

    /* execute parity fix in constant time */
    do { if ((ret = (mbedtls_mpi_copy(&M, m))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_sub_mpi(&mm, &grp->N, m))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_safe_cond_assign(&M, &mm, *parity_trick))) != 0) goto cleanup; } while (0);

    /* actual scalar recoding */
    ecp_comb_recode_core(k, d, w, &M);

cleanup:
    mbedtls_mpi_free(&mm);
    mbedtls_mpi_free(&M);

    return ret;
}

/*
 * Perform comb multiplication (for short Weierstrass curves)
 * once the auxiliary table has been pre-computed.
 *
 * Scalar recoding may use a parity trick that makes us compute -m * P,
 * if that is the case we'll need to recover m * P at the end.
 */
static int ecp_mul_comb_after_precomp(const mbedtls_ecp_group *grp,
                                      mbedtls_ecp_point *R,
                                      const mbedtls_mpi *m,
                                      const mbedtls_ecp_point *T,
                                      unsigned char T_size,
                                      unsigned char w,
                                      size_t d,
                                      int (*f_rng)(void *, unsigned char *, size_t),
                                      void *p_rng,
                                      mbedtls_ecp_restart_ctx *rs_ctx)
{
    int ret = -0x006E;
    unsigned char parity_trick;
    unsigned char k[(384 + 1) / 2 + 1];
    mbedtls_ecp_point *RR = R;

# 2189 "L:/PLT/pcac/mbedTLS/library/ecp.c"

    do { if ((ret = (ecp_comb_recode_scalar(grp, m, k, d, w, &parity_trick))) != 0) goto cleanup; } while (0);

    do { if ((ret = (ecp_mul_comb_core(grp, RR, T, T_size, k, d, f_rng, p_rng, rs_ctx))) != 0) goto cleanup; } while (0);

    do { if ((ret = (ecp_safe_invert_jac(grp, RR, parity_trick))) != 0) goto cleanup; } while (0);

# 2204 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    /*
     * Knowledge of the jacobian coordinates may leak the last few bits of the
     * scalar [1], and since our MPI implementation isn't constant-flow,
     * inversion (used for coordinate normalization) may leak the full value
     * of its input via side-channels [2].
     *
     * [1] https://eprint.iacr.org/2003/191
     * [2] https://eprint.iacr.org/2020/055
     *
     * Avoid the leak by randomizing coordinates before we normalize them.
     */
    if (f_rng != 0) {
        do { if ((ret = (ecp_randomize_jac(grp, RR, f_rng, p_rng))) != 0) goto cleanup; } while (0);
    }

    do { if ((ret = (ecp_normalize_jac(grp, RR))) != 0) goto cleanup; } while (0);







cleanup:
    return ret;
}

/*
 * Pick window size based on curve size and whether we optimize for base point
 */
static unsigned char ecp_pick_window_size(const mbedtls_ecp_group *grp,
                                          unsigned char p_eq_g)
{
    unsigned char w;

    /*
     * Minimize the number of multiplications, that is minimize
     * 10 * d * w + 18 * 2^(w-1) + 11 * d + 7 * w, with d = ceil( nbits / w )
     * (see costs of the various parts, with 1S = 1M)
     */
    w = grp->nbits >= 384 ? 5 : 4;

    /*
     * If P == G, pre-compute a bit more, since this may be re-used later.
     * Just adding one avoids upping the cost of the first mul too much,
     * and the memory cost too.
     */
    if (p_eq_g) {
        w++;
    }

    /*
     * If static comb table may not be used (!p_eq_g) or static comb table does
     * not exists, make sure w is within bounds.
     * (The last test is useful only for very small curves in the test suite.)
     *
     * The user reduces MBEDTLS_ECP_WINDOW_SIZE does not changes the size of
     * static comb table, because the size of static comb table is fixed when
     * it is generated.
     */

    if ((!p_eq_g || !ecp_group_is_static_comb_table(grp)) && w > 4) {
        w = 4;
    }

    if (w >= grp->nbits) {
        w = 2;
    }

    return w;
}

/*
 * Multiplication using the comb method - for curves in short Weierstrass form
 *
 * This function is mainly responsible for administrative work:
 * - managing the restart context if enabled
 * - managing the table of precomputed points (passed between the below two
 *   functions): allocation, computation, ownership transfer, freeing.
 *
 * It delegates the actual arithmetic work to:
 *      ecp_precompute_comb() and ecp_mul_comb_with_precomp()
 *
 * See comments on ecp_comb_recode_core() regarding the computation strategy.
 */
static int ecp_mul_comb(mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
                        const mbedtls_mpi *m, const mbedtls_ecp_point *P,
                        int (*f_rng)(void *, unsigned char *, size_t),
                        void *p_rng,
                        mbedtls_ecp_restart_ctx *rs_ctx)
{
    int ret = -0x006E;
    unsigned char w, p_eq_g, i;
    size_t d;
    unsigned char T_size = 0, T_ok = 0;
    mbedtls_ecp_point *T = 0;

    (void) rs_ctx;;

    /* Is P the base point ? */

    p_eq_g = (mbedtls_mpi_cmp_mpi(&P->Y, &grp->G . Y) == 0 &&
              mbedtls_mpi_cmp_mpi(&P->X, &grp->G . X) == 0);




    /* Pick window size and deduce related sizes */
    w = ecp_pick_window_size(grp, p_eq_g);
    T_size = 1U << (w - 1);
    d = (grp->nbits + w - 1) / w;

    /* Pre-computed table: do we have it already for the base point? */
    if (p_eq_g && grp->T != 0) {
        /* second pointer to the same table, will be deleted on exit */
        T = grp->T;
        T_ok = 1;
    } else
# 2334 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    /* Allocate table if we didn't have any */
    {
        T = mbedtls_calloc(T_size, sizeof(mbedtls_ecp_point));
        if (T == 0) {
            ret = -0x4D80;
            goto cleanup;
        }

        for (i = 0; i < T_size; i++) {
            mbedtls_ecp_point_init(&T[i]);
        }

        T_ok = 0;
    }

    /* Compute table (or finish computing it) if not done already */
    if (!T_ok) {
        do { if ((ret = (ecp_precompute_comb(grp, T, P, w, d, rs_ctx))) != 0) goto cleanup; } while (0);

        if (p_eq_g) {
            /* almost transfer ownership of T to the group, but keep a copy of
             * the pointer to use for calling the next function more easily */
            grp->T = T;
            grp->T_size = T_size;
        }
    }

    /* Actual comb multiplication using precomputed points */
    do { if ((ret = (ecp_mul_comb_after_precomp(grp, R, m, T, T_size, w, d, f_rng, p_rng, rs_ctx))) != 0) goto cleanup; } while (0);



cleanup:

    /* does T belong to the group? */
    if (T == grp->T) {
        T = 0;
    }

    /* does T belong to the restart context? */
# 2382 "L:/PLT/pcac/mbedTLS/library/ecp.c"

    /* did T belong to us? then let's destroy it! */
    if (T != 0) {
        for (i = 0; i < T_size; i++) {
            mbedtls_ecp_point_free(&T[i]);
        }
        mbedtls_free(T);
    }

    /* prevent caller from using invalid value */
    int should_free_R = (ret != 0);
# 2399 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    if (should_free_R) {
        mbedtls_ecp_point_free(R);
    }

    (void) rs_ctx;;

    return ret;
}




/*
 * For Montgomery curves, we do all the internal arithmetic in projective
 * coordinates. Import/export of points uses only the x coordinates, which is
 * internally represented as X / Z.
 *
 * For scalar multiplication, we'll use a Montgomery ladder.
 */

/*
 * Normalize Montgomery x/z coordinates: X = X/Z, Z = 1
 * Cost: 1M + 1I
 */
static int ecp_normalize_mxz(const mbedtls_ecp_group *grp, mbedtls_ecp_point *P)
{









    int ret = -0x006E;
    do { if ((ret = (mbedtls_mpi_inv_mod((&P->Z), (&P->Z), &grp->P))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &P->X, &P->X, &P->Z))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_lset(&P->Z, 1))) != 0) goto cleanup; } while (0);

cleanup:
    return ret;

}

/*
 * Randomize projective x/z coordinates:
 * (X, Z) -> (l X, l Z) for random l
 * This is sort of the reverse operation of ecp_normalize_mxz().
 *
 * This countermeasure was first suggested in [2].
 * Cost: 2M
 */
static int ecp_randomize_mxz(const mbedtls_ecp_group *grp, mbedtls_ecp_point *P,
                             int (*f_rng)(void *, unsigned char *, size_t), void *p_rng)
{









    int ret = -0x006E;
    mbedtls_mpi l;
    mbedtls_mpi_init(&l);

    /* Generate l such that 1 < l < p */
    do { if ((ret = (mbedtls_mpi_random((&l), 2, &grp->P, f_rng, p_rng))) != 0) goto cleanup; } while (0);

    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &P->X, &P->X, &l))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &P->Z, &P->Z, &l))) != 0) goto cleanup; } while (0);

cleanup:
    mbedtls_mpi_free(&l);

    if (ret == -0x000E) {
        ret = -0x4D00;
    }
    return ret;

}

/*
 * Double-and-add: R = 2P, S = P + Q, with d = X(P - Q),
 * for Montgomery curves in x/z coordinates.
 *
 * http://www.hyperelliptic.org/EFD/g1p/auto-code/montgom/xz/ladder/mladd-1987-m.op3
 * with
 * d =  X1
 * P = (X2, Z2)
 * Q = (X3, Z3)
 * R = (X4, Z4)
 * S = (X5, Z5)
 * and eliminating temporary variables tO, ..., t4.
 *
 * Cost: 5M + 4S
 */
static int ecp_double_add_mxz(const mbedtls_ecp_group *grp,
                              mbedtls_ecp_point *R, mbedtls_ecp_point *S,
                              const mbedtls_ecp_point *P, const mbedtls_ecp_point *Q,
                              const mbedtls_mpi *d,
                              mbedtls_mpi T[4])
{









    int ret = -0x006E;

    do { if ((ret = (mbedtls_mpi_add_mod(grp, &T[0], &P->X, &P->Z))) != 0) goto cleanup; } while (0);   /* Pp := PX + PZ                    */
    do { if ((ret = (mbedtls_mpi_sub_mod(grp, &T[1], &P->X, &P->Z))) != 0) goto cleanup; } while (0);   /* Pm := PX - PZ                    */
    do { if ((ret = (mbedtls_mpi_add_mod(grp, &T[2], &Q->X, &Q->Z))) != 0) goto cleanup; } while (0);   /* Qp := QX + XZ                    */
    do { if ((ret = (mbedtls_mpi_sub_mod(grp, &T[3], &Q->X, &Q->Z))) != 0) goto cleanup; } while (0);   /* Qm := QX - QZ                    */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &T[3], &T[3], &T[0]))) != 0) goto cleanup; } while (0);   /* Qm * Pp                          */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &T[2], &T[2], &T[1]))) != 0) goto cleanup; } while (0);   /* Qp * Pm                          */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &T[0], &T[0], &T[0]))) != 0) goto cleanup; } while (0);            /* Pp^2                             */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &T[1], &T[1], &T[1]))) != 0) goto cleanup; } while (0);            /* Pm^2                             */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &R->X, &T[0], &T[1]))) != 0) goto cleanup; } while (0);   /* Pp^2 * Pm^2                      */
    do { if ((ret = (mbedtls_mpi_sub_mod(grp, &T[0], &T[0], &T[1]))) != 0) goto cleanup; } while (0);   /* Pp^2 - Pm^2                      */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &R->Z, &grp->A, &T[0]))) != 0) goto cleanup; } while (0);   /* A * (Pp^2 - Pm^2)                */
    do { if ((ret = (mbedtls_mpi_add_mod(grp, &R->Z, &T[1], &R->Z))) != 0) goto cleanup; } while (0);   /* [ A * (Pp^2-Pm^2) ] + Pm^2       */
    do { if ((ret = (mbedtls_mpi_add_mod(grp, &S->X, &T[3], &T[2]))) != 0) goto cleanup; } while (0);   /* Qm*Pp + Qp*Pm                    */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &S->X, &S->X, &S->X))) != 0) goto cleanup; } while (0);            /* (Qm*Pp + Qp*Pm)^2                */
    do { if ((ret = (mbedtls_mpi_sub_mod(grp, &S->Z, &T[3], &T[2]))) != 0) goto cleanup; } while (0);   /* Qm*Pp - Qp*Pm                    */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &S->Z, &S->Z, &S->Z))) != 0) goto cleanup; } while (0);            /* (Qm*Pp - Qp*Pm)^2                */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &S->Z, d, &S->Z))) != 0) goto cleanup; } while (0);   /* d * ( Qm*Pp - Qp*Pm )^2          */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &R->Z, &T[0], &R->Z))) != 0) goto cleanup; } while (0);   /* [A*(Pp^2-Pm^2)+Pm^2]*(Pp^2-Pm^2) */

cleanup:

    return ret;

}

/*
 * Multiplication with Montgomery ladder in x/z coordinates,
 * for curves in Montgomery form
 */
static int ecp_mul_mxz(mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
                       const mbedtls_mpi *m, const mbedtls_ecp_point *P,
                       int (*f_rng)(void *, unsigned char *, size_t),
                       void *p_rng)
{
    int ret = -0x006E;
    size_t i;
    unsigned char b;
    mbedtls_ecp_point RP;
    mbedtls_mpi PX;
    mbedtls_mpi tmp[4];
    mbedtls_ecp_point_init(&RP); mbedtls_mpi_init(&PX);

    mpi_init_many(tmp, sizeof(tmp) / sizeof(mbedtls_mpi));

    if (f_rng == 0) {
        return -0x4F80;
    }

    /* Save PX and read from P before writing to R, in case P == R */
    do { if ((ret = (mbedtls_mpi_copy(&PX, &P->X))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_ecp_copy(&RP, P))) != 0) goto cleanup; } while (0);

    /* Set R to zero in modified x/z coordinates */
    do { if ((ret = (mbedtls_mpi_lset(&R->X, 1))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_lset(&R->Z, 0))) != 0) goto cleanup; } while (0);
    mbedtls_mpi_free(&R->Y);

    /* RP.X might be slightly larger than P, so reduce it */
    while (mbedtls_mpi_cmp_mpi((&RP . X), &grp->P) >= 0) do { if ((ret = (mbedtls_mpi_sub_abs((&RP . X), (&RP . X), &grp->P))) != 0) goto cleanup; } while (0);

    /* Randomize coordinates of the starting point */
    do { if ((ret = (ecp_randomize_mxz(grp, &RP, f_rng, p_rng))) != 0) goto cleanup; } while (0);

    /* Loop invariant: R = result so far, RP = R + P */
    i = grp->nbits + 1; /* one past the (zero-based) required msb for private keys */
    while (i-- > 0) {
        b = mbedtls_mpi_get_bit(m, i);
        /*
         *  if (b) R = 2R + P else R = 2R,
         * which is:
         *  if (b) double_add( RP, R, RP, R )
         *  else   double_add( R, RP, R, RP )
         * but using safe conditional swaps to avoid leaks
         */
        do { if ((ret = (mbedtls_mpi_safe_cond_swap((&R->X), (&RP . X), (b)))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_mpi_safe_cond_swap((&R->Z), (&RP . Z), (b)))) != 0) goto cleanup; } while (0);
        do { if ((ret = (ecp_double_add_mxz(grp, R, &RP, R, &RP, &PX, tmp))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_mpi_safe_cond_swap((&R->X), (&RP . X), (b)))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_mpi_safe_cond_swap((&R->Z), (&RP . Z), (b)))) != 0) goto cleanup; } while (0);
    }

    /*
     * Knowledge of the projective coordinates may leak the last few bits of the
     * scalar [1], and since our MPI implementation isn't constant-flow,
     * inversion (used for coordinate normalization) may leak the full value
     * of its input via side-channels [2].
     *
     * [1] https://eprint.iacr.org/2003/191
     * [2] https://eprint.iacr.org/2020/055
     *
     * Avoid the leak by randomizing coordinates before we normalize them.
     */
    do { if ((ret = (ecp_randomize_mxz(grp, R, f_rng, p_rng))) != 0) goto cleanup; } while (0);
    do { if ((ret = (ecp_normalize_mxz(grp, R))) != 0) goto cleanup; } while (0);

cleanup:
    mbedtls_ecp_point_free(&RP); mbedtls_mpi_free(&PX);

    mpi_free_many(tmp, sizeof(tmp) / sizeof(mbedtls_mpi));
    return ret;
}



/*
 * Restartable multiplication R = m * P
 *
 * This internal function can be called without an RNG in case where we know
 * the inputs are not sensitive.
 */
static int ecp_mul_restartable_internal(mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
                                        const mbedtls_mpi *m, const mbedtls_ecp_point *P,
                                        int (*f_rng)(void *, unsigned char *, size_t), void *p_rng,
                                        mbedtls_ecp_restart_ctx *rs_ctx)
{
    int ret = -0x4F80;




# 2642 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    (void) rs_ctx;








    int restarting = 0;



    /* skip argument check when restarting */
    if (!restarting) {
        /* check_privkey is free */
        ;

        /* Common sanity checks */
        do { if ((ret = (mbedtls_ecp_check_privkey(grp, m))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_ecp_check_pubkey(grp, P))) != 0) goto cleanup; } while (0);
    }

    ret = -0x4F80;

    if (mbedtls_ecp_get_type(grp) == MBEDTLS_ECP_TYPE_MONTGOMERY) {
        do { if ((ret = (ecp_mul_mxz(grp, R, m, P, f_rng, p_rng))) != 0) goto cleanup; } while (0);
    }


    if (mbedtls_ecp_get_type(grp) == MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS) {
        do { if ((ret = (ecp_mul_comb(grp, R, m, P, f_rng, p_rng, rs_ctx))) != 0) goto cleanup; } while (0);
    }


cleanup:













    return ret;
}

/*
 * Restartable multiplication R = m * P
 */
int mbedtls_ecp_mul_restartable(mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
                                const mbedtls_mpi *m, const mbedtls_ecp_point *P,
                                int (*f_rng)(void *, unsigned char *, size_t), void *p_rng,
                                mbedtls_ecp_restart_ctx *rs_ctx)
{
    if (f_rng == 0) {
        return -0x4F80;
    }

    return ecp_mul_restartable_internal(grp, R, m, P, f_rng, p_rng, rs_ctx);
}

/*
 * Multiplication R = m * P
 */
int mbedtls_ecp_mul(mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
                    const mbedtls_mpi *m, const mbedtls_ecp_point *P,
                    int (*f_rng)(void *, unsigned char *, size_t), void *p_rng)
{
    return mbedtls_ecp_mul_restartable(grp, R, m, P, f_rng, p_rng, 0);
}



/*
 * Check that an affine point is valid as a public key,
 * short weierstrass curves (SEC1 *******)
 */
static int ecp_check_pubkey_sw(const mbedtls_ecp_group *grp, const mbedtls_ecp_point *pt)
{
    int ret = -0x006E;
    mbedtls_mpi YY, RHS;

    /* pt coordinates must be normalized for our checks */
    if (mbedtls_mpi_cmp_int(&pt->X, 0) < 0 ||
        mbedtls_mpi_cmp_int(&pt->Y, 0) < 0 ||
        mbedtls_mpi_cmp_mpi(&pt->X, &grp->P) >= 0 ||
        mbedtls_mpi_cmp_mpi(&pt->Y, &grp->P) >= 0) {
        return -0x4C80;
    }

    mbedtls_mpi_init(&YY); mbedtls_mpi_init(&RHS);

    /*
     * YY = Y^2
     * RHS = X^3 + A X + B
     */
    do { if ((ret = (mbedtls_mpi_mul_mod(grp, &YY, &pt->Y, &pt->Y))) != 0) goto cleanup; } while (0);
    do { if ((ret = (ecp_sw_rhs(grp, &RHS, &pt->X))) != 0) goto cleanup; } while (0);

    if (mbedtls_mpi_cmp_mpi(&YY, &RHS) != 0) {
        ret = -0x4C80;
    }

cleanup:

    mbedtls_mpi_free(&YY); mbedtls_mpi_free(&RHS);

    return ret;
}




/*
 * R = m * P with shortcuts for m == 0, m == 1 and m == -1
 * NOT constant-time - ONLY for short Weierstrass!
 */
static int mbedtls_ecp_mul_shortcuts(mbedtls_ecp_group *grp,
                                     mbedtls_ecp_point *R,
                                     const mbedtls_mpi *m,
                                     const mbedtls_ecp_point *P,
                                     mbedtls_ecp_restart_ctx *rs_ctx)
{
    int ret = -0x006E;
    mbedtls_mpi tmp;
    mbedtls_mpi_init(&tmp);

    if (mbedtls_mpi_cmp_int(m, 0) == 0) {
        do { if ((ret = (mbedtls_ecp_check_pubkey(grp, P))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_ecp_set_zero(R))) != 0) goto cleanup; } while (0);
    } else if (mbedtls_mpi_cmp_int(m, 1) == 0) {
        do { if ((ret = (mbedtls_ecp_check_pubkey(grp, P))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_ecp_copy(R, P))) != 0) goto cleanup; } while (0);
    } else if (mbedtls_mpi_cmp_int(m, -1) == 0) {
        do { if ((ret = (mbedtls_ecp_check_pubkey(grp, P))) != 0) goto cleanup; } while (0);
        do { if ((ret = (mbedtls_ecp_copy(R, P))) != 0) goto cleanup; } while (0);
        do { unsigned char nonzero = mbedtls_mpi_cmp_int(((&R->Y)), 0) != 0; do { if ((ret = (mbedtls_mpi_sub_mpi(&tmp, &grp->P, ((&R->Y))))) != 0) goto cleanup; } while (0); do { if ((ret = (mbedtls_mpi_safe_cond_assign(((&R->Y)), &tmp, nonzero & 1))) != 0) goto cleanup; } while (0); } while (0);
    } else {
        do { if ((ret = (ecp_mul_restartable_internal(grp, R, m, P, 0, 0, rs_ctx))) != 0) goto cleanup; } while (0);

    }

cleanup:
    mbedtls_mpi_free(&tmp);

    return ret;
}

/*
 * Restartable linear combination
 * NOT constant-time
 */
int mbedtls_ecp_muladd_restartable(
    mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
    const mbedtls_mpi *m, const mbedtls_ecp_point *P,
    const mbedtls_mpi *n, const mbedtls_ecp_point *Q,
    mbedtls_ecp_restart_ctx *rs_ctx)
{
    int ret = -0x006E;
    mbedtls_ecp_point mP;
    mbedtls_ecp_point *pmP = &mP;
    mbedtls_ecp_point *pR = R;
    mbedtls_mpi tmp[4];



    if (mbedtls_ecp_get_type(grp) != MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS) {
        return -0x4E80;
    }

    mbedtls_ecp_point_init(&mP);
    mpi_init_many(tmp, sizeof(tmp) / sizeof(mbedtls_mpi));

    (void) rs_ctx;;

# 2841 "L:/PLT/pcac/mbedTLS/library/ecp.c"

    do { if ((ret = (mbedtls_ecp_mul_shortcuts(grp, pmP, m, P, rs_ctx))) != 0) goto cleanup; } while (0);
# 2850 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    do { if ((ret = (mbedtls_ecp_mul_shortcuts(grp, pR, n, Q, rs_ctx))) != 0) goto cleanup; } while (0);







# 2865 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    ;
    do { if ((ret = (ecp_add_mixed(grp, pR, pmP, pR, tmp))) != 0) goto cleanup; } while (0);
# 2874 "L:/PLT/pcac/mbedTLS/library/ecp.c"
    ;
    do { if ((ret = (ecp_normalize_jac(grp, pR))) != 0) goto cleanup; } while (0);







cleanup:

    mpi_free_many(tmp, sizeof(tmp) / sizeof(mbedtls_mpi));







    mbedtls_ecp_point_free(&mP);

    (void) rs_ctx;;

    return ret;
}

/*
 * Linear combination
 * NOT constant-time
 */
int mbedtls_ecp_muladd(mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
                       const mbedtls_mpi *m, const mbedtls_ecp_point *P,
                       const mbedtls_mpi *n, const mbedtls_ecp_point *Q)
{
    return mbedtls_ecp_muladd_restartable(grp, R, m, P, n, Q, 0);
}








/*
 * Constants for the two points other than 0, 1, -1 (mod p) in
 * https://cr.yp.to/ecdh.html#validate
 * See ecp_check_pubkey_x25519().
 */
static const mbedtls_mpi_uint x25519_bad_point_1[] = {
    ((mbedtls_mpi_uint) (0xe0) << 0) | ((mbedtls_mpi_uint) (0xeb) << 8) | ((mbedtls_mpi_uint) (0x7a) << 16) | ((mbedtls_mpi_uint) (0x7c) << 24), ((mbedtls_mpi_uint) (0x3b) << 0) | ((mbedtls_mpi_uint) (0x41) << 8) | ((mbedtls_mpi_uint) (0xb8) << 16) | ((mbedtls_mpi_uint) (0xae) << 24),
    ((mbedtls_mpi_uint) (0x16) << 0) | ((mbedtls_mpi_uint) (0x56) << 8) | ((mbedtls_mpi_uint) (0xe3) << 16) | ((mbedtls_mpi_uint) (0xfa) << 24), ((mbedtls_mpi_uint) (0xf1) << 0) | ((mbedtls_mpi_uint) (0x9f) << 8) | ((mbedtls_mpi_uint) (0xc4) << 16) | ((mbedtls_mpi_uint) (0x6a) << 24),
    ((mbedtls_mpi_uint) (0xda) << 0) | ((mbedtls_mpi_uint) (0x09) << 8) | ((mbedtls_mpi_uint) (0x8d) << 16) | ((mbedtls_mpi_uint) (0xeb) << 24), ((mbedtls_mpi_uint) (0x9c) << 0) | ((mbedtls_mpi_uint) (0x32) << 8) | ((mbedtls_mpi_uint) (0xb1) << 16) | ((mbedtls_mpi_uint) (0xfd) << 24),
    ((mbedtls_mpi_uint) (0x86) << 0) | ((mbedtls_mpi_uint) (0x62) << 8) | ((mbedtls_mpi_uint) (0x05) << 16) | ((mbedtls_mpi_uint) (0x16) << 24), ((mbedtls_mpi_uint) (0x5f) << 0) | ((mbedtls_mpi_uint) (0x49) << 8) | ((mbedtls_mpi_uint) (0xb8) << 16) | ((mbedtls_mpi_uint) (0x00) << 24),
};
static const mbedtls_mpi_uint x25519_bad_point_2[] = {
    ((mbedtls_mpi_uint) (0x5f) << 0) | ((mbedtls_mpi_uint) (0x9c) << 8) | ((mbedtls_mpi_uint) (0x95) << 16) | ((mbedtls_mpi_uint) (0xbc) << 24), ((mbedtls_mpi_uint) (0xa3) << 0) | ((mbedtls_mpi_uint) (0x50) << 8) | ((mbedtls_mpi_uint) (0x8c) << 16) | ((mbedtls_mpi_uint) (0x24) << 24),
    ((mbedtls_mpi_uint) (0xb1) << 0) | ((mbedtls_mpi_uint) (0xd0) << 8) | ((mbedtls_mpi_uint) (0xb1) << 16) | ((mbedtls_mpi_uint) (0x55) << 24), ((mbedtls_mpi_uint) (0x9c) << 0) | ((mbedtls_mpi_uint) (0x83) << 8) | ((mbedtls_mpi_uint) (0xef) << 16) | ((mbedtls_mpi_uint) (0x5b) << 24),
    ((mbedtls_mpi_uint) (0x04) << 0) | ((mbedtls_mpi_uint) (0x44) << 8) | ((mbedtls_mpi_uint) (0x5c) << 16) | ((mbedtls_mpi_uint) (0xc4) << 24), ((mbedtls_mpi_uint) (0x58) << 0) | ((mbedtls_mpi_uint) (0x1c) << 8) | ((mbedtls_mpi_uint) (0x8e) << 16) | ((mbedtls_mpi_uint) (0x86) << 24),
    ((mbedtls_mpi_uint) (0xd8) << 0) | ((mbedtls_mpi_uint) (0x22) << 8) | ((mbedtls_mpi_uint) (0x4e) << 16) | ((mbedtls_mpi_uint) (0xdd) << 24), ((mbedtls_mpi_uint) (0xd0) << 0) | ((mbedtls_mpi_uint) (0x9f) << 8) | ((mbedtls_mpi_uint) (0x11) << 16) | ((mbedtls_mpi_uint) (0x57) << 24),
};
static const mbedtls_mpi ecp_x25519_bad_point_1 = { . p = (mbedtls_mpi_uint *) (x25519_bad_point_1), . s = 1, . n = (sizeof(x25519_bad_point_1) / sizeof(mbedtls_mpi_uint)) };

static const mbedtls_mpi ecp_x25519_bad_point_2 = { . p = (mbedtls_mpi_uint *) (x25519_bad_point_2), . s = 1, . n = (sizeof(x25519_bad_point_2) / sizeof(mbedtls_mpi_uint)) };



/*
 * Check that the input point is not one of the low-order points.
 * This is recommended by the "May the Fourth" paper:
 * https://eprint.iacr.org/2017/806.pdf
 * Those points are never sent by an honest peer.
 */
static int ecp_check_bad_points_mx(const mbedtls_mpi *X, const mbedtls_mpi *P,
                                   const mbedtls_ecp_group_id grp_id)
{
    int ret;
    mbedtls_mpi XmP;

    mbedtls_mpi_init(&XmP);

    /* Reduce X mod P so that we only need to check values less than P.
     * We know X < 2^256 so we can proceed by subtraction. */
    do { if ((ret = (mbedtls_mpi_copy(&XmP, X))) != 0) goto cleanup; } while (0);
    while (mbedtls_mpi_cmp_mpi(&XmP, P) >= 0) {
        do { if ((ret = (mbedtls_mpi_sub_mpi(&XmP, &XmP, P))) != 0) goto cleanup; } while (0);
    }

    /* Check against the known bad values that are less than P. For Curve448
     * these are 0, 1 and -1. For Curve25519 we check the values less than P
     * from the following list: https://cr.yp.to/ecdh.html#validate */
    if (mbedtls_mpi_cmp_int(&XmP, 1) <= 0) {  /* takes care of 0 and 1 */
        ret = -0x4C80;
        goto cleanup;
    }


    if (grp_id == MBEDTLS_ECP_DP_CURVE25519) {
        if (mbedtls_mpi_cmp_mpi(&XmP, &ecp_x25519_bad_point_1) == 0) {
            ret = -0x4C80;
            goto cleanup;
        }

        if (mbedtls_mpi_cmp_mpi(&XmP, &ecp_x25519_bad_point_2) == 0) {
            ret = -0x4C80;
            goto cleanup;
        }
    }




    /* Final check: check if XmP + 1 is P (final because it changes XmP!) */
    do { if ((ret = (mbedtls_mpi_add_int(&XmP, &XmP, 1))) != 0) goto cleanup; } while (0);
    if (mbedtls_mpi_cmp_mpi(&XmP, P) == 0) {
        ret = -0x4C80;
        goto cleanup;
    }

    ret = 0;

cleanup:
    mbedtls_mpi_free(&XmP);

    return ret;
}

/*
 * Check validity of a public key for Montgomery curves with x-only schemes
 */
static int ecp_check_pubkey_mx(const mbedtls_ecp_group *grp, const mbedtls_ecp_point *pt)
{
    /* [Curve25519 p. 5] Just check X is the correct number of bytes */
    /* Allow any public value, if it's too big then we'll just reduce it mod p
     * (RFC 7748 sec. 5 para. 3). */
    if (mbedtls_mpi_size(&pt->X) > (grp->nbits + 7) / 8) {
        return -0x4C80;
    }

    /* Implicit in all standards (as they don't consider negative numbers):
     * X must be non-negative. This is normally ensured by the way it's
     * encoded for transmission, but let's be extra sure. */
    if (mbedtls_mpi_cmp_int(&pt->X, 0) < 0) {
        return -0x4C80;
    }

    return ecp_check_bad_points_mx(&pt->X, &grp->P, grp->id);
}


/*
 * Check that a point is valid as a public key
 */
int mbedtls_ecp_check_pubkey(const mbedtls_ecp_group *grp,
                             const mbedtls_ecp_point *pt)
{
    /* Must use affine coordinates */
    if (mbedtls_mpi_cmp_int(&pt->Z, 1) != 0) {
        return -0x4C80;
    }


    if (mbedtls_ecp_get_type(grp) == MBEDTLS_ECP_TYPE_MONTGOMERY) {
        return ecp_check_pubkey_mx(grp, pt);
    }


    if (mbedtls_ecp_get_type(grp) == MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS) {
        return ecp_check_pubkey_sw(grp, pt);
    }

    return -0x4F80;
}

/*
 * Check that an mbedtls_mpi is valid as a private key
 */
int mbedtls_ecp_check_privkey(const mbedtls_ecp_group *grp,
                              const mbedtls_mpi *d)
{

    if (mbedtls_ecp_get_type(grp) == MBEDTLS_ECP_TYPE_MONTGOMERY) {
        /* see RFC 7748 sec. 5 para. 5 */
        if (mbedtls_mpi_get_bit(d, 0) != 0 ||
            mbedtls_mpi_get_bit(d, 1) != 0 ||
            mbedtls_mpi_bitlen(d) - 1 != grp->nbits) {  /* mbedtls_mpi_bitlen is one-based! */
            return -0x4C80;
        }

        /* see [Curve25519] page 5 */
        if (grp->nbits == 254 && mbedtls_mpi_get_bit(d, 2) != 0) {
            return -0x4C80;
        }

        return 0;
    }


    if (mbedtls_ecp_get_type(grp) == MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS) {
        /* see SEC1 3.2 */
        if (mbedtls_mpi_cmp_int(d, 1) < 0 ||
            mbedtls_mpi_cmp_mpi(d, &grp->N) >= 0) {
            return -0x4C80;
        } else {
            return 0;
        }
    }


    return -0x4F80;
}


static
int mbedtls_ecp_gen_privkey_mx(size_t high_bit,
                               mbedtls_mpi *d,
                               int (*f_rng)(void *, unsigned char *, size_t),
                               void *p_rng)
{
    int ret = -0x4F80;
    size_t n_random_bytes = high_bit / 8 + 1;

    /* [Curve25519] page 5 */
    /* Generate a (high_bit+1)-bit random number by generating just enough
     * random bytes, then shifting out extra bits from the top (necessary
     * when (high_bit+1) is not a multiple of 8). */
    do { if ((ret = (mbedtls_mpi_fill_random(d, n_random_bytes, f_rng, p_rng))) != 0) goto cleanup; } while (0);

    do { if ((ret = (mbedtls_mpi_shift_r(d, 8 * n_random_bytes - high_bit - 1))) != 0) goto cleanup; } while (0);

    do { if ((ret = (mbedtls_mpi_set_bit(d, high_bit, 1))) != 0) goto cleanup; } while (0);

    /* Make sure the last two bits are unset for Curve448, three bits for
       Curve25519 */
    do { if ((ret = (mbedtls_mpi_set_bit(d, 0, 0))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_mpi_set_bit(d, 1, 0))) != 0) goto cleanup; } while (0);
    if (high_bit == 254) {
        do { if ((ret = (mbedtls_mpi_set_bit(d, 2, 0))) != 0) goto cleanup; } while (0);
    }

cleanup:
    return ret;
}



static int mbedtls_ecp_gen_privkey_sw(
    const mbedtls_mpi *N, mbedtls_mpi *d,
    int (*f_rng)(void *, unsigned char *, size_t), void *p_rng)
{
    int ret = mbedtls_mpi_random(d, 1, N, f_rng, p_rng);
    switch (ret) {
        case -0x000E:
            return -0x4D00;
        default:
            return ret;
    }
}


/*
 * Generate a private key
 */
int mbedtls_ecp_gen_privkey(const mbedtls_ecp_group *grp,
                            mbedtls_mpi *d,
                            int (*f_rng)(void *, unsigned char *, size_t),
                            void *p_rng)
{

    if (mbedtls_ecp_get_type(grp) == MBEDTLS_ECP_TYPE_MONTGOMERY) {
        return mbedtls_ecp_gen_privkey_mx(grp->nbits, d, f_rng, p_rng);
    }



    if (mbedtls_ecp_get_type(grp) == MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS) {
        return mbedtls_ecp_gen_privkey_sw(&grp->N, d, f_rng, p_rng);
    }


    return -0x4F80;
}


/*
 * Generate a keypair with configurable base point
 */
int mbedtls_ecp_gen_keypair_base(mbedtls_ecp_group *grp,
                                 const mbedtls_ecp_point *G,
                                 mbedtls_mpi *d, mbedtls_ecp_point *Q,
                                 int (*f_rng)(void *, unsigned char *, size_t),
                                 void *p_rng)
{
    int ret = -0x006E;
    do { if ((ret = (mbedtls_ecp_gen_privkey(grp, d, f_rng, p_rng))) != 0) goto cleanup; } while (0);
    do { if ((ret = (mbedtls_ecp_mul(grp, Q, d, G, f_rng, p_rng))) != 0) goto cleanup; } while (0);

cleanup:
    return ret;
}

/*
 * Generate key pair, wrapper for conventional base point
 */
int mbedtls_ecp_gen_keypair(mbedtls_ecp_group *grp,
                            mbedtls_mpi *d, mbedtls_ecp_point *Q,
                            int (*f_rng)(void *, unsigned char *, size_t),
                            void *p_rng)
{
    return mbedtls_ecp_gen_keypair_base(grp, &grp->G, d, Q, f_rng, p_rng);
}

/*
 * Generate a keypair, prettier wrapper
 */
int mbedtls_ecp_gen_key(mbedtls_ecp_group_id grp_id, mbedtls_ecp_keypair *key,
                        int (*f_rng)(void *, unsigned char *, size_t), void *p_rng)
{
    int ret = -0x006E;
    if ((ret = mbedtls_ecp_group_load(&key->grp, grp_id)) != 0) {
        return ret;
    }

    return mbedtls_ecp_gen_keypair(&key->grp, &key->d, &key->Q, f_rng, p_rng);
}


int mbedtls_ecp_set_public_key(mbedtls_ecp_group_id grp_id,
                               mbedtls_ecp_keypair *key,
                               const mbedtls_ecp_point *Q)
{
    int ret = -0x006E;

    if (key->grp.id == MBEDTLS_ECP_DP_NONE) {
        /* Group not set yet */
        if ((ret = mbedtls_ecp_group_load(&key->grp, grp_id)) != 0) {
            return ret;
        }
    } else if (key->grp.id != grp_id) {
        /* Group mismatch */
        return -0x4F80;
    }
    return mbedtls_ecp_copy(&key->Q, Q);
}




/*
 * Read a private key.
 */
int mbedtls_ecp_read_key(mbedtls_ecp_group_id grp_id, mbedtls_ecp_keypair *key,
                         const unsigned char *buf, size_t buflen)
{
    int ret = 0;

    if ((ret = mbedtls_ecp_group_load(&key->grp, grp_id)) != 0) {
        return ret;
    }

    ret = -0x4E80;


    if (mbedtls_ecp_get_type(&key->grp) == MBEDTLS_ECP_TYPE_MONTGOMERY) {
        /*
         * Mask the key as mandated by RFC7748 for Curve25519 and Curve448.
         */
        if (grp_id == MBEDTLS_ECP_DP_CURVE25519) {
            if (buflen != 32) {
                return -0x4C80;
            }

            do { if ((ret = (mbedtls_mpi_read_binary_le(&key->d, buf, buflen))) != 0) goto cleanup; } while (0);

            /* Set the three least significant bits to 0 */
            do { if ((ret = (mbedtls_mpi_set_bit(&key->d, 0, 0))) != 0) goto cleanup; } while (0);
            do { if ((ret = (mbedtls_mpi_set_bit(&key->d, 1, 0))) != 0) goto cleanup; } while (0);
            do { if ((ret = (mbedtls_mpi_set_bit(&key->d, 2, 0))) != 0) goto cleanup; } while (0);

            /* Set the most significant bit to 0 */
            do { if ((ret = (mbedtls_mpi_set_bit(&key->d, 32 * 8 - 1, 0))) != 0) goto cleanup; } while (0);




            /* Set the second most significant bit to 1 */
            do { if ((ret = (mbedtls_mpi_set_bit(&key->d, 32 * 8 - 2, 1))) != 0) goto cleanup; } while (0);



        } else if (grp_id == MBEDTLS_ECP_DP_CURVE448) {
            if (buflen != 56) {
                return -0x4C80;
            }

            do { if ((ret = (mbedtls_mpi_read_binary_le(&key->d, buf, buflen))) != 0) goto cleanup; } while (0);

            /* Set the two least significant bits to 0 */
            do { if ((ret = (mbedtls_mpi_set_bit(&key->d, 0, 0))) != 0) goto cleanup; } while (0);
            do { if ((ret = (mbedtls_mpi_set_bit(&key->d, 1, 0))) != 0) goto cleanup; } while (0);

            /* Set the most significant bit to 1 */
            do { if ((ret = (mbedtls_mpi_set_bit(&key->d, 56 * 8 - 1, 1))) != 0) goto cleanup; } while (0);



        }
    }


    if (mbedtls_ecp_get_type(&key->grp) == MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS) {
        do { if ((ret = (mbedtls_mpi_read_binary(&key->d, buf, buflen))) != 0) goto cleanup; } while (0);
    }


    if (ret == 0) {
        do { if ((ret = (mbedtls_ecp_check_privkey(&key->grp, &key->d))) != 0) goto cleanup; } while (0);
    }

cleanup:

    if (ret != 0) {
        mbedtls_mpi_free(&key->d);
    }

    return ret;
}

/*
 * Write a private key.
 */

int mbedtls_ecp_write_key(mbedtls_ecp_keypair *key,
                          unsigned char *buf, size_t buflen)
{
    int ret = -0x006E;


    if (mbedtls_ecp_get_type(&key->grp) == MBEDTLS_ECP_TYPE_MONTGOMERY) {
        if (key->grp.id == MBEDTLS_ECP_DP_CURVE25519) {
            if (buflen < 32) {
                return -0x4F00;
            }

        } else if (key->grp.id == MBEDTLS_ECP_DP_CURVE448) {
            if (buflen < 56) {
                return -0x4F00;
            }
        }
        do { if ((ret = (mbedtls_mpi_write_binary_le(&key->d, buf, buflen))) != 0) goto cleanup; } while (0);
    }


    if (mbedtls_ecp_get_type(&key->grp) == MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS) {
        do { if ((ret = (mbedtls_mpi_write_binary(&key->d, buf, buflen))) != 0) goto cleanup; } while (0);
    }


cleanup:

    return ret;
}


int mbedtls_ecp_write_key_ext(const mbedtls_ecp_keypair *key,
                              size_t *olen, unsigned char *buf, size_t buflen)
{
    size_t len = (key->grp.nbits + 7) / 8;
    if (len > buflen) {
        /* For robustness, ensure *olen <= buflen even on error. */
        *olen = 0;
        return -0x4F00;
    }
    *olen = len;

    /* Private key not set */
    if (key->d.n == 0) {
        return -0x4F80;
    }


    if (mbedtls_ecp_get_type(&key->grp) == MBEDTLS_ECP_TYPE_MONTGOMERY) {
        return mbedtls_mpi_write_binary_le(&key->d, buf, len);
    }



    if (mbedtls_ecp_get_type(&key->grp) == MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS) {
        return mbedtls_mpi_write_binary(&key->d, buf, len);
    }


    /* Private key set but no recognized curve type? This shouldn't happen. */
    return -0x006E;
}

/*
 * Write a public key.
 */
int mbedtls_ecp_write_public_key(const mbedtls_ecp_keypair *key,
                                 int format, size_t *olen,
                                 unsigned char *buf, size_t buflen)
{
    return mbedtls_ecp_point_write_binary(&key->grp, &key->Q,
                                          format, olen, buf, buflen);
}



/*
 * Check a public-private key pair
 */
int mbedtls_ecp_check_pub_priv(
    const mbedtls_ecp_keypair *pub, const mbedtls_ecp_keypair *prv,
    int (*f_rng)(void *, unsigned char *, size_t), void *p_rng)
{
    int ret = -0x006E;
    mbedtls_ecp_point Q;
    mbedtls_ecp_group grp;
    if (pub->grp.id == MBEDTLS_ECP_DP_NONE ||
        pub->grp.id != prv->grp.id ||
        mbedtls_mpi_cmp_mpi(&pub->Q.X, &prv->Q.X) ||
        mbedtls_mpi_cmp_mpi(&pub->Q.Y, &prv->Q.Y) ||
        mbedtls_mpi_cmp_mpi(&pub->Q.Z, &prv->Q.Z)) {
        return -0x4F80;
    }

    mbedtls_ecp_point_init(&Q);
    mbedtls_ecp_group_init(&grp);

    /* mbedtls_ecp_mul() needs a non-const group... */
    mbedtls_ecp_group_copy(&grp, &prv->grp);

    /* Also checks d is valid */
    do { if ((ret = (mbedtls_ecp_mul(&grp, &Q, &prv->d, &prv->grp . G, f_rng, p_rng))) != 0) goto cleanup; } while (0);

    if (mbedtls_mpi_cmp_mpi(&Q.X, &prv->Q.X) ||
        mbedtls_mpi_cmp_mpi(&Q.Y, &prv->Q.Y) ||
        mbedtls_mpi_cmp_mpi(&Q.Z, &prv->Q.Z)) {
        ret = -0x4F80;
        goto cleanup;
    }

cleanup:
    mbedtls_ecp_point_free(&Q);
    mbedtls_ecp_group_free(&grp);

    return ret;
}

int mbedtls_ecp_keypair_calc_public(mbedtls_ecp_keypair *key,
                                    int (*f_rng)(void *, unsigned char *, size_t),
                                    void *p_rng)
{
    return mbedtls_ecp_mul(&key->grp, &key->Q, &key->d, &key->grp.G,
                           f_rng, p_rng);
}


mbedtls_ecp_group_id mbedtls_ecp_keypair_get_group_id(
    const mbedtls_ecp_keypair *key)
{
    return key->grp.id;
}

/*
 * Export generic key-pair parameters.
 */
int mbedtls_ecp_export(const mbedtls_ecp_keypair *key, mbedtls_ecp_group *grp,
                       mbedtls_mpi *d, mbedtls_ecp_point *Q)
{
    int ret = -0x006E;

    if (grp != 0 && (ret = mbedtls_ecp_group_copy(grp, &key->grp)) != 0) {
        return ret;
    }

    if (d != 0 && (ret = mbedtls_mpi_copy(d, &key->d)) != 0) {
        return ret;
    }

    if (Q != 0 && (ret = mbedtls_ecp_copy(Q, &key->Q)) != 0) {
        return ret;
    }

    return 0;
}

int mbedtls_ecp_decompress(
   const mbedtls_ecp_group *grp,
   const unsigned char *input, size_t ilen,
   unsigned char *output, size_t *olen, size_t osize) 
{
   int ret;
   size_t plen;
   mbedtls_mpi r;
   mbedtls_mpi x;
   mbedtls_mpi n;

   plen = mbedtls_mpi_size(&grp->P);

   *olen = 2 * plen + 1;

   if (osize < *olen)
       return(-0x4F00);

   if (ilen != plen + 1)
       return(-0x4F80);

   if (input[0] != 0x02 && input[0] != 0x03)
       return(-0x4F80);

   // output will consist of 0x04|X|Y
   memcpy(output, input, ilen);
   output[0] = 0x04;

   mbedtls_mpi_init(&r);
   mbedtls_mpi_init(&x);
   mbedtls_mpi_init(&n);

   // x <= input
   do { if ((ret = (mbedtls_mpi_read_binary(&x, input + 1, plen))) != 0) goto cleanup; } while (0);

   // r = x^2
   do { if ((ret = (mbedtls_mpi_mul_mpi(&r, &x, &x))) != 0) goto cleanup; } while (0);

   // r = x^2 + a
   if (grp->A.p == 0) {
       // Special case where a is -3
       do { if ((ret = (mbedtls_mpi_sub_int(&r, &r, 3))) != 0) goto cleanup; } while (0);
   } else {
       do { if ((ret = (mbedtls_mpi_add_mpi(&r, &r, &grp->A))) != 0) goto cleanup; } while (0);
   }

   // r = x^3 + ax
   do { if ((ret = (mbedtls_mpi_mul_mpi(&r, &r, &x))) != 0) goto cleanup; } while (0);

   // r = x^3 + ax + b
   do { if ((ret = (mbedtls_mpi_add_mpi(&r, &r, &grp->B))) != 0) goto cleanup; } while (0);

   // Calculate square root of r over finite field P:
   //   r = sqrt(x^3 + ax + b) = (x^3 + ax + b) ^ ((P + 1) / 4) (mod P)

   // n = P + 1
   do { if ((ret = (mbedtls_mpi_add_int(&n, &grp->P, 1))) != 0) goto cleanup; } while (0);

   // n = (P + 1) / 4
   do { if ((ret = (mbedtls_mpi_shift_r(&n, 2))) != 0) goto cleanup; } while (0);

   // r ^ ((P + 1) / 4) (mod p)
   do { if ((ret = (mbedtls_mpi_exp_mod(&r, &r, &n, &grp->P, 0))) != 0) goto cleanup; } while (0);

   // Select solution that has the correct "sign" (equals odd/even solution in finite group)
   if ((input[0] == 0x03) != mbedtls_mpi_get_bit(&r, 0)) {
       // r = p - r
       do { if ((ret = (mbedtls_mpi_sub_mpi(&r, &grp->P, &r))) != 0) goto cleanup; } while (0);
   }

   // y => output
   ret = mbedtls_mpi_write_binary(&r, output + 1 + plen, plen);

cleanup:
   mbedtls_mpi_free(&r);
   mbedtls_mpi_free(&x);
   mbedtls_mpi_free(&n);

   return(ret);
}

int mbedtls_ecp_compress(
   const mbedtls_ecp_group *grp,
   const unsigned char *input, size_t ilen,
   unsigned char *output, size_t *olen, size_t osize
) {
   size_t plen;

   plen = mbedtls_mpi_size(&grp->P);

   *olen = plen + 1;

   if (osize < *olen)
       return(-0x4F00);

   if (ilen != 2 * plen + 1)
       return (-0x4F80);

   if (input[0] != 0x04)
       return(-0x4F80);

   // output will consist of 0x0?|X
   memcpy(output, input, *olen);

   // Encode even/odd of Y into first byte (either 0x02 or 0x03)
   output[0] = 0x02 + (input[2 * plen] & 1);

   return(0);
}

# 3810 "L:/PLT/pcac/mbedTLS/library/ecp.c"



