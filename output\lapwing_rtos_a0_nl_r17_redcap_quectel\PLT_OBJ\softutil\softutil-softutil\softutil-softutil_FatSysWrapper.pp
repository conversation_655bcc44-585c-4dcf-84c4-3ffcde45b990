//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_FatSysWrapper.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/FatSysWrapper.c
typedef unsigned int size_t ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef UINT32 Attribs_Mmu ;
typedef void ( * mmuNotifyEventFunc ) ( MMU_ERROR_STATUS mmuErrorHandle ) ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0 = 0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 , GPIO_PIN_33 , GPIO_PIN_34 , GPIO_PIN_35 , GPIO_PIN_36 , GPIO_PIN_37 , GPIO_PIN_38 , GPIO_PIN_39 ,	 
	 
 GPIO_PIN_40 , GPIO_PIN_41 , GPIO_PIN_42 , GPIO_PIN_43 , GPIO_PIN_44 , GPIO_PIN_45 , GPIO_PIN_46 , GPIO_PIN_47 ,	 
 GPIO_PIN_48 , GPIO_PIN_49 , GPIO_PIN_50 , GPIO_PIN_51 , GPIO_PIN_52 , GPIO_PIN_53 , GPIO_PIN_54 , GPIO_PIN_55 ,	 
 GPIO_PIN_56 , GPIO_PIN_57 , GPIO_PIN_58 , GPIO_PIN_59 , GPIO_PIN_60 , GPIO_PIN_61 , GPIO_PIN_62 , GPIO_PIN_63 ,	 
	 
 GPIO_PIN_64 , GPIO_PIN_65 , GPIO_PIN_66 , GPIO_PIN_67 , GPIO_PIN_68 , GPIO_PIN_69 , GPIO_PIN_70 , GPIO_PIN_71 ,	 
 GPIO_PIN_72 , GPIO_PIN_73 , GPIO_PIN_74 , GPIO_PIN_75 , GPIO_PIN_76 , GPIO_PIN_77 , GPIO_PIN_78 , GPIO_PIN_79 ,	 
 GPIO_PIN_80 , GPIO_PIN_81 , GPIO_PIN_82 , GPIO_PIN_83 , GPIO_PIN_84 , GPIO_PIN_85 , GPIO_PIN_86 , GPIO_PIN_87 ,	 
 GPIO_PIN_88 , GPIO_PIN_89 , GPIO_PIN_90 , GPIO_PIN_91 , GPIO_PIN_92 , GPIO_PIN_93 , GPIO_PIN_94 , GPIO_PIN_95 ,	 
	 
 GPIO_PIN_96 , GPIO_PIN_97 , GPIO_PIN_98 , GPIO_PIN_99 , GPIO_PIN_100 , GPIO_PIN_101 , GPIO_PIN_102 , GPIO_PIN_103 ,	 
 GPIO_PIN_104 , GPIO_PIN_105 , GPIO_PIN_106 , GPIO_PIN_107 , GPIO_PIN_108 , GPIO_PIN_109 , GPIO_PIN_110 , GPIO_PIN_111 ,	 
 GPIO_PIN_112 , GPIO_PIN_113 , GPIO_PIN_114 , GPIO_PIN_115 , GPIO_PIN_116 , GPIO_PIN_117 , GPIO_PIN_118 , GPIO_PIN_119 ,	 
 GPIO_PIN_120 , GPIO_PIN_121 , GPIO_PIN_122 , GPIO_PIN_123 , GPIO_PIN_124 , GPIO_PIN_125 , GPIO_PIN_126 , GPIO_PIN_127 ,	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

typedef union {
 rw_region_item compress_rw_region_list [ 64 ] ;

 UINT8 filer [ 2048 ] ;
 } LOADTABLE_AREA_RW_CPZ_INFO ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef unsigned int size_t ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef unsigned char BYTE ;
typedef unsigned short int WORD ;
typedef unsigned long DWORD ;
typedef WORD * WORD_PTR ;
typedef DWORD * DWORD_PTR ;
typedef BYTE BYTE_BITMASK ;
typedef VOID_PTR * VOID_PTR_PTR ;
typedef WORD FLASH_DATA_WIDTH ;
typedef char FDI_TCHAR ;
typedef OSASemaRef SEM_ID ;
typedef int SEM_STATUS ;
typedef SEM_MTX * SEM_MTX_ID ;
typedef WORD IDTYPE ;
typedef DATA_LOOKUP * DATA_LOOKUP_PTR ;
typedef DATA_LOCATION * DATA_LOC_PTR ;
typedef OPEN_PARAM * OPEN_STREAM_PTR ;
typedef COMMAND * COMMAND_PTR ;
typedef UNIT_HEADER * UNIT_HDR_PTR ;
typedef MULTI_INSTANCE * MULTI_INST_PTR ;
typedef BLOCK_INFO * BLK_INFO_PTR ;
typedef COMMAND_CONTROL * CMD_CNTRL_PTR ;
typedef WORD FILE_ID ;
typedef unsigned int size_t ;
typedef unsigned int clock_t ;
typedef unsigned int time_t ;
typedef va_list __gnuc_va_list ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_INT_IFC ,	 
 DIAG_EXT_IFC	 
 } DIAG_IF_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_UART_SPEED_921600 ,	 
 DIAG_UART_SPEED_1842000 ,	 
 DIAG_UART_SPEED_3000000 ,	 
 DIAG_UART_DISABLE ,	 
 } DIAG_UART_SPEED_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_MEM_ENABLE ,	 
 DIAG_MEM_DISABLE	 
 } DIAG_MEM_SWITCH;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 DIAG_DEV_USB ,	 
 DIAG_DEV_SD ,	 
 DIAG_DEV_FS ,	 
 DIAG_DEV_UART ,	 
 DIAG_DEV_SPI ,	 
 DIAG_DEV_NONE	 
 } DIAG_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 SULOG_DEV_USB ,	 
 SULOG_DEV_SD	 
 } SULOG_DEV_TYPE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 DIAG_IF_TYPE useIntIf ; /* use internal interface or external interface */	 
 DIAG_UART_SPEED_TYPE useHighSpeedUART ; /* high speed UART ( 460800 ) or low speed UART ( 115200 ) */	 
 DIAG_MEM_SWITCH diagMemSwitch ;	 
 DIAG_DEV_TYPE diagDevType ;	 
 SULOG_DEV_TYPE sulogDevType ;	 
 BOOL sdlAutoDelete ;	 
 } diagCfgDataS;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 dbID ;	 
 UINT32 filterBitLength ;	 
 UINT32 reserved [ 6 ] ;	 
 } DIAG_Nvm_Filter_File_Header_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MASS_STORAGE_DISABLE = 0x0 ,	 
 MASS_STORAGE_ENABLE = 0x1	 
 } MassStorage_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* MIFI driver*/	 
 USB_GENERIC_MIFI_DRIVER = 0x0 ,	 
 USB_MARVELL_MIFI_DRIVER = 0x1 ,	 
 USB_ASR_MIFI_DRIVER = 0x2 ,	 
 USB_GENERIC_MOD_DRIVER = 0x10 ,	 
 USB_GENERIC_MOD_ECM_DRIVER = 0x12 ,	 
 USB_DIAG_UAC_DRIVER = 0x14 ,	 
 USB_RNDIS_ONLY_DRIVER = 0x15 ,	 
	 
 /* MBIM driver*/	 
 USB_MBIM_ONLY_DRIVER = 0x40 ,	 
 USB_MBIM_GENERIC_DRIVER = 0x41 ,	 
 USB_MBIM_MAX_DRIVER = 0x4F ,	 
	 
 /* Other driver*/	 
 USB_CDROM_ONLY_DRIVER = 0x91 ,	 
 USB_CDROM_DIAG_DRIVER = 0x92 ,	 
 USB_DIAG_ONLY_DRIVER = 0x93 ,	 
 USB_MODEM_ONLY_DRIVER = 0x94 ,	 
 USB_MODEM_DIAG_DRIVER = 0x95 ,	 
	 
 USB_MAX_DRIVER = 0xFF	 
 } Usb_driver_typeE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_AUTO_INSTALL_DISABLE = 0x0 ,	 
 USB_AUTO_INSTALL_ENABLE = 0x1	 
 } Usb_auto_install_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 USB_OS_DETECT_DISABLE = 0x0 ,	 
 USB_OS_DETECT_ENABLE = 0x1	 
 } Usb_OS_detect_type;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Usb_driver_typeE usb_driver ;	 
 MassStorage_ConfigE mass_storage ;	 
 Usb_auto_install_type auto_install ;	 
 Usb_OS_detect_type os_detect ;	 
 } Usb_DriverS;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 YMODEM_DISABLE = 0x0 ,	 
 YMODEM_ENABLE = 0x1	 
 } Ymodem_Dump_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Ymodem_Dump_config config ;	 
 } Ymodem_Dump_type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MBIM_ONLY_DESP = 0x0 ,	 
 MBIM_ATDIAG_DESP = 0x1	 
 } MBIM_DESP_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 MBIM_DESP_config config ;	 
 } MBIM_CFG_type;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 EEH_DUMP_DEV_USB_SD ,	 
 EEH_DUMP_DEV_SPI	 
 } EEH_DUMP_DEV_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PMU_POR = 1 ,	 
 PMU_EMR ,	 
 PMU_WDTR = ( PMU_EMR+2 )	 
 } PMU_LastResetStatus;

typedef UINT8 UART_Activity ;
//ICAT EXPORTED STRUCT 
 typedef struct /* This is structure of the UART Configuration */ 
 {	 
 UART_OpMode opMode ; /* fifo mode , non fifo mode or DMA for basic interface*/	 
 UART_TriggerLevel triggerLevel ; /* the trigger level interrupt on 1 , 8 , 16 , 32 */	 
 UART_BaudRates baudRate ; /* the rate of the transmit and the receive up to 111520 ( default - 9600 ) .*/	 
 UART_WordLen numDataBits ; /* 5 , 6 , 7 , or 8 number of data bits in the UART data frame ( default - 8 ) . */	 
 UART_StopBits stopBits ; /* 1 , 1.500000 or 2 stop bits in the UART data frame ( default - 1 ) . */	 
 UART_ParityTBits parityBitType ; /* Even , Odd or no-parity bit type in the UART data frame ( default - Non ) . */	 
 UART_InterfaceType interfaceType ; /* number of interface that the UART driver supplies ( default - UART_IF_TYPE_L2 ) */	 
 BOOL modemSignal ; /* enable operate modem - TRUE , disable modem - FALSE */	 
 BOOL flowControl ; /* enable Auto flow Control - TRUE , disable Auto flow Control - FALSE */	 
 UINT8 sleepMode ; /* enable sleep mode - TRUE , more fine control - see UARTSleepMode enum */	 
 BOOL auto_baud ; /* enable auto_baud , auto-baud-rate detection within the UART ( default - FALSE ) */	 
 UART_SIRConfigure sirIrDA ;	 
 } UARTConfiguration;

//ICAT EXPORTED ENUM 
 typedef enum // change the order -1 to + 
 {	 
 UART_RC_OK = 1 , /* 1 - no errors */	 
	 
 UART_RC_PORT_NUM_ERROR = -100 , /* -100 - Error in the UART port number */	 
 UART_RC_NO_DATA_TO_READ , /* -99 - Eror no data to read from the FIFO UART */	 
 UART_RC_ILLEGAL_BAUD_RATE , /* -98 - Error in the UART Bayd Rate */	 
 UART_RC_UART_PARITY_BITS_ERROR , /* -97 - Error in parity bit */	 
 UART_RC_UART_ONE_STOP_BIT_ERROR , /* -96 - Error in one stop bit */	 
 UART_RC_ONE_HALF_OR_TWO_STOP_BIT_ERROR , /* -95 - Error in two stop bit */	 
 UART_RC_BAD_INTERFACE_TYPE , /* -94 - Error in the Interface Type */	 
 UART_RC_UART_NOT_AVAILABLE , /* -93 - Error in try to open UART that is open */	 
 UART_RC_NO_DATA_TO_WRITE , /* -92 - Error No data to writ the len = 0 */	 
 UART_RC_NOT_ALL_BYTE_WRITTEN , /* -91 - Error Not all the Byte write to the UART FIFO */	 
 UART_RC_ISR_ALREADY_BIND , /* -90 - Error try to bind ISR for Basic Interface */	 
 UART_RC_WRONG_ISR_UNBIND , /* -89 - Error in the UnBind ISR for Basic Interface */	 
 UART_RC_FIFO_NOT_EMPTY , /* -88 - Error , the UART FIFO not empty */	 
 UART_RC_UART_OPEN , /* -87 - Error try chance the configurr when the UART open */	 
 UART_RC_GPIO_ERR , /* -86 - Error in the Configure of the GPIO */	 
 UART_RC_IRDA_CONFIG_ERR /* -85 - Illegal IrDA configuration */	 
 } UART_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAM_LOG = 0x00 ,	 
 USB_RAM_LOG = 0x01 ,	 
 MAX_RAM_LOG = 0x02	 
 } Ram_Log_Type;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ERR_RAMLOG_ENABLE = 0x00 ,	 
 USB_RAMLOG_ENABLE = 0x01	 
 } RamLog_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 LOG_DISABLE = 0x0 ,	 
 UART_LOG_ENABLE = 0x1 ,	 
 ACAT_LOG_ENABLE = 0x2	 
 } Log_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MSG_DISABLE = 0x0 ,	 
 ACAT_MSG_ENABLE = 0x1	 
 } Msg_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RTI_LOG_DISABLE = 0x0 ,	 
 RTI_LOG_ENABLE = 0x1	 
 } RTI_ConfigE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Log_ConfigE log_cfg ;	 
 Msg_ConfigE msg_cfg ;	 
 RTI_ConfigE rti_cfg ;	 
 RamLog_ConfigE ramlog ;	 
 } Log_ConfigS;

typedef void ( *UARTNotifyInterrupt ) ( UART_Port ) ;
typedef void ( *UsbLogPrint_t ) ( const char * , ... ) ;
typedef volatile unsigned long VUINT32_T ;
typedef unsigned long UINT32_T ;
typedef volatile unsigned int VUINT_T ;
typedef unsigned int UINT_T ;
typedef int INT_T ;
typedef unsigned short UINT16_T , USHORT ;
typedef volatile unsigned short VUINT16_T ;
typedef unsigned char UINT8_T ;
typedef char INT8_T ;
typedef unsigned int UINT , *PUINT ;
typedef unsigned long long UINT64 , *PUINT64 ;
typedef unsigned char UCHAR , BYTE , *PUCHAR ;
typedef int INT , *PINT ;
typedef long long INT64 , *PINT64 ;
typedef char CHAR , *PCHAR ;
typedef unsigned int size_t ;
typedef volatile UINT VUINT , *PVUINT ;
typedef volatile UINT64 VUINT64 , *PVUINT64 ;
typedef volatile unsigned int VUINT32 , *PVUINT32 ;
typedef volatile UINT16 VUINT16 , *PVUINT16 ;
typedef volatile UINT8 VUINT8 , *PVUINT8 ;
typedef volatile UCHAR VUCHAR , *PVUCHAR ;
typedef volatile INT VINT , *PVINT ;
typedef volatile INT64 VINT64 , *PVINT64 ;
typedef volatile int VINT32 , *PVINT32 ;
typedef volatile INT16 VINT16 , *PVINT16 ;
typedef volatile char VINT8 , *PVINT8 ;
typedef volatile CHAR VCHAR , *PVCHAR ;
typedef UINT16 WORD ;
typedef signed char int8_t ;
typedef unsigned char uint8_t ;
typedef signed short int16_t ;
typedef unsigned short uint16_t ;
typedef signed int int32_t ;
typedef unsigned int uint32_t ;
typedef signed long long int int64_t ;
typedef unsigned long long int uint64_t ;
typedef UINT32 rti_uint32_t ;
typedef UINT64 rti_uint64_t ;
typedef TX_THREAD* rti_thread_t ;
//ICAT EXPORTED STRUCT 
 typedef struct {	 
 UINT32 product_id ;	 
 UINT8 data [ 20 ] ;	 
 } InfoForBoardTracking_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 rti_mode_none = 0x00 ,	 
 rti_check_mode = 0x01 ,	 
 rti_timer_mode = 0x02 ,	 
 rti_log2acat_mode = 0x03 ,	 
 rti_psoff_mode = 0x04 ,	 
 rti_uarttrace_mode = 0x05 ,	 
 rti_rfuarttest_mode = 0xFF ,	 
	 
 rti_urtlog_mode = 0x100 ,	 
 rti_usbtrace_mode = 0x101 ,	 
 rti_muxtrace_mode = 0x102 ,	 
 rti_fsyslog_mode = 0x103 ,	 
 rti_mode_max = 0xFFFF	 
 } rti_mode;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_DISABLE=0 ,	 
 RTI_EN_VER1=1 ,	 
 RTI_EN_VER2=2	 
 } RTI_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_QUE_DISABLE=0 ,	 
 RTI_QUE_ENABLE=1	 
 } RTI_QUE_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 RTI_EVT_DISABLE=0 ,	 
 RTI_EVT_ENABLE=1	 
 } RTI_EVT_TYPE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 TIME_OUT_3MS=0x62 ,	 
 TIME_OUT_4MS=0x83 ,	 
 TIME_OUT_5MS=0xA4 ,	 
 TIME_OUT_6MS=0xC4 ,	 
 TIME_OUT_MAX=0xFF	 
 } Timeout_Threshold;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 RTI_TYPE rtiType ;	 
 RTI_QUE_TYPE rtiQueType ;	 
 RTI_EVT_TYPE rtiEvtType ;	 
	 
 int rtiChange ;	 
 int rtiHT ;	 
 int rtiLT ;	 
	 
 int modeChange ;	 
 int modeHT ;	 
 int modeLT ;	 
	 
 Timeout_Threshold Timeout ;	 
 rti_mode rtiMode ;	 
 } RTICfg_t;

typedef signed char SINT8 ;
typedef signed short SINT16 ;
typedef signed long SINT32 ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef float FLOAT32 ;
typedef double FLOAT64 ;
typedef long double FLOAT80 ;
typedef signed short TCHAR ;
typedef TCHAR * TCHAR_PTR ;
typedef SINT8 * SINT8_PTR ;
typedef SINT16 * SINT16_PTR ;
typedef SINT32 * SINT32_PTR ;
typedef UINT8 * UINT8_PTR ;
typedef UINT16 * UINT16_PTR ;
typedef UINT32 * UINT32_PTR ;
typedef FLOAT32 * FLOAT32_PTR ;
typedef FLOAT64 * FLOAT64_PTR ;
typedef FLOAT80 * FLOAT80_PTR ;
typedef void * VOID_PTR ;
typedef UINT8 BOOL ;
typedef UINT8 BOOLEAN ;
typedef UINT32 FFS_FileDirTime ;
typedef FFS_FileDirTime* FFS_FileDirTimePtr ;
typedef OSATaskRef OS_Task_fdi ;
typedef void ( *OS_TaskEntryPtr ) ( UINT32 , void* ) ;
typedef OSASemaRef OS_Semaphore ;
typedef _OS_Mutex* OS_Mutex_fdi ;
typedef UINT32 OS_IRQ_Disable_Func ( void ) ;
typedef UINT32 OS_IRQ_Enable_Func ( UINT32 irq_level ) ;
typedef void OS_Mem_Free_Func ( void * ptr ) ;
typedef void * OS_Mem_Malloc_Func ( UINT32 size ) ;
typedef BOOL OS_MutexCreate_Func ( OS_Mutex_fdi * mutex_ptr ,
 BOOL owned ,
 const TCHAR * name ) ;
typedef BOOL OS_MutexInit_Func ( OS_Mutex_fdi * mutex_ptr ,
 OS_Mutex_fdi mtx_str ,
 BOOL owned ,
 const TCHAR * name ) ;
typedef BOOL OS_MutexClear_Func ( OS_Mutex_fdi * mutex_ptr ) ;
typedef BOOL OS_MutexDestroy_Func ( OS_Mutex_fdi* mutex_ptr ) ;
typedef BOOL OS_MutexPend_Func ( OS_Mutex_fdi* mutex_ptr , UINT32 timeout ) ;
typedef BOOL OS_MutexRelease_Func ( OS_Mutex_fdi* mutex_ptr ) ;
typedef BOOL OS_SemCreate_Func ( OS_Semaphore * sem_ptr ,
 UINT32 initial_count ,
 const TCHAR * name ) ;
typedef BOOL OS_SemDestroy_Func ( OS_Semaphore * sem_ptr ) ;
typedef BOOL OS_SemPend_Func ( const OS_Semaphore * sem_ptr , UINT32 timeout ) ;
typedef BOOL OS_SemRelease_Func ( const OS_Semaphore * sem_ptr ) ;
typedef BOOL OS_Task_Create_Func ( OS_Task_fdi * task_ptr ,
 const TCHAR * task_name ,
 OS_TaskEntryPtr func_ptr ,
 UINT8 argc ,
 void * argv ) ;
typedef BOOL OS_Task_Destroy_Func ( const OS_Task_fdi * task_ptr ) ;
typedef OS_Task_fdi OS_Task_GetCurrent_Func ( void ) ;
typedef UINT16 OS_Task_GetPriority_Func ( const OS_Task_fdi * task_ptr ) ;
typedef BOOL OS_Task_SetPriority_Func ( const OS_Task_fdi * task_ptr ,
 UINT16 priority ) ;
typedef PLAT_OEM_SubArrayInfo *PLAT_OEM_SubArrayInfoPtr ;
typedef UINT32 LockStatus ;
typedef UINT32 FLASH_ERROR ;
typedef UINT32 FLASH_VolumeHandle ;
typedef UINT32 *FLASH_VolumeHandlePtr ;
typedef signed int ptrdiff_t ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef long double max_align_t ;
typedef signed char D_INT8 ;
typedef unsigned char D_UINT8 ;
typedef short D_INT16 ;
typedef unsigned short D_UINT16 ;
typedef long D_INT32 ;
typedef unsigned long D_UINT32 ;
typedef long long D_INT64 ;
typedef unsigned long long D_UINT64 ;
typedef D_INT32 D_INTPTR ;
typedef D_UINT32 D_UINTPTR ;
typedef D_UINT32 DCLSTATUS ;
typedef D_UINT16 D_BOOL ;
typedef char D_CHAR ;
typedef unsigned char D_UCHAR ;
typedef D_UINT8 D_BUFFER ;
typedef D_UINT64 DCLHRTIMESTAMP ;
typedef D_UINT32 DCLTIMESTAMP ;
typedef void ( *PFNDCLOUTPUTSTRING ) ( void * pUserData , const char * pszString ) ;
typedef char tfsChar ;
typedef int DLP_size_t ;
typedef unsigned int DLP_mode_t ;
typedef long DLP_off_t ;
typedef unsigned short DLP_dev_t ;
typedef unsigned long DLP_ino_t ;
typedef unsigned short DLP_nlink_t ;
typedef unsigned short DLP_uid_t ;
typedef unsigned short DLP_gid_t ;
typedef unsigned long DLP_time_t ;
typedef int DLP_blksize_t ;
typedef unsigned long DLP_blkcnt_t ;
typedef void DLP_DIR ;
typedef int GFSSize_t ;
typedef long GFSOff_t ;
typedef void GFS_DIR ;
typedef int GFS_size_t ;
typedef unsigned int GFS_mode_t ;
typedef long GFS_off_t ;
typedef unsigned short GFS_dev_t ;
typedef unsigned long GFS_ino_t ;
typedef unsigned short GFS_nlink_t ;
typedef unsigned short GFS_uid_t ;
typedef unsigned short GFS_gid_t ;
typedef unsigned long GFS_time_t ;
typedef unsigned long GFS_blkcnt_t ;
typedef unsigned int GFS_fileID ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
typedef void ( *free_cb ) ( void *buf ) ;
typedef void ( *complete_cb ) ( pUsb3Dwc pProps , Usb3DwcEp *pEp , Usb3DwcReqQ *pReq , UINT32 xfer ) ;
typedef unsigned int time_t ;
typedef UINT_T ( *InitFlash_F ) ( UINT8_T FlashNum , FlashBootType_T FlashBootType , UINT8_T *P_DefaultPartitionNum ) ;
typedef UINT_T ( *FinalizeFlash_F ) ( FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *ReadFlash_F ) ( UINT_T FlashOffset , UINT_T LocalBuffer , UINT_T Size , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *WriteFlash_F ) ( UINT_T FlashOffset , UINT_T LocalBuffer , UINT_T Size , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *EraseFlash_F ) ( UINT_T FlashOffset , UINT_T Size , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *ResetFlash_F ) ( FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *WipeFlash_F ) ( FlashBootType_T FlashBootType ) ;
typedef void ( *ChangePartition_F ) ( UINT_T PartitionNum , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *WriteOTP_F ) ( UINT_T FlashOffset , UINT_T LocalBuffer , UINT_T Size ) ;
typedef UINT_T ( *ReadOTP_F ) ( UINT_T FlashOffset , UINT_T LocalBuffer , UINT_T Size ) ;
typedef UINT_T ( *LockOTP_F ) ( void ) ;
typedef UINT_T ( *ConvertToLogicalAddr_F ) ( UINT_T FlashLocation , FlashBootType_T FlashBootType ) ;
typedef UINT_T ( *DataSearchFunc_F ) ( UINT_T *pResult , void *pResvHeader , UINT_T pkgID ) ;
typedef UINT_T ( *DataSearchFindNext_F ) ( UINT_T *pResult ) ;
typedef UINT_T ( *FlashInitAfterTimDownload_F ) ( void *pResvHeader , DataSearchFunc_F , DataSearchFunc_F , DataSearchFindNext_F , UINT_T *myPkgIDs ) ;
typedef unsigned int time_t ;
typedef unsigned char BOOL ;
typedef signed char BYTE8 ;
typedef unsigned char UBYTE8 ;
typedef short HWD16 ;
typedef long WORD32 ;
typedef unsigned short UHWD16 ;
typedef unsigned long UWORD32 ;
typedef unsigned long long ULLONG64 ;
typedef char S8 ;
typedef HWD16 S16 ;
typedef WORD32 S32 ;
typedef UBYTE8 U8 ;
typedef UHWD16 U16 ;
typedef UWORD32 U32 ;
typedef void ( *apTYPE_rCallback ) ( UWORD32 ) ;
typedef WORD32 apError ;
typedef UWORD32 fatTYPE_tSector ;
typedef UWORD32 fatTYPE_tCluster ;
typedef UWORD32 fatTYPE_tEntry ;
typedef UWORD32 fatTYPE_tSize ;
typedef signed char int8_t ;
typedef signed short int int16_t ;
typedef signed int int32_t ;
typedef signed __int64 int64_t ;
typedef unsigned char uint8_t ;
typedef unsigned short int uint16_t ;
typedef unsigned int uint32_t ;
typedef unsigned __int64 uint64_t ;
typedef signed char int_least8_t ;
typedef signed short int int_least16_t ;
typedef signed int int_least32_t ;
typedef signed __int64 int_least64_t ;
typedef unsigned char uint_least8_t ;
typedef unsigned short int uint_least16_t ;
typedef unsigned int uint_least32_t ;
typedef unsigned __int64 uint_least64_t ;
typedef signed int int_fast8_t ;
typedef signed int int_fast16_t ;
typedef signed int int_fast32_t ;
typedef signed __int64 int_fast64_t ;
typedef unsigned int uint_fast8_t ;
typedef unsigned int uint_fast16_t ;
typedef unsigned int uint_fast32_t ;
typedef unsigned __int64 uint_fast64_t ;
typedef signed int intptr_t ;
typedef unsigned int uintptr_t ;
typedef signed long long intmax_t ;
typedef unsigned long long uintmax_t ;
typedef unsigned short wchar_t ;
typedef uint32_t lfs_size_t ;
typedef uint32_t lfs_off_t ;
typedef int32_t lfs_ssize_t ;
typedef int32_t lfs_soff_t ;
typedef uint32_t lfs_block_t ;
DIAG_FILTER ( FDI , fatsys , F_Open_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI fopen start: %s: mode: %s " , filename_ptr , mode );

DIAG_FILTER ( FDI , fatsys , F_Open_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI fopen succeed: %s: handle: %d " , filename_ptr , handle );

DIAG_FILTER ( FDI , fatsys , F_Open_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI fopen error: %s , mode: %s " , filename_ptr , mode );

DIAG_FILTER ( FDI , fatsys , F_Close_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI close start , handle: %d " , stream );

DIAG_FILTER ( FDI , fatsys , F_Close_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI close error , handle: %d , errCode: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_Close_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI close succeed , handle: %d " , stream );

DIAG_FILTER ( FDI , fatsys , F_read_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI read error , handle: %d , errCode: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_readEx_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI read error , handle: %d , errCode: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_write_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI write start , handle: %d , size: %ld " , stream , element_size*count );

DIAG_FILTER ( FDI , fatsys , F_write_error0 , DIAG_INFORMATION)  
 diagPrintf ( " FDI write error , handle: %d , element size is 0 " , stream );

DIAG_FILTER ( FDI , fatsys , F_write_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI write error , handle: %d , errCode: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_write_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI write succeed , handle: %d , actual size: %ld " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_writeEx_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI write start , handle: %d , size: %ld " , stream , element_size*count );

DIAG_FILTER ( FDI , fatsys , F_writeEx_zero_size , DIAG_INFORMATION)  
 diagPrintf ( " FDI write error , handle: %d , size is 0 " , stream );

DIAG_FILTER ( FDI , fatsys , F_writeEx_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI write error , handle: %d , errCode: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_writeEx_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI write succeed , handle: %d , actual size: %ld " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_seek_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI seek start , handle: %d , offset:%d , wherefrom: %d " , stream , offset , wherefrom );

DIAG_FILTER ( FDI , fatsys , F_seek_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI seek error , handle: %d , errCode: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_seek_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI seek succeed , handle: %d , res: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_tell_start , DIAG_INFORMATION)  
 diagPrintf ( " Fdi tell start , handle: %d " , stream );

DIAG_FILTER ( FDI , fatsys , F_tell_succeed , DIAG_INFORMATION)  
 diagPrintf ( " Fdi tell succeed , handle: %d " , stream );

DIAG_FILTER ( FDI , fatsys , F_Opendir , DIAG_INFORMATION)  
 diagPrintf ( " Fdi open dir error: %s , status: %d " , dir_name , handle );

DIAG_FILTER ( FDI , fatsys , F_Opendir_succeed , DIAG_INFORMATION)  
 diagPrintf ( " Fdi open dir succeed: %s: handle: %d " , dir_name , handle );

DIAG_FILTER ( FDI , fatsys , F_Closedir , DIAG_INFORMATION)  
 diagPrintf ( " Fdi close dir error: handle:%d , status: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_Closedir_succeed , DIAG_INFORMATION)  
 diagPrintf ( " Fdi close dir succeed:handle: %d , res: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_findfirst_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI findfirst start , filename_ptr: %s " , filename_ptr );

DIAG_FILTER ( FDI , fatsys , F_findfirst_error_0 , DIAG_INFORMATION)  
 diagPrintf ( " FDI findfirst error , ERR_PARAM " );

DIAG_FILTER ( FDI , fatsys , F_findfirst_error_1 , DIAG_INFORMATION)  
 diagPrintf ( " Fdi findfirst error 1 , errCode: %d " , ret );

DIAG_FILTER ( FDI , fatsys , F_findfirst_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI findfirst succeed , the found file: %s " , gFullFileName );

DIAG_FILTER ( FDI , fatsys , FDI_Stat_failure , DIAG_INFORMATION)  
 diagPrintf ( " FDI_Stat failure , ret: %d " , ret );

DIAG_FILTER ( FDI , fatsys , F_findnext_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI findnext start " );

DIAG_FILTER ( FDI , fatsys , F_findnext_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI findnext error errCode: %d " , rec );

DIAG_FILTER ( FDI , fatsys , F_findnext_finished , DIAG_INFORMATION)  
 diagPrintf ( " FDI find process finished: the final file: %s " , gFullFileName );

DIAG_FILTER ( FDI , fatsys , F_findnext_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI findnext succeed: the found file: %s , isDir %d " , gFullFileName , Stat.st_mode & fatTYPE_DIR );

DIAG_FILTER ( FDI , fatsys , F_remove_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI remove start , file: %s " , filename_ptr );

DIAG_FILTER ( FDI , fatsys , F_remove_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI remove error , file: %s , errCode: %d " , filename_ptr , res );

DIAG_FILTER ( FDI , fatsys , F_remove_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI remove succeed , file: %s " , filename_ptr );

DIAG_FILTER ( FDI , fatsys , F_rename_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI rename start , file: %s " , name );

DIAG_FILTER ( FDI , fatsys , F_rename_error , DIAG_INFORMATION)  
 diagPrintf ( " FDI rename error , file: %s , errCode: %d " , name , res );

DIAG_FILTER ( FDI , fatsys , F_rename_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI rename succeed , file: %s " , name );

DIAG_FILTER ( FDI , fatsys , F_mkdir_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI mkdir start , file: %s " , dir_name );

DIAG_FILTER ( FDI , fatsys , F_mkdir_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI mkdir succeed , file: %s " , dir_name );

DIAG_FILTER ( FDI , fatsys , F_eof_start , DIAG_INFORMATION)  
 diagPrintf ( " Fdi eof start , handle: %d " , stream );

DIAG_FILTER ( FDI , fatsys , F_eof_succeed , DIAG_INFORMATION)  
 diagPrintf ( " Fdi eof succeed , handle: %d , res: %d " , stream , res );

DIAG_FILTER ( FDI , fatsys , F_access_start , DIAG_INFORMATION)  
 diagPrintf ( " FDI access start , file: %s , mode %lu " , path , mode );

DIAG_FILTER ( FDI , fatsys , F_access_succeed , DIAG_INFORMATION)  
 diagPrintf ( " FDI access succeed , file: %s " , path );

