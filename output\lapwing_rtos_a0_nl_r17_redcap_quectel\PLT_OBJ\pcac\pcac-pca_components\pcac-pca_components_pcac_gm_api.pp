//PPC Version : V2.1.9.30
//PPL Source File Name : pcac-pca_components_pcac_gm_api.ppp
//PPL Source File Name : L:/PLT/pcac/pca_components/src/pcac_gm_api.c
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef UINT16 PCAC_STATUS ;
typedef UINT16 PCAC_INIT_STATUS ;
typedef UINT32 MSL_TRACE_LEVEL ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
typedef UINT32 PCAC_TRACE_LEVEL ;
