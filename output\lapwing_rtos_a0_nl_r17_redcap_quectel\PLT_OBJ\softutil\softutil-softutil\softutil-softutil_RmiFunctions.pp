//PPC Version : V2.1.9.30
//PPL Source File Name : softutil-softutil_RmiFunctions.ppp
//PPL Source File Name : L:/PLT/softutil/softutil/src/RmiFunctions.c
typedef unsigned int size_t ;
typedef va_list __gnuc_va_list ;
typedef unsigned int size_t ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0 = 0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 , GPIO_PIN_33 , GPIO_PIN_34 , GPIO_PIN_35 , GPIO_PIN_36 , GPIO_PIN_37 , GPIO_PIN_38 , GPIO_PIN_39 ,	 
	 
 GPIO_PIN_40 , GPIO_PIN_41 , GPIO_PIN_42 , GPIO_PIN_43 , GPIO_PIN_44 , GPIO_PIN_45 , GPIO_PIN_46 , GPIO_PIN_47 ,	 
 GPIO_PIN_48 , GPIO_PIN_49 , GPIO_PIN_50 , GPIO_PIN_51 , GPIO_PIN_52 , GPIO_PIN_53 , GPIO_PIN_54 , GPIO_PIN_55 ,	 
 GPIO_PIN_56 , GPIO_PIN_57 , GPIO_PIN_58 , GPIO_PIN_59 , GPIO_PIN_60 , GPIO_PIN_61 , GPIO_PIN_62 , GPIO_PIN_63 ,	 
	 
 GPIO_PIN_64 , GPIO_PIN_65 , GPIO_PIN_66 , GPIO_PIN_67 , GPIO_PIN_68 , GPIO_PIN_69 , GPIO_PIN_70 , GPIO_PIN_71 ,	 
 GPIO_PIN_72 , GPIO_PIN_73 , GPIO_PIN_74 , GPIO_PIN_75 , GPIO_PIN_76 , GPIO_PIN_77 , GPIO_PIN_78 , GPIO_PIN_79 ,	 
 GPIO_PIN_80 , GPIO_PIN_81 , GPIO_PIN_82 , GPIO_PIN_83 , GPIO_PIN_84 , GPIO_PIN_85 , GPIO_PIN_86 , GPIO_PIN_87 ,	 
 GPIO_PIN_88 , GPIO_PIN_89 , GPIO_PIN_90 , GPIO_PIN_91 , GPIO_PIN_92 , GPIO_PIN_93 , GPIO_PIN_94 , GPIO_PIN_95 ,	 
	 
 GPIO_PIN_96 , GPIO_PIN_97 , GPIO_PIN_98 , GPIO_PIN_99 , GPIO_PIN_100 , GPIO_PIN_101 , GPIO_PIN_102 , GPIO_PIN_103 ,	 
 GPIO_PIN_104 , GPIO_PIN_105 , GPIO_PIN_106 , GPIO_PIN_107 , GPIO_PIN_108 , GPIO_PIN_109 , GPIO_PIN_110 , GPIO_PIN_111 ,	 
 GPIO_PIN_112 , GPIO_PIN_113 , GPIO_PIN_114 , GPIO_PIN_115 , GPIO_PIN_116 , GPIO_PIN_117 , GPIO_PIN_118 , GPIO_PIN_119 ,	 
 GPIO_PIN_120 , GPIO_PIN_121 , GPIO_PIN_122 , GPIO_PIN_123 , GPIO_PIN_124 , GPIO_PIN_125 , GPIO_PIN_126 , GPIO_PIN_127 ,	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 short data0 ;	 
 short data1 ;	 
 short data2 ;	 
 } DummyStruct;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 DUMMY_ZERO = 0 ,	 
 DUMMY_ONE ,	 
 DUMMY_TWO ,	 
 DUMMY_THREE	 
 } DummyEnum;

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , restartCmd 
 void restartCmd ( void* pv ) 
 {	 
 // doRestart ( ) ;	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , ArmRead16 
 void armRead16 ( void* p ) 
 {	 
 UINT32 addr=* ( UINT32* ) p ;	 
 UINT32 val ;	 
 val=* ( UINT16* ) addr ;	 
DIAG_FILTER ( SW_PLAT , CONTROL , ArmRead16Reply , DIAG_INFORMATION)  
 diagPrintf ( " Read from %lx ( 16 bit ) : %x " , addr , val );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , ArmRead32 
 void armRead32 ( void* p ) 
 {	 
 UINT32 addr=* ( UINT32* ) p ;	 
 UINT32 val ;	 
 val=* ( UINT32* ) addr ;	 
DIAG_FILTER ( SW_PLAT , CONTROL , ArmRead32Reply , DIAG_INFORMATION)  
 diagPrintf ( " Read from %lx ( 32 bit ) : %lx " , addr , val );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , ArmWrite16 
 void armWrite16 ( void* p ) 
 {	 
 UINT32 addr=* ( UINT32* ) p ;	 
 UINT32 val=* ( ( UINT32* ) p+1 ) ;	 
 * ( UINT16* ) addr=val ;	 
DIAG_FILTER ( SW_PLAT , CONTROL , ArmWrite16Reply , DIAG_INFORMATION)  
 diagPrintf ( " Written to %lx ( 16 bit ) : %x " , addr , val );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , ArmWrite32 
 void armWrite32 ( void* p ) 
 {	 
 UINT32 addr=* ( UINT32* ) p ;	 
 UINT32 val=* ( ( UINT32* ) p+1 ) ;	 
 * ( UINT32* ) addr=val ;	 
DIAG_FILTER ( SW_PLAT , CONTROL , ArmWrite32Reply , DIAG_INFORMATION)  
 diagPrintf ( " Written to %lx ( 32 bit ) : %lx " , addr , val );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , memTest16 
 void memTest16 ( void* p ) 
 {	 
 UINT32 addr=* ( UINT32* ) p ;	 
 UINT32 size=* ( ( UINT32* ) p+1 ) ;	 
 UINT32 i , val ;	 
 UINT32 failureCount=0 ;	 
 UINT16 shift=0xa5a5 ;	 
	 
 if ( size>0x800000 ) size=0x800000 ;	 
 else size&=~1 ; // even	 
 addr&=~1 ;	 
	 
DIAG_FILTER ( SW_PLAT , CONTROL , memTestReplyWrite , DIAG_INFORMATION)  
 diagTextPrintf ( " Mem Test: Write " );

	 
	 
 for ( i=0 ; i<size ; i+=2 )	 
 {		 
 * ( UINT16* ) ( addr+i ) = ( UINT16 ) ( addr+i+shift ) ;		 
 }	 
	 
DIAG_FILTER ( SW_PLAT , CONTROL , memTestReplyRead , DIAG_INFORMATION)  
 diagTextPrintf ( " Mem Test: Read Back " );

	 
	 
 for ( i=0 ; i<size ; i+=2 )	 
 {		 
 val=* ( UINT16* ) ( addr+i ) ;		 
 if ( val!= ( UINT16 ) ( addr+i+shift ) ) failureCount++ ;		 
 }	 
	 
DIAG_FILTER ( SW_PLAT , CONTROL , memTestReply , DIAG_INFORMATION)  
 diagPrintf ( " Mem Test Done , %d failures " , failureCount );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , AAAP , getPlpVersion 
 void getPlpVersion ( void *p ) 
 {	 
 // extern const char plpVersion [ ] ;	 
 const char plpVersion [ ] = " Not available " ;	 
DIAG_FILTER ( SW_PLAT , AAAP , plpVersionReport , DIAG_INFORMATION)  
 diagPrintf ( " PLP version: %s " , plpVersion );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , AAAP , holdPLP 
 void holdPLPCmd ( void *p ) 
 {	 
 // holdPLP ( ) ;	 
DIAG_FILTER ( SW_PLAT , AAAP , holdPLP_reply , DIAG_INFORMATION)  
 diagTextPrintf ( " PLP is held in reset " );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , malloc 
 void exportedMalloc ( UINT32 *p , int len ) 
 {	 
 if ( len>=sizeof ( UINT32 ) )	 
 {		 
 UINT32 size=*p ;		 
 UINT32 addr= ( UINT32 ) malloc ( size ) ;		 
DIAG_FILTER ( SW_PLAT , CONTROL , malloc_reply , DIAG_INFORMATION)  
 diagPrintf ( " Allocated: 0x%lx " , addr );

		 
		 
 /*mischecked by coverity*/		 
 /*coverity [ leaked_storage ] */		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , free 
 void exportedFree ( UINT32 *p , int len ) 
 {	 
 if ( len>=sizeof ( UINT32 ) )	 
 {		 
 UINT32 addr=*p ;		 
 free ( ( void * ) addr ) ;		 
DIAG_FILTER ( SW_PLAT , CONTROL , free_reply , DIAG_INFORMATION)  
 diagPrintf ( " Deallocated: 0x%lx " , addr );

		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , ICacheParityErrorCount 
 void ICacheParityErrorCount ( void *p ) 
 {	 
 extern UINT32 ICParityCount ;	 
DIAG_FILTER ( SW_PLAT , CONTROL , ICacheParityErrorCount_rep , DIAG_INFORMATION)  
 diagPrintf ( " IC parity error numer: %d " , ICParityCount );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , GetSteppingID 
 void expGetSteppingID ( void *p ) 
 {	 
 CPU_Version id=GetCpuVersion ( ) ;	 
 char name [ ] = " Bx " ;	 
 switch ( id )	 
 {		 
 case CPU_HERMON_B0 :		 
 case CPU_HERMON_B1 :		 
 case CPU_HERMON_B2 :		 
 case CPU_HERMON_B3 :		 
 name [ 1 ] = ' 0 ' + ( id-CPU_HERMON_B0 ) ;		 
 break ;		 
 default:		 
 strcpy ( name , " ?? " ) ;		 
 }	 
DIAG_FILTER ( SW_PLAT , CONTROL , GetSteppingID_rep , DIAG_INFORMATION)  
 diagPrintf ( " Hermon stepping is: %s " , name );

	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , writeUINT8 
 void writeUINT8 ( volatile void *base ) 
 {	 
 UINT32 *desAdd = ( UINT32* ) ( * ( UINT32* ) ( base ) ) ;	 
 UINT8 val2Write = * ( ( UINT8* ) base+4 ) ;	 
	 
 *desAdd = ( *desAdd & 0xffffff00 ) | val2Write ;	 
	 
DIAG_FILTER ( SW_PLAT , CONTROL , writeUINT8 , writeUINT8a)  
 diagPrintf ( " address %lx contain %lx " , desAdd , *desAdd );

	 
	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , readUINT32burst 
 void readUINT32 ( volatile void *base , void *byteAmount ) 
 {	 
 UINT32 numOfUINT32 , i , *baseAdd ;	 
 numOfUINT32 = * ( ( UINT32* ) base+1 ) ;	 
 baseAdd = ( UINT32* ) ( * ( UINT32* ) base ) ;	 
	 
 if ( ( UINT32 ) byteAmount==4 )	 
 numOfUINT32=1 ; /* in case only address is given*/	 
	 
 for ( i=numOfUINT32 ; i>0 ; i-- )	 
 {		 
DIAG_FILTER ( SW_PLAT , CONTROL , readUINT32 , readUINT32a)  
 diagPrintf ( " address %lx contain %lx " , baseAdd , *baseAdd );

		 
 baseAdd++ ;		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , readUINT16burst 
 void readUINT16 ( volatile void *base , void *byteAmount ) 
 {	 
 UINT32 numOfUINT16 , i ;	 
 UINT16 *baseAdd ;	 
	 
 numOfUINT16 = * ( ( UINT32* ) base+1 ) ;	 
 baseAdd = ( UINT16 * ) ( * ( UINT32* ) base ) ;	 
	 
 if ( ( UINT32 ) byteAmount==4 )	 
 numOfUINT16=1 ; /* in case only address is given*/	 
	 
 for ( i=numOfUINT16 ; i>0 ; i-- )	 
 {		 
DIAG_FILTER ( SW_PLAT , CONTROL , readUINT16 , readUINT16a)  
 diagPrintf ( " address %lx contain %x " , baseAdd , *baseAdd );

		 
 baseAdd++ ;		 
 }	 
 }

//ICAT EXPORTED FUNCTION - SW_PLAT , CONTROL , readUINT8burst 
 void readUINT8 ( volatile void *base , void *byteAmount ) 
 {	 
 UINT32 numOfUINT8 , i ;	 
 UINT8 *baseAdd ;	 
	 
 numOfUINT8 = * ( ( UINT32* ) base+1 ) ;	 
 baseAdd = ( UINT8 * ) ( * ( UINT32* ) base ) ;	 
	 
 if ( ( UINT32 ) byteAmount==4 )	 
 numOfUINT8=1 ; /* in case only address is given*/	 
	 
 for ( i=numOfUINT8 ; i>0 ; i-- )	 
 {		 
DIAG_FILTER ( SW_PLAT , CONTROL , readUINT8 , readUINT8a)  
 diagPrintf ( " address %lx contain %x " , baseAdd , *baseAdd );

		 
 baseAdd++ ;		 
 }	 
 }

