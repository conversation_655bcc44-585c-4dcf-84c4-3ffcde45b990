# 1 "L:/PLT/softutil/softutil/src/mep_data.c"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

 /*****************************************************************************
 *				Mep_data.c
 *****************************************************************************
 *
 *	A sample implementation of f8, the 3GPP Confidentiality algorithm.
 *
 *	This has been coded for clarity, not necessarily for efficiency.
 *
 *	This will compile and run correctly on both Intel (little endian)
 *  and Sparc (big endian) machines. (Compilers used supported 32-bit ints)
 *
 *	Version 1.0		05 November  1999
 *
 *****************************************************************************/

# 1 "L:/PLT/softutil/softutil/inc/mep.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/





/**************************************************************************************/
/**************************************************************************************/

# 1 "L:/PLT/hal/core/inc/utils.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

//Copyright 2005, Intel Corporation, All rights reserved.
/************************************************************************/
/*   Utils.h - 															*/
/* 																		*/
/************************************************************************/




# 1 "L:/PLT/csw/platform/inc/global_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/****************************************************************************
 *
 * Name:          global_types.h
 *
 * Description:   Standard type definitions
 *
 ****************************************************************************
 *
 *
 *
 *
 ****************************************************************************
 *                  Copyright (c) Intel 2000
 ***************************************************************************/




# 1 "L:/PLT/csw/platform/inc/gbl_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ============================================================================
File        : gbl_types.h
Description : Global types file for testing the
              os/kal package.

Notes       : This file is only used to test the compilation and
              archiving for the os/kal package.

Copyright 2001, Intel Corporation, All rights reserved.
============================================================================ */




/* Use the Xscale environment types */
# 1 "L:/PLT/env/win32/inc/xscale_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : xscale_types.h
Description : Global types file for the Xscale environment.

Notes       : This file is designed for use in the arm environment
              and is referenced from the gbl_types.h file. Use of
			  this file requires ENV_XSCALE to be defined in xscale_env.mak.
              
Copyright 2001, Intel Corporation, All rights reserved.
=========================================================================== */




typedef unsigned char	BOOL;
typedef unsigned char   UINT8;
typedef unsigned short  UINT16;
typedef unsigned long   UINT32;

typedef char            CHAR;
typedef signed char     INT8;
typedef signed short    INT16;
typedef signed long     INT32;













/*                         end of xscale_types.h
--------------------------------------------------------------------------- */



# 23 "L:/PLT/csw/platform/inc/gbl_types.h"


/* Use the NordHeim environment types */




/* Use the Arm environment types */




/* Use the Gnu environment types */




/* Use the Microsoft Visual C environment types */




  /* Standard typedefs */
  typedef unsigned char   Bool;         /* Boolean                        */

  /* Standard typedefs - to retain compatibility with TDMA */
  typedef UINT8           		 BYTE;         			/* Unsigned 8-bit quantity        */
  typedef UINT8            		 UBYTE;        			/* Unsigned 8-bit quantity        */
  typedef UINT16          		 UWORD;        			/* Unsigned 16-bit quantity       */
  typedef UINT16          		 WORD;         			/* Unsigned 16-bit quantity       */
  typedef INT16           		 SWORD;        			/* Signed 16-bit quantity         */
  typedef UINT32                 DWORD;        			/* Unsigned 32-bit quantity       */
  typedef unsigned long long     UINT64;                /* Unsigned 64-bit quantity       */
  typedef void* 		         VOID_PTR;























  /* A NULL value is required such that it is not mistaken for a valid */
  /* value which includes values in the range of modulo 64. */


  /* Definition of NULL is required */















/*                      end of gbl_types.h
--------------------------------------------------------------------------- */



# 25 "L:/PLT/csw/platform/inc/global_types.h"
# 1 "L:/PLT/hal/core/inc/utils.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

//Copyright 2005, Intel Corporation, All rights reserved.
/************************************************************************/
/*   Utils.h - 															*/
/* 																		*/
/************************************************************************/

# 312 "L:/PLT/hal/core/inc/utils.h"

# 26 "L:/PLT/csw/platform/inc/global_types.h"

  /* Standard typedefs */
  typedef volatile UINT8  *V_UINT8_PTR;  /* Ptr to volatile unsigned 8-bit quantity       */
  typedef volatile UINT16 *V_UINT16_PTR; /* Ptr to volatile unsigned 16-bit quantity       */
  typedef volatile UINT32 *V_UINT32_PTR; /* Ptr to volatile unsigned 32-bit quantity       */

  typedef unsigned int    U32Bits;
  typedef BOOL BOOLEAN;


  typedef const char *    SwVersion;



  /* Handy macros */
# 47 "L:/PLT/csw/platform/inc/global_types.h"


  /* Bit fields macros */
  // Yaeli Karni - need to work also when number GT 32 ! (march 06)


//strncat by shashal 



 







# 16 "L:/PLT/hal/core/inc/utils.h"
//should be used in BootLoader & Flasher ONLY
//#define LOW_LEVEL_ASSERTS_ONLY

//#if defined (_TAVOR_HARBELL_) || defined(SILICON_PV2)

// To Be Deleted...




typedef enum
{
	CPU_HERMON_B0 = 0,
	CPU_HERMON_B1,
	CPU_HERMON_B2,
	CPU_HERMON_B3,
	CPU_HERMON_TCB874,
	CPU_HERMONL_A0,
	CPU_HERMONC_A0,
	CPU_HERMON_TCC874,
	CPU_HERMONEL_A0,
	CPU_MANITOBA_OTHER,
	CPU_BVD,
	CPU_TAVOR_A0,
	CPU_TAVOR_B0,
	CPU_TAVOR_B1,
	CPU_TAVOR_B2,
	CPU_TAVOR_PV_A0,
	CPU_TAVOR_PV_B0,
	CPU_TAVOR_PV_C0,
	CPU_TTC,
	CPU_OTHER
}CPU_Version;  //if this enum changed, update also the CPU_Version_str[]

// Returns the CPU version according to the above list
CPU_Version GetCpuVersion(void);

typedef enum CPU_family_tag
{
	CPU_TAVOR_PV2,		//Z0, A0, B0
	CPU_TAVOR_MG1,		// Z0=A0, A1, B0
	CPU_TAVOR_MG2,		// A0
	CPU_ESHEL,		// A0
	CPU_NEVO,		// A0
	CPU_ESHEL_LTE,	// A0
	CPU_FAMILY_UNKN
}CPU_family;

CPU_family GetCpuFamily(void);
// Returns the original CPSR
// exp: old_level = disableInterrupts();
// old_level MUST be local automatic variable !!
unsigned long disableInterrupts(void);

// Returns the original CPSR
// exp: old_level = enableInterrupts();
// old_level MUST be local automatic variable !!
unsigned long enableInterrupts(void);

// Restores the IF bits in the CPSR to that value of the CPSR passed.
// The latter should be obtained from the enable/disable functions
// exp: restoreInterrupts(old_level);
// old_level MUST be local automatic variable !!
void restoreInterrupts(unsigned long ir);

// Count Leading Zeros
// Returns: 0 for 0x8xxxxxxx; 1 for 0x04xxxxxx; 31 for 0x00000001; 32 for 0
int _clz(unsigned long x);

// revert
// Returns: reverted endian value.change the order of bytes in the 32bit parameter from big to little endian and vice versa.
unsigned long _rev(unsigned long x);

// CP14 functions
void _xsFreqChange(void);

// Enter idle mode
void _xsGoIdle(void); //just idle the Xscale core
void setIdle(void);   //same as previous
void setIdleExt(UINT32 newXPCR, UINT32 oldXPCR); //idle the core with shutting down the MEMC and modifying PMU.XPCR

//
// General: soft-restart the image
//
void doRestart(void);

// Function analog of ASSERT
void fatalError(int condition);

void asm_isb(void);
void asm_dsb(void);

// enable performance count
void enable_performance_count(void);

// Get performance count
UINT32 get_performance_count(void);

// Set performance count
void set_performance_count(UINT32 value);


// Assert macros





extern void utilsAssertFail(const char      *cond,
                            const char      *file,
                            signed short    line,
                            unsigned char   allowDiag);




//regular ASSERTs
# 141 "L:/PLT/hal/core/inc/utils.h"

# 150 "L:/PLT/hal/core/inc/utils.h"

# 158 "L:/PLT/hal/core/inc/utils.h"













//
// CP14: Performance monitoring unit access
//

// Read/Set PMNC (Control Register, see Elkart core EAS chapter 8)
// Here are the bit definitions:
# 184 "L:/PLT/hal/core/inc/utils.h"



void   cp14SetPMNC(UINT32 value);
UINT32 cp14ReadPMNC(void);

// Read the Clock Counter register (core clock or same/64 depending on the PMNC_CCNT_DIV64 bit - below)
UINT32 cp_ReadCCNT(void);  // NEW generic name. OLD & NEW are aliased
UINT32 cp14ReadCCNT(void); // OLD non-generic name, to be obsolete.
UINT32 cp14SetCCNT(UINT32 value);
UINT32  cp14ReadEVTSEL(void);
void  cp14SetEVTSEL(UINT32 value);

UINT32 getCpRateKHz(void); // returns CP-counter rate in kHz or 0-unknown/default. Depends upon Core frequency


//
// CP6: WS Primary INTC co-processor bus access
//

UINT32 cp6ReadICPR(void);
UINT32 cp6ReadICIP(void);
UINT32 cp6ReadICFP(void);
UINT32 cp6ReadICHP(void);
UINT32 cp6ReadICMR(void);
void cp6WriteICMR(UINT32 value);







//#if defined (_TAVOR_HARBELL_)|| defined(SILICON_PV2)

//ON-CHIP trace buffer is not supported on TAVOR A0 but on B0 only with JTAG protocol
// Let's put macro-stubs meanwhile



//#define ReadTBREG(x)                0
//#define ReadCHKPT1(x)               0
//#define ReadCHKPT0(x)               0
# 243 "L:/PLT/hal/core/inc/utils.h"

// CPSR mode
# 256 "L:/PLT/hal/core/inc/utils.h"


UINT32 ReadSP(void);
UINT32 Read_SPSR(void);
UINT32 ReadCPSR(void);
UINT32 ReadMode_R13(UINT32 mode);
UINT32 ReadMode_R14(UINT32 mode);

// Set SP for the CPU mode specified by CPSR
void SetMode_R13(UINT32 mode, UINT32 sp);

// Set SP and SL (v7) for the current CPU mode
void SetSystemStack(UINT32 sp, UINT32 limit);

// Reads the r0-r14,pc,cpsr values into the given buffer (see EE_RegInfo_Data_t)
void   ReadRegisterContext(UINT32* pBuffer);

// Restores r0-r13,pc,cpsr values from the given buffer (see EE_RegInfo_Data_t)
// LR is not restored!
// Jumps to pBuffer->PC
void   RestoreRegisterContext(UINT32* pBuffer);

// Restores r0-r12 values from the given buffer (see EE_RegInfo_Data_t)
// r13, LR, CPSR are not restored!
// Returns from exception mode and jumps to pBuffer->PC
void   RestoreRegisterContextEx(UINT32* pBuffer, UINT32 setExcModeSP);

//#if !defined (_TAVOR_HARBELL_) && !defined(SILICON_PV2) /* XSCALE only */
# 306 "L:/PLT/hal/core/inc/utils.h"

void	doTurboFrequencyChange(UINT32 fBit,UINT32 tBit);
UINT32	GetTurboFrequencyChangeCfgBits(void *pRegAddress);
UINT32	RunOperationUnderSpecificStack_ASM(void *pFuncAddress,void *pStackAddress, UINT32 funcParam1);



# 15 "L:/PLT/softutil/softutil/inc/mep.h"




/**************************************************************************************/
/**************************************************************************************/




//#if defined (SS_FEATURE) 
# 33 "L:/PLT/softutil/softutil/inc/mep.h"











//#if defined (SS_FEATURE) && defined(SS_IPC_SUPPORT)







//#ifdef SS_IPC_SUPPORT 





/**************************************************************************************/
/**************************************************************************************/


typedef enum
{
	MEP_LSB,
	MEP_MSB
} MEP_SIGNIFICANT_BITs;


typedef enum
{
	MEP_FALSE,
	MEP_TRUE
} MEP_BOOL;



typedef enum
{
	MEP_DB_FIX,
	MEP_DB_MEP,
	MEP_DB_UDP
} MEP_DB_TYPE;


typedef enum
{
	MEP_UDP_RC_OK,
	MEP_UDP_RC_NOT_OK,
	MEP_UDP_RC_NOT_SUPPORT,
	MEP_UDP_RC_FILE_INTEGRITY_FAILED,
	MEP_UDP_RC_FILE_CONFIDENTIALITY_FAILED




} MEP_UDP_RC;


typedef enum
{
	MEP_FIX_RC 			= 0x2000,                // These three enum's will be OR operation with the above
	MEP_CHANGEABLE_RC 	= 0x4000,                // MEP and UDP enum's to identify the source of problem.
	UDP_CHANGEABLE_RC 	= 0x8000
} MEP_RC_GENERAL;



typedef enum
{
	MEP_CAT_NP,                      // Network personalization
	MEP_CAT_NSP,                     // Network subset personalization
	MEP_CAT_SP,                      // Service provider
	MEP_CAT_CP,                      // Corporate personalization
	MEP_CAT_SIM_LOCK,                // SIM/USIM personalization
	MEP_MAX_CAT
} MEP_CAT;


typedef enum
{
	MEP_CODE_ID_NP,                      // Network personalization
	MEP_CODE_ID_NSP,                     // Network subset personalization
	MEP_CODE_ID_SP,                      // Service provider
	MEP_CODE_ID_CP,                      // Corporate personalization
	MEP_CODE_ID_SIM_LOCK,                // SIM/USIM personalization
	MEP_MAX_CODE_ID_CAT
} MEP_CODE_ID;


typedef enum
{
	UDP_CAT_SD,                      // SIM Detection
	UDP_CAT_USD,                     // Unauthorized SIM Detection
	UDP_MAX_CAT
} UDP_CAT;



typedef enum
{
	MEP_SI_OFF,
	MEP_SI_ON,
	MEP_SI_DISABLE
} MEP_SI;


typedef enum
{
	UDP_SI_OFF,
	UDP_SI_ON,
} UDP_SI;



typedef enum
{
	MEP_ENCRYPT,
	MEP_DECRYPT
} MEP_ENC_DEC;


/**************************************************************************************/
/**************************************************************************************/

typedef UINT8  	MEP_BCD;    			/* 0-9 and 15 (for unused) */


typedef struct
{
  UINT8      	signature[20];
} MEP_SIGNATURE;

//ICAT EXPORTED STRUCT
typedef struct
{
	UINT8		number[32];
	UINT8		length;
} MEP_PASSWORD;

//ICAT EXPORTED STRUCT
typedef struct
{
	UINT16		year;
	UINT8		month;
	UINT8		day;
} MEP_DATE;


typedef UINT16 MEP_MNC;
typedef UINT16 MEP_MCC;
typedef UINT16 MEP_AccessTechnologyId;
typedef UINT8 MEP_Boolean;


typedef struct
{
	MEP_MCC		mcc;                 	// Mobile Country Code
	MEP_MNC		mnc;                 	// Mobile Network Code
	UINT16		AccessTechnology;
} MEP_PLMN;                          	// Public Land Mobile Network


typedef struct
{
	MEP_PLMN    	plmn;
	MEP_Boolean     mncThreeDigitsDecoding;  // Is three digit encoding used
} MEP_SIMPLMN;

//#if !defined (SS_IPC_SUPPORT) /* define MEP_CODE_PLMN_LIST for DKB and GED version */
# 217 "L:/PLT/softutil/softutil/inc/mep.h"

typedef struct
{
  MEP_BCD		networkSubsetId[2];     /* Bits 0-3 = IMSI digit 6 ,  Bits 4-7 = IMSI digit 7 */
} MEP_CODE_NS;

//#if defined(SS_IPC_SUPPORT)


//ICAT EXPORTED STRUCT
typedef struct
{
  UINT8      	serviceproviderId[4];
} MEP_CODE_SP;

# 239 "L:/PLT/softutil/softutil/inc/mep.h"

//#if defined(SS_IPC_SUPPORT)

//ICAT EXPORTED STRUCT
typedef struct
{
  UINT8     	corporateId[4];
} MEP_CODE_CP;
# 254 "L:/PLT/softutil/softutil/inc/mep.h"


typedef struct
{
    UINT8        length;
    UINT8        contents[8];
} MEP_IMSI;


typedef struct
{
  MEP_IMSI      simId;							/* store the raw IMSI (whole IMSI) */
} MEP_CODE_SIMUSIM;



/******************************************  Mep Fix Data  ********************************************/
//#if defined(SS_IPC_SUPPORT)





//ICAT EXPORTED STRUCT
typedef struct 
{
	MEP_SIMPLMN 		networkIds[100];	
	MEP_CODE_NS   		lockNS[100];
	MEP_CODE_SP			lockSP[100];
	MEP_CODE_CP			lockCP[100];
	MEP_CODE_SIMUSIM 	SimUsim;
}MEP_CODE;
# 297 "L:/PLT/softutil/softutil/inc/mep.h"
//ICAT EXPORTED STRUCT
typedef struct
{
	MEP_CODE            code;
	UINT8				DefaultSiActivated;
	UINT8				DefaultSiEnabled;
} MEP_CAT_DATA;

//ICAT EXPORTED STRUCT
typedef struct
{
	MEP_PASSWORD		CatPsw[MEP_MAX_CAT];
	MEP_PASSWORD		UnblockPsw;
//#if defined(SS_IPC_SUPPORT) && defined(EXTENED_TRIAL_LIMIT_MEP)


	UINT8				TrialLimit[MEP_MAX_CAT];



} MEP_BLOCK_DATA;

//#if defined(SS_IPC_SUPPORT)

//ICAT EXPORTED STRUCT
typedef struct
{
	/*modified for #499867 2014.0318 by yunhail begin*/   
	UINT32				VersionSize; 
	/*modified for #499867 2014.0318 by yunhail end*/   
	MEP_CAT_DATA   		data;                                      // !!! Run F8 And F9 on this field
	MEP_BLOCK_DATA		blocking;                                  // !!! Run F8 And F9 on this field
	UINT8      			signature[20];             // This is the output of the F9 function
	UINT8				DataIsEncrypted;                           // Should be set to FALSE or TRUE -- do not run F8 | F9
} MEP_FIX;
# 342 "L:/PLT/softutil/softutil/inc/mep.h"



/******************************************  Mep Changeable Data  *******************************************/


// Regarding the SI field -- it will be manage as bit wise
//                           0 = Not_activate or Disable,  1 = activate or Enable
// bit 0 -- Sim/Usim category
// bit 1 -- Network category
// bit 2 -- Network Subset category
// bit 3 -- Service Provider category
// bit 4 -- Corporate category

//ICAT EXPORTED STRUCT
typedef struct
{
	UINT8				SiActivated;
	UINT8				SiEnabled;
	UINT8				TrialCounter[MEP_MAX_CAT];
	UINT8				padding[15];
	UINT8      			signature[20];
} MEP_CHANGEABLE;




/******************************************  Udp Changeable Data  *******************************************/
//ICAT EXPORTED STRUCT
typedef struct
{
	UINT8				iccid[10];
	MEP_DATE			date;
} UDP_ASL_DATA;

//ICAT EXPORTED STRUCT
typedef struct
{
	UDP_ASL_DATA		data[10];
	UINT8				NumAslEntries;
} UDP_ASL;


//ICAT EXPORTED STRUCT
typedef struct
{
	MEP_PASSWORD		psw;
	UDP_ASL				asl;
	UINT8				si;
	UINT8				padding[15];
	UINT8      			signature[20];
} UDP_CHANGEABLE;




/*********************************************  Mep Main Database   *******************************************/

typedef struct
{
	MEP_FIX					MepFix;
	MEP_CHANGEABLE			MepChangeable;
	UDP_CHANGEABLE     		UdpChangeable;
	MEP_UDP_RC				MepErrorCode;
	MEP_UDP_RC	 			UdpErrorCode;
} MEP_DATA_BASE;



/**************************************************************************************/
/**************************************************************************************/

// Platform API

MEP_UDP_RC MEPInit ( MEP_FIX   *MepDdrBuffaddr );
MEP_UDP_RC UdpInit ( void );
MEP_UDP_RC MEPPhase1Iinit ( void );
MEP_UDP_RC MEPPhase2Iinit ( void );

MEP_UDP_RC MEPInit_2 ( MEP_FIX   *MepDdrBuffaddr );
MEP_UDP_RC MEPPhase1Iinit_2 ( void );
MEP_UDP_RC MEPPhase2Iinit_2 ( void );
MEP_UDP_RC UdpInit_2 ( void );


/**************************************************************************************/
/**************************************************************************************/


////////////////////////////
// callbacks definition
////////////////////////////

typedef  void  (*MEP_Callback)(MEP_UDP_RC*);


typedef union
{
    void    (*MEP_PutTC_Callback)(MEP_UDP_RC*) ;
    void    (*MEP_PutSI_Callback)(MEP_UDP_RC*) ;
    void    (*UDP_PutSI_Callback)(MEP_UDP_RC*) ;
    void    (*UDP_PutASL_Callback)(MEP_UDP_RC*) ;
    void    (*UDP_PutPassword_Callback)(MEP_UDP_RC*) ;
} MEP_UDP_CallBackFuncS;

//ICAT EXPORTED STRUCT
typedef struct
{
	UINT32 ulDebugInfoTag;
	UINT32 ulMrdInfo;
	UINT32 ulMepInfo;
}MRD_DEBUG_INFO;

/**************************************************************************************/
/**************************************************************************************/

// L1 API

MEP_UDP_RC MEP_GetCK ( MEP_CAT   category , MEP_PASSWORD   *ck );
MEP_UDP_RC MEP_GetGC ( MEP_CODE_ID   codeId , MEP_CODE   *gc );
MEP_UDP_RC MEP_GetTC ( MEP_CAT   category , UINT8   *tc );
MEP_UDP_RC MEP_GetSI ( UINT8   *SiActivated , UINT8   *SiEnabled );
//#if defined(SS_IPC_SUPPORT) && defined(EXTENED_TRIAL_LIMIT_MEP)

MEP_UDP_RC MEP_GetTL ( MEP_CAT   category, UINT8     *tl );



MEP_UDP_RC ME_GetUPW ( MEP_PASSWORD     *Upsw );

MEP_UDP_RC MEP_PutTC ( UINT8   *tc , MEP_CAT     category , MEP_UDP_CallBackFuncS 	*CallBackFunc );
MEP_UDP_RC MEP_PutSI ( UINT8   *SiActivated , UINT8   *SiEnabled , MEP_UDP_CallBackFuncS 	*CallBackFunc );

MEP_UDP_RC MEP_GetCK_2 ( MEP_CAT   category , MEP_PASSWORD   *ck );
MEP_UDP_RC MEP_GetGC_2 ( MEP_CODE_ID   codeId , MEP_CODE   *gc );
MEP_UDP_RC MEP_GetTC_2 ( MEP_CAT   category , UINT8   *tc );
MEP_UDP_RC MEP_GetSI_2 ( UINT8   *SiActivated , UINT8   *SiEnabled );
//#if defined(SS_IPC_SUPPORT) && defined(EXTENED_TRIAL_LIMIT_MEP)

MEP_UDP_RC MEP_GetTL_2 ( MEP_CAT   category, UINT8     *tl );



MEP_UDP_RC ME_GetUPW_2 ( MEP_PASSWORD     *Upsw );

MEP_UDP_RC MEP_PutTC_2 ( UINT8   *tc , MEP_CAT     category , MEP_UDP_CallBackFuncS 	*CallBackFunc );
MEP_UDP_RC MEP_PutSI_2 ( UINT8   *SiActivated , UINT8   *SiEnabled , MEP_UDP_CallBackFuncS 	*CallBackFunc );


MEP_UDP_RC UDP_GetSI ( UINT8   *SiActivated );
MEP_UDP_RC UDP_GetASL ( UDP_ASL   *asl );
MEP_UDP_RC UDP_GetPassword ( MEP_PASSWORD     *psw );


MEP_UDP_RC UDP_PutSI ( UINT8   *SiActivated , MEP_UDP_CallBackFuncS 	*CallBackFunc );
MEP_UDP_RC UDP_PutASL ( UDP_ASL   *asl , MEP_UDP_CallBackFuncS 	*CallBackFunc );
MEP_UDP_RC UDP_PutPassword ( MEP_PASSWORD     *psw , MEP_UDP_CallBackFuncS 	*CallBackFunc );

void MEP_UpdateSignature(MEP_FIX *mep_fix);
//#if defined(SS_IPC_SUPPORT)

int MEP_UpdateToMRD(UINT32 version, UINT32 date, MEP_FIX* fix, const char* name);


/**************************************************************************************/
/**************************************************************************************/
# 22 "L:/PLT/softutil/softutil/src/mep_data.c"

/**************************************************************************************/
/**************************************************************************************/


MEP_DATA_BASE	   mep_database;
MEP_DATA_BASE	   mep_database_2;


/**************************************************************************************/
/**************************************************************************************/
